import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/category_spec_template.dart';
import '../../domain/entities/category_templates.dart';
import 'specification_field_widget.dart';

/// 分类规格模板查看器
/// 
/// 用于展示和编辑分类规格模板
class CategorySpecTemplateViewer extends ConsumerStatefulWidget {
  final String categoryCode;
  final Map<String, dynamic>? initialValues;
  final ValueChanged<Map<String, dynamic>>? onValuesChanged;
  final bool isReadOnly;

  const CategorySpecTemplateViewer({
    super.key,
    required this.categoryCode,
    this.initialValues,
    this.onValuesChanged,
    this.isReadOnly = false,
  });

  @override
  ConsumerState<CategorySpecTemplateViewer> createState() => 
      _CategorySpecTemplateViewerState();
}

class _CategorySpecTemplateViewerState 
    extends ConsumerState<CategorySpecTemplateViewer> {
  late CategorySpecTemplate? template;
  late Map<String, dynamic> values;
  final Map<String, bool> _groupExpanded = {};

  @override
  void initState() {
    super.initState();
    template = VanHubCategoryTemplates.getTemplate(widget.categoryCode);
    values = Map<String, dynamic>.from(widget.initialValues ?? {});
    
    // 初始化分组展开状态
    if (template != null) {
      for (final group in template!.fieldGroups) {
        _groupExpanded[group.id] = group.defaultExpanded;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (template == null) {
      return _buildNoTemplateWidget();
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildRequiredFields(),
            const SizedBox(height: 16),
            _buildOptionalFields(),
          ],
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          _getCategoryIcon(template!.categoryCode),
          size: 28,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                template!.categoryName,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (template!.description != null) ...[
                const SizedBox(height: 4),
                Text(
                  template!.description!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        Chip(
          label: Text('v${template!.version}'),
          backgroundColor: Colors.grey[200],
        ),
      ],
    );
  }

  /// 构建必填字段
  Widget _buildRequiredFields() {
    if (template!.requiredFields.isEmpty) {
      return const SizedBox.shrink();
    }

    return _buildFieldSection(
      title: '必填字段',
      icon: Icons.star,
      color: Colors.red,
      fields: template!.requiredFields,
    );
  }

  /// 构建可选字段
  Widget _buildOptionalFields() {
    if (template!.optionalFields.isEmpty) {
      return const SizedBox.shrink();
    }

    // 按分组组织字段
    final groupedFields = <String, List<SpecField>>{};
    final ungroupedFields = <SpecField>[];

    for (final field in template!.optionalFields) {
      if (field.groupId != null) {
        groupedFields.putIfAbsent(field.groupId!, () => []).add(field);
      } else {
        ungroupedFields.add(field);
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分组字段
        ...template!.fieldGroups.map((group) {
          final fields = groupedFields[group.id] ?? [];
          if (fields.isEmpty) return const SizedBox.shrink();
          
          return _buildFieldGroup(group, fields);
        }),
        
        // 未分组字段
        if (ungroupedFields.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildFieldSection(
            title: '其他字段',
            icon: Icons.more_horiz,
            color: Colors.grey,
            fields: ungroupedFields,
          ),
        ],
      ],
    );
  }

  /// 构建字段分组
  Widget _buildFieldGroup(SpecFieldGroup group, List<SpecField> fields) {
    final isExpanded = _groupExpanded[group.id] ?? group.defaultExpanded;

    return Column(
      children: [
        const SizedBox(height: 16),
        Card(
          elevation: 1,
          child: Column(
            children: [
              ListTile(
                leading: Icon(
                  _getGroupIcon(group.icon),
                  color: Theme.of(context).primaryColor,
                ),
                title: Text(
                  group.label,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: group.description != null 
                    ? Text(group.description!) 
                    : null,
                trailing: group.collapsible 
                    ? Icon(isExpanded ? Icons.expand_less : Icons.expand_more)
                    : null,
                onTap: group.collapsible ? () {
                  setState(() {
                    _groupExpanded[group.id] = !isExpanded;
                  });
                } : null,
              ),
              if (isExpanded) ...[
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: fields.map((field) => 
                      _buildFieldWidget(field)
                    ).toList(),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建字段区域
  Widget _buildFieldSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<SpecField> fields,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...fields.map((field) => _buildFieldWidget(field)),
      ],
    );
  }

  /// 构建字段组件
  Widget _buildFieldWidget(SpecField field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: SpecificationFieldWidget(
        field: field,
        value: values[field.name],
        isReadOnly: widget.isReadOnly,
        onChanged: (value) {
          setState(() {
            values[field.name] = value;
          });
          widget.onValuesChanged?.call(values);
        },
      ),
    );
  }

  /// 构建无模板提示
  Widget _buildNoTemplateWidget() {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '未找到分类模板',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '分类代码: ${widget.categoryCode}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: 导航到模板管理页面
              },
              icon: const Icon(Icons.add),
              label: const Text('创建模板'),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String categoryCode) {
    switch (categoryCode.toUpperCase()) {
      case 'ELECTRICAL':
        return Icons.electrical_services;
      case 'PLUMBING':
        return Icons.water_drop;
      case 'STORAGE':
        return Icons.inventory_2;
      case 'BEDDING':
        return Icons.bed;
      case 'KITCHEN':
        return Icons.kitchen;
      case 'BATHROOM':
        return Icons.bathroom;
      case 'EXTERIOR':
        return Icons.directions_car;
      case 'CHASSIS':
        return Icons.build;
      default:
        return Icons.category;
    }
  }

  /// 获取分组图标
  IconData _getGroupIcon(String? iconName) {
    if (iconName == null) return Icons.folder;
    
    switch (iconName) {
      case 'electrical_services':
        return Icons.electrical_services;
      case 'battery_full':
        return Icons.battery_full;
      case 'power':
        return Icons.power;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'security':
        return Icons.security;
      case 'water_drop':
        return Icons.water_drop;
      case 'water_pump':
        return Icons.water;
      case 'water_tank':
        return Icons.storage;
      case 'filter_alt':
        return Icons.filter_alt;
      case 'plumbing':
        return Icons.plumbing;
      case 'verified':
        return Icons.verified;
      case 'inventory_2':
        return Icons.inventory_2;
      case 'storage':
        return Icons.storage;
      case 'lock':
        return Icons.lock;
      case 'construction':
        return Icons.construction;
      case 'shield':
        return Icons.shield;
      case 'bed':
        return Icons.bed;
      case 'airline_seat_flat':
        return Icons.airline_seat_flat;
      case 'hotel':
        return Icons.hotel;
      case 'kitchen':
        return Icons.kitchen;
      case 'local_fire_department':
        return Icons.local_fire_department;
      case 'wash':
        return Icons.wash;
      case 'cabinet':
        return Icons.kitchen;
      case 'bathroom':
        return Icons.bathroom;
      case 'wc':
        return Icons.wc;
      case 'shower':
        return Icons.shower;
      case 'directions_car':
        return Icons.directions_car;
      case 'palette':
        return Icons.palette;
      case 'luggage':
        return Icons.luggage;
      case 'beach_access':
        return Icons.beach_access;
      case 'lightbulb':
        return Icons.lightbulb;
      case 'build':
        return Icons.build;
      case 'tire_repair':
        return Icons.build;
      case 'settings_input_component':
        return Icons.settings_input_component;
      case 'speed':
        return Icons.speed;
      case 'waves':
        return Icons.waves;
      default:
        return Icons.folder;
    }
  }
}
