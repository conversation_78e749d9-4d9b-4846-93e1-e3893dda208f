import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/components/vanhub_chart.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../providers/chart_data_provider.dart';

/// 实时图表类型
enum RealTimeChartType {
  costTrend,
  progressStats,
  materialStats,
}

/// 实时图表组件
class RealTimeChartWidget extends ConsumerStatefulWidget {
  final String projectId;
  final RealTimeChartType chartType;
  final String title;
  final double? height;
  final bool enableAutoRefresh;
  final Duration refreshInterval;

  const RealTimeChartWidget({
    super.key,
    required this.projectId,
    required this.chartType,
    required this.title,
    this.height = 300,
    this.enableAutoRefresh = true,
    this.refreshInterval = const Duration(seconds: 30),
  });

  @override
  ConsumerState<RealTimeChartWidget> createState() => _RealTimeChartWidgetState();
}

class _RealTimeChartWidgetState extends ConsumerState<RealTimeChartWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // 启动数据监控
    if (widget.enableAutoRefresh) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(chartDataManagerProvider.notifier).startMonitoring(widget.projectId);
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.enableAutoRefresh) {
      ref.read(chartDataManagerProvider.notifier).stopMonitoring(widget.projectId);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            SizedBox(
              height: widget.height,
              child: _buildChart(),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.1, end: 0);
  }

  /// 构建图表头部
  Widget _buildHeader() {
    final lastUpdate = ref.watch(chartDataManagerProvider)[widget.projectId];
    
    return Row(
      children: [
        Icon(
          _getChartIcon(),
          color: VanHubBrandColors.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (lastUpdate != null)
                Text(
                  '最后更新: ${_formatTime(lastUpdate)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
            ],
          ),
        ),
        _buildRefreshButton(),
        const SizedBox(width: 8),
        _buildStatusIndicator(),
      ],
    );
  }

  /// 构建刷新按钮
  Widget _buildRefreshButton() {
    return IconButton(
      icon: AnimatedRotation(
        turns: _isRefreshing ? 1 : 0,
        duration: const Duration(milliseconds: 500),
        child: const Icon(Icons.refresh),
      ),
      onPressed: _isRefreshing ? null : _handleRefresh,
      tooltip: '手动刷新',
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator() {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: widget.enableAutoRefresh ? Colors.green : Colors.grey,
        shape: BoxShape.circle,
      ),
    ).animate(onPlay: (controller) => controller.repeat())
        .shimmer(duration: 2000.ms, color: Colors.white.withValues(alpha: 0.5));
  }

  /// 构建图表
  Widget _buildChart() {
    switch (widget.chartType) {
      case RealTimeChartType.costTrend:
        return _buildCostTrendChart();
      case RealTimeChartType.progressStats:
        return _buildProgressStatsChart();
      case RealTimeChartType.materialStats:
        return _buildMaterialStatsChart();
    }
  }

  /// 构建成本趋势图表
  Widget _buildCostTrendChart() {
    final costTrendAsync = ref.watch(realTimeCostTrendProvider(widget.projectId));
    
    return costTrendAsync.when(
      data: (series) {
        if (series.isEmpty) {
          return const Center(child: Text('暂无数据'));
        }
        
        return VanHubChart.line(
          series: series.map((s) => ChartDataSeries(
            name: s.name,
            color: Color(int.parse(s.color.substring(1), radix: 16) + 0xFF000000),
            data: s.data.map((point) => ChartDataPoint(
              x: point.x,
              y: point.y,
            )).toList(),
          )).toList(),
          enableAnimation: true,
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(height: 8),
            Text('加载失败: ${error.toString()}'),
          ],
        ),
      ),
    );
  }

  /// 构建进度统计图表
  Widget _buildProgressStatsChart() {
    final progressAsync = ref.watch(realTimeProgressStatsProvider(widget.projectId));
    
    return progressAsync.when(
      data: (stats) {
        final taskBreakdown = stats['task_breakdown'] as Map<String, dynamic>;
        
        return VanHubChart.pie(
          series: [
            ChartDataSeries(
              name: '任务分布',
              color: VanHubBrandColors.primary,
              data: [
                ChartDataPoint(x: 0, y: taskBreakdown['completed'].toDouble()),
                ChartDataPoint(x: 1, y: taskBreakdown['in_progress'].toDouble()),
                ChartDataPoint(x: 2, y: taskBreakdown['pending'].toDouble()),
              ],
            ),
          ],
          enableAnimation: true,
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('加载失败: ${error.toString()}'),
      ),
    );
  }

  /// 构建材料统计图表
  Widget _buildMaterialStatsChart() {
    final materialStatsAsync = ref.watch(realTimeMaterialStatsProvider(widget.projectId));
    
    return materialStatsAsync.when(
      data: (series) {
        if (series.isEmpty) {
          return const Center(child: Text('暂无数据'));
        }
        
        return VanHubChart.bar(
          series: series.map((s) => ChartDataSeries(
            name: s.name,
            color: Color(int.parse(s.color.substring(1), radix: 16) + 0xFF000000),
            data: s.data.map((point) => ChartDataPoint(
              x: point.x,
              y: point.y,
            )).toList(),
          )).toList(),
          enableAnimation: true,
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('加载失败: ${error.toString()}'),
      ),
    );
  }

  /// 处理手动刷新
  Future<void> _handleRefresh() async {
    setState(() {
      _isRefreshing = true;
    });
    
    _animationController.forward();
    
    try {
      switch (widget.chartType) {
        case RealTimeChartType.costTrend:
          await ref.read(realTimeCostTrendProvider(widget.projectId).notifier).refresh(widget.projectId);
          break;
        case RealTimeChartType.progressStats:
          await ref.read(realTimeProgressStatsProvider(widget.projectId).notifier).refresh(widget.projectId);
          break;
        case RealTimeChartType.materialStats:
          await ref.read(realTimeMaterialStatsProvider(widget.projectId).notifier).refresh(widget.projectId);
          break;
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        _animationController.reset();
      }
    }
  }

  /// 获取图表图标
  IconData _getChartIcon() {
    switch (widget.chartType) {
      case RealTimeChartType.costTrend:
        return Icons.trending_up;
      case RealTimeChartType.progressStats:
        return Icons.pie_chart;
      case RealTimeChartType.materialStats:
        return Icons.bar_chart;
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else {
      return '${time.month}/${time.day} ${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
