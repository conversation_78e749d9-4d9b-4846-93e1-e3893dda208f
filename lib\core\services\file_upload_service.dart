import 'dart:io';
import 'package:fpdart/fpdart.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../errors/failures.dart';
import 'image_processing_service.dart';

/// 文件上传配置
class FileUploadConfig {
  final int maxFileSizeBytes;
  final List<String> allowedMimeTypes;
  final List<String> allowedExtensions;
  final bool enableCompression;
  final int imageQuality;
  final int maxImageWidth;
  final int maxImageHeight;
  final String storageBucket;
  final String? pathPrefix;

  const FileUploadConfig({
    this.maxFileSizeBytes = 50 * 1024 * 1024, // 50MB
    this.allowedMimeTypes = const [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/mov',
      'video/avi',
      'application/pdf',
      'text/plain',
    ],
    this.allowedExtensions = const [
      '.jpg', '.jpeg', '.png', '.gif', '.webp',
      '.mp4', '.mov', '.avi',
      '.pdf', '.txt',
    ],
    this.enableCompression = true,
    this.imageQuality = 85,
    this.maxImageWidth = 1920,
    this.maxImageHeight = 1080,
    this.storageBucket = 'media',
    this.pathPrefix,
  });

  /// 图片配置
  static const FileUploadConfig images = FileUploadConfig(
    maxFileSizeBytes: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    enableCompression: true,
    imageQuality: 85,
    maxImageWidth: 1920,
    maxImageHeight: 1080,
  );

  /// 视频配置
  static const FileUploadConfig videos = FileUploadConfig(
    maxFileSizeBytes: 100 * 1024 * 1024, // 100MB
    allowedMimeTypes: ['video/mp4', 'video/mov', 'video/avi', 'video/quicktime'],
    allowedExtensions: ['.mp4', '.mov', '.avi'],
    enableCompression: false,
  );

  /// 文档配置
  static const FileUploadConfig documents = FileUploadConfig(
    maxFileSizeBytes: 20 * 1024 * 1024, // 20MB
    allowedMimeTypes: ['application/pdf', 'text/plain', 'application/msword'],
    allowedExtensions: ['.pdf', '.txt', '.doc', '.docx'],
    enableCompression: false,
  );
}

/// 文件上传进度回调
typedef UploadProgressCallback = void Function(double progress, int bytesUploaded, int totalBytes);

/// 文件上传结果
class FileUploadResult {
  final String id;
  final String fileName;
  final String filePath;
  final String publicUrl;
  final String mimeType;
  final int fileSize;
  final Map<String, dynamic> metadata;
  final DateTime uploadedAt;

  const FileUploadResult({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.publicUrl,
    required this.mimeType,
    required this.fileSize,
    required this.metadata,
    required this.uploadedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'publicUrl': publicUrl,
      'mimeType': mimeType,
      'fileSize': fileSize,
      'metadata': metadata,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }
}

/// 文件验证结果
class FileValidationResult {
  final bool isValid;
  final String? errorMessage;
  final Map<String, dynamic> fileInfo;

  const FileValidationResult({
    required this.isValid,
    this.errorMessage,
    required this.fileInfo,
  });

  static FileValidationResult valid(Map<String, dynamic> fileInfo) {
    return FileValidationResult(isValid: true, fileInfo: fileInfo);
  }

  static FileValidationResult invalid(String errorMessage) {
    return FileValidationResult(
      isValid: false,
      errorMessage: errorMessage,
      fileInfo: {},
    );
  }
}

/// 文件上传服务
class FileUploadService {
  final SupabaseClient _supabaseClient;
  final FileUploadConfig _defaultConfig;
  final ImageProcessingService _imageProcessingService;

  FileUploadService({
    required SupabaseClient supabaseClient,
    FileUploadConfig? defaultConfig,
    ImageProcessingService? imageProcessingService,
  }) : _supabaseClient = supabaseClient,
       _defaultConfig = defaultConfig ?? const FileUploadConfig(),
       _imageProcessingService = imageProcessingService ?? ImageProcessingService();

  /// 上传单个文件
  Future<Either<Failure, FileUploadResult>> uploadFile({
    required File file,
    required String destinationPath,
    FileUploadConfig? config,
    UploadProgressCallback? onProgress,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final uploadConfig = config ?? _defaultConfig;
      
      // 验证文件
      final validationResult = await validateFile(file, uploadConfig);
      if (!validationResult.isValid) {
        return Left(ValidationFailure(message: validationResult.errorMessage!));
      }

      // 生成唯一文件名
      final fileName = _generateUniqueFileName(file, destinationPath);
      final fullPath = uploadConfig.pathPrefix != null 
          ? '${uploadConfig.pathPrefix}/$fileName'
          : fileName;

      // 预处理文件（如果需要）
      File processedFile = file;
      if (uploadConfig.enableCompression && _isImage(file)) {
        final compressionResult = await _processImage(file, uploadConfig);
        processedFile = compressionResult.fold(
          (failure) => file, // 压缩失败时使用原文件
          (result) => result.processedFile,
        );
      }

      // 上传文件
      final uploadResult = await _uploadToStorage(
        file: processedFile,
        path: fullPath,
        bucket: uploadConfig.storageBucket,
        onProgress: onProgress,
      );

      return uploadResult.fold(
        (failure) => Left(failure),
        (storageResult) async {
          // 获取公共URL
          final publicUrl = _supabaseClient.storage
              .from(uploadConfig.storageBucket)
              .getPublicUrl(fullPath);

          // 构建结果
          final result = FileUploadResult(
            id: _generateFileId(),
            fileName: path.basename(file.path),
            filePath: fullPath,
            publicUrl: publicUrl,
            mimeType: validationResult.fileInfo['mimeType'] ?? 'application/octet-stream',
            fileSize: validationResult.fileInfo['fileSize'] ?? 0,
            metadata: {
              ...validationResult.fileInfo,
              ...?metadata,
            },
            uploadedAt: DateTime.now(),
          );

          return Right(result);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '文件上传失败: $e'));
    }
  }

  /// 批量上传文件
  Future<Either<Failure, List<FileUploadResult>>> uploadMultipleFiles({
    required List<File> files,
    required String destinationPath,
    FileUploadConfig? config,
    UploadProgressCallback? onProgress,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final results = <FileUploadResult>[];
      final totalFiles = files.length;
      
      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        
        final result = await uploadFile(
          file: file,
          destinationPath: destinationPath,
          config: config,
          onProgress: (progress, uploaded, total) {
            // 计算总体进度
            final fileProgress = (i + progress) / totalFiles;
            onProgress?.call(fileProgress, uploaded, total * totalFiles);
          },
          metadata: metadata,
        );

        final uploadResult = result.fold(
          (failure) => throw Exception('上传文件 ${file.path} 失败: ${failure.message}'),
          (success) => success,
        );

        results.add(uploadResult);
      }

      return Right(results);
    } catch (e) {
      return Left(UnknownFailure(message: '批量上传失败: $e'));
    }
  }

  /// 验证文件
  Future<FileValidationResult> validateFile(File file, FileUploadConfig config) async {
    try {
      // 检查文件是否存在
      if (!await file.exists()) {
        return FileValidationResult.invalid('文件不存在');
      }

      // 获取文件信息
      final fileSize = await file.length();
      final fileName = path.basename(file.path);
      final fileExtension = path.extension(fileName).toLowerCase();
      final mimeType = lookupMimeType(file.path) ?? 'application/octet-stream';

      // 检查文件大小
      if (fileSize > config.maxFileSizeBytes) {
        final maxSizeMB = (config.maxFileSizeBytes / (1024 * 1024)).toStringAsFixed(1);
        return FileValidationResult.invalid('文件大小超过限制 (${maxSizeMB}MB)');
      }

      // 检查文件扩展名
      if (config.allowedExtensions.isNotEmpty && 
          !config.allowedExtensions.contains(fileExtension)) {
        return FileValidationResult.invalid('不支持的文件格式: $fileExtension');
      }

      // 检查MIME类型
      if (config.allowedMimeTypes.isNotEmpty && 
          !config.allowedMimeTypes.contains(mimeType)) {
        return FileValidationResult.invalid('不支持的文件类型: $mimeType');
      }

      // 构建文件信息
      final fileInfo = {
        'fileName': fileName,
        'fileSize': fileSize,
        'fileExtension': fileExtension,
        'mimeType': mimeType,
        'isImage': _isImageMimeType(mimeType),
        'isVideo': _isVideoMimeType(mimeType),
        'isDocument': _isDocumentMimeType(mimeType),
      };

      return FileValidationResult.valid(fileInfo);
    } catch (e) {
      return FileValidationResult.invalid('文件验证失败: $e');
    }
  }

  /// 删除文件
  Future<Either<Failure, void>> deleteFile({
    required String filePath,
    String? bucket,
  }) async {
    try {
      await _supabaseClient.storage
          .from(bucket ?? _defaultConfig.storageBucket)
          .remove([filePath]);
      
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '删除文件失败: $e'));
    }
  }

  /// 获取文件信息
  Future<Either<Failure, Map<String, dynamic>>> getFileInfo({
    required String filePath,
    String? bucket,
  }) async {
    try {
      final info = await _supabaseClient.storage
          .from(bucket ?? _defaultConfig.storageBucket)
          .info(filePath);
      
      return Right({
        'id': info.id,
        'name': info.name,
        'size': info.size,
        'created_at': info.createdAt,
        'updated_at': info.updatedAt,
        'last_accessed_at': info.lastAccessedAt,
        'metadata': info.metadata,
      });
    } catch (e) {
      return Left(UnknownFailure(message: '获取文件信息失败: $e'));
    }
  }

  /// 生成唯一文件名
  String _generateUniqueFileName(File file, String destinationPath) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final originalName = path.basenameWithoutExtension(file.path);
    final extension = path.extension(file.path);
    final sanitizedName = _sanitizeFileName(originalName);
    
    return '$destinationPath/${timestamp}_$sanitizedName$extension';
  }

  /// 清理文件名
  String _sanitizeFileName(String fileName) {
    return fileName
        .replaceAll(RegExp(r'[^\w\-_.]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .toLowerCase();
  }

  /// 生成文件ID
  String _generateFileId() {
    return 'file_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// 上传到存储
  Future<Either<Failure, String>> _uploadToStorage({
    required File file,
    required String path,
    required String bucket,
    UploadProgressCallback? onProgress,
  }) async {
    try {
      // 注意：Supabase Flutter SDK 目前不支持上传进度回调
      // 这里我们模拟进度更新
      onProgress?.call(0.0, 0, await file.length());
      
      final result = await _supabaseClient.storage
          .from(bucket)
          .upload(path, file);
      
      onProgress?.call(1.0, await file.length(), await file.length());
      
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: '存储上传失败: $e'));
    }
  }

  /// 处理图片
  Future<Either<Failure, ImageProcessingResult>> _processImage(File file, FileUploadConfig config) async {
    try {
      final imageConfig = ImageProcessingConfig(
        maxWidth: config.maxImageWidth,
        maxHeight: config.maxImageHeight,
        quality: config.imageQuality,
        maintainAspectRatio: true,
        autoRotate: true,
        removeExifData: true,
      );

      return await _imageProcessingService.processImage(
        inputFile: file,
        config: imageConfig,
      );
    } catch (e) {
      return Left(UnknownFailure(message: '图片处理失败: $e'));
    }
  }

  /// 检查是否为图片文件
  bool _isImage(File file) {
    final mimeType = lookupMimeType(file.path);
    return _isImageMimeType(mimeType);
  }

  /// 检查是否为图片MIME类型
  bool _isImageMimeType(String? mimeType) {
    return mimeType?.startsWith('image/') ?? false;
  }

  /// 检查是否为视频MIME类型
  bool _isVideoMimeType(String? mimeType) {
    return mimeType?.startsWith('video/') ?? false;
  }

  /// 检查是否为文档MIME类型
  bool _isDocumentMimeType(String? mimeType) {
    if (mimeType == null) return false;
    return mimeType.startsWith('application/') || 
           mimeType.startsWith('text/') ||
           mimeType == 'application/pdf';
  }
}
