import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeline_tile/timeline_tile.dart';

import '../../../../core/widgets/error_display_widget.dart';
import '../../../../core/error/ui_failure.dart';
import '../../domain/entities/timeline.dart';
import '../../domain/entities/enums.dart';
import '../providers/timeline_provider.dart';
import 'log_detail_page.dart';

/// 时间轴页面
class TimelinePage extends ConsumerStatefulWidget {
  final String projectId;

  const TimelinePage({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<TimelinePage> createState() => _TimelinePageState();
}

class _TimelinePageState extends ConsumerState<TimelinePage> {
  @override
  void initState() {
    super.initState();
    _loadTimeline();
  }

  /// 加载时间轴
  Future<void> _loadTimeline() async {
    await ref.read(timelineProvider.notifier).getProjectTimeline(widget.projectId);
  }

  @override
  Widget build(BuildContext context) {
    final timelineState = ref.watch(timelineProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('项目时间轴'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: 实现筛选功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('筛选功能尚未实现')),
              );
            },
          ),
        ],
      ),
      body: _buildBody(timelineState),
      floatingActionButton: FloatingActionButton(
        onPressed: _addMilestone,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// 构建页面主体
  Widget _buildBody(TimelineState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.failure != null) {
      return ErrorDisplayWidget(
        failure: UIFailure.unknown(
          message: state.failure!.message ?? '未知错误',
          details: state.failure!.toString(),
        ),
        onRetry: _loadTimeline,
      );
    }

    final timeline = state.timeline;
    if (timeline == null || timeline.items.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadTimeline,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        itemCount: timeline.items.length,
        itemBuilder: (context, index) {
          final item = timeline.items[index];
          final isFirst = index == 0;
          final isLast = index == timeline.items.length - 1;

          return TimelineTile(
            alignment: TimelineAlign.manual,
            lineXY: 0.2,
            isFirst: isFirst,
            isLast: isLast,
            indicatorStyle: IndicatorStyle(
              width: 20,
              color: _getIndicatorColor(item),
              iconStyle: IconStyle(
                color: Colors.white,
                iconData: item.isMilestone ? Icons.flag : Icons.note,
              ),
            ),
            beforeLineStyle: LineStyle(
              color: Colors.grey[300]!,
            ),
            afterLineStyle: LineStyle(
              color: Colors.grey[300]!,
            ),
            endChild: _buildTimelineItemCard(item),
            startChild: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                item.dateDisplayText,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.timeline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无时间轴数据',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '添加日志或里程碑以构建项目时间轴',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addMilestone,
            icon: const Icon(Icons.add),
            label: const Text('添加里程碑'),
          ),
        ],
      ),
    );
  }

  /// 构建时间轴项目卡片
  Widget _buildTimelineItemCard(TimelineItem item) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _onTimelineItemTap(item),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      item.itemTitle,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (item.isMilestone && item.milestoneStatus != null)
                    _buildStatusChip(item.milestoneStatus!),
                ],
              ),
              if (item.itemDescription != null) ...[
                const SizedBox(height: 8),
                Text(
                  item.itemDescription!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 8),
              if (item.itemSystemName != null)
                Text(
                  '系统: ${item.itemSystemName}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(MilestoneStatus status) {
    Color color;
    String text;

    switch (status) {
      case MilestoneStatus.planned:
        color = Colors.blue;
        text = '计划中';
        break;
      case MilestoneStatus.inProgress:
        color = Colors.amber;
        text = '进行中';
        break;
      case MilestoneStatus.completed:
        color = Colors.green;
        text = '已完成';
        break;
      case MilestoneStatus.overdue:
        color = Colors.red;
        text = '已延期';
        break;
      case MilestoneStatus.cancelled:
        color = Colors.grey;
        text = '已取消';
        break;
      case MilestoneStatus.overdue:
        color = Colors.deepOrange;
        text = '已逾期';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 获取指示器颜色
  Color _getIndicatorColor(TimelineItem item) {
    if (item.isMilestone) {
      final status = item.milestoneStatus;
      switch (status) {
        case MilestoneStatus.planned:
          return Colors.blue;
        case MilestoneStatus.inProgress:
          return Colors.amber;
        case MilestoneStatus.completed:
          return Colors.green;
        case MilestoneStatus.overdue:
          return Colors.red;
        case MilestoneStatus.cancelled:
          return Colors.grey;
        case MilestoneStatus.overdue:
          return Colors.deepOrange;
        case null:
          return Colors.blue;
      }
    } else {
      return Colors.teal;
    }
  }

  /// 时间轴项目点击事件
  void _onTimelineItemTap(TimelineItem item) {
    if (item.isLogEntry) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => LogDetailPage(logId: item.itemId),
        ),
      );
    } else {
      // TODO: 实现里程碑详情页面
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('里程碑: ${item.itemTitle}')),
      );
    }
  }

  /// 添加里程碑
  void _addMilestone() {
    // TODO: 实现添加里程碑功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加里程碑功能尚未实现')),
    );
  }
}