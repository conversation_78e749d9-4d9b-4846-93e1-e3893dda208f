import 'package:freezed_annotation/freezed_annotation.dart';
import '../services/share_service.dart';

part 'share_link.freezed.dart';
part 'share_link.g.dart';

/// 分享链接实体
@freezed
class ShareLink with _$ShareLink {
  const factory ShareLink({
    required String id,
    required String contentId,
    required ShareContentType contentType,
    required String userId,
    required String shareUrl,
    required SharePermissionLevel permissionLevel,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? expiresAt,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? accessToken,
    @Default(0) int viewCount,
    @Default(0) int shareCount,
    @Default(true) bool isActive,
    @Default(false) bool requiresAuth,
    @Default([]) List<String> allowedUsers,
    @Default({}) Map<String, dynamic> metadata,
  }) = _ShareLink;

  factory ShareLink.fromJson(Map<String, dynamic> json) => 
      _$ShareLinkFromJson(json);
}

/// 分享链接扩展方法
extension ShareLinkX on ShareLink {
  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否可访问
  bool get isAccessible {
    return isActive && !isExpired;
  }

  /// 获取分享类型文本
  String get contentTypeText {
    switch (contentType) {
      case ShareContentType.project:
        return '项目';
      case ShareContentType.material:
        return '材料';
      case ShareContentType.bomItem:
        return 'BOM项目';
      case ShareContentType.modificationLog:
        return '改装日志';
      case ShareContentType.chart:
        return '图表';
      case ShareContentType.report:
        return '报告';
    }
  }

  /// 获取权限级别文本
  String get permissionLevelText {
    switch (permissionLevel) {
      case SharePermissionLevel.public:
        return '公开';
      case SharePermissionLevel.unlisted:
        return '不公开';
      case SharePermissionLevel.private:
        return '私有';
    }
  }

  /// 获取权限级别描述
  String get permissionLevelDescription {
    switch (permissionLevel) {
      case SharePermissionLevel.public:
        return '任何人都可以通过搜索找到并访问';
      case SharePermissionLevel.unlisted:
        return '只有拥有链接的人才能访问';
      case SharePermissionLevel.private:
        return '需要权限验证才能访问';
    }
  }

  /// 获取剩余有效时间
  Duration? get remainingTime {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// 获取剩余有效时间文本
  String get remainingTimeText {
    final remaining = remainingTime;
    if (remaining == null) return '永久有效';
    if (remaining == Duration.zero) return '已过期';
    
    if (remaining.inDays > 0) {
      return '${remaining.inDays}天后过期';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours}小时后过期';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes}分钟后过期';
    } else {
      return '即将过期';
    }
  }

  /// 检查用户是否有访问权限
  bool canUserAccess(String? userId) {
    if (!isAccessible) return false;
    
    switch (permissionLevel) {
      case SharePermissionLevel.public:
        return true;
      case SharePermissionLevel.unlisted:
        return true;
      case SharePermissionLevel.private:
        if (userId == null) return false;
        return userId == this.userId || allowedUsers.contains(userId);
    }
  }

  /// 创建分享文本
  String createShareText() {
    final buffer = StringBuffer();
    
    if (title != null) {
      buffer.writeln('📋 ${title!}');
    }
    
    if (description != null) {
      buffer.writeln(description!);
    }
    
    buffer.writeln();
    buffer.writeln('🔗 查看详情: $shareUrl');
    buffer.writeln();
    buffer.writeln('📱 来自VanHub改装宝');
    
    return buffer.toString();
  }

  /// 创建社交媒体分享文本
  String createSocialShareText({String? platform}) {
    final buffer = StringBuffer();
    
    switch (platform?.toLowerCase()) {
      case 'wechat':
      case '微信':
        buffer.writeln('分享一个$contentTypeText给你');
        if (title != null) buffer.writeln('📋 ${title!}');
        break;
        
      case 'weibo':
      case '微博':
        buffer.write('【VanHub改装宝】');
        if (title != null) buffer.write('分享$contentTypeText：${title!}');
        buffer.write(' #房车改装# #DIY#');
        break;
        
      case 'qq':
        buffer.writeln('来看看这个$contentTypeText');
        if (title != null) buffer.writeln(title!);
        break;
        
      default:
        return createShareText();
    }
    
    if (description != null && description!.length <= 100) {
      buffer.writeln(description!);
    }
    
    buffer.writeln(shareUrl);
    
    return buffer.toString();
  }

  /// 获取Open Graph元数据
  Map<String, String> getOpenGraphMetadata() {
    return {
      'og:title': title ?? '$contentTypeText - VanHub改装宝',
      'og:description': description ?? '来自VanHub改装宝的$contentTypeText分享',
      'og:url': shareUrl,
      'og:type': 'article',
      'og:site_name': 'VanHub改装宝',
      if (thumbnailUrl != null) 'og:image': thumbnailUrl!,
    };
  }

  /// 复制链接
  ShareLink copyWith({
    String? id,
    String? contentId,
    ShareContentType? contentType,
    String? userId,
    String? shareUrl,
    SharePermissionLevel? permissionLevel,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiresAt,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? accessToken,
    int? viewCount,
    int? shareCount,
    bool? isActive,
    bool? requiresAuth,
    List<String>? allowedUsers,
    Map<String, dynamic>? metadata,
  }) {
    return ShareLink(
      id: id ?? this.id,
      contentId: contentId ?? this.contentId,
      contentType: contentType ?? this.contentType,
      userId: userId ?? this.userId,
      shareUrl: shareUrl ?? this.shareUrl,
      permissionLevel: permissionLevel ?? this.permissionLevel,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      accessToken: accessToken ?? this.accessToken,
      viewCount: viewCount ?? this.viewCount,
      shareCount: shareCount ?? this.shareCount,
      isActive: isActive ?? this.isActive,
      requiresAuth: requiresAuth ?? this.requiresAuth,
      allowedUsers: allowedUsers ?? this.allowedUsers,
      metadata: metadata ?? this.metadata,
    );
  }
}
