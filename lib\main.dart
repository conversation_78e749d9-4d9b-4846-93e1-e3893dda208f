import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/api/supabase_config.dart';
import 'core/theme/vanhub_theme_v2.dart';
import 'core/localization/vanhub_l10n.dart';
import 'features/home/<USER>/pages/home_page.dart';
import 'features/test/test_core_features_page.dart';
import 'features/modification_log/presentation/pages/test_log_system_page.dart';
import 'features/modification_log/presentation/pages/test_log_editor_page.dart';
import 'features/material/presentation/pages/specification_test_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await SupabaseConfig.initialize();
    debugPrint('Supabase初始化成功');

    // 确保Supabase完全初始化后再启动应用
    runApp(const ProviderScope(child: VanHubApp()));
  } catch (e) {
    debugPrint('Supabase初始化失败: $e');

    // 即使Supabase初始化失败，也启动应用（游客模式）
    runApp(const ProviderScope(child: VanHubApp()));
  }
}

class VanHubApp extends StatelessWidget {
  const VanHubApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VanHub 改装宝',
      theme: VanHubThemeV2.getLightTheme(),
      darkTheme: VanHubThemeV2.getDarkTheme(),
      themeMode: ThemeMode.system,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        VanHubL10nDelegate(),
      ],
      supportedLocales: VanHubL10n.supportedLocales,
      home: const HomePage(),
      routes: {
        '/test': (context) => const TestCoreFeaturesPage(),
        '/test_log_system': (context) => const TestLogSystemPage(),
        '/test_log_editor': (context) => const TestLogEditorPage(),
        '/test_specification': (context) => const SpecificationTestPage(),
      },
      debugShowCheckedModeBanner: false,
    );
  }
}