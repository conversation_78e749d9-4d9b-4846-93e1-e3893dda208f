/// VanHub Input Component 2.0
/// 
/// 智能输入组件，支持浮动标签和高级验证
/// 
/// 特性：
/// - 浮动标签动画
/// - 智能验证系统
/// - 多种输入类型
/// - 响应式设计
/// - 情感化反馈
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';

/// 输入框类型枚举
enum VanHubInputType {
  text,       // 文本输入
  email,      // 邮箱输入
  password,   // 密码输入
  number,     // 数字输入
  phone,      // 电话输入
  multiline,  // 多行文本
  search,     // 搜索输入
  url,        // URL输入
}

/// 输入框变体枚举
enum VanHubInputVariant {
  outlined,   // 轮廓样式
  filled,     // 填充样式
  underlined, // 下划线样式
  borderless, // 无边框样式
}

/// 输入框尺寸枚举
enum VanHubInputSize {
  sm,  // 小尺寸
  md,  // 中等尺寸
  lg,  // 大尺寸
}

/// 验证规则类
class ValidationRule {
  final String Function(String?) validator;
  final String errorMessage;

  const ValidationRule({
    required this.validator,
    required this.errorMessage,
  });

  /// 必填验证
  static ValidationRule required([String? message]) {
    return ValidationRule(
      validator: (value) => value?.isEmpty ?? true ? 'required' : '',
      errorMessage: message ?? '此字段为必填项',
    );
  }

  /// 邮箱验证
  static ValidationRule email([String? message]) {
    return ValidationRule(
      validator: (value) {
        if (value?.isEmpty ?? true) return '';
        final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
        return emailRegex.hasMatch(value!) ? '' : 'email';
      },
      errorMessage: message ?? '请输入有效的邮箱地址',
    );
  }

  /// 最小长度验证
  static ValidationRule minLength(int length, [String? message]) {
    return ValidationRule(
      validator: (value) => (value?.length ?? 0) < length ? 'minLength' : '',
      errorMessage: message ?? '至少需要$length个字符',
    );
  }

  /// 数字验证
  static ValidationRule numeric([String? message]) {
    return ValidationRule(
      validator: (value) {
        if (value?.isEmpty ?? true) return '';
        return double.tryParse(value!) == null ? 'numeric' : '';
      },
      errorMessage: message ?? '请输入有效的数字',
    );
  }
}

/// VanHub Input 2.0 - 智能输入组件
class VanHubInputV2 extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final String? initialValue;
  final VanHubInputType type;
  final VanHubInputVariant variant;
  final VanHubInputSize size;
  final bool enabled;
  final bool readOnly;
  final bool obscureText;
  final int? maxLines;
  final int? maxLength;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final List<ValidationRule> validationRules;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool showFloatingLabel;
  final bool enableValidation;
  final EmotionalState? emotionalState;
  
  // 动画配置
  final Duration animationDuration;
  final Curve animationCurve;

  const VanHubInputV2({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.initialValue,
    this.type = VanHubInputType.text,
    this.variant = VanHubInputVariant.outlined,
    this.size = VanHubInputSize.md,
    this.enabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.maxLines,
    this.maxLength,
    this.textInputAction,
    this.onChanged,
    this.onTap,
    this.onEditingComplete,
    this.onSubmitted,
    this.controller,
    this.focusNode,
    this.validationRules = const [],
    this.prefixIcon,
    this.suffixIcon,
    this.showFloatingLabel = true,
    this.enableValidation = true,
    this.emotionalState,
    this.animationDuration = VanHubAnimationDurations.fast,
    this.animationCurve = VanHubAnimationCurves.easeOut,
  });

  /// 文本输入构造函数
  const VanHubInputV2.text({
    Key? key,
    String? label,
    String? hint,
    String? initialValue,
    bool enabled = true,
    ValueChanged<String>? onChanged,
    TextEditingController? controller,
    List<ValidationRule> validationRules = const [],
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) : this(
          key: key,
          label: label,
          hint: hint,
          initialValue: initialValue,
          type: VanHubInputType.text,
          enabled: enabled,
          onChanged: onChanged,
          controller: controller,
          validationRules: validationRules,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
        );

  /// 邮箱输入构造函数
  VanHubInputV2.email({
    Key? key,
    String? label,
    String? hint,
    String? initialValue,
    bool enabled = true,
    ValueChanged<String>? onChanged,
    TextEditingController? controller,
    List<ValidationRule> validationRules = const [],
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) : this(
          key: key,
          label: label ?? '邮箱',
          hint: hint ?? '请输入邮箱地址',
          initialValue: initialValue,
          type: VanHubInputType.email,
          enabled: enabled,
          onChanged: onChanged,
          controller: controller,
          validationRules: validationRules.isEmpty
              ? [ValidationRule.required(), ValidationRule.email()]
              : validationRules,
          prefixIcon: prefixIcon ?? const Icon(Icons.email_outlined),
          suffixIcon: suffixIcon,
        );

  /// 密码输入构造函数
  VanHubInputV2.password({
    Key? key,
    String? label,
    String? hint,
    String? initialValue,
    bool enabled = true,
    ValueChanged<String>? onChanged,
    TextEditingController? controller,
    List<ValidationRule> validationRules = const [],
    Widget? prefixIcon,
  }) : this(
          key: key,
          label: label ?? '密码',
          hint: hint ?? '请输入密码',
          initialValue: initialValue,
          type: VanHubInputType.password,
          enabled: enabled,
          onChanged: onChanged,
          controller: controller,
          obscureText: true,
          validationRules: validationRules.isEmpty
              ? [ValidationRule.required(), ValidationRule.minLength(6)]
              : validationRules,
          prefixIcon: prefixIcon ?? const Icon(Icons.lock_outlined),
        );

  @override
  State<VanHubInputV2> createState() => _VanHubInputV2State();
}

class _VanHubInputV2State extends State<VanHubInputV2>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _labelController;
  late AnimationController _errorController;
  late Animation<double> _labelAnimation;
  late Animation<double> _errorAnimation;
  
  bool _isFocused = false;
  bool _hasError = false;
  bool _obscureText = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();
    _obscureText = widget.obscureText;
    
    _labelController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _errorController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _labelAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _labelController,
      curve: widget.animationCurve,
    ));

    _errorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _errorController,
      curve: widget.animationCurve,
    ));

    _focusNode.addListener(_onFocusChange);
    _controller.addListener(_onTextChange);

    // 初始化标签状态
    if (_controller.text.isNotEmpty) {
      _labelController.value = 1.0;
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _labelController.dispose();
    _errorController.dispose();
    super.dispose();
  }

  /// 处理焦点变化
  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused || _controller.text.isNotEmpty) {
      _labelController.forward();
    } else {
      _labelController.reverse();
    }

    // 触觉反馈
    if (_isFocused) {
      HapticFeedback.lightImpact();
    }
  }

  /// 处理文本变化
  void _onTextChange() {
    widget.onChanged?.call(_controller.text);
    
    if (widget.enableValidation) {
      _validateInput();
    }

    // 更新标签状态
    if (_controller.text.isNotEmpty && _labelController.value == 0.0) {
      _labelController.forward();
    } else if (_controller.text.isEmpty && !_isFocused && _labelController.value == 1.0) {
      _labelController.reverse();
    }
  }

  /// 验证输入
  void _validateInput() {
    String? error;
    
    for (final rule in widget.validationRules) {
      final result = rule.validator(_controller.text);
      if (result.isNotEmpty) {
        error = rule.errorMessage;
        break;
      }
    }

    setState(() {
      _hasError = error != null;
      _errorText = error;
    });

    if (_hasError) {
      _errorController.forward();
    } else {
      _errorController.reverse();
    }
  }

  /// 切换密码可见性
  void _toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
    HapticFeedback.lightImpact();
  }

  /// 获取键盘类型
  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case VanHubInputType.email:
        return TextInputType.emailAddress;
      case VanHubInputType.number:
        return TextInputType.number;
      case VanHubInputType.phone:
        return TextInputType.phone;
      case VanHubInputType.multiline:
        return TextInputType.multiline;
      case VanHubInputType.url:
        return TextInputType.url;
      default:
        return TextInputType.text;
    }
  }

  /// 获取输入格式化器
  List<TextInputFormatter> _getInputFormatters() {
    switch (widget.type) {
      case VanHubInputType.number:
        return [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'))];
      case VanHubInputType.phone:
        return [FilteringTextInputFormatter.digitsOnly];
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInputField(),
        if (widget.helperText != null || _hasError) ...[
          SizedBox(height: VanHubResponsiveSpacing.xs),
          _buildHelperText(),
        ],
      ],
    );
  }

  /// 构建输入框
  Widget _buildInputField() {
    return AnimatedBuilder(
      animation: Listenable.merge([_labelAnimation, _errorAnimation]),
      builder: (context, child) {
        return TextFormField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          obscureText: _obscureText,
          maxLines: widget.type == VanHubInputType.multiline ? null : 1,
          maxLength: widget.maxLength,
          keyboardType: _getKeyboardType(),
          textInputAction: widget.textInputAction,
          inputFormatters: _getInputFormatters(),
          onTap: widget.onTap,
          onEditingComplete: widget.onEditingComplete,
          onFieldSubmitted: widget.onSubmitted,
          decoration: InputDecoration(
            labelText: widget.showFloatingLabel ? widget.label : null,
            hintText: widget.hint,
            prefixIcon: widget.prefixIcon,
            suffixIcon: _buildSuffixIcon(),
            border: _getBorder(),
            enabledBorder: _getBorder(),
            focusedBorder: _getFocusedBorder(),
            errorBorder: _getErrorBorder(),
            focusedErrorBorder: _getErrorBorder(),
            filled: widget.variant == VanHubInputVariant.filled,
            fillColor: widget.variant == VanHubInputVariant.filled
                ? VanHubSemanticColors.getBackgroundColor(context, level: 2)
                : null,
            contentPadding: _getContentPadding(),
            counterText: '',
          ),
          style: _getTextStyle(),
        );
      },
    );
  }

  /// 构建后缀图标
  Widget? _buildSuffixIcon() {
    if (widget.type == VanHubInputType.password) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_outlined : Icons.visibility_off_outlined,
          size: 20,
        ),
        onPressed: _toggleObscureText,
      );
    }
    return widget.suffixIcon;
  }

  /// 构建帮助文本
  Widget _buildHelperText() {
    return AnimatedBuilder(
      animation: _errorAnimation,
      builder: (context, child) {
        return Text(
          _hasError ? _errorText! : widget.helperText!,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: _hasError 
                ? VanHubSemanticColors.error
                : VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
        );
      },
    );
  }

  /// 获取边框样式
  InputBorder _getBorder() {
    switch (widget.variant) {
      case VanHubInputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: VanHubSemanticColors.getBorderColor(context),
            width: 1,
          ),
        );
      case VanHubInputVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(
            color: VanHubSemanticColors.getBorderColor(context),
            width: 1,
          ),
        );
      case VanHubInputVariant.borderless:
        return InputBorder.none;
      default:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        );
    }
  }

  /// 获取聚焦边框样式
  InputBorder _getFocusedBorder() {
    final color = widget.emotionalState != null
        ? VanHubBrandColors.getEmotionalColor(widget.emotionalState!)
        : VanHubBrandColors.primary;

    switch (widget.variant) {
      case VanHubInputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: color, width: 2),
        );
      case VanHubInputVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(color: color, width: 2),
        );
      default:
        return _getBorder();
    }
  }

  /// 获取错误边框样式
  InputBorder _getErrorBorder() {
    switch (widget.variant) {
      case VanHubInputVariant.outlined:
        return OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: VanHubSemanticColors.error,
            width: 2,
          ),
        );
      case VanHubInputVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(
            color: VanHubSemanticColors.error,
            width: 2,
          ),
        );
      default:
        return _getBorder();
    }
  }

  /// 获取内边距
  EdgeInsets _getContentPadding() {
    switch (widget.size) {
      case VanHubInputSize.sm:
        return EdgeInsets.symmetric(
          horizontal: VanHubResponsiveSpacing.sm,
          vertical: VanHubResponsiveSpacing.sm,
        );
      case VanHubInputSize.lg:
        return EdgeInsets.symmetric(
          horizontal: VanHubResponsiveSpacing.lg,
          vertical: VanHubResponsiveSpacing.lg,
        );
      default:
        return EdgeInsets.symmetric(
          horizontal: VanHubResponsiveSpacing.md,
          vertical: VanHubResponsiveSpacing.md,
        );
    }
  }

  /// 获取文本样式
  TextStyle? _getTextStyle() {
    return Theme.of(context).textTheme.bodyMedium?.copyWith(
      color: widget.enabled 
          ? VanHubSemanticColors.getTextColor(context)
          : VanHubSemanticColors.textDisabled,
    );
  }
}
