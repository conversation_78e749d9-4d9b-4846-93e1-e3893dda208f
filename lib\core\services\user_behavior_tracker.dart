import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// part 'user_behavior_tracker.g.dart'; // 暂时禁用代码生成

/// 用户行为类型枚举
enum UserBehaviorType {
  /// 材料浏览
  materialView,
  /// 材料搜索
  materialSearch,
  /// BOM添加
  bomAddition,
  /// 项目创建
  projectCreation,
  /// 材料收藏
  materialFavorite,
  /// 材料评价
  materialRating,
  /// 推荐点击
  recommendationClick,
  /// 分享操作
  shareAction,
}

/// 用户行为数据模型
class UserBehaviorData {
  final String id;
  final String userId;
  final UserBehaviorType type;
  final String targetId; // 目标对象ID（材料ID、项目ID等）
  final Map<String, dynamic> context; // 上下文数据
  final DateTime timestamp;
  final String? sessionId; // 会话ID
  final double? duration; // 持续时间（秒）

  const UserBehaviorData({
    required this.id,
    required this.userId,
    required this.type,
    required this.targetId,
    required this.context,
    required this.timestamp,
    this.sessionId,
    this.duration,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'behavior_type': type.name,
    'target_id': targetId,
    'context': context,
    'timestamp': timestamp.toIso8601String(),
    'session_id': sessionId,
    'duration': duration,
  };

  factory UserBehaviorData.fromJson(Map<String, dynamic> json) => UserBehaviorData(
    id: json['id'],
    userId: json['user_id'],
    type: UserBehaviorType.values.firstWhere((e) => e.name == json['behavior_type']),
    targetId: json['target_id'],
    context: json['context'] ?? {},
    timestamp: DateTime.parse(json['timestamp']),
    sessionId: json['session_id'],
    duration: json['duration']?.toDouble(),
  );
}

/// 用户行为追踪服务
class UserBehaviorTracker {
  late final SupabaseClient _supabaseClient;
  String? _currentSessionId;
  final Map<String, DateTime> _viewStartTimes = {};

  UserBehaviorTracker() {
    _supabaseClient = Supabase.instance.client;
    _currentSessionId = _generateSessionId();
  }

  /// 生成会话ID
  String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// 记录材料浏览行为
  Future<void> trackMaterialView(
    String materialId, {
    Map<String, dynamic>? context,
  }) async {
    final userId = _supabaseClient.auth.currentUser?.id;
    if (userId == null) return; // 游客模式不记录行为

    // 记录浏览开始时间
    _viewStartTimes[materialId] = DateTime.now();

    await _trackBehavior(
      type: UserBehaviorType.materialView,
      targetId: materialId,
      context: {
        'source': context?['source'] ?? 'unknown',
        'search_query': context?['search_query'],
        'category': context?['category'],
        ...?context,
      },
    );
  }

  /// 记录材料浏览结束（计算浏览时长）
  Future<void> trackMaterialViewEnd(String materialId) async {
    final startTime = _viewStartTimes.remove(materialId);
    if (startTime == null) return;

    final duration = DateTime.now().difference(startTime).inSeconds.toDouble();
    
    await _trackBehavior(
      type: UserBehaviorType.materialView,
      targetId: materialId,
      context: {'view_end': true},
      duration: duration,
    );
  }

  /// 记录搜索行为
  Future<void> trackMaterialSearch(
    String query,
    List<String> resultIds, {
    String? category,
    Map<String, dynamic>? filters,
  }) async {
    await _trackBehavior(
      type: UserBehaviorType.materialSearch,
      targetId: 'search_${DateTime.now().millisecondsSinceEpoch}',
      context: {
        'query': query,
        'result_count': resultIds.length,
        'result_ids': resultIds.take(10).toList(), // 只记录前10个结果
        'category': category,
        'filters': filters ?? {},
      },
    );
  }

  /// 记录BOM添加行为
  Future<void> trackBomAddition(
    String materialId,
    String projectId, {
    int? quantity,
    double? price,
  }) async {
    await _trackBehavior(
      type: UserBehaviorType.bomAddition,
      targetId: materialId,
      context: {
        'project_id': projectId,
        'quantity': quantity,
        'price': price,
      },
    );
  }

  /// 记录推荐点击行为
  Future<void> trackRecommendationClick(
    String materialId,
    String recommendationType,
    double relevanceScore, {
    String? sourceContext,
  }) async {
    await _trackBehavior(
      type: UserBehaviorType.recommendationClick,
      targetId: materialId,
      context: {
        'recommendation_type': recommendationType,
        'relevance_score': relevanceScore,
        'source_context': sourceContext,
      },
    );
  }

  /// 记录材料收藏行为
  Future<void> trackMaterialFavorite(
    String materialId,
    bool isFavorited,
  ) async {
    await _trackBehavior(
      type: UserBehaviorType.materialFavorite,
      targetId: materialId,
      context: {
        'is_favorited': isFavorited,
      },
    );
  }

  /// 通用行为记录方法
  Future<void> _trackBehavior({
    required UserBehaviorType type,
    required String targetId,
    required Map<String, dynamic> context,
    double? duration,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) return; // 游客模式不记录行为

      final behaviorData = UserBehaviorData(
        id: 'behavior_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        type: type,
        targetId: targetId,
        context: context,
        timestamp: DateTime.now(),
        sessionId: _currentSessionId,
        duration: duration,
      );

      // 异步保存到数据库，不阻塞UI
      unawaited(_saveBehaviorToDatabase(behaviorData));
      
      // 本地缓存用于实时推荐
      _cacheBehaviorLocally(behaviorData);
      
    } catch (e) {
      debugPrint('记录用户行为失败: $e');
    }
  }

  /// 保存行为数据到数据库
  Future<void> _saveBehaviorToDatabase(UserBehaviorData behaviorData) async {
    try {
      await _supabaseClient
          .from('user_behaviors')
          .insert(behaviorData.toJson());
    } catch (e) {
      debugPrint('保存用户行为到数据库失败: $e');
    }
  }

  /// 本地缓存行为数据
  void _cacheBehaviorLocally(UserBehaviorData behaviorData) {
    // TODO: 实现本地缓存逻辑，用于实时推荐
    // 可以使用SharedPreferences或内存缓存
  }

  /// 获取用户行为历史
  Future<List<UserBehaviorData>> getUserBehaviors(
    String userId, {
    UserBehaviorType? type,
    DateTime? since,
    int limit = 100,
  }) async {
    try {
      var query = _supabaseClient
          .from('user_behaviors')
          .select()
          .eq('user_id', userId)
          .order('timestamp', ascending: false)
          .limit(limit);

      if (type != null) {
        query = query.eq('behavior_type', type.name);
      }

      if (since != null) {
        query = query.gte('timestamp', since.toIso8601String());
      }

      final response = await query;
      return (response as List)
          .map((json) => UserBehaviorData.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('获取用户行为历史失败: $e');
      return [];
    }
  }

  /// 获取材料的用户交互统计
  Future<Map<String, dynamic>> getMaterialInteractionStats(String materialId) async {
    try {
      final response = await _supabaseClient
          .rpc('get_material_interaction_stats', params: {
        'material_id': materialId,
      });

      return response ?? {};
    } catch (e) {
      debugPrint('获取材料交互统计失败: $e');
      return {};
    }
  }
}
