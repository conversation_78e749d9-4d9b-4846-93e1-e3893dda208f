import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';

/// 设置同步状态
enum SyncStatus {
  idle,
  syncing,
  success,
  failed,
  conflict,
}

/// 设置同步结果
class SettingsSyncResult {
  final SyncStatus status;
  final String? message;
  final AppSettings? settings;
  final DateTime timestamp;

  const SettingsSyncResult({
    required this.status,
    this.message,
    this.settings,
    required this.timestamp,
  });

  bool get isSuccess => status == SyncStatus.success;
  bool get isFailed => status == SyncStatus.failed;
  bool get hasConflict => status == SyncStatus.conflict;
}

/// 设置备份信息
class SettingsBackup {
  final String id;
  final String name;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final String deviceInfo;
  final String appVersion;

  const SettingsBackup({
    required this.id,
    required this.name,
    required this.data,
    required this.createdAt,
    required this.deviceInfo,
    required this.appVersion,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
    };
  }

  factory SettingsBackup.fromJson(Map<String, dynamic> json) {
    return SettingsBackup(
      id: json['id'],
      name: json['name'],
      data: json['data'],
      createdAt: DateTime.parse(json['createdAt']),
      deviceInfo: json['deviceInfo'],
      appVersion: json['appVersion'],
    );
  }
}

/// 设置服务接口
abstract class SettingsService {
  /// 获取用户设置
  Future<Either<Failure, AppSettings>> getUserSettings(String userId);

  /// 更新用户设置
  Future<Either<Failure, AppSettings>> updateUserSettings(AppSettings settings);

  /// 批量更新设置
  Future<Either<Failure, AppSettings>> updateMultipleSettings(
    String userId,
    Map<String, dynamic> updates,
  );

  /// 重置设置为默认值
  Future<Either<Failure, AppSettings>> resetToDefaults(String userId);

  /// 同步设置到云端
  Future<Either<Failure, SettingsSyncResult>> syncToCloud(String userId);

  /// 从云端同步设置
  Future<Either<Failure, SettingsSyncResult>> syncFromCloud(String userId);

  /// 强制同步（解决冲突）
  Future<Either<Failure, SettingsSyncResult>> forceSyncToCloud(
    String userId,
    AppSettings settings,
  );

  /// 获取同步状态
  Future<Either<Failure, SyncStatus>> getSyncStatus(String userId);

  /// 订阅设置变更
  Stream<AppSettings> subscribeToSettings(String userId);

  /// 订阅同步状态变更
  Stream<SyncStatus> subscribeToSyncStatus(String userId);

  /// 创建设置备份
  Future<Either<Failure, SettingsBackup>> createBackup(
    String userId,
    String backupName,
  );

  /// 获取备份列表
  Future<Either<Failure, List<SettingsBackup>>> getBackups(String userId);

  /// 从备份恢复设置
  Future<Either<Failure, AppSettings>> restoreFromBackup(
    String userId,
    String backupId,
  );

  /// 删除备份
  Future<Either<Failure, void>> deleteBackup(String backupId);

  /// 导出设置
  Future<Either<Failure, Map<String, dynamic>>> exportSettings(String userId);

  /// 导入设置
  Future<Either<Failure, AppSettings>> importSettings(
    String userId,
    Map<String, dynamic> settingsData,
  );

  /// 验证设置数据
  Future<Either<Failure, List<String>>> validateSettings(
    Map<String, dynamic> settingsData,
  );

  /// 获取设置历史记录
  Future<Either<Failure, List<AppSettings>>> getSettingsHistory(
    String userId,
    {int limit = 10}
  );

  /// 清理过期备份
  Future<Either<Failure, int>> cleanupExpiredBackups(String userId);

  /// 获取设置统计信息
  Future<Either<Failure, Map<String, dynamic>>> getSettingsStats(String userId);

  /// 检查设置完整性
  Future<Either<Failure, bool>> checkSettingsIntegrity(String userId);

  /// 修复损坏的设置
  Future<Either<Failure, AppSettings>> repairSettings(String userId);

  /// 获取默认设置
  Future<Either<Failure, AppSettings>> getDefaultSettings(String userId);

  /// 比较设置差异
  Future<Either<Failure, Map<String, dynamic>>> compareSettings(
    AppSettings settings1,
    AppSettings settings2,
  );

  /// 应用设置差异
  Future<Either<Failure, AppSettings>> applySettingsDiff(
    AppSettings baseSettings,
    Map<String, dynamic> diff,
  );

  /// 获取设置模板
  Future<Either<Failure, List<Map<String, dynamic>>>> getSettingsTemplates();

  /// 应用设置模板
  Future<Either<Failure, AppSettings>> applySettingsTemplate(
    String userId,
    String templateId,
  );

  /// 创建自定义设置模板
  Future<Either<Failure, void>> createSettingsTemplate(
    String userId,
    String templateName,
    AppSettings settings,
  );

  /// 分享设置配置
  Future<Either<Failure, String>> shareSettingsConfig(
    String userId,
    List<String> settingKeys,
  );

  /// 导入分享的设置配置
  Future<Either<Failure, AppSettings>> importSharedConfig(
    String userId,
    String configCode,
  );
}
