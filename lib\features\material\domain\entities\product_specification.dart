import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_specification.freezed.dart';
part 'product_specification.g.dart';

/// 产品规格实体
/// 
/// 用于存储结构化的产品技术参数和规格信息
@freezed
class ProductSpecification with _$ProductSpecification {
  const factory ProductSpecification({
    /// 规格ID
    required String id,
    
    /// 关联的材料ID
    required String materialId,
    
    /// 产品类别
    required String category,
    
    /// 子类别
    String? subcategory,
    
    /// 基础规格信息
    required BasicSpecification basicSpec,
    
    /// 技术参数
    required TechnicalParameters technicalParams,
    
    /// 物理属性
    required PhysicalProperties physicalProps,
    
    /// 性能指标
    PerformanceMetrics? performanceMetrics,
    
    /// 认证信息
    List<CertificationInfo>? certifications,
    
    /// 兼容性信息
    CompatibilityInfo? compatibility,
    
    /// 安装要求
    InstallationRequirements? installation,
    
    /// 维护信息
    MaintenanceInfo? maintenance,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
  }) = _ProductSpecification;

  factory ProductSpecification.fromJson(Map<String, dynamic> json) => 
      _$ProductSpecificationFromJson(json);
}

/// 基础规格信息
@freezed
class BasicSpecification with _$BasicSpecification {
  const factory BasicSpecification({
    /// 品牌
    required String brand,
    
    /// 型号
    required String model,
    
    /// 产品名称
    required String productName,
    
    /// 制造商
    String? manufacturer,
    
    /// 产地
    String? countryOfOrigin,
    
    /// 生产日期
    DateTime? manufacturingDate,
    
    /// 保修期（月）
    int? warrantyMonths,
    
    /// 产品描述
    String? description,
  }) = _BasicSpecification;

  factory BasicSpecification.fromJson(Map<String, dynamic> json) => 
      _$BasicSpecificationFromJson(json);
}

/// 技术参数
@freezed
class TechnicalParameters with _$TechnicalParameters {
  const factory TechnicalParameters({
    /// 电气参数
    ElectricalSpecs? electrical,
    
    /// 机械参数
    MechanicalSpecs? mechanical,
    
    /// 流体参数
    FluidSpecs? fluid,
    
    /// 热力参数
    ThermalSpecs? thermal,
    
    /// 化学参数
    ChemicalSpecs? chemical,
    
    /// 其他自定义参数
    Map<String, dynamic>? customParams,
  }) = _TechnicalParameters;

  factory TechnicalParameters.fromJson(Map<String, dynamic> json) => 
      _$TechnicalParametersFromJson(json);
}

/// 电气参数
@freezed
class ElectricalSpecs with _$ElectricalSpecs {
  const factory ElectricalSpecs({
    /// 额定电压 (V)
    double? ratedVoltage,
    
    /// 电压范围 (V)
    VoltageRange? voltageRange,
    
    /// 额定电流 (A)
    double? ratedCurrent,
    
    /// 最大电流 (A)
    double? maxCurrent,
    
    /// 额定功率 (W)
    double? ratedPower,
    
    /// 最大功率 (W)
    double? maxPower,
    
    /// 容量 (Ah/Wh)
    double? capacity,
    
    /// 容量单位
    String? capacityUnit,
    
    /// 效率 (%)
    double? efficiency,
    
    /// 功率因数
    double? powerFactor,
    
    /// 频率 (Hz)
    double? frequency,
    
    /// 绝缘等级
    String? insulationClass,
    
    /// 防护等级 (IP等级)
    String? protectionRating,
  }) = _ElectricalSpecs;

  factory ElectricalSpecs.fromJson(Map<String, dynamic> json) => 
      _$ElectricalSpecsFromJson(json);
}

/// 电压范围
@freezed
class VoltageRange with _$VoltageRange {
  const factory VoltageRange({
    required double min,
    required double max,
    String? unit,
  }) = _VoltageRange;

  factory VoltageRange.fromJson(Map<String, dynamic> json) => 
      _$VoltageRangeFromJson(json);
}

/// 机械参数
@freezed
class MechanicalSpecs with _$MechanicalSpecs {
  const factory MechanicalSpecs({
    /// 最大承重 (kg)
    double? maxLoad,
    
    /// 工作压力 (MPa)
    double? workingPressure,
    
    /// 最大压力 (MPa)
    double? maxPressure,
    
    /// 转速 (rpm)
    double? rotationSpeed,
    
    /// 扭矩 (N·m)
    double? torque,
    
    /// 硬度
    String? hardness,
    
    /// 强度等级
    String? strengthGrade,
  }) = _MechanicalSpecs;

  factory MechanicalSpecs.fromJson(Map<String, dynamic> json) => 
      _$MechanicalSpecsFromJson(json);
}

/// 流体参数
@freezed
class FluidSpecs with _$FluidSpecs {
  const factory FluidSpecs({
    /// 流量 (L/min)
    double? flowRate,
    
    /// 扬程 (m)
    double? head,
    
    /// 吸程 (m)
    double? suctionHead,
    
    /// 介质温度范围 (°C)
    TemperatureRange? mediumTempRange,
    
    /// 适用介质
    List<String>? suitableMediums,
    
    /// 管径 (mm)
    double? pipeDiameter,
    
    /// 接口类型
    String? connectionType,
  }) = _FluidSpecs;

  factory FluidSpecs.fromJson(Map<String, dynamic> json) => 
      _$FluidSpecsFromJson(json);
}

/// 温度范围
@freezed
class TemperatureRange with _$TemperatureRange {
  const factory TemperatureRange({
    required double min,
    required double max,
    @Default('°C') String unit,
  }) = _TemperatureRange;

  factory TemperatureRange.fromJson(Map<String, dynamic> json) => 
      _$TemperatureRangeFromJson(json);
}

/// 热力参数
@freezed
class ThermalSpecs with _$ThermalSpecs {
  const factory ThermalSpecs({
    /// 工作温度范围 (°C)
    TemperatureRange? workingTempRange,
    
    /// 存储温度范围 (°C)
    TemperatureRange? storageTempRange,
    
    /// 导热系数 (W/m·K)
    double? thermalConductivity,
    
    /// 比热容 (J/kg·K)
    double? specificHeat,
    
    /// 热阻 (K/W)
    double? thermalResistance,
    
    /// 阻燃等级
    String? flameRetardantGrade,
  }) = _ThermalSpecs;

  factory ThermalSpecs.fromJson(Map<String, dynamic> json) => 
      _$ThermalSpecsFromJson(json);
}

/// 化学参数
@freezed
class ChemicalSpecs with _$ChemicalSpecs {
  const factory ChemicalSpecs({
    /// 材质
    String? material,
    
    /// 化学成分
    Map<String, double>? composition,
    
    /// pH值范围
    PhRange? phRange,
    
    /// 耐腐蚀性
    String? corrosionResistance,
    
    /// 化学兼容性
    List<String>? chemicalCompatibility,
  }) = _ChemicalSpecs;

  factory ChemicalSpecs.fromJson(Map<String, dynamic> json) => 
      _$ChemicalSpecsFromJson(json);
}

/// pH值范围
@freezed
class PhRange with _$PhRange {
  const factory PhRange({
    required double min,
    required double max,
  }) = _PhRange;

  factory PhRange.fromJson(Map<String, dynamic> json) => 
      _$PhRangeFromJson(json);
}

/// 物理属性
@freezed
class PhysicalProperties with _$PhysicalProperties {
  const factory PhysicalProperties({
    /// 尺寸
    required Dimensions dimensions,
    
    /// 重量 (kg)
    required double weight,
    
    /// 颜色
    String? color,
    
    /// 材质
    String? material,
    
    /// 表面处理
    String? surfaceTreatment,
    
    /// 密度 (kg/m³)
    double? density,
    
    /// 体积 (L)
    double? volume,
  }) = _PhysicalProperties;

  factory PhysicalProperties.fromJson(Map<String, dynamic> json) => 
      _$PhysicalPropertiesFromJson(json);
}

/// 尺寸信息
@freezed
class Dimensions with _$Dimensions {
  const factory Dimensions({
    /// 长度 (mm)
    required double length,
    
    /// 宽度 (mm)
    required double width,
    
    /// 高度 (mm)
    required double height,
    
    /// 直径 (mm) - 圆形产品
    double? diameter,
    
    /// 厚度 (mm)
    double? thickness,
    
    /// 单位
    @Default('mm') String unit,
  }) = _Dimensions;

  factory Dimensions.fromJson(Map<String, dynamic> json) => 
      _$DimensionsFromJson(json);
}

/// 性能指标
@freezed
class PerformanceMetrics with _$PerformanceMetrics {
  const factory PerformanceMetrics({
    /// 使用寿命 (小时/年)
    double? lifespan,
    
    /// 寿命单位
    String? lifespanUnit,
    
    /// 循环次数
    int? cycleCount,
    
    /// 可靠性等级
    String? reliabilityGrade,
    
    /// 故障率
    double? failureRate,
    
    /// 维护间隔 (小时)
    double? maintenanceInterval,
    
    /// 性能等级
    String? performanceGrade,
  }) = _PerformanceMetrics;

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) => 
      _$PerformanceMetricsFromJson(json);
}

/// 认证信息
@freezed
class CertificationInfo with _$CertificationInfo {
  const factory CertificationInfo({
    /// 认证名称
    required String name,
    
    /// 认证机构
    required String authority,
    
    /// 认证编号
    String? certificateNumber,
    
    /// 认证日期
    DateTime? certificationDate,
    
    /// 有效期
    DateTime? expiryDate,
    
    /// 认证标准
    String? standard,
  }) = _CertificationInfo;

  factory CertificationInfo.fromJson(Map<String, dynamic> json) => 
      _$CertificationInfoFromJson(json);
}

/// 兼容性信息
@freezed
class CompatibilityInfo with _$CompatibilityInfo {
  const factory CompatibilityInfo({
    /// 兼容的产品型号
    List<String>? compatibleModels,
    
    /// 兼容的品牌
    List<String>? compatibleBrands,
    
    /// 兼容的接口类型
    List<String>? compatibleInterfaces,
    
    /// 不兼容的产品
    List<String>? incompatibleProducts,
    
    /// 兼容性说明
    String? compatibilityNotes,
  }) = _CompatibilityInfo;

  factory CompatibilityInfo.fromJson(Map<String, dynamic> json) => 
      _$CompatibilityInfoFromJson(json);
}

/// 安装要求
@freezed
class InstallationRequirements with _$InstallationRequirements {
  const factory InstallationRequirements({
    /// 安装难度等级 (1-5)
    int? difficultyLevel,
    
    /// 所需工具
    List<String>? requiredTools,
    
    /// 安装时间 (小时)
    double? installationTime,
    
    /// 安装环境要求
    String? environmentRequirements,
    
    /// 安装注意事项
    List<String>? installationNotes,
    
    /// 是否需要专业安装
    bool? requiresProfessionalInstallation,
  }) = _InstallationRequirements;

  factory InstallationRequirements.fromJson(Map<String, dynamic> json) => 
      _$InstallationRequirementsFromJson(json);
}

/// 维护信息
@freezed
class MaintenanceInfo with _$MaintenanceInfo {
  const factory MaintenanceInfo({
    /// 维护难度等级 (1-5)
    int? difficultyLevel,
    
    /// 维护间隔 (月)
    int? maintenanceInterval,
    
    /// 维护项目
    List<String>? maintenanceItems,
    
    /// 维护工具
    List<String>? maintenanceTools,
    
    /// 维护注意事项
    List<String>? maintenanceNotes,
    
    /// 更换周期 (月)
    int? replacementCycle,
  }) = _MaintenanceInfo;

  factory MaintenanceInfo.fromJson(Map<String, dynamic> json) => 
      _$MaintenanceInfoFromJson(json);
}
