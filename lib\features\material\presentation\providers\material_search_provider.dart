import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material.dart';
import '../../domain/entities/search_criteria.dart';
import '../../../../core/di/injection_container.dart';

part 'material_search_provider.g.dart';

/// 材料搜索状态
@riverpod
class MaterialSearchState extends _$MaterialSearchState {
  @override
  Future<List<Material>> build() async {
    // 初始状态返回空列表
    return [];
  }

  /// 执行搜索
  Future<Either<Failure, List<Material>>> search(SearchCriteria criteria) async {
    state = const AsyncLoading();
    
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) {
      state = const AsyncData([]);
      return const Left(ValidationFailure(message: '用户未登录'));
    }

    final result = await ref.read(materialSearchServiceProvider).searchMaterials(userId, criteria);
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (materials) {
        state = AsyncData(materials);
      },
    );
    
    return result;
  }

  /// 高级搜索
  Future<Either<Failure, List<Material>>> advancedSearch(
    Map<String, dynamic> filters, {
    int limit = 20,
    int offset = 0,
  }) async {
    state = const AsyncLoading();
    
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) {
      state = const AsyncData([]);
      return const Left(ValidationFailure(message: '用户未登录'));
    }

    final result = await ref.read(materialSearchServiceProvider).advancedSearch(
      userId,
      filters,
      limit: limit,
      offset: offset,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (materials) {
        state = AsyncData(materials);
      },
    );
    
    return result;
  }

  /// 按标签搜索
  Future<Either<Failure, List<Material>>> searchByTags(
    List<String> tags, {
    int limit = 20,
    int offset = 0,
  }) async {
    state = const AsyncLoading();
    
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) {
      state = const AsyncData([]);
      return const Left(ValidationFailure(message: '用户未登录'));
    }

    final result = await ref.read(materialSearchServiceProvider).searchByTags(
      userId,
      tags,
      limit: limit,
      offset: offset,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (materials) {
        state = AsyncData(materials);
      },
    );
    
    return result;
  }

  /// 按项目类型搜索
  Future<Either<Failure, List<Material>>> searchByProjectType(
    String vehicleBrand,
    String vehicleModel, {
    String? systemType,
    int limit = 20,
    int offset = 0,
  }) async {
    state = const AsyncLoading();
    
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) {
      state = const AsyncData([]);
      return const Left(ValidationFailure(message: '用户未登录'));
    }

    final result = await ref.read(materialSearchServiceProvider).searchByProjectType(
      userId,
      vehicleBrand,
      vehicleModel,
      systemType: systemType,
      limit: limit,
      offset: offset,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (materials) {
        state = AsyncData(materials);
      },
    );
    
    return result;
  }

  /// 按系统类型搜索
  Future<Either<Failure, List<Material>>> searchBySystemType(
    String systemType, {
    int limit = 20,
    int offset = 0,
  }) async {
    state = const AsyncLoading();
    
    final userId = ref.read(currentUserIdProvider);
    if (userId == null) {
      state = const AsyncData([]);
      return const Left(ValidationFailure(message: '用户未登录'));
    }

    final result = await ref.read(materialSearchServiceProvider).searchBySystemType(
      userId,
      systemType,
      limit: limit,
      offset: offset,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (materials) {
        state = AsyncData(materials);
      },
    );
    
    return result;
  }

  /// 清空搜索结果
  void clearResults() {
    state = const AsyncData([]);
  }
}

/// 搜索建议提供者
@riverpod
Future<List<String>> searchSuggestions(
  SearchSuggestionsRef ref,
  String query,
) async {
  if (query.length < 2) return [];
  
  final userId = ref.read(currentUserIdProvider);
  if (userId == null) return [];

  final result = await ref.read(materialSearchServiceProvider).getSuggestions(
    userId,
    query,
    limit: 10,
  );
  
  return result.fold(
    (failure) => [],
    (suggestions) => suggestions,
  );
}

/// 热门搜索词提供者
@riverpod
Future<List<String>> popularSearchTerms(PopularSearchTermsRef ref) async {
  final result = await ref.read(materialSearchServiceProvider).getPopularSearchTerms(
    limit: 10,
  );
  
  return result.fold(
    (failure) => [],
    (terms) => terms,
  );
}