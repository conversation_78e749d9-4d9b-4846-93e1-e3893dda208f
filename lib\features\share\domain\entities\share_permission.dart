import 'package:freezed_annotation/freezed_annotation.dart';
import '../services/share_service.dart';

part 'share_permission.freezed.dart';
part 'share_permission.g.dart';

/// 分享权限实体
@freezed
class SharePermission with _$SharePermission {
  const factory SharePermission({
    required String id,
    required String contentId,
    required ShareContentType contentType,
    required String ownerId,
    required SharePermissionLevel permissionLevel,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default([]) List<String> allowedUsers,
    @Default([]) List<String> blockedUsers,
    @Default([]) List<String> allowedRoles,
    @Default([]) List<String> allowedIps,
    @Default([]) List<String> blockedIps,
    @Default(true) bool isActive,
    @Default(false) bool requiresApproval,
    @Default(false) bool allowAnonymous,
    @Default(100) int maxAccessCount,
    @Default(0) int currentAccessCount,
    DateTime? expiresAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _SharePermission;

  factory SharePermission.fromJson(Map<String, dynamic> json) => 
      _$SharePermissionFromJson(json);
}

/// 分享权限扩展方法
extension SharePermissionX on SharePermission {
  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否已达到访问上限
  bool get isAccessLimitReached {
    return currentAccessCount >= maxAccessCount;
  }

  /// 是否可访问
  bool get isAccessible {
    return isActive && !isExpired && !isAccessLimitReached;
  }

  /// 检查用户是否有权限
  bool canUserAccess(String? userId) {
    if (!isAccessible) return false;
    
    // 检查是否被阻止
    if (userId != null && blockedUsers.contains(userId)) {
      return false;
    }
    
    switch (permissionLevel) {
      case SharePermissionLevel.public:
        return allowAnonymous || userId != null;
        
      case SharePermissionLevel.unlisted:
        return allowAnonymous || userId != null;
        
      case SharePermissionLevel.private:
        if (userId == null) return allowAnonymous;
        return userId == ownerId || allowedUsers.contains(userId);
    }
  }

  /// 检查IP是否有权限
  bool canIpAccess(String ipAddress) {
    if (blockedIps.contains(ipAddress)) return false;
    if (allowedIps.isEmpty) return true;
    return allowedIps.contains(ipAddress);
  }

  /// 获取剩余访问次数
  int get remainingAccessCount {
    return (maxAccessCount - currentAccessCount).clamp(0, maxAccessCount);
  }

  /// 获取权限描述
  String get permissionDescription {
    switch (permissionLevel) {
      case SharePermissionLevel.public:
        return '公开访问${allowAnonymous ? '（包括匿名用户）' : '（需要登录）'}';
      case SharePermissionLevel.unlisted:
        return '链接访问${allowAnonymous ? '（包括匿名用户）' : '（需要登录）'}';
      case SharePermissionLevel.private:
        return '私有访问（仅限授权用户）';
    }
  }

  /// 获取访问限制描述
  List<String> get accessRestrictions {
    final restrictions = <String>[];
    
    if (!allowAnonymous) {
      restrictions.add('需要登录');
    }
    
    if (requiresApproval) {
      restrictions.add('需要审批');
    }
    
    if (maxAccessCount < 1000) {
      restrictions.add('限制访问$maxAccessCount次');
    }
    
    if (expiresAt != null) {
      final remaining = expiresAt!.difference(DateTime.now());
      if (remaining.inDays > 0) {
        restrictions.add('${remaining.inDays}天后过期');
      } else if (remaining.inHours > 0) {
        restrictions.add('${remaining.inHours}小时后过期');
      } else {
        restrictions.add('即将过期');
      }
    }
    
    if (allowedUsers.isNotEmpty) {
      restrictions.add('限制${allowedUsers.length}个用户');
    }
    
    if (blockedUsers.isNotEmpty) {
      restrictions.add('阻止${blockedUsers.length}个用户');
    }
    
    return restrictions;
  }

  /// 添加允许用户
  SharePermission addAllowedUser(String userId) {
    if (allowedUsers.contains(userId)) return this;
    return copyWith(
      allowedUsers: [...allowedUsers, userId],
      blockedUsers: blockedUsers.where((id) => id != userId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// 移除允许用户
  SharePermission removeAllowedUser(String userId) {
    return copyWith(
      allowedUsers: allowedUsers.where((id) => id != userId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// 添加阻止用户
  SharePermission addBlockedUser(String userId) {
    if (blockedUsers.contains(userId)) return this;
    return copyWith(
      blockedUsers: [...blockedUsers, userId],
      allowedUsers: allowedUsers.where((id) => id != userId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// 移除阻止用户
  SharePermission removeBlockedUser(String userId) {
    return copyWith(
      blockedUsers: blockedUsers.where((id) => id != userId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// 增加访问计数
  SharePermission incrementAccessCount() {
    return copyWith(
      currentAccessCount: currentAccessCount + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// 重置访问计数
  SharePermission resetAccessCount() {
    return copyWith(
      currentAccessCount: 0,
      updatedAt: DateTime.now(),
    );
  }

  /// 设置过期时间
  SharePermission setExpiration(DateTime expiresAt) {
    return copyWith(
      expiresAt: expiresAt,
      updatedAt: DateTime.now(),
    );
  }

  /// 移除过期时间
  SharePermission removeExpiration() {
    return copyWith(
      expiresAt: null,
      updatedAt: DateTime.now(),
    );
  }

  /// 启用权限
  SharePermission enable() {
    return copyWith(
      isActive: true,
      updatedAt: DateTime.now(),
    );
  }

  /// 禁用权限
  SharePermission disable() {
    return copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
    );
  }
}
