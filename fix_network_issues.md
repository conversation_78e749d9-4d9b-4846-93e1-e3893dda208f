# VanHub 网络问题解决方案

## 问题描述
Flutter项目在下载依赖时遇到网络连接问题，特别是 `speech_to_text` 和 `share_plus` 包无法下载。

## 解决方案

### 方案1：使用国内镜像源（推荐）

1. **设置环境变量**（在PowerShell中执行）：
```powershell
# 设置Pub镜像源
$env:PUB_HOSTED_URL = "https://pub.flutter-io.cn"
$env:FLUTTER_STORAGE_BASE_URL = "https://storage.flutter-io.cn"

# 或者使用腾讯云镜像
# $env:PUB_HOSTED_URL = "https://mirrors.cloud.tencent.com/dart-pub"
# $env:FLUTTER_STORAGE_BASE_URL = "https://mirrors.cloud.tencent.com/flutter"
```

2. **清理缓存并重新获取依赖**：
```powershell
flutter clean
flutter pub cache clean
flutter pub get
```

### 方案2：临时禁用有问题的依赖

我已经为您做了以下修改：

1. **临时禁用了 `speech_to_text` 依赖**：
   - 在 `pubspec.yaml` 中注释掉了 `speech_to_text: ^7.0.0`
   - 修改了 `voice_search_service.dart` 使用模拟实现

2. **添加了 `share_plus` 依赖**：
   - 在 `pubspec.yaml` 中添加了 `share_plus: ^10.1.2`

3. **修复了代码冲突**：
   - 删除了重复的方法定义
   - 移除了对不存在生成文件的引用

### 方案3：手动下载依赖（备用方案）

如果镜像源仍然不工作，您可以：

1. **使用VPN或代理**：
   - 配置系统代理
   - 或使用Flutter代理设置

2. **离线安装**：
   - 从GitHub手动下载包
   - 使用本地路径依赖

## 当前项目状态

✅ **已修复的问题**：
- 语音搜索服务重复方法定义
- BOM导出服务重复方法定义
- 缺失的share_plus依赖
- 生成文件引用问题

⚠️ **临时禁用的功能**：
- 真实语音识别（使用模拟实现）
- Riverpod代码生成（改为手动实现）

## 下一步操作

1. **尝试方案1**：设置镜像源并重新获取依赖
2. **如果成功**：运行 `flutter run -d chrome` 启动项目
3. **如果仍有问题**：可以继续使用当前的临时解决方案

## 恢复语音功能（网络问题解决后）

当网络问题解决后，您可以：

1. 取消注释 `pubspec.yaml` 中的 `speech_to_text: ^7.0.0`
2. 恢复 `voice_search_service.dart` 中的真实实现
3. 运行 `flutter pub get` 重新获取依赖

## 联系支持

如果问题持续存在，建议：
- 检查网络连接和防火墙设置
- 尝试不同的网络环境
- 联系网络管理员检查代理设置
