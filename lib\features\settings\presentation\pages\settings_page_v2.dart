/// VanHub Settings Page 2.0
/// 
/// 现代化设置页面，使用新设计系统
/// 
/// 特性：
/// - 分组设置管理
/// - 主题切换动画
/// - 个性化配置
/// - 数据同步状态
/// - 隐私安全设置
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../../core/design_system/foundation/animations/animation_tokens.dart';
import '../../../../core/providers/auth_state_provider.dart';

/// 设置分组枚举
enum SettingsGroup {
  account,      // 账户设置
  appearance,   // 外观设置
  notifications, // 通知设置
  privacy,      // 隐私设置
  data,         // 数据管理
  about,        // 关于应用
}

/// VanHub设置页面 2.0
class SettingsPageV2 extends ConsumerStatefulWidget {
  const SettingsPageV2({super.key});

  @override
  ConsumerState<SettingsPageV2> createState() => _SettingsPageV2State();
}

class _SettingsPageV2State extends ConsumerState<SettingsPageV2>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  
  bool _isDarkMode = false;
  bool _enableNotifications = true;
  bool _enableAnalytics = true;
  bool _enableAutoSync = true;
  String _selectedLanguage = '中文';

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: VanHubAnimationDurations.normal,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: VanHubAnimationDurations.slow,
      vsync: this,
    );
    
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
      appBar: _buildAppBar(),
      body: _buildSettingsContent(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        '设置',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: VanHubBrandColors.onPrimary,
        ),
      ),
      backgroundColor: VanHubBrandColors.primary,
      foregroundColor: VanHubBrandColors.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.help_outline),
          onPressed: _showHelp,
          tooltip: '帮助',
        ),
      ],
    );
  }

  /// 构建设置内容
  Widget _buildSettingsContent() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeController,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUserProfile(),
                SizedBox(height: VanHubResponsiveSpacing.xl),
                _buildSettingsGroup(SettingsGroup.account),
                SizedBox(height: VanHubResponsiveSpacing.lg),
                _buildSettingsGroup(SettingsGroup.appearance),
                SizedBox(height: VanHubResponsiveSpacing.lg),
                _buildSettingsGroup(SettingsGroup.notifications),
                SizedBox(height: VanHubResponsiveSpacing.lg),
                _buildSettingsGroup(SettingsGroup.privacy),
                SizedBox(height: VanHubResponsiveSpacing.lg),
                _buildSettingsGroup(SettingsGroup.data),
                SizedBox(height: VanHubResponsiveSpacing.lg),
                _buildSettingsGroup(SettingsGroup.about),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建用户资料卡片
  Widget _buildUserProfile() {
    final user = ref.watch(currentUserIdProvider);
    
    return VanHubCardV2(
      variant: VanHubCardVariant.filled,
      size: VanHubCardSize.lg,
      enable3DEffect: true,
      child: Row(
        children: [
          // 头像
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: VanHubBrandColors.getEmotionalGradient(EmotionalState.energetic),
            ),
            child: Center(
              child: Text(
                user?.substring(0, 1).toUpperCase() ?? 'G',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: VanHubBrandColors.onPrimary,
                ),
              ),
            ),
          ),
          
          SizedBox(width: VanHubResponsiveSpacing.lg),
          
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user ?? '游客用户',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: VanHubResponsiveSpacing.xs),
                Text(
                  user != null ? 'VanHub会员' : '未登录',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: VanHubSemanticColors.getTextColor(context, secondary: true),
                  ),
                ),
                SizedBox(height: VanHubResponsiveSpacing.sm),
                if (user != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: VanHubSemanticColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: VanHubSemanticColors.success.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      '已验证',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: VanHubSemanticColors.success,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // 编辑按钮
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editProfile,
            tooltip: '编辑资料',
          ),
        ],
      ),
    );
  }

  /// 构建设置分组
  Widget _buildSettingsGroup(SettingsGroup group) {
    final groupData = _getGroupData(group);
    
    return VanHubCardV2.outlined(
      size: VanHubCardSize.md,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分组标题
          Row(
            children: [
              Icon(
                groupData['icon'] as IconData,
                color: VanHubBrandColors.primary,
                size: 20,
              ),
              SizedBox(width: VanHubResponsiveSpacing.sm),
              Text(
                groupData['title'] as String,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.md),
          
          // 设置项列表
          ...(_getSettingsItems(group).asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return _buildSettingsItem(item, index);
          }).toList()),
        ],
      ),
    );
  }

  /// 构建设置项
  Widget _buildSettingsItem(Map<String, dynamic> item, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      child: Row(
        children: [
          Icon(
            item['icon'] as IconData,
            size: 20,
            color: VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
          SizedBox(width: VanHubResponsiveSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['title'] as String,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (item['subtitle'] != null) ...[
                  SizedBox(height: VanHubResponsiveSpacing.xs),
                  Text(
                    item['subtitle'] as String,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: VanHubSemanticColors.getTextColor(context, secondary: true),
                    ),
                  ),
                ],
              ],
            ),
          ),
          _buildSettingsControl(item),
        ],
      ),
    );
  }

  /// 构建设置控件
  Widget _buildSettingsControl(Map<String, dynamic> item) {
    switch (item['type'] as String) {
      case 'switch':
        return Switch(
          value: _getSettingValue(item['key'] as String) as bool,
          onChanged: (value) => _updateSetting(item['key'] as String, value),
          activeColor: VanHubBrandColors.primary,
        );
      
      case 'dropdown':
        return DropdownButton<String>(
          value: _getSettingValue(item['key'] as String) as String,
          items: (item['options'] as List<String>).map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(option),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateSetting(item['key'] as String, value);
            }
          },
        );
      
      case 'action':
        return IconButton(
          icon: const Icon(Icons.chevron_right),
          onPressed: () => _handleAction(item['action'] as String),
        );
      
      default:
        return const SizedBox.shrink();
    }
  }

  /// 获取分组数据
  Map<String, dynamic> _getGroupData(SettingsGroup group) {
    switch (group) {
      case SettingsGroup.account:
        return {
          'title': '账户设置',
          'icon': Icons.account_circle,
        };
      case SettingsGroup.appearance:
        return {
          'title': '外观设置',
          'icon': Icons.palette,
        };
      case SettingsGroup.notifications:
        return {
          'title': '通知设置',
          'icon': Icons.notifications,
        };
      case SettingsGroup.privacy:
        return {
          'title': '隐私设置',
          'icon': Icons.security,
        };
      case SettingsGroup.data:
        return {
          'title': '数据管理',
          'icon': Icons.storage,
        };
      case SettingsGroup.about:
        return {
          'title': '关于应用',
          'icon': Icons.info,
        };
    }
  }

  /// 获取设置项列表
  List<Map<String, dynamic>> _getSettingsItems(SettingsGroup group) {
    switch (group) {
      case SettingsGroup.account:
        return [
          {
            'title': '修改密码',
            'icon': Icons.lock,
            'type': 'action',
            'action': 'change_password',
          },
          {
            'title': '绑定邮箱',
            'icon': Icons.email,
            'type': 'action',
            'action': 'bind_email',
          },
        ];
      
      case SettingsGroup.appearance:
        return [
          {
            'title': '深色模式',
            'subtitle': '切换应用主题',
            'icon': Icons.dark_mode,
            'type': 'switch',
            'key': 'dark_mode',
          },
          {
            'title': '语言设置',
            'icon': Icons.language,
            'type': 'dropdown',
            'key': 'language',
            'options': ['中文', 'English'],
          },
        ];
      
      case SettingsGroup.notifications:
        return [
          {
            'title': '推送通知',
            'subtitle': '接收应用推送消息',
            'icon': Icons.notifications_active,
            'type': 'switch',
            'key': 'notifications',
          },
        ];
      
      case SettingsGroup.privacy:
        return [
          {
            'title': '数据分析',
            'subtitle': '帮助改进应用体验',
            'icon': Icons.analytics,
            'type': 'switch',
            'key': 'analytics',
          },
        ];
      
      case SettingsGroup.data:
        return [
          {
            'title': '自动同步',
            'subtitle': '自动备份数据到云端',
            'icon': Icons.sync,
            'type': 'switch',
            'key': 'auto_sync',
          },
          {
            'title': '导出数据',
            'icon': Icons.download,
            'type': 'action',
            'action': 'export_data',
          },
          {
            'title': '清除缓存',
            'icon': Icons.clear_all,
            'type': 'action',
            'action': 'clear_cache',
          },
        ];
      
      case SettingsGroup.about:
        return [
          {
            'title': '规格系统测试',
            'icon': Icons.science,
            'type': 'action',
            'action': 'test_specification',
          },
          {
            'title': '规格演示材料',
            'icon': Icons.view_list,
            'type': 'action',
            'action': 'demo_materials',
          },
          {
            'title': '版本信息',
            'icon': Icons.info_outline,
            'type': 'action',
            'action': 'version_info',
          },
          {
            'title': '用户协议',
            'icon': Icons.description,
            'type': 'action',
            'action': 'terms',
          },
          {
            'title': '隐私政策',
            'icon': Icons.privacy_tip,
            'type': 'action',
            'action': 'privacy_policy',
          },
        ];
    }
  }

  /// 获取设置值
  dynamic _getSettingValue(String key) {
    switch (key) {
      case 'dark_mode':
        return _isDarkMode;
      case 'notifications':
        return _enableNotifications;
      case 'analytics':
        return _enableAnalytics;
      case 'auto_sync':
        return _enableAutoSync;
      case 'language':
        return _selectedLanguage;
      default:
        return null;
    }
  }

  /// 更新设置
  void _updateSetting(String key, dynamic value) {
    setState(() {
      switch (key) {
        case 'dark_mode':
          _isDarkMode = value as bool;
          break;
        case 'notifications':
          _enableNotifications = value as bool;
          break;
        case 'analytics':
          _enableAnalytics = value as bool;
          break;
        case 'auto_sync':
          _enableAutoSync = value as bool;
          break;
        case 'language':
          _selectedLanguage = value as String;
          break;
      }
    });
    
    // TODO: 保存设置到本地存储
  }

  /// 处理操作
  void _handleAction(String action) {
    switch (action) {
      case 'test_specification':
        _openSpecificationTest();
        break;
      case 'demo_materials':
        _openDemoMaterials();
        break;
      case 'change_password':
        _changePassword();
        break;
      case 'bind_email':
        _bindEmail();
        break;
      case 'export_data':
        _exportData();
        break;
      case 'clear_cache':
        _clearCache();
        break;
      case 'version_info':
        _showVersionInfo();
        break;
      case 'terms':
        _showTerms();
        break;
      case 'privacy_policy':
        _showPrivacyPolicy();
        break;
    }
  }

  // 操作方法
  void _editProfile() {
    // TODO: 实现编辑资料功能
  }

  void _showHelp() {
    // TODO: 显示帮助信息
  }

  void _openSpecificationTest() {
    Navigator.pushNamed(context, '/test_specification');
  }

  void _openDemoMaterials() {
    Navigator.pushNamed(context, '/demo_materials');
  }

  void _changePassword() {
    // TODO: 实现修改密码功能
  }

  void _bindEmail() {
    // TODO: 实现绑定邮箱功能
  }

  void _exportData() {
    // TODO: 实现导出数据功能
  }

  void _clearCache() {
    // TODO: 实现清除缓存功能
  }

  void _showVersionInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('版本信息'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('VanHub 改装宝'),
            Text('版本：2.0.0'),
            Text('构建：2025.01.25'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showTerms() {
    // TODO: 显示用户协议
  }

  void _showPrivacyPolicy() {
    // TODO: 显示隐私政策
  }
}
