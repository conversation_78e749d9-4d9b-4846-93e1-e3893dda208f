/// VanHub BOM Tree View 2.0
///
/// 高端BOM树形视图组件
/// 基于VanHub Design System 2.0设计
///
/// 特性：
/// 1. 层次结构可视化 - 清晰的BOM层级展示
/// 2. 拖拽操作 - 支持拖拽重新排序和分组
/// 3. 实时计算 - 动态成本和数量计算
/// 4. 状态管理 - 直观的BOM项目状态展示
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/animations/animations.dart';
import '../../../../core/design_system/components/atoms/vanhub_button.dart';
import '../../domain/entities/bom_item.dart';
import '../../domain/entities/bom_item_status.dart' as domain_status; // 使用别名避免冲突


/// BOM树形节点
class BOMTreeNode {
  final BomItem item;
  final List<BOMTreeNode> children;
  final int level;
  bool isExpanded;
  bool isSelected;

  BOMTreeNode({
    required this.item,
    this.children = const [],
    this.level = 0,
    this.isExpanded = true,
    this.isSelected = false,
  });
}

/// BOM树形视图2.0组件
class BOMTreeView2 extends ConsumerStatefulWidget {
  /// BOM项目列表
  final List<BomItem> bomItems;
  
  /// 加载状态
  final bool isLoading;
  
  /// 是否可编辑
  final bool isEditable;
  
  /// 是否显示成本
  final bool showCost;
  
  /// 项目点击回调
  final Function(BomItem)? onItemTap;
  
  /// 项目编辑回调
  final Function(BomItem)? onItemEdit;
  
  /// 项目删除回调
  final Function(BomItem)? onItemDelete;
  
  /// 项目状态更改回调
  final Function(BomItem, domain_status.BomItemStatus)? onStatusChange;
  
  /// 拖拽重排回调
  final Function(List<BomItem>)? onReorder;

  const BOMTreeView2({
    super.key,
    required this.bomItems,
    this.isLoading = false,
    this.isEditable = true,
    this.showCost = true,
    this.onItemTap,
    this.onItemEdit,
    this.onItemDelete,
    this.onStatusChange,
    this.onReorder,
  });

  @override
  ConsumerState<BOMTreeView2> createState() => _BOMTreeView2State();
}

class _BOMTreeView2State extends ConsumerState<BOMTreeView2>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  final List<BOMTreeNode> _treeNodes = [];
  final Set<String> _selectedItems = {};

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: VanHubAnimations.medium,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: VanHubAnimations.standard,
    ));
    _buildTreeNodes();
    _fadeController.forward();
  }

  @override
  void didUpdateWidget(BOMTreeView2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.bomItems != widget.bomItems) {
      _buildTreeNodes();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    if (_treeNodes.isEmpty) {
      return _buildEmptyState();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          // BOM统计信息
          _buildBOMSummary(),
          
          SizedBox(height: VanHubSpacing.md),
          
          // BOM树形列表
          Expanded(
            child: _buildBOMTree(),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: VanHubColors.primary,
          ),
          SizedBox(height: VanHubSpacing.md),
          Text(
            '正在加载BOM数据...',
            style: TextStyle(
              fontSize: 14,
              color: VanHubColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(VanHubSpacing.lg),
            decoration: BoxDecoration(
              color: VanHubColors.surface,
              borderRadius: BorderRadius.circular(VanHubSpacing.radiusLarge * 2),
            ),
            child: Icon(
              Icons.list_alt,
              size: 64,
              color: VanHubColors.outline,
            ),
          ),
          SizedBox(height: VanHubSpacing.lg),
          Text(
            'BOM清单为空',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: VanHubColors.textPrimary,
            ),
          ),
          SizedBox(height: VanHubSpacing.sm),
          Text(
            '开始添加您的第一个BOM项目',
            style: TextStyle(
              fontSize: 14,
              color: VanHubColors.textSecondary,
            ),
          ),
          SizedBox(height: VanHubSpacing.lg),
          VanHubButton(
            text: '添加BOM项目',
            onPressed: () => _onAddBOMItem(),
            icon: Icons.add,
            variant: VanHubButtonVariant.primary,
          ),
        ],
      ),
    );
  }

  /// 构建BOM统计信息
  Widget _buildBOMSummary() {
    final totalItems = widget.bomItems.length;
    final totalCost = _calculateTotalCost();
    final completedItems = _getCompletedItemsCount();
    final progress = totalItems > 0 ? completedItems / totalItems : 0.0;

    return Card(
      color: VanHubColors.primary.withOpacity(0.05),
      child: Padding(
        padding: EdgeInsets.all(VanHubSpacing.md),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  color: VanHubColors.primary,
                  size: 20,
                ),
                SizedBox(width: VanHubSpacing.sm),
                Text(
                  'BOM概览',
                  style: TextStyle(
                    fontSize: 16,
                    color: VanHubColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (widget.isEditable)
                  TextButton.icon(
                    onPressed: () => _onBatchEdit(),
                    icon: Icon(Icons.edit_outlined, size: 16),
                    label: Text('批量操作'),
                    style: TextButton.styleFrom(
                      foregroundColor: VanHubColors.primary,
                    ),
                  ),
              ],
            ),
            
            SizedBox(height: VanHubSpacing.md),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: '总项目',
                    value: totalItems.toString(),
                    icon: Icons.inventory_2_outlined,
                    color: VanHubColors.primary,
                  ),
                ),
                
                if (widget.showCost) ...[
                  SizedBox(width: VanHubSpacing.md),
                  Expanded(
                    child: _buildSummaryItem(
                      title: '总成本',
                      value: '¥${totalCost.toStringAsFixed(2)}',
                      icon: Icons.account_balance_wallet_outlined,
                      color: VanHubColors.secondary,
                    ),
                  ),
                ],
                
                SizedBox(width: VanHubSpacing.md),
                Expanded(
                  child: _buildSummaryItem(
                    title: '完成度',
                    value: '${(progress * 100).toInt()}%',
                    icon: Icons.check_circle_outline,
                    color: VanHubColors.success,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: VanHubSpacing.md),
            
            // 进度条
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '整体进度',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      '$completedItems/$totalItems 已完成',
                      style: TextStyle(
                        fontSize: 12,
                        color: VanHubColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: VanHubSpacing.sm),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: VanHubColors.outlineVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    VanHubColors.success,
                  ),
                  minHeight: 6,
                  borderRadius: BorderRadius.circular(3),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计项目
  Widget _buildSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(VanHubSpacing.sm),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: color,
                  fontSize: 14,
                ),
              ),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: VanHubColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建BOM树形列表
  Widget _buildBOMTree() {
    return ReorderableListView.builder(
      onReorder: widget.isEditable ? _onReorderItems : (o, n) {},
      itemCount: _getVisibleNodes().length,
      itemBuilder: (context, index) {
        final node = _getVisibleNodes()[index];
        return _buildBOMTreeItem(node, index);
      },
    );
  }

  /// 构建BOM树形项目
  Widget _buildBOMTreeItem(BOMTreeNode node, int index) {
    final status = node.item.status;
    final statusConfig = _getStatusConfig(status);
    
    return Container(
      key: ValueKey(node.item.id),
      margin: EdgeInsets.only(bottom: VanHubSpacing.sm),
      child: TweenAnimationBuilder<double>(
        duration: Duration(milliseconds: 200 + (index * 30)),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(20 * (1 - value), 0),
            child: Opacity(
              opacity: value,
              child: Card(
                child: Padding(
                  padding: EdgeInsets.only(
                    left: node.level * VanHubSpacing.lg,
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          // 展开/折叠按钮
                          if (node.children.isNotEmpty)
                            IconButton(
                              onPressed: () => _toggleNodeExpansion(node),
                              icon: Icon(
                                node.isExpanded 
                                    ? Icons.keyboard_arrow_down
                                    : Icons.keyboard_arrow_right,
                                size: 16,
                                color: VanHubColors.textSecondary,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 24,
                                minHeight: 24,
                              ),
                            )
                          else
                            const SizedBox(width: 24),
                          
                          SizedBox(width: VanHubSpacing.sm),
                          
                          // 选择框
                          if (widget.isEditable)
                            Checkbox(
                              value: _selectedItems.contains(node.item.id),
                              onChanged: (value) => _toggleItemSelection(node.item.id),
                            ),
                          
                          SizedBox(width: VanHubSpacing.sm),
                          
                          // 项目图标
                          Container(
                            padding: EdgeInsets.all(VanHubSpacing.sm),
                            decoration: BoxDecoration(
                              color: statusConfig.color.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
                            ),
                            child: Icon(
                              Icons.build_circle_outlined,
                              color: statusConfig.color,
                              size: 20,
                            ),
                          ),
                          
                          SizedBox(width: VanHubSpacing.md),
                          
                          // 项目信息
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        node.item.materialName,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    
                                    // 状态标签
                                    Container(
                                      padding: EdgeInsets.all(VanHubSpacing.xs),
                                      decoration: BoxDecoration(
                                        color: statusConfig.color.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
                                        border: Border.all(
                                          color: statusConfig.color.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            statusConfig.icon,
                                            color: statusConfig.color,
                                            size: 10,
                                          ),
                                          SizedBox(width: VanHubSpacing.xs),
                                          Text(
                                            statusConfig.label,
                                            style: TextStyle(
                                              color: statusConfig.color,
                                              fontSize: 10,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                
                                SizedBox(height: VanHubSpacing.xs),
                                
                                Row(
                                  children: [
                                    Text(
                                      '数量: ${node.item.quantity}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: VanHubColors.textSecondary,
                                      ),
                                    ),
                                    
                                    // 单位显示
                                    Text(
                                      ' 个',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: VanHubColors.textSecondary,
                                      ),
                                    ),
                                    
                                    if (widget.showCost && node.item.estimatedPrice != null) ...[
                                      SizedBox(width: VanHubSpacing.md),
                                      Text(
                                        '单价: ¥${node.item.estimatedPrice!.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: VanHubColors.secondary,
                                        ),
                                      ),
                                      SizedBox(width: VanHubSpacing.md),
                                      Text(
                                        '小计: ¥${(node.item.estimatedPrice! * node.item.quantity).toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: VanHubColors.secondary,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          // 操作按钮
                          if (widget.isEditable) ...[
                            PopupMenuButton<String>(
                              onSelected: (value) => _onItemMenuSelected(value, node.item),
                              itemBuilder: (context) => [
                                PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit_outlined, size: 16),
                                      SizedBox(width: VanHubSpacing.sm),
                                      Text('编辑'),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'duplicate',
                                  child: Row(
                                    children: [
                                      Icon(Icons.copy_outlined, size: 16),
                                      SizedBox(width: VanHubSpacing.sm),
                                      Text('复制'),
                                    ],
                                  ),
                                ),
                                PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete_outline, size: 16, color: VanHubColors.error),
                                      SizedBox(width: VanHubSpacing.sm),
                                      Text('删除', style: TextStyle(color: VanHubColors.error)),
                                    ],
                                  ),
                                ),
                              ],
                              child: Container(
                                padding: EdgeInsets.all(VanHubSpacing.sm),
                                child: Icon(
                                  Icons.more_vert,
                                  size: 16,
                                  color: VanHubColors.textSecondary,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      
                      // 备注信息
                      if (node.item.notes?.isNotEmpty == true) ...[
                        SizedBox(height: VanHubSpacing.sm),
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(VanHubSpacing.sm),
                          decoration: BoxDecoration(
                            color: VanHubColors.surfaceVariant,
                            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
                          ),
                          child: Text(
                            node.item.notes!,
                            style: TextStyle(
                              fontSize: 12,
                              color: VanHubColors.textSecondary,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 工具方法
  void _buildTreeNodes() {
    // This is a simplified version. A real implementation would build a nested tree.
    _treeNodes.clear();
    _treeNodes.addAll(widget.bomItems.map((item) {
      return BOMTreeNode(
        item: item,
        level: 0, 
      );
    }).toList());
  }

  List<BOMTreeNode> _getVisibleNodes() {
    // This is a simplified version. A real implementation would respect isExpanded.
    return _treeNodes;
  }

  double _calculateTotalCost() {
    return widget.bomItems.fold(0.0, (sum, item) {
      return sum + ((item.estimatedPrice ?? 0) * item.quantity);
    });
  }

  int _getCompletedItemsCount() {
    return widget.bomItems.where((item) {
      final status = item.status;
      // 使用domain_status.BomItemStatus来比较状态
      return status == domain_status.BomItemStatus.installed;
    }).length;
  }

  _StatusConfig _getStatusConfig(domain_status.BomItemStatus status) {
    // 使用BomItemStatus的枚举值来比较状态
    if (status == domain_status.BomItemStatus.pending) {
      return _StatusConfig(
        label: '计划中',
        icon: Icons.edit_note_outlined,
        color: VanHubColors.textSecondary,
      );
    } else if (status == domain_status.BomItemStatus.ordered) {
      return _StatusConfig(
        label: '已订购',
        icon: Icons.shopping_cart_outlined,
        color: VanHubColors.warning,
      );
    } else if (status == domain_status.BomItemStatus.received) {
      return _StatusConfig(
        label: '已收货',
        icon: Icons.inventory_outlined,
        color: VanHubColors.info,
      );
    } else if (status == domain_status.BomItemStatus.installed) {
      return _StatusConfig(
        label: '已安装',
        icon: Icons.build_outlined,
        color: VanHubColors.success,
      );
    } else {
      return _StatusConfig(
        label: '已取消',
        icon: Icons.cancel_outlined,
        color: VanHubColors.error,
      );
    }
  }

  // 事件处理
  void _toggleNodeExpansion(BOMTreeNode node) {
    setState(() {
      node.isExpanded = !node.isExpanded;
    });
  }

  void _toggleItemSelection(String itemId) {
    setState(() {
      if (_selectedItems.contains(itemId)) {
        _selectedItems.remove(itemId);
      } else {
        _selectedItems.add(itemId);
      }
    });
  }

  void _onReorderItems(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    setState(() {
      final node = _treeNodes.removeAt(oldIndex);
      _treeNodes.insert(newIndex, node);
    });
    
    final reorderedItems = _treeNodes.map((node) => node.item).toList();
    widget.onReorder?.call(reorderedItems);
  }

  void _onItemMenuSelected(String value, BomItem item) {
    switch (value) {
      case 'edit':
        widget.onItemEdit?.call(item);
        break;
      case 'duplicate':
        // TODO: 实现复制功能
        break;
      case 'delete':
        widget.onItemDelete?.call(item);
        break;
    }
  }

  void _onAddBOMItem() {
    // TODO: 实现添加BOM项目
  }

  void _onBatchEdit() {
    // TODO: 实现批量编辑
  }
}

/// 状态配置
class _StatusConfig {
  final String label;
  final IconData icon;
  final Color color;

  const _StatusConfig({
    required this.label,
    required this.icon,
    required this.color,
  });
}