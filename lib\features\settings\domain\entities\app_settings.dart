import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_settings.freezed.dart';
part 'app_settings.g.dart';

/// 主题模式
enum AppThemeMode {
  light,
  dark,
  system,
}

/// 语言设置
enum AppLanguage {
  chinese,
  english,
}

/// 数据同步频率
enum SyncFrequency {
  realtime,
  hourly,
  daily,
  manual,
}

/// 应用设置实体
@freezed
class AppSettings with _$AppSettings {
  const factory AppSettings({
    required String id,
    required String userId,
    
    // 外观设置
    @Default(AppThemeMode.system) AppThemeMode themeMode,
    @Default(AppLanguage.chinese) AppLanguage language,
    @Default(1.0) double textScaleFactor,
    @Default(true) bool enableAnimations,
    @Default(true) bool useSystemAccentColor,
    int? customAccentColor,
    
    // 通知设置
    @Default(true) bool enablePushNotifications,
    @Default(true) bool enableLocalNotifications,
    @Default(true) bool enableSoundNotifications,
    @Default(true) bool enableVibrationNotifications,
    @Default(true) bool enableProjectNotifications,
    @Default(true) bool enableBomNotifications,
    @Default(true) bool enableReminderNotifications,
    @Default(false) bool enableMarketingNotifications,
    
    // 数据同步设置
    @Default(true) bool enableCloudSync,
    @Default(SyncFrequency.realtime) SyncFrequency syncFrequency,
    @Default(true) bool syncOnWifiOnly,
    @Default(true) bool enableAutoBackup,
    @Default(7) int backupRetentionDays,
    
    // 隐私安全设置
    @Default(true) bool enableAnalytics,
    @Default(false) bool enableCrashReporting,
    @Default(true) bool enableUsageStatistics,
    @Default(false) bool enableBiometricAuth,
    @Default(5) int autoLockMinutes,
    
    // 功能设置
    @Default(true) bool enableSmartRecommendations,
    @Default(true) bool enablePriceAlerts,
    @Default(true) bool enableInventoryTracking,
    @Default(false) bool enableExperimentalFeatures,
    @Default(true) bool enableOfflineMode,
    
    // 显示设置
    @Default(true) bool showWelcomeScreen,
    @Default(true) bool showTutorials,
    @Default(false) bool showDebugInfo,
    @Default(true) bool enableHapticFeedback,
    @Default(50) int listPageSize,
    
    // 导出设置
    @Default('PDF') String defaultExportFormat,
    @Default(true) bool includeImagesInExport,
    @Default(false) bool compressExportFiles,
    
    // 高级设置
    @Default(30) int cacheExpirationDays,
    @Default(100) int maxCacheSize, // MB
    @Default(true) bool enableImageCompression,
    @Default(80) int imageCompressionQuality,
    
    // 元数据
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? lastSyncAt,
    @Default(1) int version,
    @Default({}) Map<String, dynamic> customSettings,
  }) = _AppSettings;

  factory AppSettings.fromJson(Map<String, dynamic> json) => 
      _$AppSettingsFromJson(json);
}

/// 设置扩展方法
extension AppSettingsX on AppSettings {
  /// 获取主题模式显示名称
  String get themeModeDisplayName {
    switch (themeMode) {
      case AppThemeMode.light:
        return '浅色模式';
      case AppThemeMode.dark:
        return '深色模式';
      case AppThemeMode.system:
        return '跟随系统';
    }
  }

  /// 获取语言显示名称
  String get languageDisplayName {
    switch (language) {
      case AppLanguage.chinese:
        return '中文';
      case AppLanguage.english:
        return 'English';
    }
  }

  /// 获取同步频率显示名称
  String get syncFrequencyDisplayName {
    switch (syncFrequency) {
      case SyncFrequency.realtime:
        return '实时同步';
      case SyncFrequency.hourly:
        return '每小时';
      case SyncFrequency.daily:
        return '每天';
      case SyncFrequency.manual:
        return '手动同步';
    }
  }

  /// 是否启用所有通知
  bool get isAllNotificationsEnabled {
    return enablePushNotifications && 
           enableLocalNotifications && 
           enableProjectNotifications && 
           enableBomNotifications && 
           enableReminderNotifications;
  }

  /// 是否启用隐私功能
  bool get isPrivacyModeEnabled {
    return !enableAnalytics && 
           !enableCrashReporting && 
           !enableUsageStatistics;
  }

  /// 获取通知设置摘要
  String get notificationSummary {
    final enabledCount = [
      enablePushNotifications,
      enableLocalNotifications,
      enableProjectNotifications,
      enableBomNotifications,
      enableReminderNotifications,
    ].where((enabled) => enabled).length;
    
    return '$enabledCount/5 项通知已启用';
  }

  /// 获取同步状态描述
  String get syncStatusDescription {
    if (!enableCloudSync) return '云端同步已关闭';
    if (lastSyncAt == null) return '尚未同步';
    
    final now = DateTime.now();
    final difference = now.difference(lastSyncAt!);
    
    if (difference.inMinutes < 1) {
      return '刚刚同步';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前同步';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前同步';
    } else {
      return '${difference.inDays}天前同步';
    }
  }

  /// 创建默认设置
  factory AppSettings.defaultSettings(String userId) {
    return AppSettings(
      id: 'settings_$userId',
      userId: userId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// 更新同步时间
  AppSettings updateSyncTime() {
    return copyWith(
      lastSyncAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// 启用所有通知
  AppSettings enableAllNotifications() {
    return copyWith(
      enablePushNotifications: true,
      enableLocalNotifications: true,
      enableProjectNotifications: true,
      enableBomNotifications: true,
      enableReminderNotifications: true,
      updatedAt: DateTime.now(),
    );
  }

  /// 禁用所有通知
  AppSettings disableAllNotifications() {
    return copyWith(
      enablePushNotifications: false,
      enableLocalNotifications: false,
      enableProjectNotifications: false,
      enableBomNotifications: false,
      enableReminderNotifications: false,
      enableMarketingNotifications: false,
      updatedAt: DateTime.now(),
    );
  }

  /// 启用隐私模式
  AppSettings enablePrivacyMode() {
    return copyWith(
      enableAnalytics: false,
      enableCrashReporting: false,
      enableUsageStatistics: false,
      enableMarketingNotifications: false,
      updatedAt: DateTime.now(),
    );
  }

  /// 重置为默认设置
  AppSettings resetToDefaults() {
    return AppSettings.defaultSettings(userId).copyWith(
      id: id,
      createdAt: createdAt,
      version: version + 1,
    );
  }

  /// 合并设置（用于同步冲突解决）
  AppSettings mergeWith(AppSettings other) {
    // 使用更新时间较新的设置
    if (other.updatedAt.isAfter(updatedAt)) {
      return other.copyWith(
        version: version + 1,
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  /// 验证设置有效性
  List<String> validate() {
    final errors = <String>[];
    
    if (textScaleFactor < 0.5 || textScaleFactor > 3.0) {
      errors.add('文字缩放比例必须在0.5-3.0之间');
    }
    
    if (autoLockMinutes < 1 || autoLockMinutes > 60) {
      errors.add('自动锁定时间必须在1-60分钟之间');
    }
    
    if (backupRetentionDays < 1 || backupRetentionDays > 365) {
      errors.add('备份保留天数必须在1-365天之间');
    }
    
    if (listPageSize < 10 || listPageSize > 200) {
      errors.add('列表页面大小必须在10-200之间');
    }
    
    if (maxCacheSize < 10 || maxCacheSize > 1000) {
      errors.add('最大缓存大小必须在10-1000MB之间');
    }
    
    if (imageCompressionQuality < 10 || imageCompressionQuality > 100) {
      errors.add('图片压缩质量必须在10-100之间');
    }
    
    return errors;
  }

  /// 获取设置摘要
  Map<String, dynamic> getSummary() {
    return {
      'theme': themeModeDisplayName,
      'language': languageDisplayName,
      'notifications': notificationSummary,
      'sync': syncStatusDescription,
      'privacy': isPrivacyModeEnabled ? '隐私模式已启用' : '标准模式',
      'version': version,
      'lastUpdated': updatedAt.toIso8601String(),
    };
  }

  /// 导出设置（用于备份）
  Map<String, dynamic> exportSettings() {
    final exported = toJson();
    // 移除敏感信息
    exported.remove('id');
    exported.remove('userId');
    exported.remove('createdAt');
    exported.remove('lastSyncAt');
    
    // 添加导出元数据
    exported['exportedAt'] = DateTime.now().toIso8601String();
    exported['exportVersion'] = '1.0';
    
    return exported;
  }

  /// 从导出数据导入设置
  static AppSettings importSettings(
    Map<String, dynamic> exportedData,
    String userId,
  ) {
    // 移除导出元数据
    exportedData.remove('exportedAt');
    exportedData.remove('exportVersion');
    
    // 添加必要字段
    exportedData['id'] = 'settings_$userId';
    exportedData['userId'] = userId;
    exportedData['createdAt'] = DateTime.now().toIso8601String();
    exportedData['updatedAt'] = DateTime.now().toIso8601String();
    
    return AppSettings.fromJson(exportedData);
  }
}
