import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../../../bom/presentation/providers/bom_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 快速添加到BOM组件
/// 提供一键添加材料到BOM的快捷操作
class QuickAddToBomWidget extends ConsumerStatefulWidget {
  final domain.Material material;
  final String projectId;
  final VoidCallback? onSuccess;
  final VoidCallback? onCancel;

  const QuickAddToBomWidget({
    super.key,
    required this.material,
    required this.projectId,
    this.onSuccess,
    this.onCancel,
  });

  @override
  ConsumerState<QuickAddToBomWidget> createState() => _QuickAddToBomWidgetState();
}

class _QuickAddToBomWidgetState extends ConsumerState<QuickAddToBomWidget> {
  final _quantityController = TextEditingController(text: '1');
  final _notesController = TextEditingController();
  bool _isAdding = false;
  bool _useCustomPrice = false;
  double? _customPrice;

  @override
  void dispose() {
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.add_shopping_cart,
                color: VanHubColors.primary,
                size: 24,
              ),
              SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: Text(
                  '添加到BOM',
                  style: VanHubTypography.titleLarge,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: widget.onCancel,
              ),
            ],
          ),
          
          SizedBox(height: VanHubSpacing.md),
          
          // 材料信息
          _buildMaterialInfo(),
          
          SizedBox(height: VanHubSpacing.lg),
          
          // 数量输入
          _buildQuantityInput(),
          
          SizedBox(height: VanHubSpacing.md),
          
          // 价格设置
          _buildPriceSettings(),
          
          SizedBox(height: VanHubSpacing.md),
          
          // 备注输入
          _buildNotesInput(),
          
          SizedBox(height: VanHubSpacing.lg),
          
          // 操作按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildMaterialInfo() {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: VanHubColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.inventory_2,
              color: VanHubColors.primary,
            ),
          ),
          SizedBox(width: VanHubSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.material.name,
                  style: VanHubTypography.titleMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (widget.material.brand != null) ...[
                  SizedBox(height: 4),
                  Text(
                    '品牌: ${widget.material.brand}',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
                ...[
                SizedBox(height: 2),
                Text(
                  '分类: ${widget.material.category}',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '数量',
          style: VanHubTypography.titleSmall,
        ),
        SizedBox(height: VanHubSpacing.xs),
        Row(
          children: [
            IconButton(
              onPressed: () {
                final current = int.tryParse(_quantityController.text) ?? 1;
                if (current > 1) {
                  _quantityController.text = (current - 1).toString();
                }
              },
              icon: const Icon(Icons.remove_circle_outline),
              color: VanHubColors.primary,
            ),
            Expanded(
              child: TextField(
                controller: _quantityController,
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            IconButton(
              onPressed: () {
                final current = int.tryParse(_quantityController.text) ?? 1;
                _quantityController.text = (current + 1).toString();
              },
              icon: const Icon(Icons.add_circle_outline),
              color: VanHubColors.primary,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriceSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '价格设置',
              style: VanHubTypography.titleSmall,
            ),
            const Spacer(),
            Switch(
              value: _useCustomPrice,
              onChanged: (value) {
                setState(() {
                  _useCustomPrice = value;
                  if (!value) {
                    _customPrice = null;
                  }
                });
              },
              activeColor: VanHubColors.primary,
            ),
            Text(
              '自定义价格',
              style: VanHubTypography.bodySmall,
            ),
          ],
        ),
        SizedBox(height: VanHubSpacing.xs),
        if (_useCustomPrice) ...[
          TextField(
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: '自定义价格',
              prefixText: '¥',
              border: OutlineInputBorder(),
              helperText: '留空使用材料库默认价格',
            ),
            onChanged: (value) {
              _customPrice = double.tryParse(value);
            },
          ),
        ] else ...[
          Container(
            padding: EdgeInsets.all(VanHubSpacing.md),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                SizedBox(width: VanHubSpacing.xs),
                Text(
                  '使用材料库价格: ¥${widget.material.price.toStringAsFixed(2) ?? '未设置'}',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotesInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '备注（可选）',
          style: VanHubTypography.titleSmall,
        ),
        SizedBox(height: VanHubSpacing.xs),
        TextField(
          controller: _notesController,
          maxLines: 2,
          decoration: const InputDecoration(
            hintText: '添加备注信息...',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isAdding ? null : widget.onCancel,
            child: const Text('取消'),
          ),
        ),
        SizedBox(width: VanHubSpacing.md),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isAdding ? null : _handleAddToBom,
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isAdding
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('添加到BOM'),
          ),
        ),
      ],
    );
  }

  Future<void> _handleAddToBom() async {
    final quantity = int.tryParse(_quantityController.text);
    if (quantity == null || quantity <= 0) {
      _showError('请输入有效的数量');
      return;
    }

    setState(() {
      _isAdding = true;
    });

    try {
      final result = await ref.read(bomControllerProvider.notifier).addMaterialToBom(
        projectId: widget.projectId,
        materialId: widget.material.id,
        quantity: quantity,
        customPrice: _useCustomPrice ? _customPrice : null,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      if (mounted) {
        result.fold(
          (failure) {
            _showError(failure.message);
          },
          (_) {
            _showSuccess();
            if (widget.onSuccess != null) {
              widget.onSuccess!();
            }
          },
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('添加失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAdding = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: VanHubColors.error,
      ),
    );
  }

  void _showSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: VanHubSpacing.xs),
            Text('${widget.material.name} 已添加到BOM'),
          ],
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}
