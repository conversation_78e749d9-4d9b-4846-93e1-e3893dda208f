import 'dart:async';
import 'package:fpdart/fpdart.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/app_settings.dart';
import '../../domain/repositories/settings_repository.dart';
import '../../domain/services/settings_service.dart';

/// 同步策略
enum SyncStrategy {
  localFirst,    // 本地优先
  cloudFirst,    // 云端优先
  newerFirst,    // 时间较新优先
  versionFirst,  // 版本较高优先
  merge,         // 智能合并
}

/// 冲突解决策略
enum ConflictResolution {
  useLocal,      // 使用本地
  useCloud,      // 使用云端
  merge,         // 合并
  askUser,       // 询问用户
}

/// 设置同步管理器
class SettingsSyncManager {
  final SettingsRepository _repository;
  final Connectivity _connectivity;
  
  // 同步状态
  final Map<String, SyncStatus> _syncStatuses = {};
  final Map<String, StreamController<SyncStatus>> _statusControllers = {};
  
  // 同步队列
  final Map<String, Timer> _syncTimers = {};
  final Set<String> _syncingUsers = {};

  SettingsSyncManager({
    required SettingsRepository repository,
    Connectivity? connectivity,
  }) : _repository = repository,
       _connectivity = connectivity ?? Connectivity();

  /// 启动自动同步
  Future<void> startAutoSync(String userId, {
    Duration interval = const Duration(minutes: 5),
    SyncStrategy strategy = SyncStrategy.newerFirst,
  }) async {
    // 取消现有定时器
    _syncTimers[userId]?.cancel();
    
    // 创建新的定时器
    _syncTimers[userId] = Timer.periodic(interval, (timer) {
      _performAutoSync(userId, strategy);
    });
    
    // 立即执行一次同步
    await _performAutoSync(userId, strategy);
  }

  /// 停止自动同步
  void stopAutoSync(String userId) {
    _syncTimers[userId]?.cancel();
    _syncTimers.remove(userId);
  }

  /// 手动同步
  Future<Either<Failure, SettingsSyncResult>> manualSync(
    String userId, {
    SyncStrategy strategy = SyncStrategy.newerFirst,
    bool force = false,
  }) async {
    if (_syncingUsers.contains(userId) && !force) {
      return Right(SettingsSyncResult(
        status: SyncStatus.syncing,
        message: '正在同步中...',
        timestamp: DateTime.now(),
      ));
    }

    return await _performSync(userId, strategy);
  }

  /// 解决同步冲突
  Future<Either<Failure, SettingsSyncResult>> resolveConflict(
    String userId,
    ConflictResolution resolution, {
    AppSettings? preferredSettings,
  }) async {
    try {
      final localResult = await _repository.getLocalSettings(userId);
      final cloudResult = await _repository.getCloudSettings(userId);

      return await localResult.fold(
        (localFailure) async => Left(localFailure),
        (localSettings) async {
          return await cloudResult.fold(
            (cloudFailure) async => Left(cloudFailure),
            (cloudSettings) async {
              AppSettings resolvedSettings;

              switch (resolution) {
                case ConflictResolution.useLocal:
                  resolvedSettings = localSettings;
                  break;
                
                case ConflictResolution.useCloud:
                  resolvedSettings = cloudSettings;
                  break;
                
                case ConflictResolution.merge:
                  resolvedSettings = _mergeSettings(localSettings, cloudSettings);
                  break;
                
                case ConflictResolution.askUser:
                  if (preferredSettings != null) {
                    resolvedSettings = preferredSettings;
                  } else {
                    return Right(SettingsSyncResult(
                      status: SyncStatus.conflict,
                      message: '需要用户选择解决方案',
                      timestamp: DateTime.now(),
                    ));
                  }
                  break;
              }

              // 保存解决后的设置
              await _repository.saveLocalSettings(resolvedSettings);
              await _repository.saveCloudSettings(resolvedSettings);

              _updateSyncStatus(userId, SyncStatus.success);

              return Right(SettingsSyncResult(
                status: SyncStatus.success,
                message: '冲突已解决',
                settings: resolvedSettings,
                timestamp: DateTime.now(),
              ));
            },
          );
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '解决冲突失败: $e'));
    }
  }

  /// 检查网络连接
  Future<bool> hasNetworkConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      return false;
    }
  }

  /// 获取同步状态
  SyncStatus getSyncStatus(String userId) {
    return _syncStatuses[userId] ?? SyncStatus.idle;
  }

  /// 订阅同步状态
  Stream<SyncStatus> subscribeSyncStatus(String userId) {
    if (!_statusControllers.containsKey(userId)) {
      _statusControllers[userId] = StreamController<SyncStatus>.broadcast();
    }
    return _statusControllers[userId]!.stream;
  }

  /// 执行自动同步
  Future<void> _performAutoSync(String userId, SyncStrategy strategy) async {
    // 检查网络连接
    if (!await hasNetworkConnection()) {
      _updateSyncStatus(userId, SyncStatus.failed);
      return;
    }

    // 执行同步
    await _performSync(userId, strategy);
  }

  /// 执行同步
  Future<Either<Failure, SettingsSyncResult>> _performSync(
    String userId,
    SyncStrategy strategy,
  ) async {
    try {
      _syncingUsers.add(userId);
      _updateSyncStatus(userId, SyncStatus.syncing);

      final localResult = await _repository.getLocalSettings(userId);
      final cloudResult = await _repository.getCloudSettings(userId);

      // 处理本地或云端不存在的情况
      if (localResult.isLeft() && cloudResult.isLeft()) {
        // 都不存在，创建默认设置
        final defaultSettings = AppSettings.defaultSettings(userId);
        await _repository.saveLocalSettings(defaultSettings);
        await _repository.saveCloudSettings(defaultSettings);
        
        _updateSyncStatus(userId, SyncStatus.success);
        return Right(SettingsSyncResult(
          status: SyncStatus.success,
          message: '已创建默认设置',
          settings: defaultSettings,
          timestamp: DateTime.now(),
        ));
      }

      if (localResult.isLeft()) {
        // 本地不存在，使用云端
        final cloudSettings = cloudResult.getOrElse(() => throw Exception());
        await _repository.saveLocalSettings(cloudSettings);
        
        _updateSyncStatus(userId, SyncStatus.success);
        return Right(SettingsSyncResult(
          status: SyncStatus.success,
          message: '已从云端恢复设置',
          settings: cloudSettings,
          timestamp: DateTime.now(),
        ));
      }

      if (cloudResult.isLeft()) {
        // 云端不存在，上传本地
        final localSettings = localResult.getOrElse(() => throw Exception());
        await _repository.saveCloudSettings(localSettings);
        
        _updateSyncStatus(userId, SyncStatus.success);
        return Right(SettingsSyncResult(
          status: SyncStatus.success,
          message: '已上传到云端',
          settings: localSettings,
          timestamp: DateTime.now(),
        ));
      }

      // 都存在，根据策略处理
      final localSettings = localResult.getOrElse(() => throw Exception());
      final cloudSettings = cloudResult.getOrElse(() => throw Exception());

      final syncResult = await _applySyncStrategy(
        userId,
        localSettings,
        cloudSettings,
        strategy,
      );

      _updateSyncStatus(userId, syncResult.status);
      return Right(syncResult);

    } catch (e) {
      _updateSyncStatus(userId, SyncStatus.failed);
      return Left(UnknownFailure(message: '同步失败: $e'));
    } finally {
      _syncingUsers.remove(userId);
    }
  }

  /// 应用同步策略
  Future<SettingsSyncResult> _applySyncStrategy(
    String userId,
    AppSettings localSettings,
    AppSettings cloudSettings,
    SyncStrategy strategy,
  ) async {
    AppSettings? resultSettings;
    String message = '';

    switch (strategy) {
      case SyncStrategy.localFirst:
        resultSettings = localSettings;
        await _repository.saveCloudSettings(localSettings);
        message = '已使用本地设置';
        break;

      case SyncStrategy.cloudFirst:
        resultSettings = cloudSettings;
        await _repository.saveLocalSettings(cloudSettings);
        message = '已使用云端设置';
        break;

      case SyncStrategy.newerFirst:
        if (localSettings.updatedAt.isAfter(cloudSettings.updatedAt)) {
          resultSettings = localSettings;
          await _repository.saveCloudSettings(localSettings);
          message = '本地设置较新，已上传';
        } else if (cloudSettings.updatedAt.isAfter(localSettings.updatedAt)) {
          resultSettings = cloudSettings;
          await _repository.saveLocalSettings(cloudSettings);
          message = '云端设置较新，已下载';
        } else {
          // 时间相同，使用版本策略
          return await _applySyncStrategy(userId, localSettings, cloudSettings, SyncStrategy.versionFirst);
        }
        break;

      case SyncStrategy.versionFirst:
        if (localSettings.version > cloudSettings.version) {
          resultSettings = localSettings;
          await _repository.saveCloudSettings(localSettings);
          message = '本地版本较高，已上传';
        } else if (cloudSettings.version > localSettings.version) {
          resultSettings = cloudSettings;
          await _repository.saveLocalSettings(cloudSettings);
          message = '云端版本较高，已下载';
        } else {
          // 版本相同，无需同步
          resultSettings = localSettings;
          message = '设置已同步';
        }
        break;

      case SyncStrategy.merge:
        resultSettings = _mergeSettings(localSettings, cloudSettings);
        await _repository.saveLocalSettings(resultSettings);
        await _repository.saveCloudSettings(resultSettings);
        message = '已合并设置';
        break;
    }

    return SettingsSyncResult(
      status: SyncStatus.success,
      message: message,
      settings: resultSettings,
      timestamp: DateTime.now(),
    );
  }

  /// 智能合并设置
  AppSettings _mergeSettings(AppSettings local, AppSettings cloud) {
    // 使用更新时间较新的设置作为基础
    final baseSettings = local.updatedAt.isAfter(cloud.updatedAt) ? local : cloud;
    final otherSettings = local.updatedAt.isAfter(cloud.updatedAt) ? cloud : local;

    // 合并策略：
    // 1. 使用较新的基础设置
    // 2. 对于布尔值，如果一个为true，则使用true（更宽松的策略）
    // 3. 对于数值，使用较新的值
    // 4. 对于枚举，使用较新的值

    return baseSettings.copyWith(
      // 通知设置：任一为true则为true
      enablePushNotifications: local.enablePushNotifications || cloud.enablePushNotifications,
      enableLocalNotifications: local.enableLocalNotifications || cloud.enableLocalNotifications,
      enableProjectNotifications: local.enableProjectNotifications || cloud.enableProjectNotifications,
      
      // 功能设置：使用较新的
      enableSmartRecommendations: baseSettings.enableSmartRecommendations,
      enablePriceAlerts: baseSettings.enablePriceAlerts,
      
      // 版本递增
      version: (local.version > cloud.version ? local.version : cloud.version) + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// 更新同步状态
  void _updateSyncStatus(String userId, SyncStatus status) {
    _syncStatuses[userId] = status;
    
    if (_statusControllers.containsKey(userId)) {
      _statusControllers[userId]!.add(status);
    }
  }

  /// 释放资源
  void dispose() {
    // 取消所有定时器
    for (final timer in _syncTimers.values) {
      timer.cancel();
    }
    _syncTimers.clear();

    // 关闭所有控制器
    for (final controller in _statusControllers.values) {
      controller.close();
    }
    _statusControllers.clear();

    _syncStatuses.clear();
    _syncingUsers.clear();
  }
}
