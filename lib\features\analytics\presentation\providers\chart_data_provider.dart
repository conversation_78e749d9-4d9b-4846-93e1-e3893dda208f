import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/analytics_data.dart';
import 'analytics_provider.dart';

part 'chart_data_provider.g.dart';

/// 图表数据刷新间隔（秒）
const int kChartRefreshInterval = 30;

/// 实时图表类型枚举
enum RealTimeChartType {
  costTrend,      // 成本趋势
  progressStats,  // 进度统计
  materialStats,  // 材料统计
}

/// 分析图表数据点
class AnalyticsChartDataPoint {
  final double x;
  final double y;
  final String? label;
  final DateTime timestamp;

  const AnalyticsChartDataPoint({
    required this.x,
    required this.y,
    this.label,
    required this.timestamp,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsChartDataPoint &&
          runtimeType == other.runtimeType &&
          x == other.x &&
          y == other.y &&
          label == other.label;

  @override
  int get hashCode => x.hashCode ^ y.hashCode ^ label.hashCode;
}

/// 分析图表数据系列
class AnalyticsChartDataSeries {
  final String name;
  final List<AnalyticsChartDataPoint> data;
  final String color;
  final DateTime lastUpdated;

  const AnalyticsChartDataSeries({
    required this.name,
    required this.data,
    required this.color,
    required this.lastUpdated,
  });

  AnalyticsChartDataSeries copyWith({
    String? name,
    List<AnalyticsChartDataPoint>? data,
    String? color,
    DateTime? lastUpdated,
  }) {
    return AnalyticsChartDataSeries(
      name: name ?? this.name,
      data: data ?? this.data,
      color: color ?? this.color,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// 实时成本趋势数据Provider
@riverpod
class RealTimeCostTrend extends _$RealTimeCostTrend {
  Timer? _refreshTimer;

  @override
  Future<List<AnalyticsChartDataSeries>> build(String projectId) async {
    // 设置自动刷新
    _setupAutoRefresh(projectId);
    
    // 获取初始数据
    return await _loadCostTrendData(projectId);
  }

  /// 设置自动刷新
  void _setupAutoRefresh(String projectId) {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      const Duration(seconds: kChartRefreshInterval),
      (_) => refresh(projectId),
    );
  }

  /// 手动刷新数据
  Future<void> refresh(String projectId) async {
    state = const AsyncLoading();
    try {
      final newData = await _loadCostTrendData(projectId);
      state = AsyncData(newData);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// 加载成本趋势数据
  Future<List<AnalyticsChartDataSeries>> _loadCostTrendData(String projectId) async {
    try {
      final analytics = await ref.read(projectAnalyticsProvider(projectId).future);
      final now = DateTime.now();
      
      // 预算数据系列
      final budgetData = analytics.monthlyTrends.asMap().entries.map((entry) {
        return AnalyticsChartDataPoint(
          x: entry.key.toDouble(),
          y: analytics.totalBudget / analytics.monthlyTrends.length,
          label: '${entry.value.year}-${entry.value.month.toString().padLeft(2, '0')}',
          timestamp: DateTime(entry.value.year, entry.value.month),
        );
      }).toList();

      // 实际支出数据系列
      final actualData = analytics.monthlyTrends.asMap().entries.map((entry) {
        return AnalyticsChartDataPoint(
          x: entry.key.toDouble(),
          y: entry.value.expenses,
          label: '${entry.value.year}-${entry.value.month.toString().padLeft(2, '0')}',
          timestamp: DateTime(entry.value.year, entry.value.month),
        );
      }).toList();

      return [
        AnalyticsChartDataSeries(
          name: '预算',
          data: budgetData,
          color: '#2196F3',
          lastUpdated: now,
        ),
        AnalyticsChartDataSeries(
          name: '实际支出',
          data: actualData,
          color: analytics.isOverBudget ? '#F44336' : '#4CAF50',
          lastUpdated: now,
        ),
      ];
    } catch (e) {
      throw Exception('加载成本趋势数据失败: $e');
    }
  }

  // Riverpod会自动处理资源清理，不需要手动dispose
  void _cancelTimer() {
    _refreshTimer?.cancel();
  }
}

/// 实时进度统计数据Provider
@riverpod
class RealTimeProgressStats extends _$RealTimeProgressStats {
  Timer? _refreshTimer;

  @override
  Future<Map<String, dynamic>> build(String projectId) async {
    _setupAutoRefresh(projectId);
    return await _loadProgressData(projectId);
  }

  void _setupAutoRefresh(String projectId) {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      const Duration(seconds: kChartRefreshInterval),
      (_) => refresh(projectId),
    );
  }

  Future<void> refresh(String projectId) async {
    state = const AsyncLoading();
    try {
      final newData = await _loadProgressData(projectId);
      state = AsyncData(newData);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  Future<Map<String, dynamic>> _loadProgressData(String projectId) async {
    try {
      final analytics = await ref.read(projectAnalyticsProvider(projectId).future);
      final progressStats = await ref.read(progressStatsProvider(projectId).future);
      
      return {
        'completion_percentage': analytics.completionPercentage,
        'completed_tasks': analytics.completedTasks,
        'total_tasks': analytics.totalTasks,
        'task_breakdown': {
          'completed': progressStats.completedTasks,
          'in_progress': progressStats.inProgressTasks,
          'pending': progressStats.pendingTasks,
        },
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('加载进度数据失败: $e');
    }
  }

  // Riverpod会自动处理资源清理，不需要手动dispose
  void _cancelTimer() {
    _refreshTimer?.cancel();
  }
}

/// 实时材料使用统计Provider
@riverpod
class RealTimeMaterialStats extends _$RealTimeMaterialStats {
  Timer? _refreshTimer;

  @override
  Future<List<AnalyticsChartDataSeries>> build(String projectId) async {
    _setupAutoRefresh(projectId);
    return await _loadMaterialStatsData(projectId);
  }

  void _setupAutoRefresh(String projectId) {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      const Duration(seconds: kChartRefreshInterval),
      (_) => refresh(projectId),
    );
  }

  Future<void> refresh(String projectId) async {
    state = const AsyncLoading();
    try {
      final newData = await _loadMaterialStatsData(projectId);
      state = AsyncData(newData);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  Future<List<AnalyticsChartDataSeries>> _loadMaterialStatsData(String projectId) async {
    try {
      final materialStats = await ref.read(materialUsageStatsProvider(projectId).future);
      final now = DateTime.now();
      
      final data = materialStats.entries.toList().asMap().entries.map((entry) {
        return AnalyticsChartDataPoint(
          x: entry.key.toDouble(),
          y: entry.value.value.toDouble(),
          label: entry.value.key,
          timestamp: now,
        );
      }).toList();

      return [
        AnalyticsChartDataSeries(
          name: '材料使用统计',
          data: data,
          color: '#FF9800',
          lastUpdated: now,
        ),
      ];
    } catch (e) {
      throw Exception('加载材料统计数据失败: $e');
    }
  }

  // Riverpod会自动处理资源清理，不需要手动dispose
  void _cancelTimer() {
    _refreshTimer?.cancel();
  }
}

/// 图表数据管理器
@riverpod
class ChartDataManager extends _$ChartDataManager {
  final Map<String, Timer> _refreshTimers = {};

  @override
  Map<String, DateTime> build() {
    return {};
  }

  /// 开始监控项目数据变化
  void startMonitoring(String projectId) {
    _refreshTimers[projectId]?.cancel();
    _refreshTimers[projectId] = Timer.periodic(
      const Duration(seconds: kChartRefreshInterval),
      (_) => _refreshProjectData(projectId),
    );
  }

  /// 停止监控项目数据变化
  void stopMonitoring(String projectId) {
    _refreshTimers[projectId]?.cancel();
    _refreshTimers.remove(projectId);
  }

  /// 刷新项目数据
  void _refreshProjectData(String projectId) {
    // 刷新相关的Provider
    ref.invalidate(realTimeCostTrendProvider(projectId));
    ref.invalidate(realTimeProgressStatsProvider(projectId));
    ref.invalidate(realTimeMaterialStatsProvider(projectId));
    
    // 更新最后刷新时间
    state = {
      ...state,
      projectId: DateTime.now(),
    };
  }

  /// 手动刷新所有数据
  void refreshAll() {
    for (final projectId in _refreshTimers.keys) {
      _refreshProjectData(projectId);
    }
  }

  // Riverpod会自动处理资源清理，不需要手动dispose
  void _cancelAllTimers() {
    for (final timer in _refreshTimers.values) {
      timer.cancel();
    }
    _refreshTimers.clear();
  }
}
