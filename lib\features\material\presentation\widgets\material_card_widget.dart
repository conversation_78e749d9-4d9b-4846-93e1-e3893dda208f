import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/material.dart' as domain;
import '../../domain/entities/product_specification.dart';
import '../providers/material_review_provider.dart';
import '../providers/specification_provider.dart';
import 'write_review_dialog_widget.dart';

/// 房车改装材料卡片 - 专为VanHub设计
class MaterialCardWidget extends ConsumerWidget {
  final domain.Material material;
  final VoidCallback? onTap;
  final VoidCallback? onAddToBom;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;

  const MaterialCardWidget({
    super.key,
    required this.material,
    this.onTap,
    this.onAddToBom,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 3,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 材料图片区域
            Container(
              height: 120,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.teal.shade300,
                    Colors.teal.shade500,
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // 背景图片或默认图案
                  if (material.imageUrl != null)
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      child: Image.network(
                        material.imageUrl!,
                        width: double.infinity,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildDefaultCover(),
                      ),
                    )
                  else
                    _buildDefaultCover(),
                  
                  // 渐变遮罩
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                  ),
                  
                  // 分类标签
                  Positioned(
                    top: 8,
                    right: 8,
                    child: _buildCategoryBadge(),
                  ),
                  
                  // 使用次数标识
                  if (material.usageCount > 0)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.repeat, color: Colors.white, size: 12),
                            const SizedBox(width: 2),
                            Text(
                              '${material.usageCount}次',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  // 品牌信息
                  if (material.brand != null)
                    Positioned(
                      bottom: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          material.brand!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            // 材料信息区域
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 材料名称
                    Text(
                      material.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // 型号信息
                    if (material.model != null) ...[
                      Text(
                        '型号: ${material.model}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                    ],
                    
                    // 规格信息
                    _buildSpecificationInfo(context, ref),
                    
                    const Spacer(),

                    // 评价信息
                    _buildReviewInfo(context, ref),

                    const SizedBox(height: 8),

                    // 价格和操作区域
                    Row(
                      children: [
                        // 价格信息
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '¥${_formatPrice(material.price)}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.teal,
                                ),
                              ),
                              if (material.purchaseDate != null)
                                Text(
                                  _formatDate(material.purchaseDate!),
                                  style: TextStyle(
                                    fontSize: 9,
                                    color: Colors.grey[500],
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // 编辑按钮
                        if (showActions && onEdit != null)
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              onPressed: onEdit,
                              icon: Icon(
                                Icons.edit,
                                color: Colors.blue,
                                size: 20,
                              ),
                              tooltip: '编辑材料',
                              padding: const EdgeInsets.all(8),
                              constraints: const BoxConstraints(
                                minWidth: 36,
                                minHeight: 36,
                              ),
                            ),
                          ),

                        // 删除按钮
                        if (showActions && onDelete != null)
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              onPressed: onDelete,
                              icon: Icon(
                                Icons.delete,
                                color: Colors.red,
                                size: 20,
                              ),
                              tooltip: '删除材料',
                              padding: const EdgeInsets.all(8),
                              constraints: const BoxConstraints(
                                minWidth: 36,
                                minHeight: 36,
                              ),
                            ),
                          ),

                        // 添加到BOM按钮
                        if (showActions && onAddToBom != null)
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.teal.shade50,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              onPressed: onAddToBom,
                              icon: Icon(
                                Icons.add_shopping_cart,
                                color: Colors.teal,
                                size: 18,
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                              padding: const EdgeInsets.all(4),
                            ),
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // 供应商信息
                    if (material.supplier != null)
                      Row(
                        children: [
                          Icon(
                            Icons.store,
                            size: 10,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 2),
                          Expanded(
                            child: Text(
                              material.supplier!,
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultCover() {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.teal.shade300,
            Colors.teal.shade500,
          ],
        ),
      ),
      child: Stack(
        children: [
          // 背景图案
          Positioned(
            right: -20,
            top: -10,
            child: Icon(
              Icons.inventory_2,
              size: 80,
              color: Colors.white.withValues(alpha: 0.2),
            ),
          ),
          // 材料图标
          Center(
            child: Icon(
              _getCategoryIcon(material.category),
              size: 40,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryBadge() {
    Color backgroundColor = _getCategoryColor(material.category);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        material.category,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 9,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '电力系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '内饰改装':
        return Icons.chair;
      case '外观改装':
        return Icons.brush;
      case '储物方案':
        return Icons.storage;
      case '床铺设计':
        return Icons.bed;
      case '厨房改装':
        return Icons.kitchen;
      case '卫浴改装':
        return Icons.bathtub;
      case '车顶改装':
        return Icons.roofing;
      case '底盘改装':
        return Icons.build;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case '电力系统':
        return Colors.amber;
      case '水路系统':
        return Colors.blue;
      case '内饰改装':
        return Colors.brown;
      case '外观改装':
        return Colors.purple;
      case '储物方案':
        return Colors.green;
      case '床铺设计':
        return Colors.indigo;
      case '厨房改装':
        return Colors.orange;
      case '卫浴改装':
        return Colors.cyan;
      case '车顶改装':
        return Colors.red;
      case '底盘改装':
        return Colors.grey;
      default:
        return Colors.teal;
    }
  }

  String _formatPrice(double price) {
    if (price >= 10000) {
      return '${(price / 10000).toStringAsFixed(1)}万';
    }
    final formatter = NumberFormat('#,##0', 'zh_CN');
    return formatter.format(price);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 30) {
      return DateFormat('MM-dd').format(date);
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else {
      return '今天';
    }
  }

  /// 构建评价信息组件
  Widget _buildReviewInfo(BuildContext context, WidgetRef ref) {
    final reviewSummaryAsync = ref.watch(materialReviewSummaryProvider(material.id));

    return reviewSummaryAsync.when(
      data: (summary) {
        if (summary.totalReviews == 0) {
          return _buildNoReviewsInfo(context);
        }
        return _buildReviewSummary(context, summary);
      },
      loading: () => const SizedBox(
        height: 16,
        child: Center(
          child: SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(strokeWidth: 1),
          ),
        ),
      ),
      error: (error, stack) => _buildNoReviewsInfo(context),
    );
  }

  /// 构建无评价状态
  Widget _buildNoReviewsInfo(BuildContext context) {
    return GestureDetector(
      onTap: () => _showWriteReviewDialog(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 12,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              '暂无评价',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '写评价',
              style: TextStyle(
                fontSize: 10,
                color: Colors.blue.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建评价摘要
  Widget _buildReviewSummary(BuildContext context, summary) {
    return GestureDetector(
      onTap: () => _showReviewDetails(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.amber.shade50,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.amber.shade200),
        ),
        child: Row(
          children: [
            // 星级评分
            Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(5, (index) {
                final starValue = index + 1;
                return Icon(
                  starValue <= summary.averageRating
                      ? Icons.star
                      : starValue - 0.5 <= summary.averageRating
                          ? Icons.star_half
                          : Icons.star_border,
                  size: 12,
                  color: Colors.amber.shade700,
                );
              }),
            ),

            const SizedBox(width: 4),

            // 评分数值
            Text(
              summary.averageRating.toStringAsFixed(1),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.amber.shade800,
              ),
            ),

            const SizedBox(width: 4),

            // 评价数量
            Text(
              '(${summary.totalReviews})',
              style: TextStyle(
                fontSize: 9,
                color: Colors.grey.shade600,
              ),
            ),

            const Spacer(),

            // 推荐度
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: _getRecommendationColor(summary.recommendationScore),
                borderRadius: BorderRadius.circular(3),
              ),
              child: Text(
                '推荐${(summary.recommendationScore * 20).toInt()}%',
                style: const TextStyle(
                  fontSize: 8,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取推荐度颜色
  Color _getRecommendationColor(double score) {
    if (score >= 4.0) return Colors.green;
    if (score >= 3.0) return Colors.orange;
    return Colors.red;
  }

  /// 显示写评价对话框
  void _showWriteReviewDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => WriteReviewDialogWidget(
        materialId: material.id,
        materialName: material.name,
      ),
    );
  }

  /// 显示评价详情
  void _showReviewDetails(BuildContext context) {
    // TODO: 导航到评价详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('查看评价详情功能')),
    );
  }

  /// 构建规格信息
  Widget _buildSpecificationInfo(BuildContext context, WidgetRef ref) {
    // 如果有传统的规格文本，优先显示
    if (material.specifications != null && material.specifications!.isNotEmpty) {
      return Column(
        children: [
          Text(
            material.specifications!,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[500],
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 6),
        ],
      );
    }

    // 如果有规格ID，显示结构化规格信息
    if (material.specificationId != null) {
      return _buildStructuredSpecification(context, ref);
    }

    // 如果有内嵌的规格对象，直接显示
    if (material.productSpecification != null) {
      return _buildSpecificationSummary(material.productSpecification!);
    }

    return const SizedBox.shrink();
  }

  /// 构建结构化规格信息（异步加载）
  Widget _buildStructuredSpecification(BuildContext context, WidgetRef ref) {
    final specificationAsync = ref.watch(specificationByMaterialIdProvider(material.id));

    return specificationAsync.when(
      data: (specification) {
        if (specification == null) return const SizedBox.shrink();
        return _buildSpecificationSummary(specification);
      },
      loading: () => Container(
        height: 20,
        child: Row(
          children: [
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(width: 6),
            Text(
              '加载规格中...',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  /// 构建规格摘要信息
  Widget _buildSpecificationSummary(ProductSpecification specification) {
    final keySpecs = _extractKeySpecifications(specification);

    if (keySpecs.isEmpty) return const SizedBox.shrink();

    return Column(
      children: [
        Wrap(
          spacing: 6,
          runSpacing: 2,
          children: keySpecs.entries.map((spec) => _buildSpecChip(spec)).toList(),
        ),
        const SizedBox(height: 6),
      ],
    );
  }

  /// 构建规格标签
  Widget _buildSpecChip(MapEntry<String, String> spec) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        '${spec.key}: ${spec.value}',
        style: const TextStyle(
          fontSize: 9,
          fontWeight: FontWeight.w500,
          color: Colors.blue,
        ),
      ),
    );
  }

  /// 提取关键规格信息
  Map<String, String> _extractKeySpecifications(ProductSpecification specification) {
    final keySpecs = <String, String>{};

    // 根据分类提取不同的关键规格
    switch (specification.category.toUpperCase()) {
      case 'ELECTRICAL':
        final electrical = specification.technicalParams.electrical;
        if (electrical != null) {
          if (electrical.ratedVoltage != null) {
            keySpecs['电压'] = '${electrical.ratedVoltage}V';
          }
          if (electrical.capacity != null) {
            keySpecs['容量'] = '${electrical.capacity}${electrical.capacityUnit ?? 'Ah'}';
          }
          if (electrical.ratedPower != null) {
            keySpecs['功率'] = '${electrical.ratedPower}W';
          }
        }
        break;

      case 'PLUMBING':
        final fluid = specification.technicalParams.fluid;
        if (fluid != null) {
          if (fluid.flowRate != null) {
            keySpecs['流量'] = '${fluid.flowRate}L/min';
          }
          if (fluid.head != null) {
            keySpecs['扬程'] = '${fluid.head}m';
          }
        }
        break;

      case 'STORAGE':
        final mechanical = specification.technicalParams.mechanical;
        if (mechanical != null) {
          if (mechanical.maxLoad != null) {
            keySpecs['承重'] = '${mechanical.maxLoad}kg';
          }
        }
        break;

      default:
        // 对于其他分类，显示基础物理属性
        final dimensions = specification.physicalProps.dimensions;
        keySpecs['尺寸'] = '${dimensions.length}×${dimensions.width}×${dimensions.height}${dimensions.unit}';
        keySpecs['重量'] = '${specification.physicalProps.weight}kg';
        break;
    }

    // 限制显示的规格数量
    if (keySpecs.length > 3) {
      final entries = keySpecs.entries.take(3).toList();
      return Map.fromEntries(entries);
    }

    return keySpecs;
  }
}