import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:timeline_tile/timeline_tile.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
// import '../providers/analytics_provider.dart';
// import '../../domain/entities/analytics_data.dart';

/// 进度时间轴节点类型
enum ProgressNodeType {
  milestone,
  task,
  checkpoint,
  warning,
}

/// 进度时间轴节点
class ProgressTimelineNode {
  final String id;
  final String title;
  final String? description;
  final DateTime date;
  final ProgressNodeType type;
  final double progress;
  final bool isCompleted;
  final bool isCurrent;
  final String? systemName;
  final Color? color;
  final IconData? icon;
  final List<String>? tags;
  final double? cost;
  final int? duration;

  const ProgressTimelineNode({
    required this.id,
    required this.title,
    this.description,
    required this.date,
    required this.type,
    required this.progress,
    required this.isCompleted,
    required this.isCurrent,
    this.systemName,
    this.color,
    this.icon,
    this.tags,
    this.cost,
    this.duration,
  });
}

/// 进度时间轴组件
class ProgressTimelineWidget extends ConsumerStatefulWidget {
  final String projectId;
  final double? height;
  final bool showProgress;
  final bool showCosts;
  final bool showDuration;
  final Function(ProgressTimelineNode)? onNodeTap;

  const ProgressTimelineWidget({
    super.key,
    required this.projectId,
    this.height = 600,
    this.showProgress = true,
    this.showCosts = true,
    this.showDuration = true,
    this.onNodeTap,
  });

  @override
  ConsumerState<ProgressTimelineWidget> createState() => _ProgressTimelineWidgetState();
}

class _ProgressTimelineWidgetState extends ConsumerState<ProgressTimelineWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  List<ProgressTimelineNode> _nodes = [];
  String _selectedFilter = '全部';

  final List<String> _filterOptions = ['全部', '里程碑', '任务', '检查点', '警告'];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadTimelineData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 加载时间轴数据
  Future<void> _loadTimelineData() async {
    // 生成模拟的进度时间轴数据
    final now = DateTime.now();
    _nodes = [
      ProgressTimelineNode(
        id: '1',
        title: '项目启动',
        description: '项目正式开始，完成初期规划和准备工作',
        date: now.subtract(const Duration(days: 30)),
        type: ProgressNodeType.milestone,
        progress: 1.0,
        isCompleted: true,
        isCurrent: false,
        systemName: '项目管理',
        color: Colors.green,
        icon: Icons.flag,
        tags: ['启动', '规划'],
        cost: 0,
        duration: 480, // 8小时
      ),
      ProgressTimelineNode(
        id: '2',
        title: '电气系统设计',
        description: '完成电气系统的详细设计和材料清单',
        date: now.subtract(const Duration(days: 25)),
        type: ProgressNodeType.task,
        progress: 1.0,
        isCompleted: true,
        isCurrent: false,
        systemName: '电气系统',
        color: Colors.blue,
        icon: Icons.electrical_services,
        tags: ['设计', '电气'],
        cost: 1200,
        duration: 360, // 6小时
      ),
      ProgressTimelineNode(
        id: '3',
        title: '材料采购完成',
        description: '所有主要材料采购完成，质量检查通过',
        date: now.subtract(const Duration(days: 20)),
        type: ProgressNodeType.checkpoint,
        progress: 1.0,
        isCompleted: true,
        isCurrent: false,
        systemName: '采购管理',
        color: Colors.orange,
        icon: Icons.shopping_cart,
        tags: ['采购', '检查'],
        cost: 15000,
        duration: 240, // 4小时
      ),
      ProgressTimelineNode(
        id: '4',
        title: '电气系统安装',
        description: '正在进行电气系统的安装工作',
        date: now.subtract(const Duration(days: 15)),
        type: ProgressNodeType.task,
        progress: 0.75,
        isCompleted: false,
        isCurrent: true,
        systemName: '电气系统',
        color: Colors.blue,
        icon: Icons.build,
        tags: ['安装', '进行中'],
        cost: 800,
        duration: 720, // 12小时
      ),
      ProgressTimelineNode(
        id: '5',
        title: '电压异常警告',
        description: '检测到电压不稳定，需要检查线路连接',
        date: now.subtract(const Duration(days: 10)),
        type: ProgressNodeType.warning,
        progress: 0.0,
        isCompleted: false,
        isCurrent: false,
        systemName: '电气系统',
        color: Colors.red,
        icon: Icons.warning,
        tags: ['警告', '电压'],
        cost: 0,
        duration: 120, // 2小时
      ),
      ProgressTimelineNode(
        id: '6',
        title: '水路系统安装',
        description: '计划开始水路系统的安装工作',
        date: now.add(const Duration(days: 5)),
        type: ProgressNodeType.task,
        progress: 0.0,
        isCompleted: false,
        isCurrent: false,
        systemName: '水路系统',
        color: Colors.cyan,
        icon: Icons.water_drop,
        tags: ['计划', '水路'],
        cost: 2500,
        duration: 480, // 8小时
      ),
      ProgressTimelineNode(
        id: '7',
        title: '项目完成',
        description: '所有系统安装完成，项目验收',
        date: now.add(const Duration(days: 30)),
        type: ProgressNodeType.milestone,
        progress: 0.0,
        isCompleted: false,
        isCurrent: false,
        systemName: '项目管理',
        color: Colors.purple,
        icon: Icons.celebration,
        tags: ['完成', '验收'],
        cost: 0,
        duration: 240, // 4小时
      ),
    ];

    _animationController.forward();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildFilterBar(),
          Expanded(
            child: _buildTimeline(),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.1, end: 0);
  }

  /// 构建头部
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      child: Row(
        children: [
          Icon(
            Icons.timeline,
            color: VanHubBrandColors.primary,
            size: 28,
          ),
          const SizedBox(width: VanHubSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '项目进度时间轴',
                  style: VanHubTypography.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '跟踪项目各个阶段的进度和关键节点',
                  style: VanHubTypography.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          _buildProgressSummary(),
        ],
      ),
    );
  }

  /// 构建进度摘要
  Widget _buildProgressSummary() {
    final completedNodes = _nodes.where((node) => node.isCompleted).length;
    final totalNodes = _nodes.length;
    final overallProgress = totalNodes > 0 ? completedNodes / totalNodes : 0.0;

    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: VanHubBrandColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            '${(overallProgress * 100).toInt()}%',
            style: VanHubTypography.headlineMedium.copyWith(
              color: VanHubBrandColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '$completedNodes/$totalNodes',
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建筛选栏
  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: VanHubSpacing.md,
        vertical: VanHubSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: _filterOptions.map((filter) {
            final isSelected = filter == _selectedFilter;
            return Padding(
              padding: const EdgeInsets.only(right: VanHubSpacing.sm),
              child: FilterChip(
                label: Text(filter),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedFilter = filter;
                  });
                },
                selectedColor: VanHubBrandColors.primary.withValues(alpha: 0.2),
                checkmarkColor: VanHubBrandColors.primary,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建时间轴
  Widget _buildTimeline() {
    final filteredNodes = _getFilteredNodes();
    
    if (filteredNodes.isEmpty) {
      return const Center(
        child: Text('暂无数据'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      itemCount: filteredNodes.length,
      itemBuilder: (context, index) {
        final node = filteredNodes[index];
        final isFirst = index == 0;
        final isLast = index == filteredNodes.length - 1;
        
        return _buildTimelineNode(node, isFirst, isLast, index);
      },
    );
  }

  /// 构建时间轴节点
  Widget _buildTimelineNode(
    ProgressTimelineNode node,
    bool isFirst,
    bool isLast,
    int index,
  ) {
    return TimelineTile(
      alignment: TimelineAlign.manual,
      lineXY: 0.1,
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        width: 40,
        color: node.color ?? VanHubBrandColors.primary,
        iconStyle: IconStyle(
          iconData: node.icon ?? Icons.circle,
          color: Colors.white,
        ),
      ),
      beforeLineStyle: LineStyle(
        color: node.isCompleted 
            ? (node.color ?? VanHubBrandColors.primary)
            : Colors.grey.shade300,
        thickness: 3,
      ),
      endChild: _buildNodeCard(node, index),
    ).animate(delay: (index * 100).ms)
        .fadeIn(duration: 300.ms)
        .slideX(begin: 0.3, end: 0);
  }

  /// 构建节点卡片
  Widget _buildNodeCard(ProgressTimelineNode node, int index) {
    return Container(
      margin: const EdgeInsets.all(VanHubSpacing.sm),
      child: Card(
        elevation: node.isCurrent ? 4 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: node.isCurrent 
              ? BorderSide(color: VanHubBrandColors.primary, width: 2)
              : BorderSide.none,
        ),
        child: InkWell(
          onTap: () => widget.onNodeTap?.call(node),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(VanHubSpacing.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNodeHeader(node),
                if (node.description != null) ...[
                  const SizedBox(height: VanHubSpacing.sm),
                  Text(
                    node.description!,
                    style: VanHubTypography.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                const SizedBox(height: VanHubSpacing.sm),
                _buildNodeProgress(node),
                const SizedBox(height: VanHubSpacing.sm),
                _buildNodeMetadata(node),
                if (node.tags != null && node.tags!.isNotEmpty) ...[
                  const SizedBox(height: VanHubSpacing.sm),
                  _buildNodeTags(node),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建节点头部
  Widget _buildNodeHeader(ProgressTimelineNode node) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                node.title,
                style: VanHubTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: node.isCurrent ? VanHubBrandColors.primary : null,
                ),
              ),
              if (node.systemName != null) ...[
                const SizedBox(height: 2),
                Text(
                  node.systemName!,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: node.color ?? VanHubBrandColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _formatDate(node.date),
              style: VanHubTypography.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
            if (node.isCompleted)
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 20,
              )
            else if (node.isCurrent)
              Icon(
                Icons.play_circle,
                color: VanHubBrandColors.primary,
                size: 20,
              ),
          ],
        ),
      ],
    );
  }

  /// 构建节点进度
  Widget _buildNodeProgress(ProgressTimelineNode node) {
    if (!widget.showProgress || node.progress == 0) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: node.progress,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(
                  node.color ?? VanHubBrandColors.primary,
                ),
              ),
            ),
            const SizedBox(width: VanHubSpacing.sm),
            Text(
              '${(node.progress * 100).toInt()}%',
              style: VanHubTypography.bodySmall.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建节点元数据
  Widget _buildNodeMetadata(ProgressTimelineNode node) {
    final metadata = <Widget>[];

    if (widget.showCosts && node.cost != null && node.cost! > 0) {
      metadata.add(_buildMetadataItem(
        Icons.attach_money,
        '¥${node.cost!.toStringAsFixed(0)}',
        Colors.green,
      ));
    }

    if (widget.showDuration && node.duration != null && node.duration! > 0) {
      metadata.add(_buildMetadataItem(
        Icons.schedule,
        _formatDuration(node.duration!),
        Colors.blue,
      ));
    }

    if (metadata.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      children: metadata
          .expand((widget) => [widget, const SizedBox(width: VanHubSpacing.md)])
          .take(metadata.length * 2 - 1)
          .toList(),
    );
  }

  /// 构建元数据项
  Widget _buildMetadataItem(IconData icon, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          text,
          style: VanHubTypography.bodySmall.copyWith(
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建节点标签
  Widget _buildNodeTags(ProgressTimelineNode node) {
    return Wrap(
      spacing: VanHubSpacing.xs,
      runSpacing: VanHubSpacing.xs,
      children: node.tags!.map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: VanHubSpacing.sm,
            vertical: 2,
          ),
          decoration: BoxDecoration(
            color: (node.color ?? VanHubBrandColors.primary).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tag,
            style: VanHubTypography.bodySmall.copyWith(
              color: node.color ?? VanHubBrandColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 获取筛选后的节点
  List<ProgressTimelineNode> _getFilteredNodes() {
    if (_selectedFilter == '全部') {
      return _nodes;
    }

    return _nodes.where((node) {
      switch (_selectedFilter) {
        case '里程碑':
          return node.type == ProgressNodeType.milestone;
        case '任务':
          return node.type == ProgressNodeType.task;
        case '检查点':
          return node.type == ProgressNodeType.checkpoint;
        case '警告':
          return node.type == ProgressNodeType.warning;
        default:
          return true;
      }
    }).toList();
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '明天';
    } else if (difference.inDays == -1) {
      return '昨天';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天后';
    } else {
      return '${-difference.inDays}天前';
    }
  }

  /// 格式化持续时间
  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes分钟';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '$hours小时${remainingMinutes > 0 ? '$remainingMinutes分钟' : ''}';
    }
  }
}
