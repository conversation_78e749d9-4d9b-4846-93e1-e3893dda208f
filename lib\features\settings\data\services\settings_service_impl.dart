import 'dart:convert';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/settings_service.dart';
import '../../domain/entities/app_settings.dart';
import '../../domain/repositories/settings_repository.dart';

/// 设置服务实现
class SettingsServiceImpl implements SettingsService {
  final SettingsRepository _repository;

  const SettingsServiceImpl({
    required SettingsRepository repository,
  }) : _repository = repository;

  @override
  Future<Either<Failure, AppSettings>> getUserSettings(String userId) async {
    try {
      // 优先从本地获取
      final localResult = await _repository.getLocalSettings(userId);
      
      return localResult.fold(
        (failure) async {
          // 本地获取失败，尝试从云端获取
          final cloudResult = await _repository.getCloudSettings(userId);
          return cloudResult.fold(
            (cloudFailure) async {
              // 云端也失败，创建默认设置
              final defaultSettings = AppSettings.defaultSettings(userId);
              await _repository.saveLocalSettings(defaultSettings);
              return Right(defaultSettings);
            },
            (cloudSettings) async {
              // 云端获取成功，保存到本地
              await _repository.saveLocalSettings(cloudSettings);
              return Right(cloudSettings);
            },
          );
        },
        (localSettings) async {
          // 本地获取成功，检查是否需要同步
          _checkAndSyncInBackground(userId, localSettings);
          return Right(localSettings);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> updateUserSettings(AppSettings settings) async {
    try {
      // 验证设置
      final validationErrors = settings.validate();
      if (validationErrors.isNotEmpty) {
        return Left(ValidationFailure(message: '设置验证失败: ${validationErrors.join(', ')}'));
      }

      final updatedSettings = settings.copyWith(
        updatedAt: DateTime.now(),
        version: settings.version + 1,
      );

      // 保存到本地
      await _repository.saveLocalSettings(updatedSettings);

      // 保存历史记录
      await _repository.saveSettingsHistory(updatedSettings);

      // 异步同步到云端
      _syncToCloudInBackground(updatedSettings);

      return Right(updatedSettings);
    } catch (e) {
      return Left(UnknownFailure(message: '更新用户设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> updateMultipleSettings(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final currentResult = await getUserSettings(userId);
      
      return await currentResult.fold(
        (failure) async => Left(failure),
        (currentSettings) async {
          // 应用更新
          var updatedSettings = currentSettings;
          
          for (final entry in updates.entries) {
            updatedSettings = _applySingleUpdate(updatedSettings, entry.key, entry.value);
          }

          return await updateUserSettings(updatedSettings);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '批量更新设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> resetToDefaults(String userId) async {
    try {
      final defaultSettings = AppSettings.defaultSettings(userId);
      return await updateUserSettings(defaultSettings);
    } catch (e) {
      return Left(UnknownFailure(message: '重置设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, SettingsSyncResult>> syncToCloud(String userId) async {
    try {
      final localResult = await _repository.getLocalSettings(userId);
      
      return await localResult.fold(
        (failure) async => Left(failure),
        (localSettings) async {
          try {
            await _repository.saveCloudSettings(localSettings);
            
            final result = SettingsSyncResult(
              status: SyncStatus.success,
              message: '设置已同步到云端',
              settings: localSettings.updateSyncTime(),
              timestamp: DateTime.now(),
            );
            
            // 更新本地同步时间
            await _repository.saveLocalSettings(result.settings!);
            
            return Right(result);
          } catch (e) {
            return Right(SettingsSyncResult(
              status: SyncStatus.failed,
              message: '同步到云端失败: $e',
              timestamp: DateTime.now(),
            ));
          }
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '同步到云端失败: $e'));
    }
  }

  @override
  Future<Either<Failure, SettingsSyncResult>> syncFromCloud(String userId) async {
    try {
      final cloudResult = await _repository.getCloudSettings(userId);
      
      return await cloudResult.fold(
        (failure) async {
          return Right(SettingsSyncResult(
            status: SyncStatus.failed,
            message: '从云端获取设置失败',
            timestamp: DateTime.now(),
          ));
        },
        (cloudSettings) async {
          final localResult = await _repository.getLocalSettings(userId);
          
          return await localResult.fold(
            (localFailure) async {
              // 本地没有设置，直接使用云端设置
              await _repository.saveLocalSettings(cloudSettings);
              return Right(SettingsSyncResult(
                status: SyncStatus.success,
                message: '已从云端恢复设置',
                settings: cloudSettings,
                timestamp: DateTime.now(),
              ));
            },
            (localSettings) async {
              // 检查冲突
              if (cloudSettings.updatedAt.isAfter(localSettings.updatedAt)) {
                // 云端更新，使用云端设置
                await _repository.saveLocalSettings(cloudSettings);
                return Right(SettingsSyncResult(
                  status: SyncStatus.success,
                  message: '已从云端更新设置',
                  settings: cloudSettings,
                  timestamp: DateTime.now(),
                ));
              } else if (localSettings.updatedAt.isAfter(cloudSettings.updatedAt)) {
                // 本地更新，保持本地设置
                return Right(SettingsSyncResult(
                  status: SyncStatus.success,
                  message: '本地设置较新，无需更新',
                  settings: localSettings,
                  timestamp: DateTime.now(),
                ));
              } else {
                // 时间相同，检查版本
                if (cloudSettings.version > localSettings.version) {
                  await _repository.saveLocalSettings(cloudSettings);
                  return Right(SettingsSyncResult(
                    status: SyncStatus.success,
                    message: '已从云端更新设置',
                    settings: cloudSettings,
                    timestamp: DateTime.now(),
                  ));
                } else {
                  return Right(SettingsSyncResult(
                    status: SyncStatus.success,
                    message: '设置已是最新版本',
                    settings: localSettings,
                    timestamp: DateTime.now(),
                  ));
                }
              }
            },
          );
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '从云端同步失败: $e'));
    }
  }

  @override
  Future<Either<Failure, SettingsSyncResult>> forceSyncToCloud(
    String userId,
    AppSettings settings,
  ) async {
    try {
      final forcedSettings = settings.copyWith(
        updatedAt: DateTime.now(),
        version: settings.version + 1,
      );

      await _repository.saveCloudSettings(forcedSettings);
      await _repository.saveLocalSettings(forcedSettings.updateSyncTime());

      return Right(SettingsSyncResult(
        status: SyncStatus.success,
        message: '设置已强制同步到云端',
        settings: forcedSettings,
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      return Left(UnknownFailure(message: '强制同步失败: $e'));
    }
  }

  @override
  Future<Either<Failure, SyncStatus>> getSyncStatus(String userId) async {
    try {
      // TODO: 实现同步状态检查逻辑
      return const Right(SyncStatus.idle);
    } catch (e) {
      return Left(UnknownFailure(message: '获取同步状态失败: $e'));
    }
  }

  @override
  Stream<AppSettings> subscribeToSettings(String userId) {
    return _repository.subscribeToSettings(userId);
  }

  @override
  Stream<SyncStatus> subscribeToSyncStatus(String userId) {
    // TODO: 实现同步状态订阅
    return Stream.value(SyncStatus.idle);
  }

  @override
  Future<Either<Failure, SettingsBackup>> createBackup(
    String userId,
    String backupName,
  ) async {
    try {
      final settingsResult = await getUserSettings(userId);
      
      return await settingsResult.fold(
        (failure) async => Left(failure),
        (settings) async {
          return await _repository.createBackup(userId, backupName, settings);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '创建备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SettingsBackup>>> getBackups(String userId) async {
    try {
      return await _repository.getBackups(userId);
    } catch (e) {
      return Left(UnknownFailure(message: '获取备份列表失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> restoreFromBackup(
    String userId,
    String backupId,
  ) async {
    try {
      final backupResult = await _repository.getBackup(backupId);
      
      return await backupResult.fold(
        (failure) async => Left(failure),
        (backup) async {
          final restoredSettings = AppSettings.fromJson(backup.data).copyWith(
            id: 'settings_$userId',
            userId: userId,
            updatedAt: DateTime.now(),
            version: 1,
          );
          
          return await updateUserSettings(restoredSettings);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '从备份恢复失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteBackup(String backupId) async {
    try {
      return await _repository.deleteBackup(backupId);
    } catch (e) {
      return Left(UnknownFailure(message: '删除备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> exportSettings(String userId) async {
    try {
      final settingsResult = await getUserSettings(userId);
      
      return settingsResult.fold(
        (failure) => Left(failure),
        (settings) => Right(settings.exportSettings()),
      );
    } catch (e) {
      return Left(UnknownFailure(message: '导出设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> importSettings(
    String userId,
    Map<String, dynamic> settingsData,
  ) async {
    try {
      final importedSettings = AppSettings.importSettings(settingsData, userId);
      
      // 验证导入的设置
      final validationErrors = importedSettings.validate();
      if (validationErrors.isNotEmpty) {
        return Left(ValidationFailure(message: '导入设置验证失败: ${validationErrors.join(', ')}'));
      }
      
      return await updateUserSettings(importedSettings);
    } catch (e) {
      return Left(UnknownFailure(message: '导入设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<String>>> validateSettings(
    Map<String, dynamic> settingsData,
  ) async {
    try {
      final settings = AppSettings.fromJson(settingsData);
      final errors = settings.validate();
      return Right(errors);
    } catch (e) {
      return Right(['设置数据格式错误: $e']);
    }
  }

  @override
  Future<Either<Failure, List<AppSettings>>> getSettingsHistory(
    String userId,
    {int limit = 10}
  ) async {
    try {
      return await _repository.getSettingsHistory(userId, limit: limit);
    } catch (e) {
      return Left(UnknownFailure(message: '获取设置历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> cleanupExpiredBackups(String userId) async {
    try {
      return await _repository.cleanupExpiredBackups(userId, 30); // 保留30天
    } catch (e) {
      return Left(UnknownFailure(message: '清理过期备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getSettingsStats(String userId) async {
    try {
      final settingsResult = await getUserSettings(userId);
      
      return settingsResult.fold(
        (failure) => Left(failure),
        (settings) => Right(settings.getSummary()),
      );
    } catch (e) {
      return Left(UnknownFailure(message: '获取设置统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkSettingsIntegrity(String userId) async {
    try {
      final settingsResult = await getUserSettings(userId);
      
      return settingsResult.fold(
        (failure) => Right(false),
        (settings) {
          final errors = settings.validate();
          return Right(errors.isEmpty);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '检查设置完整性失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> repairSettings(String userId) async {
    try {
      final settingsResult = await getUserSettings(userId);
      
      return await settingsResult.fold(
        (failure) async {
          // 设置损坏，创建默认设置
          return await resetToDefaults(userId);
        },
        (settings) async {
          final errors = settings.validate();
          if (errors.isEmpty) {
            return Right(settings); // 设置正常
          }
          
          // 尝试修复设置
          var repairedSettings = settings;
          
          // 修复文字缩放比例
          if (settings.textScaleFactor < 0.5 || settings.textScaleFactor > 3.0) {
            repairedSettings = repairedSettings.copyWith(textScaleFactor: 1.0);
          }
          
          // 修复自动锁定时间
          if (settings.autoLockMinutes < 1 || settings.autoLockMinutes > 60) {
            repairedSettings = repairedSettings.copyWith(autoLockMinutes: 5);
          }
          
          // 修复其他设置...
          
          return await updateUserSettings(repairedSettings);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '修复设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> getDefaultSettings(String userId) async {
    try {
      return Right(AppSettings.defaultSettings(userId));
    } catch (e) {
      return Left(UnknownFailure(message: '获取默认设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> compareSettings(
    AppSettings settings1,
    AppSettings settings2,
  ) async {
    try {
      final json1 = settings1.toJson();
      final json2 = settings2.toJson();
      final differences = <String, dynamic>{};
      
      for (final key in json1.keys) {
        if (json1[key] != json2[key]) {
          differences[key] = {
            'old': json1[key],
            'new': json2[key],
          };
        }
      }
      
      return Right(differences);
    } catch (e) {
      return Left(UnknownFailure(message: '比较设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> applySettingsDiff(
    AppSettings baseSettings,
    Map<String, dynamic> diff,
  ) async {
    try {
      var updatedSettings = baseSettings;
      
      for (final entry in diff.entries) {
        final key = entry.key;
        final newValue = entry.value['new'];
        updatedSettings = _applySingleUpdate(updatedSettings, key, newValue);
      }
      
      return Right(updatedSettings);
    } catch (e) {
      return Left(UnknownFailure(message: '应用设置差异失败: $e'));
    }
  }

  // TODO: 实现其余方法
  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getSettingsTemplates() async {
    return const Right([]);
  }

  @override
  Future<Either<Failure, AppSettings>> applySettingsTemplate(String userId, String templateId) async {
    return Left(UnknownFailure(message: '功能暂未实现'));
  }

  @override
  Future<Either<Failure, void>> createSettingsTemplate(String userId, String templateName, AppSettings settings) async {
    return Left(UnknownFailure(message: '功能暂未实现'));
  }

  @override
  Future<Either<Failure, String>> shareSettingsConfig(String userId, List<String> settingKeys) async {
    return Left(UnknownFailure(message: '功能暂未实现'));
  }

  @override
  Future<Either<Failure, AppSettings>> importSharedConfig(String userId, String configCode) async {
    return Left(UnknownFailure(message: '功能暂未实现'));
  }

  /// 后台检查并同步
  void _checkAndSyncInBackground(String userId, AppSettings localSettings) {
    // TODO: 实现后台同步检查
  }

  /// 后台同步到云端
  void _syncToCloudInBackground(AppSettings settings) {
    // TODO: 实现后台同步
  }

  /// 应用单个设置更新
  AppSettings _applySingleUpdate(AppSettings settings, String key, dynamic value) {
    switch (key) {
      case 'themeMode':
        return settings.copyWith(themeMode: AppThemeMode.values[value]);
      case 'language':
        return settings.copyWith(language: AppLanguage.values[value]);
      case 'textScaleFactor':
        return settings.copyWith(textScaleFactor: value.toDouble());
      case 'enableAnimations':
        return settings.copyWith(enableAnimations: value as bool);
      case 'enablePushNotifications':
        return settings.copyWith(enablePushNotifications: value as bool);
      case 'enableCloudSync':
        return settings.copyWith(enableCloudSync: value as bool);
      case 'syncFrequency':
        return settings.copyWith(syncFrequency: SyncFrequency.values[value]);
      // 添加更多设置字段...
      default:
        return settings;
    }
  }
}
