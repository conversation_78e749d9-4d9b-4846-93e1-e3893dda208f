import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_theme.dart';

part 'theme_provider.g.dart';

/// 主题模式枚举
enum ThemeMode {
  light,    // 浅色模式
  dark,     // 深色模式
  system,   // 跟随系统
}

/// 主题模式扩展
extension ThemeModeX on ThemeMode {
  String get displayName {
    switch (this) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }
  
  String get description {
    switch (this) {
      case ThemeMode.light:
        return '始终使用浅色主题';
      case ThemeMode.dark:
        return '始终使用深色主题';
      case ThemeMode.system:
        return '根据系统设置自动切换';
    }
  }
  
  IconData get icon {
    switch (this) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}

/// 主题配置类
class ThemeConfig {
  final ThemeMode themeMode;
  final bool useSystemAccentColor;
  final Color? customAccentColor;
  final double textScaleFactor;
  final bool enableAnimations;

  const ThemeConfig({
    this.themeMode = ThemeMode.system,
    this.useSystemAccentColor = true,
    this.customAccentColor,
    this.textScaleFactor = 1.0,
    this.enableAnimations = true,
  });

  ThemeConfig copyWith({
    ThemeMode? themeMode,
    bool? useSystemAccentColor,
    Color? customAccentColor,
    double? textScaleFactor,
    bool? enableAnimations,
  }) {
    return ThemeConfig(
      themeMode: themeMode ?? this.themeMode,
      useSystemAccentColor: useSystemAccentColor ?? this.useSystemAccentColor,
      customAccentColor: customAccentColor ?? this.customAccentColor,
      textScaleFactor: textScaleFactor ?? this.textScaleFactor,
      enableAnimations: enableAnimations ?? this.enableAnimations,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.index,
      'useSystemAccentColor': useSystemAccentColor,
      'customAccentColor': customAccentColor?.value,
      'textScaleFactor': textScaleFactor,
      'enableAnimations': enableAnimations,
    };
  }

  factory ThemeConfig.fromJson(Map<String, dynamic> json) {
    return ThemeConfig(
      themeMode: ThemeMode.values[json['themeMode'] ?? 0],
      useSystemAccentColor: json['useSystemAccentColor'] ?? true,
      customAccentColor: json['customAccentColor'] != null 
          ? Color(json['customAccentColor'])
          : null,
      textScaleFactor: json['textScaleFactor']?.toDouble() ?? 1.0,
      enableAnimations: json['enableAnimations'] ?? true,
    );
  }
}

/// SharedPreferences Provider
@riverpod
Future<SharedPreferences> sharedPreferences(SharedPreferencesRef ref) async {
  return await SharedPreferences.getInstance();
}

/// 主题配置Provider
@riverpod
class ThemeController extends _$ThemeController {
  static const String _themeConfigKey = 'theme_config';

  @override
  Future<ThemeConfig> build() async {
    final prefs = await ref.watch(sharedPreferencesProvider.future);
    final configJson = prefs.getString(_themeConfigKey);
    
    if (configJson != null) {
      try {
        final Map<String, dynamic> json = 
            Map<String, dynamic>.from(Uri.splitQueryString(configJson));
        return ThemeConfig.fromJson(json);
      } catch (e) {
        // 如果解析失败，返回默认配置
        return const ThemeConfig();
      }
    }
    
    return const ThemeConfig();
  }

  /// 更新主题模式
  Future<void> updateThemeMode(ThemeMode themeMode) async {
    final currentConfig = await future;
    final newConfig = currentConfig.copyWith(themeMode: themeMode);
    await _saveConfig(newConfig);
    state = AsyncData(newConfig);
  }

  /// 切换主题模式
  Future<void> toggleThemeMode() async {
    final currentConfig = await future;
    ThemeMode newMode;
    
    switch (currentConfig.themeMode) {
      case ThemeMode.light:
        newMode = ThemeMode.dark;
        break;
      case ThemeMode.dark:
        newMode = ThemeMode.system;
        break;
      case ThemeMode.system:
        newMode = ThemeMode.light;
        break;
    }
    
    await updateThemeMode(newMode);
  }

  /// 更新自定义主色调
  Future<void> updateCustomAccentColor(Color? color) async {
    final currentConfig = await future;
    final newConfig = currentConfig.copyWith(
      customAccentColor: color,
      useSystemAccentColor: color == null,
    );
    await _saveConfig(newConfig);
    state = AsyncData(newConfig);
  }

  /// 更新文字缩放比例
  Future<void> updateTextScaleFactor(double factor) async {
    final currentConfig = await future;
    final newConfig = currentConfig.copyWith(textScaleFactor: factor);
    await _saveConfig(newConfig);
    state = AsyncData(newConfig);
  }

  /// 切换动画开关
  Future<void> toggleAnimations() async {
    final currentConfig = await future;
    final newConfig = currentConfig.copyWith(
      enableAnimations: !currentConfig.enableAnimations,
    );
    await _saveConfig(newConfig);
    state = AsyncData(newConfig);
  }

  /// 重置为默认配置
  Future<void> resetToDefault() async {
    const defaultConfig = ThemeConfig();
    await _saveConfig(defaultConfig);
    state = const AsyncData(defaultConfig);
  }

  /// 保存配置到本地存储
  Future<void> _saveConfig(ThemeConfig config) async {
    final prefs = await ref.read(sharedPreferencesProvider.future);
    final configJson = Uri(queryParameters: 
        config.toJson().map((key, value) => MapEntry(key, value.toString()))
    ).query;
    await prefs.setString(_themeConfigKey, configJson);
  }
}

/// 当前主题数据Provider
@riverpod
ThemeData currentTheme(CurrentThemeRef ref) {
  final themeConfigAsync = ref.watch(themeControllerProvider);

  return themeConfigAsync.when(
    data: (config) {
      switch (config.themeMode) {
        case ThemeMode.light:
          return AppTheme.lightTheme;
        case ThemeMode.dark:
          return AppTheme.darkTheme;
        case ThemeMode.system:
          return AppTheme.lightTheme; // 简化处理，默认使用浅色主题
      }
    },
    loading: () => AppTheme.lightTheme,
    error: (_, __) => AppTheme.lightTheme,
  );
}

/// 当前是否为深色模式Provider
@riverpod
bool isDarkMode(IsDarkModeRef ref) {
  final themeConfigAsync = ref.watch(themeControllerProvider);
  
  return themeConfigAsync.when(
    data: (config) {
      switch (config.themeMode) {
        case ThemeMode.light:
          return false;
        case ThemeMode.dark:
          return true;
        case ThemeMode.system:
          return false; // 简化处理，默认为浅色
      }
    },
    loading: () => false,
    error: (_, __) => false,
  );
}

/// 主题切换动画Provider
@riverpod
class ThemeAnimationController extends _$ThemeAnimationController {
  @override
  bool build() {
    return false;
  }

  /// 开始主题切换动画
  void startAnimation() {
    state = true;
  }

  /// 结束主题切换动画
  void endAnimation() {
    state = false;
  }
}

/// 主题预设颜色
class ThemePresets {
  static const List<Color> accentColors = [
    Color(0xFF2196F3), // 蓝色（默认）
    Color(0xFF4CAF50), // 绿色
    Color(0xFFFF9800), // 橙色
    Color(0xFFF44336), // 红色
    Color(0xFF9C27B0), // 紫色
    Color(0xFF00BCD4), // 青色
    Color(0xFFFFEB3B), // 黄色
    Color(0xFF795548), // 棕色
    Color(0xFF607D8B), // 蓝灰色
    Color(0xFFE91E63), // 粉色
  ];

  static const List<String> accentColorNames = [
    '蓝色',
    '绿色',
    '橙色',
    '红色',
    '紫色',
    '青色',
    '黄色',
    '棕色',
    '蓝灰色',
    '粉色',
  ];

  static String getColorName(Color color) {
    final index = accentColors.indexOf(color);
    return index >= 0 ? accentColorNames[index] : '自定义';
  }
}
