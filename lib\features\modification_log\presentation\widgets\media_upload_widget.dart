import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';

/// 媒体上传组件
/// 支持图片、视频上传，拖拽排序，预览功能
class MediaUploadWidget extends ConsumerStatefulWidget {
  final List<LogMedia> initialMedia;
  final Function(List<LogMedia>) onMediaChanged;
  final bool isEditable;
  final int maxMediaCount;
  final List<MediaType> allowedTypes;

  const MediaUploadWidget({
    super.key,
    this.initialMedia = const [],
    required this.onMediaChanged,
    this.isEditable = true,
    this.maxMediaCount = 10,
    this.allowedTypes = const [MediaType.image, MediaType.video],
  });

  @override
  ConsumerState<MediaUploadWidget> createState() => _MediaUploadWidgetState();
}

class _MediaUploadWidgetState extends ConsumerState<MediaUploadWidget> {
  final ImagePicker _imagePicker = ImagePicker();
  List<LogMedia> _mediaList = [];
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _mediaList = List.from(widget.initialMedia);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 12),
        _buildMediaGrid(),
        if (widget.isEditable && _mediaList.length < widget.maxMediaCount) ...[
          const SizedBox(height: 16),
          _buildUploadButtons(),
        ],
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.perm_media, size: 20),
        const SizedBox(width: 8),
        Text(
          '媒体文件',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (_mediaList.isNotEmpty)
          Text(
            '${_mediaList.length}/${widget.maxMediaCount}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
      ],
    );
  }

  Widget _buildMediaGrid() {
    if (_mediaList.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _mediaList.length,
      itemBuilder: (context, index) {
        return _buildDraggableMediaItem(_mediaList[index], index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[50],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              '暂无媒体文件',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaItem(LogMedia media, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: _buildMediaPreview(media),
          ),
        ),
        if (widget.isEditable) _buildMediaActions(index),
        _buildMediaTypeIndicator(media.type),
      ],
    );
  }

  Widget _buildDraggableMediaItem(LogMedia media, int index) {
    if (!widget.isEditable) {
      return _buildMediaItem(media, index);
    }

    return LongPressDraggable<int>(
      data: index,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue, width: 2),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: _buildMediaPreview(media),
          ),
        ),
      ),
      childWhenDragging: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
          color: Colors.grey[100],
        ),
        child: const Center(
          child: Icon(
            Icons.drag_indicator,
            color: Colors.grey,
            size: 32,
          ),
        ),
      ),
      child: DragTarget<int>(
        onAcceptWithDetails: (details) {
          _onReorderMedia(details.data, index);
        },
        builder: (context, candidateData, rejectedData) {
          return _buildMediaItem(media, index);
        },
      ),
    );
  }

  Widget _buildMediaPreview(LogMedia media) {
    switch (media.type) {
      case MediaType.image:
        if (media.url.startsWith('http')) {
          return CachedNetworkImage(
            imageUrl: media.url,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            placeholder: (context, url) => const Center(
              child: CircularProgressIndicator(),
            ),
            errorWidget: (context, url, error) => const Center(
              child: Icon(Icons.error, color: Colors.red),
            ),
          );
        } else {
          return Image.file(
            File(media.url),
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
        }
      case MediaType.video:
        return Container(
          color: Colors.black87,
          child: const Center(
            child: Icon(
              Icons.play_circle_outline,
              color: Colors.white,
              size: 32,
            ),
          ),
        );
      default:
        return Container(
          color: Colors.grey[200],
          child: const Center(
            child: Icon(Icons.insert_drive_file, size: 32),
          ),
        );
    }
  }

  Widget _buildMediaActions(int index) {
    return Positioned(
      top: 4,
      right: 4,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.drag_handle, color: Colors.white, size: 16),
              onPressed: null, // 只是视觉指示器
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
            ),
          ),
          const SizedBox(width: 4),
          // 删除按钮
          Container(
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white, size: 16),
              onPressed: () => _confirmRemoveMedia(index),
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minWidth: 24,
                minHeight: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaTypeIndicator(MediaType type) {
    IconData icon;
    Color color;
    
    switch (type) {
      case MediaType.image:
        icon = Icons.image;
        color = Colors.blue;
        break;
      case MediaType.video:
        icon = Icons.videocam;
        color = Colors.red;
        break;
      default:
        icon = Icons.insert_drive_file;
        color = Colors.grey;
    }

    return Positioned(
      bottom: 4,
      left: 4,
      child: Container(
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: color.withOpacity(0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 12,
        ),
      ),
    );
  }

  Widget _buildUploadButtons() {
    return Row(
      children: [
        if (widget.allowedTypes.contains(MediaType.image))
          Expanded(
            child: _buildUploadButton(
              icon: Icons.add_a_photo,
              label: '添加图片',
              onPressed: _isUploading ? null : () => _pickImage(),
            ),
          ),
        if (widget.allowedTypes.contains(MediaType.image) && 
            widget.allowedTypes.contains(MediaType.video))
          const SizedBox(width: 12),
        if (widget.allowedTypes.contains(MediaType.video))
          Expanded(
            child: _buildUploadButton(
              icon: Icons.videocam,
              label: '添加视频',
              onPressed: _isUploading ? null : () => _pickVideo(),
            ),
          ),
      ],
    );
  }

  Widget _buildUploadButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: _isUploading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(icon),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      setState(() => _isUploading = true);
      
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        await _addMedia(image.path, MediaType.image);
      }
    } catch (e) {
      _showError('选择图片失败: $e');
    } finally {
      setState(() => _isUploading = false);
    }
  }

  Future<void> _pickVideo() async {
    try {
      setState(() => _isUploading = true);
      
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        await _addMedia(video.path, MediaType.video);
      }
    } catch (e) {
      _showError('选择视频失败: $e');
    } finally {
      setState(() => _isUploading = false);
    }
  }

  Future<void> _addMedia(String filePath, MediaType type) async {
    final file = File(filePath);
    final fileName = file.path.split('/').last;
    
    final media = LogMedia(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      logId: '', // 将在保存日志时设置
      type: type,
      url: filePath, // 本地路径，上传后会更新为网络URL
      filename: fileName,
      caption: '',
      sortOrder: _mediaList.length,
      uploadedAt: DateTime.now(),
      uploadedBy: '', // 将在保存时设置
    );

    setState(() {
      _mediaList.add(media);
    });

    widget.onMediaChanged(_mediaList);
  }

  void _confirmRemoveMedia(int index) {
    final media = _mediaList[index];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('删除媒体文件'),
          content: Text('确定要删除 "${media.filename}" 吗？此操作无法撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _removeMedia(index);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  void _removeMedia(int index) {
    setState(() {
      _mediaList.removeAt(index);
      // 重新排序
      for (int i = 0; i < _mediaList.length; i++) {
        _mediaList[i] = _mediaList[i].copyWith(sortOrder: i);
      }
    });

    widget.onMediaChanged(_mediaList);

    // 显示删除成功的提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('媒体文件已删除'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _onReorderMedia(int oldIndex, int newIndex) {
    if (oldIndex == newIndex) return;

    setState(() {
      final LogMedia item = _mediaList.removeAt(oldIndex);
      _mediaList.insert(newIndex, item);

      // 重新设置所有项目的排序
      for (int i = 0; i < _mediaList.length; i++) {
        _mediaList[i] = _mediaList[i].copyWith(sortOrder: i);
      }
    });

    widget.onMediaChanged(_mediaList);

    // 显示重新排序成功的提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('媒体文件顺序已更新'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
