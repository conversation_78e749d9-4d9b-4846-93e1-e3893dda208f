import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/bom_item.dart' as domain;

/// BOM物料项卡片 - 专为VanHub设计
class BomItemCardWidget extends ConsumerWidget {
  final domain.BomItem bomItem;
  final VoidCallback? onTap;
  final Function(domain.BomItemStatus)? onStatusChange;
  final VoidCallback? onSaveToLibrary;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onDuplicate;
  final bool showActions;

  const BomItemCardWidget({
    super.key,
    required this.bomItem,
    this.onTap,
    this.onStatusChange,
    this.onSaveToLibrary,
    this.onEdit,
    this.onDelete,
    this.onDuplicate,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          debugPrint('🔍 [BomItemCard] 点击物料卡片: ${bomItem.materialName} (ID: ${bomItem.id})');
          onTap?.call();
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部信息行
              Row(
                children: [
                  // 状态指示器
                  _buildStatusIndicator(),
                  const SizedBox(width: 12),
                  
                  // 物料名称和分类
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          bomItem.materialName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (bomItem.category != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            bomItem.category!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // 数量信息
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.indigo.shade50,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.indigo.shade200),
                    ),
                    child: Text(
                      '×${bomItem.quantity}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo.shade700,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 规格和描述
              if (bomItem.specifications != null) ...[
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          bomItem.specifications!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],
              
              // 价格和时间信息
              Row(
                children: [
                  // 价格信息
                  if (bomItem.estimatedPrice != null) ...[
                    Icon(Icons.attach_money, size: 16, color: Colors.green[600]),
                    const SizedBox(width: 4),
                    Text(
                      '预估: ¥${_formatPrice(bomItem.estimatedPrice!)}',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.green[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  
                  // 实际价格
                  if (bomItem.actualPrice != null) ...[
                    Icon(Icons.receipt, size: 16, color: Colors.orange[600]),
                    const SizedBox(width: 4),
                    Text(
                      '实际: ¥${_formatPrice(bomItem.actualPrice!)}',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.orange[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                  ] else
                    const Spacer(),
                  
                  // 计划日期
                  if (bomItem.plannedDate != null) ...[
                    Icon(Icons.schedule, size: 14, color: Colors.grey[500]),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(bomItem.plannedDate!),
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 进度条（如果有子任务）
              if (bomItem.status != domain.BomItemStatus.pending) ...[
                LinearProgressIndicator(
                  value: _getProgressValue(bomItem.status),
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getStatusColor(bomItem.status),
                  ),
                  minHeight: 4,
                ),
                const SizedBox(height: 12),
              ],
              
              // 操作按钮区域
              if (showActions) ...[
                Row(
                  children: [
                    // 状态更新按钮
                    if (onStatusChange != null)
                      _buildStatusButton(),
                    
                    const Spacer(),
                    
                    // 保存到材料库按钮
                    if (onSaveToLibrary != null)
                      TextButton.icon(
                        onPressed: onSaveToLibrary,
                        icon: Icon(Icons.library_add, size: 16, color: Colors.teal[600]),
                        label: Text(
                          '保存到库',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.teal[600],
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    
                    const SizedBox(width: 8),
                    
                    // 更多操作按钮
                    PopupMenuButton<String>(
                      icon: Icon(Icons.more_vert, size: 18, color: Colors.grey[600]),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 16),
                              SizedBox(width: 8),
                              Text('编辑'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy, size: 16),
                              SizedBox(width: 8),
                              Text('复制'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 16, color: Colors.red),
                              SizedBox(width: 8),
                              Text('删除', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'duplicate':
                            onDuplicate?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                    ),
                  ],
                ),
              ],
              
              // 备注信息
              if (bomItem.notes != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.amber.shade200),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.note, size: 14, color: Colors.amber[700]),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          bomItem.notes!,
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.amber[800],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final color = _getStatusColor(bomItem.status);
    final icon = _getStatusIcon(bomItem.status);
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Widget _buildStatusButton() {
    final nextStatus = _getNextStatus(bomItem.status);
    if (nextStatus == null) return const SizedBox.shrink();
    
    return ElevatedButton.icon(
      onPressed: () => onStatusChange?.call(nextStatus),
      icon: Icon(_getStatusIcon(nextStatus), size: 14),
      label: Text(
        _getStatusDisplayName(nextStatus),
        style: const TextStyle(fontSize: 12),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: _getStatusColor(nextStatus),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  Color _getStatusColor(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return Colors.grey;
      case domain.BomItemStatus.ordered:
        return Colors.blue;
      case domain.BomItemStatus.received:
        return Colors.orange;
      case domain.BomItemStatus.installed:
        return Colors.green;
      case domain.BomItemStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return Icons.schedule;
      case domain.BomItemStatus.ordered:
        return Icons.shopping_cart;
      case domain.BomItemStatus.received:
        return Icons.inventory;
      case domain.BomItemStatus.installed:
        return Icons.check_circle;
      case domain.BomItemStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusDisplayName(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return '待采购';
      case domain.BomItemStatus.ordered:
        return '已下单';
      case domain.BomItemStatus.received:
        return '已收货';
      case domain.BomItemStatus.installed:
        return '已安装';
      case domain.BomItemStatus.cancelled:
        return '已取消';
    }
  }

  domain.BomItemStatus? _getNextStatus(domain.BomItemStatus currentStatus) {
    switch (currentStatus) {
      case domain.BomItemStatus.pending:
        return domain.BomItemStatus.ordered;
      case domain.BomItemStatus.ordered:
        return domain.BomItemStatus.received;
      case domain.BomItemStatus.received:
        return domain.BomItemStatus.installed;
      case domain.BomItemStatus.installed:
      case domain.BomItemStatus.cancelled:
        return null; // 已完成或已取消，无下一状态
    }
  }

  double _getProgressValue(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return 0.2;
      case domain.BomItemStatus.ordered:
        return 0.4;
      case domain.BomItemStatus.received:
        return 0.6;
      case domain.BomItemStatus.installed:
        return 1.0;
      case domain.BomItemStatus.cancelled:
        return 0.0;
    }
  }

  String _formatPrice(double price) {
    if (price >= 10000) {
      return '${(price / 10000).toStringAsFixed(1)}万';
    }
    final formatter = NumberFormat('#,##0', 'zh_CN');
    return formatter.format(price);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 0) {
      return '${(-difference.inDays)}天后';
    } else {
      return '今天';
    }
  }
}