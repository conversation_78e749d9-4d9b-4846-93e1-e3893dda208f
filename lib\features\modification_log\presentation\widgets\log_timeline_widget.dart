import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 时间轴事件类型
enum TimelineEventType {
  milestone,    // 里程碑
  log,         // 日志
  task,        // 任务
  note,        // 笔记
}

/// 时间轴事件
class TimelineEvent {
  final String id;
  final String title;
  final String? description;
  final TimelineEventType type;
  final DateTime createdAt;
  final bool isCompleted;
  final bool isCurrent;
  final String? logId;
  final Color? color;
  final IconData? icon;
  final Map<String, dynamic> metadata;

  const TimelineEvent({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.createdAt,
    this.isCompleted = false,
    this.isCurrent = false,
    this.logId,
    this.color,
    this.icon,
    this.metadata = const {},
  });

  /// 获取事件类型颜色
  Color get typeColor {
    if (color != null) return color!;
    
    switch (type) {
      case TimelineEventType.milestone:
        return Colors.purple;
      case TimelineEventType.log:
        return VanHubBrandColors.primary;
      case TimelineEventType.task:
        return Colors.blue;
      case TimelineEventType.note:
        return Colors.green;
    }
  }

  /// 获取事件类型图标
  IconData get typeIcon {
    if (icon != null) return icon!;
    
    switch (type) {
      case TimelineEventType.milestone:
        return Icons.flag;
      case TimelineEventType.log:
        return Icons.article;
      case TimelineEventType.task:
        return Icons.task_alt;
      case TimelineEventType.note:
        return Icons.note;
    }
  }

  /// 获取状态图标
  IconData get statusIcon {
    if (isCurrent) return Icons.play_circle_fill;
    if (isCompleted) return Icons.check_circle;
    return Icons.radio_button_unchecked;
  }

  /// 获取状态颜色
  Color get statusColor {
    if (isCurrent) return Colors.orange;
    if (isCompleted) return Colors.green;
    return Colors.grey;
  }
}

/// 日志时间轴组件
class LogTimelineWidget extends ConsumerStatefulWidget {
  final String projectId;
  final String? currentLogId;
  final Function(TimelineEvent)? onEventTap;
  final Function(TimelineEvent)? onEventLongPress;
  final bool showProgress;
  final bool allowInteraction;
  final int maxEvents;

  const LogTimelineWidget({
    super.key,
    required this.projectId,
    this.currentLogId,
    this.onEventTap,
    this.onEventLongPress,
    this.showProgress = true,
    this.allowInteraction = true,
    this.maxEvents = 10,
  });

  @override
  ConsumerState<LogTimelineWidget> createState() => _LogTimelineWidgetState();
}

class _LogTimelineWidgetState extends ConsumerState<LogTimelineWidget> {
  List<TimelineEvent> _events = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTimelineEvents();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_events.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          if (widget.showProgress) _buildProgressIndicator(),
          _buildTimeline(),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timeline,
              size: 32,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: VanHubSpacing.xs),
            Text(
              '暂无时间轴事件',
              style: VanHubTypography.bodyMedium.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: VanHubBrandColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.timeline,
              color: VanHubBrandColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: VanHubSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '项目时间轴',
                  style: VanHubTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${_events.length} 个事件',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          if (widget.allowInteraction)
            IconButton(
              onPressed: _showAllEvents,
              icon: const Icon(Icons.open_in_full),
              tooltip: '查看完整时间轴',
            ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final completedCount = _events.where((e) => e.isCompleted).length;
    final progress = _events.isEmpty ? 0.0 : completedCount / _events.length;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '完成进度',
                style: VanHubTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${(progress * 100).toInt()}% ($completedCount/${_events.length})',
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: VanHubSpacing.xs),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(VanHubBrandColors.primary),
          ),
          const SizedBox(height: VanHubSpacing.md),
        ],
      ),
    );
  }

  Widget _buildTimeline() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      itemCount: _events.length,
      itemBuilder: (context, index) {
        final event = _events[index];
        final isLast = index == _events.length - 1;
        return _buildTimelineItem(event, isLast, index);
      },
    );
  }

  Widget _buildTimelineItem(TimelineEvent event, bool isLast, int index) {
    final isCurrentLog = event.logId == widget.currentLogId;
    
    return InkWell(
      onTap: widget.allowInteraction ? () => _handleEventTap(event) : null,
      onLongPress: widget.allowInteraction ? () => _handleEventLongPress(event) : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.only(bottom: VanHubSpacing.sm),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 时间轴线和节点
            SizedBox(
              width: 40,
              child: Column(
                children: [
                  // 节点
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isCurrentLog 
                          ? VanHubBrandColors.primary 
                          : event.statusColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Icon(
                      isCurrentLog ? Icons.my_location : event.statusIcon,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                  
                  // 连接线
                  if (!isLast)
                    Container(
                      width: 2,
                      height: 40,
                      color: Colors.grey.shade300,
                    ),
                ],
              ),
            ),
            
            const SizedBox(width: VanHubSpacing.sm),
            
            // 事件内容
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(VanHubSpacing.sm),
                decoration: BoxDecoration(
                  color: isCurrentLog 
                      ? VanHubBrandColors.primary.withValues(alpha: 0.05)
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: isCurrentLog 
                      ? Border.all(color: VanHubBrandColors.primary.withValues(alpha: 0.3))
                      : Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: event.typeColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            event.typeIcon,
                            color: event.typeColor,
                            size: 14,
                          ),
                        ),
                        const SizedBox(width: VanHubSpacing.xs),
                        Expanded(
                          child: Text(
                            event.title,
                            style: VanHubTypography.titleSmall.copyWith(
                              fontWeight: isCurrentLog ? FontWeight.w600 : FontWeight.w500,
                              color: isCurrentLog ? VanHubBrandColors.primary : null,
                            ),
                          ),
                        ),
                        Text(
                          _formatEventTime(event.createdAt),
                          style: VanHubTypography.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                    
                    if (event.description != null) ...[
                      const SizedBox(height: VanHubSpacing.xs),
                      Text(
                        event.description!,
                        style: VanHubTypography.bodySmall.copyWith(
                          color: Colors.grey.shade700,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    
                    // 状态标签
                    if (event.isCompleted || event.isCurrent) ...[
                      const SizedBox(height: VanHubSpacing.xs),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: VanHubSpacing.xs,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: event.statusColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          event.isCurrent ? '进行中' : '已完成',
                          style: VanHubTypography.bodySmall.copyWith(
                            color: event.statusColor,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ).animate(delay: (index * 100).ms)
        .fadeIn(duration: 300.ms)
        .slideX(begin: 0.2, end: 0);
  }

  String _formatEventTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${dateTime.month}/${dateTime.day}';
    }
  }

  void _handleEventTap(TimelineEvent event) {
    widget.onEventTap?.call(event);
    
    if (event.logId != null) {
      // 导航到日志详情
      // TODO: 实现导航逻辑
    }
  }

  void _handleEventLongPress(TimelineEvent event) {
    widget.onEventLongPress?.call(event);
    
    // 显示上下文菜单
    _showEventContextMenu(event);
  }

  void _showEventContextMenu(TimelineEvent event) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(VanHubSpacing.md),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(event.typeIcon),
              title: Text(event.title),
              subtitle: Text(event.description ?? ''),
            ),
            const Divider(),
            if (event.logId != null)
              ListTile(
                leading: const Icon(Icons.open_in_new),
                title: const Text('查看日志'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: 导航到日志详情
                },
              ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('编辑事件'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 编辑事件
              },
            ),
            if (!event.isCompleted)
              ListTile(
                leading: const Icon(Icons.check),
                title: const Text('标记完成'),
                onTap: () {
                  Navigator.pop(context);
                  _markEventCompleted(event);
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showAllEvents() {
    // TODO: 导航到完整时间轴页面
  }

  void _markEventCompleted(TimelineEvent event) {
    setState(() {
      final index = _events.indexWhere((e) => e.id == event.id);
      if (index != -1) {
        _events[index] = TimelineEvent(
          id: event.id,
          title: event.title,
          description: event.description,
          type: event.type,
          createdAt: event.createdAt,
          isCompleted: true,
          isCurrent: false,
          logId: event.logId,
          color: event.color,
          icon: event.icon,
          metadata: event.metadata,
        );
      }
    });
  }

  Future<void> _loadTimelineEvents() async {
    // TODO: 从实际的Provider加载数据
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _events = _generateMockEvents();
      _isLoading = false;
    });
  }

  List<TimelineEvent> _generateMockEvents() {
    final now = DateTime.now();
    return [
      TimelineEvent(
        id: '1',
        title: '项目启动',
        description: '开始改装计划',
        type: TimelineEventType.milestone,
        createdAt: now.subtract(const Duration(days: 30)),
        isCompleted: true,
      ),
      TimelineEvent(
        id: '2',
        title: '材料采购',
        description: '购买基础材料',
        type: TimelineEventType.task,
        createdAt: now.subtract(const Duration(days: 25)),
        isCompleted: true,
      ),
      TimelineEvent(
        id: '3',
        title: '拆解工作',
        description: '拆解原有设备',
        type: TimelineEventType.log,
        createdAt: now.subtract(const Duration(days: 20)),
        isCompleted: true,
        logId: widget.currentLogId,
      ),
      TimelineEvent(
        id: '4',
        title: '安装新设备',
        description: '安装新的改装设备',
        type: TimelineEventType.log,
        createdAt: now.subtract(const Duration(days: 15)),
        isCurrent: true,
      ),
      TimelineEvent(
        id: '5',
        title: '测试验收',
        description: '测试改装效果',
        type: TimelineEventType.milestone,
        createdAt: now.subtract(const Duration(days: 5)),
        isCompleted: false,
      ),
    ];
  }
}
