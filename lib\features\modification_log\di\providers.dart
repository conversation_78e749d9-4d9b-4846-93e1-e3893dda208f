import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/api/supabase_config.dart';
import '../../../core/di/injection_container.dart';
import '../../../core/services/file_upload_service.dart';
import '../data/datasources/log_remote_datasource.dart';
import '../data/datasources/media_remote_datasource.dart';
import '../data/datasources/timeline_remote_datasource.dart';
import '../data/repositories/log_repository_impl.dart';
import '../data/repositories/media_repository_impl.dart';
import '../data/repositories/timeline_repository_impl.dart';
import '../domain/repositories/log_repository.dart';
import '../domain/repositories/media_repository.dart';
import '../domain/repositories/timeline_repository.dart';
import '../domain/services/smart_linkage_service.dart';
import '../domain/usecases/add_milestone_usecase.dart';
import '../domain/usecases/create_log_entry_usecase.dart';
import '../domain/usecases/delete_log_entry_usecase.dart';
import '../domain/usecases/delete_media_usecase.dart';
import '../domain/usecases/get_log_entry_usecase.dart';
import '../domain/usecases/get_log_media_usecase.dart';
import '../domain/usecases/get_project_logs_usecase.dart';
import '../domain/usecases/get_project_milestones_usecase.dart';
import '../domain/usecases/get_project_timeline_usecase.dart';
import '../domain/usecases/get_system_logs_usecase.dart';
import '../domain/usecases/search_logs_usecase.dart';
import '../domain/usecases/update_log_entry_usecase.dart';
import '../domain/usecases/upload_media_usecase.dart';

part 'providers.g.dart';

// ============================================================================
// Data Sources
// ============================================================================

@riverpod
LogRemoteDataSource logRemoteDataSource(LogRemoteDataSourceRef ref) {
  return LogRemoteDataSourceImpl(supabaseClient: SupabaseConfig.client);
}

@riverpod
MediaRemoteDataSource mediaRemoteDataSource(MediaRemoteDataSourceRef ref) {
  return MediaRemoteDataSourceImpl(
    supabaseClient: SupabaseConfig.client,
    fileUploadService: ref.read(fileUploadServiceProvider),
  );
}

@riverpod
TimelineRemoteDataSource timelineRemoteDataSource(TimelineRemoteDataSourceRef ref) {
  return TimelineRemoteDataSourceImpl(supabaseClient: SupabaseConfig.client);
}

// ============================================================================
// Repositories
// ============================================================================

@riverpod
LogRepository logRepository(LogRepositoryRef ref) {
  return LogRepositoryImpl(
    remoteDataSource: ref.read(logRemoteDataSourceProvider),
  );
}

@riverpod
MediaRepository mediaRepository(MediaRepositoryRef ref) {
  return MediaRepositoryImpl(
    remoteDataSource: ref.read(mediaRemoteDataSourceProvider),
  );
}

@riverpod
TimelineRepository timelineRepository(TimelineRepositoryRef ref) {
  return TimelineRepositoryImpl(
    remoteDataSource: ref.read(timelineRemoteDataSourceProvider),
  );
}

// ============================================================================
// Services
// ============================================================================

@riverpod
SmartLinkageService smartLinkageService(SmartLinkageServiceRef ref) {
  return SmartLinkageService(
    logRepository: ref.read(logRepositoryProvider),
    timelineRepository: ref.read(timelineRepositoryProvider),
    bomRepository: ref.read(bomRepositoryProvider),
  );
}

// ============================================================================
// Use Cases - Log
// ============================================================================

@riverpod
CreateLogEntryUseCase createLogEntryUseCase(CreateLogEntryUseCaseRef ref) {
  return CreateLogEntryUseCase(ref.read(logRepositoryProvider));
}

@riverpod
UpdateLogEntryUseCase updateLogEntryUseCase(UpdateLogEntryUseCaseRef ref) {
  return UpdateLogEntryUseCase(ref.read(logRepositoryProvider));
}

@riverpod
DeleteLogEntryUseCase deleteLogEntryUseCase(DeleteLogEntryUseCaseRef ref) {
  return DeleteLogEntryUseCase(ref.read(logRepositoryProvider));
}

@riverpod
GetLogEntryUseCase getLogEntryUseCase(GetLogEntryUseCaseRef ref) {
  return GetLogEntryUseCase(ref.read(logRepositoryProvider));
}

@riverpod
GetProjectLogsUseCase getProjectLogsUseCase(GetProjectLogsUseCaseRef ref) {
  return GetProjectLogsUseCase(ref.read(logRepositoryProvider));
}

@riverpod
GetSystemLogsUseCase getSystemLogsUseCase(GetSystemLogsUseCaseRef ref) {
  return GetSystemLogsUseCase(ref.read(logRepositoryProvider));
}

@riverpod
SearchLogsUseCase searchLogsUseCase(SearchLogsUseCaseRef ref) {
  return SearchLogsUseCase(ref.read(logRepositoryProvider));
}

// ============================================================================
// Use Cases - Media
// ============================================================================

@riverpod
UploadMediaUseCase uploadMediaUseCase(UploadMediaUseCaseRef ref) {
  return UploadMediaUseCase(ref.read(mediaRepositoryProvider));
}

@riverpod
GetLogMediaUseCase getLogMediaUseCase(GetLogMediaUseCaseRef ref) {
  return GetLogMediaUseCase(ref.read(mediaRepositoryProvider));
}

@riverpod
DeleteMediaUseCase deleteMediaUseCase(DeleteMediaUseCaseRef ref) {
  return DeleteMediaUseCase(ref.read(mediaRepositoryProvider));
}

// ============================================================================
// Use Cases - Timeline
// ============================================================================

@riverpod
GetProjectTimelineUseCase getProjectTimelineUseCase(GetProjectTimelineUseCaseRef ref) {
  return GetProjectTimelineUseCase(ref.read(timelineRepositoryProvider));
}

@riverpod
AddMilestoneUseCase addMilestoneUseCase(AddMilestoneUseCaseRef ref) {
  return AddMilestoneUseCase(ref.read(timelineRepositoryProvider));
}

@riverpod
GetProjectMilestonesUseCase getProjectMilestonesUseCase(GetProjectMilestonesUseCaseRef ref) {
  return GetProjectMilestonesUseCase(ref.read(timelineRepositoryProvider));
}
