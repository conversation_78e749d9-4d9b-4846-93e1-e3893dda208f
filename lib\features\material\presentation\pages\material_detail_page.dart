import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/specification_provider.dart';
import '../widgets/product_specification_widget.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';

/// 材料详情页面
/// 
/// 展示材料的完整信息，包括详细规格
class MaterialDetailPage extends ConsumerStatefulWidget {
  final domain.Material material;

  const MaterialDetailPage({
    Key? key,
    required this.material,
  }) : super(key: key);

  @override
  ConsumerState<MaterialDetailPage> createState() => _MaterialDetailPageState();
}

class _MaterialDetailPageState extends ConsumerState<MaterialDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.material.name),
        backgroundColor: VanHubBrandColors.primary,
        foregroundColor: VanHubBrandColors.onPrimary,
        bottom: TabBar(
          controller: _tabController,
          labelColor: VanHubBrandColors.onPrimary,
          unselectedLabelColor: VanHubBrandColors.onPrimary.withOpacity(0.7),
          indicatorColor: VanHubBrandColors.onPrimary,
          tabs: const [
            Tab(text: '基本信息', icon: Icon(Icons.info_outline)),
            Tab(text: '详细规格', icon: Icon(Icons.engineering)),
            Tab(text: '使用记录', icon: Icon(Icons.history)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editMaterial,
            tooltip: '编辑材料',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareMaterial,
            tooltip: '分享材料',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBasicInfoTab(),
          _buildSpecificationTab(),
          _buildUsageHistoryTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addToBom,
        backgroundColor: VanHubBrandColors.primary,
        foregroundColor: VanHubBrandColors.onPrimary,
        icon: const Icon(Icons.add_shopping_cart),
        label: const Text('添加到BOM'),
      ),
    );
  }

  /// 构建基本信息标签页
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMaterialHeader(),
          const SizedBox(height: 24),
          _buildBasicInfoCard(),
          const SizedBox(height: 16),
          _buildPriceInfoCard(),
          const SizedBox(height: 16),
          _buildSupplierInfoCard(),
        ],
      ),
    );
  }

  /// 构建规格标签页
  Widget _buildSpecificationTab() {
    // 如果有内嵌的规格对象，直接显示
    if (widget.material.productSpecification != null) {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: ProductSpecificationWidget(
          specification: widget.material.productSpecification!,
        ),
      );
    }

    // 如果有规格ID，异步加载规格
    if (widget.material.specificationId != null) {
      return _buildAsyncSpecification();
    }

    // 没有规格信息
    return _buildNoSpecificationView();
  }

  /// 构建使用记录标签页
  Widget _buildUsageHistoryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '使用统计',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildStatItem('使用次数', '${widget.material.usageCount}次'),
                  const Divider(),
                  _buildStatItem('最后使用', widget.material.lastUsedAt != null 
                      ? _formatDate(widget.material.lastUsedAt!) 
                      : '从未使用'),
                  const Divider(),
                  _buildStatItem('添加时间', _formatDate(widget.material.createdAt)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '使用记录',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          // TODO: 实现使用记录列表
          const Card(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Center(
                child: Text('暂无使用记录'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建材料头部信息
  Widget _buildMaterialHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 材料图片
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    Colors.teal.shade300,
                    Colors.teal.shade500,
                  ],
                ),
              ),
              child: widget.material.imageUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        widget.material.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => 
                            const Icon(Icons.image, color: Colors.white, size: 40),
                      ),
                    )
                  : const Icon(Icons.category, color: Colors.white, size: 40),
            ),
            const SizedBox(width: 16),
            // 材料信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.material.name,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (widget.material.brand != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      widget.material.brand!,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  if (widget.material.model != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '型号: ${widget.material.model}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  Chip(
                    label: Text(widget.material.category),
                    backgroundColor: Colors.teal.withOpacity(0.1),
                    side: BorderSide(color: Colors.teal.withOpacity(0.3)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基本信息',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (widget.material.description.isNotEmpty) ...[
              _buildInfoItem('描述', widget.material.description),
              const SizedBox(height: 8),
            ],
            if (widget.material.specifications != null) ...[
              _buildInfoItem('规格', widget.material.specifications!),
              const SizedBox(height: 8),
            ],
            if (widget.material.tags != null && widget.material.tags!.isNotEmpty) ...[
              _buildInfoItem('标签', widget.material.tags!.join(', ')),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建价格信息卡片
  Widget _buildPriceInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '价格信息',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoItem('参考价格', '¥${widget.material.price.toStringAsFixed(2)}'),
            if (widget.material.minPrice != null || widget.material.maxPrice != null) ...[
              const SizedBox(height: 8),
              _buildInfoItem(
                '价格范围', 
                '¥${widget.material.minPrice?.toStringAsFixed(2) ?? '未知'} - ¥${widget.material.maxPrice?.toStringAsFixed(2) ?? '未知'}'
              ),
            ],
            if (widget.material.purchaseDate != null) ...[
              const SizedBox(height: 8),
              _buildInfoItem('采购日期', _formatDate(widget.material.purchaseDate!)),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建供应商信息卡片
  Widget _buildSupplierInfoCard() {
    if (widget.material.supplier == null && widget.material.supplierUrl == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '供应商信息',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (widget.material.supplier != null) ...[
              _buildInfoItem('供应商', widget.material.supplier!),
              const SizedBox(height: 8),
            ],
            if (widget.material.supplierUrl != null) ...[
              InkWell(
                onTap: () => _openSupplierUrl(),
                child: Text(
                  '查看购买链接',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建异步规格信息
  Widget _buildAsyncSpecification() {
    final specificationAsync = ref.watch(specificationByMaterialIdProvider(widget.material.id));
    
    return specificationAsync.when(
      data: (specification) {
        if (specification == null) {
          return _buildNoSpecificationView();
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: ProductSpecificationWidget(specification: specification),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text('加载规格信息失败: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(specificationByMaterialIdProvider(widget.material.id)),
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建无规格信息视图
  Widget _buildNoSpecificationView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.description_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text('暂无详细规格信息'),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _createSpecification,
            icon: const Icon(Icons.add),
            label: const Text('创建规格'),
          ),
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(value),
        ),
      ],
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 编辑材料
  void _editMaterial() {
    // TODO: 导航到编辑页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('编辑材料功能')),
    );
  }

  /// 分享材料
  void _shareMaterial() {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享材料功能')),
    );
  }

  /// 添加到BOM
  void _addToBom() {
    // TODO: 实现添加到BOM功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加到BOM功能')),
    );
  }

  /// 创建规格
  void _createSpecification() {
    // TODO: 导航到规格创建页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('创建规格功能')),
    );
  }

  /// 打开供应商链接
  void _openSupplierUrl() {
    // TODO: 打开浏览器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('打开购买链接功能')),
    );
  }
}
