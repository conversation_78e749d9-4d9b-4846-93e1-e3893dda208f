import 'dart:convert';
import 'dart:math';
import 'package:fpdart/fpdart.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/services.dart';
import 'package:crypto/crypto.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/share_service.dart';
import '../../domain/entities/share_link.dart';
import '../../domain/entities/share_config.dart';

/// 分享服务实现
class ShareServiceImpl implements ShareService {
  final SupabaseClient _supabaseClient;
  final String _baseUrl;

  ShareServiceImpl({
    required SupabaseClient supabaseClient,
    String? baseUrl,
  }) : _supabaseClient = supabaseClient,
       _baseUrl = baseUrl ?? 'https://vanhub.app/share';

  @override
  Future<Either<Failure, ShareLink>> generateShareLink({
    required String contentId,
    required ShareContentType contentType,
    required String userId,
    ShareConfig? config,
  }) async {
    try {
      final shareConfig = config ?? const ShareConfig();
      
      // 检查是否可以分享
      final canShare = await canShareContent(
        contentId: contentId,
        contentType: contentType,
        userId: userId,
      );
      
      final canShareResult = canShare.fold(
        (failure) => throw Exception(failure.message),
        (result) => result,
      );
      
      if (!canShareResult) {
        return Left(ValidationFailure(message: '无权分享此内容'));
      }

      // 生成分享ID和URL
      final shareId = _generateShareId();
      final shareUrl = '$_baseUrl/$shareId';
      
      // 生成访问令牌（如果需要）
      String? accessToken;
      if (shareConfig.requiresAuth || shareConfig.needsPassword) {
        accessToken = _generateAccessToken(shareId, userId);
      }

      // 获取内容预览信息
      final previewResult = await generateSharePreview(
        contentId: contentId,
        contentType: contentType,
      );
      
      final preview = previewResult.fold(
        (failure) => <String, dynamic>{},
        (data) => data,
      );

      // 创建分享链接对象
      final shareLink = ShareLink(
        id: shareId,
        contentId: contentId,
        contentType: contentType,
        userId: userId,
        shareUrl: shareUrl,
        permissionLevel: shareConfig.permissionLevel,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        expiresAt: shareConfig.expiresAt,
        title: preview['title'] as String?,
        description: preview['description'] as String?,
        thumbnailUrl: preview['thumbnailUrl'] as String?,
        accessToken: accessToken,
        isActive: true,
        requiresAuth: shareConfig.requiresAuth,
        allowedUsers: shareConfig.allowedUsers,
        metadata: {
          ...shareConfig.metadata,
          'allowComments': shareConfig.allowComments,
          'allowLikes': shareConfig.allowLikes,
          'trackViews': shareConfig.trackViews,
          'notifyOnAccess': shareConfig.notifyOnAccess,
          'customMessage': shareConfig.customMessage,
          'accessPassword': shareConfig.accessPassword,
        },
      );

      // 保存到数据库
      final data = shareLink.toJson();
      await _supabaseClient
          .from('share_links')
          .insert(data);

      return Right(shareLink);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '生成分享链接失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '生成分享链接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ShareLink>> getShareLink(String shareId) async {
    try {
      final response = await _supabaseClient
          .from('share_links')
          .select()
          .eq('id', shareId)
          .single();

      final shareLink = ShareLink.fromJson(response);
      return Right(shareLink);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure(message: '分享链接不存在'));
      }
      return Left(ServerFailure(message: '获取分享链接失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取分享链接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> validateShareAccess({
    required String shareId,
    String? userId,
    String? accessToken,
  }) async {
    try {
      final linkResult = await getShareLink(shareId);
      
      return linkResult.fold(
        (failure) => Left(failure),
        (shareLink) {
          // 检查链接是否有效
          if (!shareLink.isAccessible) {
            return const Right(false);
          }

          // 检查权限级别
          switch (shareLink.permissionLevel) {
            case SharePermissionLevel.public:
              return const Right(true);
              
            case SharePermissionLevel.unlisted:
              return const Right(true);
              
            case SharePermissionLevel.private:
              if (userId == null) return const Right(false);
              
              // 检查是否是所有者
              if (userId == shareLink.userId) return const Right(true);
              
              // 检查是否在允许列表中
              if (shareLink.allowedUsers.contains(userId)) {
                return const Right(true);
              }
              
              // 检查访问令牌
              if (accessToken != null && shareLink.accessToken == accessToken) {
                return const Right(true);
              }
              
              return const Right(false);
          }
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '验证分享访问权限失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ShareLink>> updateShareConfig({
    required String shareId,
    required ShareConfig config,
    required String userId,
  }) async {
    try {
      // 先获取现有链接
      final linkResult = await getShareLink(shareId);
      
      return await linkResult.fold(
        (failure) async => Left(failure),
        (shareLink) async {
          // 检查权限
          if (shareLink.userId != userId) {
            return Left(ValidationFailure(message: '无权修改此分享链接'));
          }

          // 更新配置
          final updatedLink = shareLink.copyWith(
            permissionLevel: config.permissionLevel,
            expiresAt: config.expiresAt,
            requiresAuth: config.requiresAuth,
            allowedUsers: config.allowedUsers,
            updatedAt: DateTime.now(),
            metadata: {
              ...shareLink.metadata,
              'allowComments': config.allowComments,
              'allowLikes': config.allowLikes,
              'trackViews': config.trackViews,
              'notifyOnAccess': config.notifyOnAccess,
              'customMessage': config.customMessage,
              'accessPassword': config.accessPassword,
            },
          );

          // 保存到数据库
          await _supabaseClient
              .from('share_links')
              .update(updatedLink.toJson())
              .eq('id', shareId);

          return Right(updatedLink);
        },
      );
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '更新分享配置失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '更新分享配置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteShareLink({
    required String shareId,
    required String userId,
  }) async {
    try {
      // 先检查权限
      final linkResult = await getShareLink(shareId);
      
      return await linkResult.fold(
        (failure) async => Left(failure),
        (shareLink) async {
          if (shareLink.userId != userId) {
            return Left(ValidationFailure(message: '无权删除此分享链接'));
          }

          await _supabaseClient
              .from('share_links')
              .delete()
              .eq('id', shareId);

          return const Right(null);
        },
      );
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '删除分享链接失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '删除分享链接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ShareLink>>> getUserShareLinks({
    required String userId,
    ShareContentType? contentType,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      var query = _supabaseClient
          .from('share_links')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      if (contentType != null) {
        query = query.eq('content_type', contentType.name);
      }

      final response = await query;
      
      final shareLinks = (response as List)
          .map((json) => ShareLink.fromJson(json))
          .toList();

      return Right(shareLinks);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取分享链接列表失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取分享链接列表失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> recordShareAccess({
    required String shareId,
    String? userId,
    String? userAgent,
    String? referrer,
  }) async {
    try {
      // 记录访问日志
      await _supabaseClient.from('share_access_logs').insert({
        'share_id': shareId,
        'user_id': userId,
        'user_agent': userAgent,
        'referrer': referrer,
        'accessed_at': DateTime.now().toIso8601String(),
      });

      // 更新访问计数
      await _supabaseClient.rpc('increment_share_view_count', params: {
        'share_id': shareId,
      });

      return const Right(null);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '记录分享访问失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '记录分享访问失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ShareStats>> getShareStats(String shareId) async {
    try {
      // 获取基本统计信息
      final response = await _supabaseClient
          .from('share_links')
          .select('id, view_count, share_count, created_at')
          .eq('id', shareId)
          .single();

      // 获取访问日志统计
      final accessLogs = await _supabaseClient
          .from('share_access_logs')
          .select('user_id, accessed_at')
          .eq('share_id', shareId)
          .order('accessed_at', ascending: false);

      final accessedBy = <String>[];
      DateTime? lastAccessedAt;
      
      for (final log in accessLogs) {
        final userId = log['user_id'] as String?;
        if (userId != null && !accessedBy.contains(userId)) {
          accessedBy.add(userId);
        }
        
        if (lastAccessedAt == null) {
          lastAccessedAt = DateTime.parse(log['accessed_at']);
        }
      }

      final stats = ShareStats(
        shareId: shareId,
        viewCount: response['view_count'] ?? 0,
        shareCount: response['share_count'] ?? 0,
        firstSharedAt: DateTime.parse(response['created_at']),
        lastAccessedAt: lastAccessedAt ?? DateTime.parse(response['created_at']),
        accessedBy: accessedBy,
        platformStats: {}, // TODO: 实现平台统计
      );

      return Right(stats);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取分享统计失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取分享统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> cleanupExpiredLinks() async {
    try {
      final response = await _supabaseClient
          .from('share_links')
          .delete()
          .lt('expires_at', DateTime.now().toIso8601String());

      // PostgreSQL返回的是删除的行数
      final deletedCount = response.length;
      return Right(deletedCount);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '清理过期链接失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '清理过期链接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> canShareContent({
    required String contentId,
    required ShareContentType contentType,
    required String userId,
  }) async {
    try {
      // 根据内容类型检查权限
      switch (contentType) {
        case ShareContentType.project:
          final response = await _supabaseClient
              .from('projects')
              .select('user_id, is_public')
              .eq('id', contentId)
              .single();
          
          return Right(response['user_id'] == userId || response['is_public'] == true);
          
        case ShareContentType.material:
          // TODO: 实现材料分享权限检查
          return const Right(true);
          
        case ShareContentType.bomItem:
          // TODO: 实现BOM项目分享权限检查
          return const Right(true);
          
        case ShareContentType.modificationLog:
          // TODO: 实现改装日志分享权限检查
          return const Right(true);
          
        default:
          return const Right(true);
      }
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '检查分享权限失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '检查分享权限失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> generateSharePreview({
    required String contentId,
    required ShareContentType contentType,
  }) async {
    try {
      switch (contentType) {
        case ShareContentType.project:
          final response = await _supabaseClient
              .from('projects')
              .select('name, description, thumbnail_url')
              .eq('id', contentId)
              .single();
          
          return Right({
            'title': response['name'],
            'description': response['description'],
            'thumbnailUrl': response['thumbnail_url'],
          });
          
        default:
          return Right({
            'title': '分享内容',
            'description': '来自VanHub改装宝的分享',
            'thumbnailUrl': null,
          });
      }
    } catch (e) {
      return Left(UnknownFailure(message: '生成分享预览失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> copyShareLinkToClipboard(String shareUrl) async {
    try {
      await Clipboard.setData(ClipboardData(text: shareUrl));
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '复制链接失败: $e'));
    }
  }

  @override
  String getShareUrl(String shareId) {
    return '$_baseUrl/$shareId';
  }

  @override
  Either<Failure, Map<String, String>> parseShareUrl(String shareUrl) {
    try {
      final uri = Uri.parse(shareUrl);
      final pathSegments = uri.pathSegments;
      
      if (pathSegments.isEmpty) {
        return Left(ValidationFailure(message: '无效的分享链接'));
      }
      
      final shareId = pathSegments.last;
      final queryParams = uri.queryParameters;
      
      return Right({
        'shareId': shareId,
        ...queryParams,
      });
    } catch (e) {
      return Left(ValidationFailure(message: '解析分享链接失败: $e'));
    }
  }

  /// 生成分享ID
  String _generateShareId() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(12, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// 生成访问令牌
  String _generateAccessToken(String shareId, String userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final data = '$shareId:$userId:$timestamp';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16);
  }
}
