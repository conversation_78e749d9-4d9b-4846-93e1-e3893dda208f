import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/notification.dart';

/// 通知查询参数
class NotificationQuery {
  final String? userId;
  final List<NotificationType>? types;
  final List<NotificationPriority>? priorities;
  final List<NotificationStatus>? statuses;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? sourceId;
  final String? sourceType;
  final String? groupId;
  final bool? isRead;
  final bool? isPersistent;
  final int limit;
  final int offset;
  final String? searchQuery;

  const NotificationQuery({
    this.userId,
    this.types,
    this.priorities,
    this.statuses,
    this.startDate,
    this.endDate,
    this.sourceId,
    this.sourceType,
    this.groupId,
    this.isRead,
    this.isPersistent,
    this.limit = 50,
    this.offset = 0,
    this.searchQuery,
  });
}

/// 通知统计信息
class NotificationStats {
  final int totalCount;
  final int unreadCount;
  final int readCount;
  final int archivedCount;
  final Map<NotificationType, int> typeStats;
  final Map<NotificationPriority, int> priorityStats;
  final DateTime? lastNotificationAt;
  final DateTime? lastReadAt;

  const NotificationStats({
    required this.totalCount,
    required this.unreadCount,
    required this.readCount,
    required this.archivedCount,
    required this.typeStats,
    required this.priorityStats,
    this.lastNotificationAt,
    this.lastReadAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalCount': totalCount,
      'unreadCount': unreadCount,
      'readCount': readCount,
      'archivedCount': archivedCount,
      'typeStats': typeStats.map((k, v) => MapEntry(k.name, v)),
      'priorityStats': priorityStats.map((k, v) => MapEntry(k.name, v)),
      'lastNotificationAt': lastNotificationAt?.toIso8601String(),
      'lastReadAt': lastReadAt?.toIso8601String(),
    };
  }
}

/// 批量操作结果
class BatchOperationResult {
  final int successCount;
  final int failureCount;
  final List<String> failedIds;
  final List<String> errors;

  const BatchOperationResult({
    required this.successCount,
    required this.failureCount,
    required this.failedIds,
    required this.errors,
  });

  bool get isSuccessful => failureCount == 0;
  int get totalCount => successCount + failureCount;
}

/// 通知服务接口
abstract class NotificationService {
  /// 创建通知
  Future<Either<Failure, Notification>> createNotification(Notification notification);

  /// 批量创建通知
  Future<Either<Failure, BatchOperationResult>> createNotifications(List<Notification> notifications);

  /// 获取通知详情
  Future<Either<Failure, Notification>> getNotification(String notificationId);

  /// 查询通知列表
  Future<Either<Failure, List<Notification>>> getNotifications(NotificationQuery query);

  /// 获取用户通知
  Future<Either<Failure, List<Notification>>> getUserNotifications({
    required String userId,
    List<NotificationType>? types,
    List<NotificationStatus>? statuses,
    int limit = 50,
    int offset = 0,
  });

  /// 获取未读通知
  Future<Either<Failure, List<Notification>>> getUnreadNotifications(String userId);

  /// 标记通知为已读
  Future<Either<Failure, Notification>> markAsRead(String notificationId);

  /// 标记通知为未读
  Future<Either<Failure, Notification>> markAsUnread(String notificationId);

  /// 批量标记为已读
  Future<Either<Failure, BatchOperationResult>> markAllAsRead(String userId);

  /// 批量标记指定通知为已读
  Future<Either<Failure, BatchOperationResult>> markNotificationsAsRead(List<String> notificationIds);

  /// 归档通知
  Future<Either<Failure, Notification>> archiveNotification(String notificationId);

  /// 批量归档通知
  Future<Either<Failure, BatchOperationResult>> archiveNotifications(List<String> notificationIds);

  /// 删除通知
  Future<Either<Failure, void>> deleteNotification(String notificationId);

  /// 批量删除通知
  Future<Either<Failure, BatchOperationResult>> deleteNotifications(List<String> notificationIds);

  /// 清空用户所有通知
  Future<Either<Failure, void>> clearAllNotifications(String userId);

  /// 获取通知统计信息
  Future<Either<Failure, NotificationStats>> getNotificationStats(String userId);

  /// 获取未读通知数量
  Future<Either<Failure, int>> getUnreadCount(String userId);

  /// 搜索通知
  Future<Either<Failure, List<Notification>>> searchNotifications({
    required String userId,
    required String query,
    List<NotificationType>? types,
    int limit = 50,
    int offset = 0,
  });

  /// 按类型获取通知
  Future<Either<Failure, List<Notification>>> getNotificationsByType({
    required String userId,
    required NotificationType type,
    int limit = 50,
    int offset = 0,
  });

  /// 按优先级获取通知
  Future<Either<Failure, List<Notification>>> getNotificationsByPriority({
    required String userId,
    required NotificationPriority priority,
    int limit = 50,
    int offset = 0,
  });

  /// 获取分组通知
  Future<Either<Failure, Map<String, List<Notification>>>> getGroupedNotifications({
    required String userId,
    String? groupId,
    int limit = 50,
    int offset = 0,
  });

  /// 清理过期通知
  Future<Either<Failure, int>> cleanupExpiredNotifications();

  /// 清理已删除通知
  Future<Either<Failure, int>> cleanupDeletedNotifications();

  /// 订阅通知更新
  Stream<List<Notification>> subscribeToNotifications(String userId);

  /// 订阅未读通知数量更新
  Stream<int> subscribeToUnreadCount(String userId);

  /// 发送推送通知
  Future<Either<Failure, void>> sendPushNotification({
    required String userId,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    String? imageUrl,
  });

  /// 批量发送推送通知
  Future<Either<Failure, BatchOperationResult>> sendBatchPushNotifications({
    required List<String> userIds,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    String? imageUrl,
  });

  /// 发送定时通知
  Future<Either<Failure, void>> scheduleNotification({
    required Notification notification,
    required DateTime scheduledAt,
  });

  /// 取消定时通知
  Future<Either<Failure, void>> cancelScheduledNotification(String notificationId);

  /// 获取定时通知列表
  Future<Either<Failure, List<Notification>>> getScheduledNotifications(String userId);

  /// 更新通知设置
  Future<Either<Failure, void>> updateNotificationSettings({
    required String userId,
    required Map<String, dynamic> settings,
  });

  /// 获取通知设置
  Future<Either<Failure, Map<String, dynamic>>> getNotificationSettings(String userId);

  /// 检查通知权限
  Future<Either<Failure, bool>> checkNotificationPermission();

  /// 请求通知权限
  Future<Either<Failure, bool>> requestNotificationPermission();
}
