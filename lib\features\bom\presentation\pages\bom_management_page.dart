import 'package:flutter/material.dart';
import '../../data/services/bom_export_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/bom_item.dart' as domain;
import '../providers/bom_provider.dart';
import '../widgets/add_material_to_bom_dialog_widget.dart';
import '../widgets/create_bom_item_dialog_widget.dart';
import '../widgets/edit_bom_item_dialog_widget.dart';
import '../widgets/enhanced_bom_statistics_widget.dart';
import '../widgets/enhanced_bom_tree_widget.dart';
import '../widgets/enhanced_bom_item_card_widget.dart';
import 'bom_item_detail_page.dart';

/// VanHub BOM管理页面 - 房车改装物料清单
class BomManagementPage extends ConsumerStatefulWidget {
  final String projectId;

  const BomManagementPage({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<BomManagementPage> createState() => _BomManagementPageState();
}

class _BomManagementPageState extends ConsumerState<BomManagementPage> {
  String _selectedStatus = '全部';
  String _searchQuery = '';
  List<domain.BomItem> _currentBomItems = []; // 存储当前的BOM项目列表

  // BOM物料状态
  final List<String> _statusOptions = [
    '全部', '计划中', '已采购', '已使用', '已完成'
  ];

  @override
  void initState() {
    super.initState();
    // 页面初始化时加载BOM数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(projectBomItemsProvider(widget.projectId));
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    
    if (currentUserId == null) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.login, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                '请先登录以查看BOM',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // 自定义AppBar - BOM主题
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Colors.indigo,
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                'BOM 物料清单',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.indigo,
                      Colors.indigo.shade700,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // 背景图案
                    Positioned(
                      right: -30,
                      top: 10,
                      child: Icon(
                        Icons.list_alt,
                        size: 120,
                        color: Colors.white.withValues(alpha: 0.1),
                      ),
                    ),
                    // 统计信息
                    Positioned(
                      left: 20,
                      bottom: 50,
                      child: _buildBomStats(),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.white),
                onPressed: () {
                  ref.invalidate(projectBomItemsProvider(widget.projectId));
                },
              ),
              IconButton(
                icon: const Icon(Icons.analytics, color: Colors.white),
                onPressed: _showBomAnalytics,
              ),
            ],
          ),
          
          // 搜索和筛选区域
          SliverToBoxAdapter(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 搜索框
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: '搜索BOM物料名称、分类...',
                        prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 15,
                        ),
                      ),
                      onChanged: (query) {
                        setState(() {
                          _searchQuery = query;
                        });
                      },
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 状态筛选
                  Row(
                    children: [
                      Icon(Icons.filter_list, color: Colors.grey[600], size: 20),
                      const SizedBox(width: 8),
                      Text(
                        '状态筛选:',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: SizedBox(
                          height: 40,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _statusOptions.length,
                            itemBuilder: (context, index) {
                              final status = _statusOptions[index];
                              final isSelected = status == _selectedStatus;
                              
                              return Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: FilterChip(
                                  label: Text(status),
                                  selected: isSelected,
                                  onSelected: (selected) {
                                    setState(() {
                                      _selectedStatus = status;
                                    });
                                  },
                                  backgroundColor: Colors.grey[200],
                                  selectedColor: Colors.indigo.shade100,
                                  checkmarkColor: Colors.indigo,
                                  labelStyle: TextStyle(
                                    color: isSelected ? Colors.indigo : Colors.grey[700],
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // BOM列表
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildBomList(),
            ),
          ),
        ],
      ),
      
      // 添加物料按钮
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 从材料库添加
          FloatingActionButton(
            heroTag: "add_from_library",
            onPressed: _showAddFromLibraryDialog,
            backgroundColor: Colors.teal,
            foregroundColor: Colors.white,
            child: const Icon(Icons.library_add),
          ),
          const SizedBox(height: 12),
          // 手动添加
          FloatingActionButton.extended(
            heroTag: "add_manual",
            onPressed: _showAddManualDialog,
            backgroundColor: Colors.indigo,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.add),
            label: const Text('添加物料'),
          ),
        ],
      ),
    );
  }

  Widget _buildBomStats() {
    final bomItemsAsync = ref.watch(projectBomItemsProvider(widget.projectId));
    
    return bomItemsAsync.when(
      data: (bomItems) {
        final totalItems = bomItems.length;
        final completedItems = bomItems.where((item) =>
          item.status == domain.BomItemStatus.installed).length;
        final totalBudget = bomItems.fold<double>(0.0, (sum, item) => 
          sum + (item.estimatedPrice ?? 0.0) * item.quantity);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '项目物料统计',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w300,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildStatItem('总计', '$totalItems', Colors.white),
                const SizedBox(width: 20),
                _buildStatItem('已完成', '$completedItems', Colors.green.shade200),
                const SizedBox(width: 20),
                _buildStatItem('预算', '¥${_formatBudget(totalBudget)}', Colors.orange.shade200),
              ],
            ),
          ],
        );
      },
      loading: () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '项目物料统计',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w300,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatItem('总计', '...', Colors.white),
              const SizedBox(width: 20),
              _buildStatItem('已完成', '...', Colors.green.shade200),
              const SizedBox(width: 20),
              _buildStatItem('预算', '...', Colors.orange.shade200),
            ],
          ),
        ],
      ),
      error: (_, __) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '项目物料统计',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w300,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatItem('总计', '0', Colors.white),
              const SizedBox(width: 20),
              _buildStatItem('已完成', '0', Colors.green.shade200),
              const SizedBox(width: 20),
              _buildStatItem('预算', '¥0', Colors.orange.shade200),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: color.withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildBomList() {
    final bomItemsAsync = ref.watch(projectBomItemsProvider(widget.projectId));

    return bomItemsAsync.when(
      data: (bomItems) {
        // 根据搜索查询和状态过滤BOM项
        final filteredItems = bomItems.where((item) {
          final matchesSearch = _searchQuery.trim().isEmpty ||
              item.materialName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (item.category?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

          final matchesStatus = _selectedStatus == '全部' ||
              _getStatusDisplayName(item.status) == _selectedStatus;

          return matchesSearch && matchesStatus;
        }).toList();

        // 存储当前过滤后的BOM项目列表，供其他方法使用
        _currentBomItems = filteredItems;

        if (filteredItems.isEmpty) {
          return SizedBox(
            height: 400,
            child: EmptyStateWidget(
              icon: Icons.list_alt,
              title: _searchQuery.trim().isEmpty && _selectedStatus == '全部' 
                  ? 'BOM清单为空' 
                  : '未找到匹配的物料',
              subtitle: _searchQuery.trim().isEmpty && _selectedStatus == '全部'
                  ? '开始添加改装所需的物料\n从材料库快速添加或手动创建'
                  : '尝试调整搜索条件或状态筛选',
              actionText: _searchQuery.trim().isEmpty && _selectedStatus == '全部' 
                  ? '添加第一个物料' 
                  : null,
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 结果统计
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                children: [
                  Icon(Icons.list_alt, color: Colors.grey[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '共 ${filteredItems.length} 个物料项',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  // 导出按钮
                  TextButton.icon(
                    onPressed: _exportBom,
                    icon: Icon(Icons.download, size: 16, color: Colors.grey[600]),
                    label: Text(
                      '导出',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
            
            // BOM树形结构展示
            _buildBomTreeView(filteredItems),

            // 添加专门的BOM树形组件测试
            const SizedBox(height: 20),
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.account_tree, color: Colors.blue),
                        const SizedBox(width: 8),
                        const Text(
                          '专业树形视图',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        ElevatedButton.icon(
                          onPressed: () => _showAdvancedTreeView(),
                          icon: const Icon(Icons.visibility),
                          label: const Text('查看树形结构'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '使用专业的BOM树形组件，支持拖拽、搜索、高级筛选等功能',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 100), // 为FAB留出空间
          ],
        );
      },
      loading: () => SizedBox(
        height: 400,
        child: const LoadingWidget(message: '加载BOM清单...'),
      ),
      error: (error, stack) => SizedBox(
        height: 400,
        child: ErrorDisplayWidget(
          message: '加载失败: ${error.toString()}',
          onRetry: () {
            ref.invalidate(projectBomItemsProvider(widget.projectId));
          },
        ),
      ),
    );
  }

  String _getStatusDisplayName(domain.BomItemStatus status) {
    switch (status) {
      case domain.BomItemStatus.pending:
        return '待采购';
      case domain.BomItemStatus.ordered:
        return '已下单';
      case domain.BomItemStatus.received:
        return '已收货';
      case domain.BomItemStatus.installed:
        return '已安装';
      case domain.BomItemStatus.cancelled:
        return '已取消';
    }
  }

  void _showAddFromLibraryDialog() {
    showDialog(
      context: context,
      builder: (context) => AddMaterialToBomDialogWidget(
        projectId: widget.projectId,
      ),
    );
  }

  void _showAddManualDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateBomItemDialogWidget(
        projectId: widget.projectId,
      ),
    );
  }

  void _showAdvancedTreeView() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              // 对话框标题栏
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.account_tree, color: Colors.white),
                    const SizedBox(width: 8),
                    const Text(
                      'BOM专业树形视图',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              // 增强版BOM树形组件
              Expanded(
                child: EnhancedBomTreeWidget(
                  projectId: widget.projectId,
                  onNodeSelected: (nodeId) {
                    debugPrint('🌳 [EnhancedBomTree] 选中节点: $nodeId');
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('选中树节点: ${nodeId.substring(0, 8)}...'),
                        duration: const Duration(seconds: 1),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                  onBomItemEdit: (bomItemId) {
                    Navigator.of(context).pop(); // 关闭对话框
                    final bomItem = _currentBomItems.firstWhere((item) => item.id == bomItemId);
                    _showEditBomItemDialog(bomItem);
                  },
                  onBomItemDelete: (bomItemId) {
                    Navigator.of(context).pop(); // 关闭对话框
                    final bomItem = _currentBomItems.firstWhere((item) => item.id == bomItemId);
                    _showDeleteBomItemDialog(bomItem);
                  },
                  onRefresh: () {
                    ref.invalidate(projectBomItemsProvider(widget.projectId));
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBomAnalytics() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              // 对话框标题栏
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.indigo,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.analytics, color: Colors.white),
                    const SizedBox(width: 8),
                    const Text(
                      'BOM数据分析',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              // 增强版统计组件
              Expanded(
                child: EnhancedBomStatisticsWidget(
                  projectId: widget.projectId,
                  showCharts: true,
                  showOverdueItems: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToBomItemDetail(String bomItemId) {
    // 添加调试日志
    debugPrint('🔍 [BOM管理] 点击BOM项目: $bomItemId');

    // 显示调试信息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('正在打开BOM项目详情: ${bomItemId.substring(0, 8)}...'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.blue,
      ),
    );

    try {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => BomItemDetailPage(
            bomItemId: bomItemId,
            projectId: widget.projectId,
          ),
        ),
      );
      debugPrint('✅ [BOM管理] 成功导航到BOM项目详情页面');
    } catch (e) {
      debugPrint('❌ [BOM管理] 导航失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('打开详情页面失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _updateBomItemStatus(String bomItemId, domain.BomItemStatus newStatus) async {
    try {
      final result = await ref.read(bomControllerProvider.notifier).updateBomItemStatus(bomItemId, newStatus);

      if (mounted) {
        result.fold(
          (failure) => _showError('更新BOM项目时发生错误: ${failure.message}'),
          (updatedBomItem) {
            _showSuccess('状态已更新为: ${_getStatusDisplayName(newStatus)}');
            // Provider刷新已在BomController中处理，这里不需要重复刷新
          },
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('更新BOM项目时发生错误: $e');
      }
    }
  }

  Future<void> _saveBomItemToLibrary(domain.BomItem bomItem) async {
    final result = await ref.read(bomControllerProvider.notifier).saveBomItemToMaterialLibrary(bomItem.id);
    
    if (mounted) {
      result.fold(
        (failure) => _showError('保存到材料库失败: ${failure.message}'),
        (materialId) => _showSuccess('已保存 "${bomItem.materialName}" 到材料库'),
      );
    }
  }

  void _exportBom() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出BOM'),
        content: const Text('请选择导出格式：'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performExport('excel');
            },
            child: const Text('Excel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performExport('pdf');
            },
            child: const Text('PDF'),
          ),
        ],
      ),
    );
  }

  Future<void> _performExport(String format) async {
    try {
      // 显示加载指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在导出...'),
            ],
          ),
        ),
      );

      // 获取BOM数据
      final bomItems = ref.read(projectBomItemsProvider(widget.projectId)).value ?? [];

      // 使用BOM导出服务
      final exportService = BomExportServiceImpl();

      final exportResult = format == 'excel'
          ? await exportService.exportToExcel(
              bomItems: bomItems,
              projectName: 'Project_${widget.projectId}',
            )
          : await exportService.exportToPdf(
              bomItems: bomItems,
              projectName: 'Project_${widget.projectId}',
            );

      // 关闭加载指示器
      Navigator.of(context).pop();

      exportResult.fold(
        (failure) {
          // 显示错误消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('导出失败: ${failure.message}')),
          );
        },
        (exportedFile) {
          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('导出成功: ${exportedFile.path}')),
          );
        },
      );
    } catch (e) {
      // 关闭加载指示器
      Navigator.of(context).pop();

      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text('导出失败：$e'),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _formatBudget(double budget) {
    if (budget >= 10000) {
      return '${(budget / 10000).toStringAsFixed(1)}万';
    } else if (budget >= 1000) {
      return '${(budget / 1000).toStringAsFixed(1)}k';
    }
    return budget.toStringAsFixed(0);
  }

  /// 显示编辑BOM项目对话框
  void _showEditBomItemDialog(domain.BomItem bomItem) {
    showDialog(
      context: context,
      barrierDismissible: false, // 防止意外关闭
      builder: (context) => EditBomItemDialogWidget(bomItem: bomItem),
    ).then((result) {
      // 如果编辑成功，刷新BOM列表
      if (result != null) {
        ref.invalidate(projectBomItemsProvider(widget.projectId));
      }
    });
  }

  /// 显示删除BOM项目确认对话框
  void _showDeleteBomItemDialog(domain.BomItem bomItem) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('确认删除'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('确定要删除BOM项目 "${bomItem.name}" 吗？'),
            const SizedBox(height: 8),
            Text(
              '数量：${bomItem.quantity}，单价：¥${bomItem.unitPrice.toStringAsFixed(2)}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            const Text(
              '此操作不可撤销，删除后项目成本统计会相应调整。',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => _deleteBomItem(bomItem),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 删除BOM项目 - 严格遵循Clean Architecture原则
  Future<void> _deleteBomItem(domain.BomItem bomItem) async {
    Navigator.of(context).pop(); // 关闭确认对话框

    try {
      // 通过Provider调用Domain层UseCase
      final result = await ref.read(bomControllerProvider.notifier).deleteBomItem(bomItem.id);

      if (mounted) {
        result.fold(
          (failure) {
            // 显示错误信息
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('删除BOM项目失败: ${failure.message}'),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: '重试',
                  textColor: Colors.white,
                  onPressed: () => _showDeleteBomItemDialog(bomItem),
                ),
              ),
            );
          },
          (_) {
            // 删除成功
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('BOM项目 "${bomItem.name}" 已删除'),
                backgroundColor: Colors.green,
              ),
            );

            // 刷新BOM列表
            ref.invalidate(projectBomItemsProvider(widget.projectId));
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除BOM项目时发生错误: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 复制BOM项目
  void _duplicateBomItem(domain.BomItem bomItem) {
    // TODO: 实现复制BOM项目功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('复制 "${bomItem.name}" 功能开发中...'),
        backgroundColor: Colors.blue,
        action: SnackBarAction(
          label: '了解',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  /// 构建BOM树形结构视图
  /// 按材料分类组织BOM项目，实现树形结构展示
  Widget _buildBomTreeView(List<domain.BomItem> bomItems) {
    // 按分类分组BOM项目
    final Map<String, List<domain.BomItem>> groupedItems = {};

    for (final item in bomItems) {
      final category = item.category ?? '未分类';
      if (!groupedItems.containsKey(category)) {
        groupedItems[category] = [];
      }
      groupedItems[category]!.add(item);
    }

    // 按分类名称排序
    final sortedCategories = groupedItems.keys.toList()..sort();

    return Column(
      children: sortedCategories.map((category) {
        final categoryItems = groupedItems[category]!;
        final categoryTotal = categoryItems.fold<double>(
          0.0,
          (sum, item) => sum + (item.quantity * item.unitPrice),
        );

        return _buildCategoryNode(
          category: category,
          items: categoryItems,
          totalValue: categoryTotal,
        );
      }).toList(),
    );
  }

  /// 构建分类节点
  Widget _buildCategoryNode({
    required String category,
    required List<domain.BomItem> items,
    required double totalValue,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          initiallyExpanded: true,
          leading: Icon(
            _getCategoryIcon(category),
            color: _getCategoryColor(category),
            size: 24,
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  category,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getCategoryColor(category).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${items.length}项',
                  style: TextStyle(
                    color: _getCategoryColor(category),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          subtitle: Text(
            '总价值: ¥${totalValue.toStringAsFixed(2)}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          children: items.map((item) => _buildBomItemNode(item)).toList(),
        ),
      ),
    );
  }

  /// 构建BOM项目节点
  Widget _buildBomItemNode(domain.BomItem item) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: EnhancedBomItemCardWidget(
        item: item,
        isEditable: true,
        showCost: true,
        onEdit: (_) => _showEditBomItemDialog(item),
        onDelete: (_) => _showDeleteBomItemDialog(item),
      ),
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case '电力系统':
        return Icons.electrical_services;
      case '水路系统':
        return Icons.water_drop;
      case '内饰改装':
        return Icons.chair;
      case '外观改装':
        return Icons.directions_car;
      case '储物方案':
        return Icons.storage;
      case '床铺设计':
        return Icons.bed;
      case '厨房改装':
        return Icons.kitchen;
      case '卫浴改装':
        return Icons.bathroom;
      case '安全设备':
        return Icons.security;
      case '通讯设备':
        return Icons.wifi;
      case '娱乐设备':
        return Icons.tv;
      default:
        return Icons.category;
    }
  }

  /// 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case '电力系统':
        return Colors.amber;
      case '水路系统':
        return Colors.blue;
      case '内饰改装':
        return Colors.brown;
      case '外观改装':
        return Colors.red;
      case '储物方案':
        return Colors.green;
      case '床铺设计':
        return Colors.purple;
      case '厨房改装':
        return Colors.orange;
      case '卫浴改装':
        return Colors.cyan;
      case '安全设备':
        return Colors.deepOrange;
      case '通讯设备':
        return Colors.indigo;
      case '娱乐设备':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }
}