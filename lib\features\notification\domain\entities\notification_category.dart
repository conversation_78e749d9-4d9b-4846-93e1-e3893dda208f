import 'package:freezed_annotation/freezed_annotation.dart';
import 'notification.dart';

part 'notification_category.freezed.dart';
part 'notification_category.g.dart';

/// 通知分类实体
@freezed
class NotificationCategory with _$NotificationCategory {
  const factory NotificationCategory({
    required String id,
    required String name,
    required String description,
    String? iconName,
    int? color,
    @Default(NotificationPriority.normal) NotificationPriority defaultPriority,
    @Default(true) bool isEnabled,
    @Default(false) bool isCustom,
    @Default(true) bool allowSound,
    @Default(true) bool allowVibration,
    @Default(true) bool showBadge,
    @Default([]) List<NotificationType> includedTypes,
    @Default([]) List<String> keywords,
    @Default([]) List<String> excludeKeywords,
    @Default({}) Map<String, dynamic> settings,
    required DateTime createdAt,
    DateTime? updatedAt,
    String? userId, // 自定义分类的创建者
  }) = _NotificationCategory;

  factory NotificationCategory.fromJson(Map<String, dynamic> json) => 
      _$NotificationCategoryFromJson(json);
}

/// 通知规则实体
@freezed
class NotificationRule with _$NotificationRule {
  const factory NotificationRule({
    required String id,
    required String categoryId,
    required String name,
    required String description,
    @Default(true) bool isEnabled,
    @Default(0) int priority,
    required Map<String, dynamic> conditions,
    required List<String> actions,
    @Default({}) Map<String, dynamic> metadata,
    required DateTime createdAt,
    DateTime? updatedAt,
  }) = _NotificationRule;

  factory NotificationRule.fromJson(Map<String, dynamic> json) => 
      _$NotificationRuleFromJson(json);
}

/// 通知分类扩展方法
extension NotificationCategoryX on NotificationCategory {
  /// 获取分类颜色
  int get categoryColor {
    return color ?? _getDefaultColor();
  }

  /// 获取分类图标
  String get categoryIcon {
    return iconName ?? _getDefaultIcon();
  }

  /// 检查通知是否匹配此分类
  bool matchesNotification(Notification notification) {
    // 检查类型匹配
    if (includedTypes.isNotEmpty && !includedTypes.contains(notification.type)) {
      return false;
    }

    // 检查关键词匹配
    if (keywords.isNotEmpty) {
      final content = '${notification.title} ${notification.message}'.toLowerCase();
      final hasKeyword = keywords.any((keyword) => content.contains(keyword.toLowerCase()));
      if (!hasKeyword) return false;
    }

    // 检查排除关键词
    if (excludeKeywords.isNotEmpty) {
      final content = '${notification.title} ${notification.message}'.toLowerCase();
      final hasExcludeKeyword = excludeKeywords.any((keyword) => content.contains(keyword.toLowerCase()));
      if (hasExcludeKeyword) return false;
    }

    return true;
  }

  /// 获取默认颜色
  int _getDefaultColor() {
    switch (name.toLowerCase()) {
      case 'system':
      case '系统':
        return 0xFF2196F3; // 蓝色
      case 'project':
      case '项目':
        return 0xFF4CAF50; // 绿色
      case 'reminder':
      case '提醒':
        return 0xFFFF9800; // 橙色
      case 'warning':
      case '警告':
        return 0xFFFF5722; // 深橙色
      case 'error':
      case '错误':
        return 0xFFF44336; // 红色
      default:
        return 0xFF9C27B0; // 紫色
    }
  }

  /// 获取默认图标
  String _getDefaultIcon() {
    switch (name.toLowerCase()) {
      case 'system':
      case '系统':
        return 'system_update';
      case 'project':
      case '项目':
        return 'folder';
      case 'reminder':
      case '提醒':
        return 'alarm';
      case 'warning':
      case '警告':
        return 'warning';
      case 'error':
      case '错误':
        return 'error';
      default:
        return 'notifications';
    }
  }

  /// 创建系统分类
  factory NotificationCategory.system() {
    return NotificationCategory(
      id: 'system',
      name: '系统通知',
      description: '系统相关的通知消息',
      iconName: 'system_update',
      color: 0xFF2196F3,
      defaultPriority: NotificationPriority.normal,
      includedTypes: [NotificationType.system],
      createdAt: DateTime.now(),
    );
  }

  /// 创建项目分类
  factory NotificationCategory.project() {
    return NotificationCategory(
      id: 'project',
      name: '项目通知',
      description: '项目相关的通知消息',
      iconName: 'folder',
      color: 0xFF4CAF50,
      defaultPriority: NotificationPriority.normal,
      includedTypes: [NotificationType.project, NotificationType.bom, NotificationType.timeline],
      createdAt: DateTime.now(),
    );
  }

  /// 创建提醒分类
  factory NotificationCategory.reminder() {
    return NotificationCategory(
      id: 'reminder',
      name: '提醒通知',
      description: '重要的提醒消息',
      iconName: 'alarm',
      color: 0xFFFF9800,
      defaultPriority: NotificationPriority.high,
      includedTypes: [NotificationType.reminder],
      createdAt: DateTime.now(),
    );
  }

  /// 创建警告分类
  factory NotificationCategory.warning() {
    return NotificationCategory(
      id: 'warning',
      name: '警告通知',
      description: '需要注意的警告消息',
      iconName: 'warning',
      color: 0xFFFF5722,
      defaultPriority: NotificationPriority.high,
      includedTypes: [NotificationType.warning],
      createdAt: DateTime.now(),
    );
  }

  /// 创建错误分类
  factory NotificationCategory.error() {
    return NotificationCategory(
      id: 'error',
      name: '错误通知',
      description: '系统错误和异常消息',
      iconName: 'error',
      color: 0xFFF44336,
      defaultPriority: NotificationPriority.urgent,
      includedTypes: [NotificationType.error],
      createdAt: DateTime.now(),
    );
  }

  /// 创建社交分类
  factory NotificationCategory.social() {
    return NotificationCategory(
      id: 'social',
      name: '社交通知',
      description: '评论、分享等社交相关通知',
      iconName: 'people',
      color: 0xFF9C27B0,
      defaultPriority: NotificationPriority.normal,
      includedTypes: [NotificationType.comment, NotificationType.share],
      createdAt: DateTime.now(),
    );
  }

  /// 创建材料分类
  factory NotificationCategory.material() {
    return NotificationCategory(
      id: 'material',
      name: '材料通知',
      description: '材料相关的通知消息',
      iconName: 'inventory',
      color: 0xFF607D8B,
      defaultPriority: NotificationPriority.normal,
      includedTypes: [NotificationType.material],
      keywords: ['材料', '价格', '库存', '采购'],
      createdAt: DateTime.now(),
    );
  }

  /// 获取所有默认分类
  static List<NotificationCategory> getDefaultCategories() {
    return [
      NotificationCategory.system(),
      NotificationCategory.project(),
      NotificationCategory.reminder(),
      NotificationCategory.warning(),
      NotificationCategory.error(),
      NotificationCategory.social(),
      NotificationCategory.material(),
    ];
  }
}

/// 通知规则扩展方法
extension NotificationRuleX on NotificationRule {
  /// 检查通知是否匹配此规则
  bool matchesNotification(Notification notification) {
    if (!isEnabled) return false;

    // 检查条件
    for (final entry in conditions.entries) {
      final key = entry.key;
      final value = entry.value;

      switch (key) {
        case 'type':
          if (notification.type.name != value) return false;
          break;
        case 'priority':
          if (notification.priority.name != value) return false;
          break;
        case 'title_contains':
          if (!notification.title.toLowerCase().contains(value.toString().toLowerCase())) {
            return false;
          }
          break;
        case 'message_contains':
          if (!notification.message.toLowerCase().contains(value.toString().toLowerCase())) {
            return false;
          }
          break;
        case 'source_type':
          if (notification.sourceType != value) return false;
          break;
        case 'has_action':
          if ((notification.actionUrl != null) != (value as bool)) return false;
          break;
        default:
          // 检查自定义条件
          final notificationData = notification.data[key];
          if (notificationData != value) return false;
      }
    }

    return true;
  }

  /// 应用规则动作
  Notification applyActions(Notification notification) {
    var updatedNotification = notification;

    for (final action in actions) {
      switch (action) {
        case 'mark_as_read':
          updatedNotification = updatedNotification.markAsRead();
          break;
        case 'set_high_priority':
          updatedNotification = updatedNotification.copyWith(priority: NotificationPriority.high);
          break;
        case 'set_low_priority':
          updatedNotification = updatedNotification.copyWith(priority: NotificationPriority.low);
          break;
        case 'archive':
          updatedNotification = updatedNotification.archive();
          break;
        case 'add_urgent_flag':
          updatedNotification = updatedNotification.copyWith(
            metadata: {
              ...updatedNotification.metadata,
              'urgent': true,
            },
          );
          break;
        default:
          // 处理自定义动作
          if (action.startsWith('set_metadata:')) {
            final parts = action.split(':');
            if (parts.length == 3) {
              final key = parts[1];
              final value = parts[2];
              updatedNotification = updatedNotification.copyWith(
                metadata: {
                  ...updatedNotification.metadata,
                  key: value,
                },
              );
            }
          }
      }
    }

    return updatedNotification;
  }

  /// 创建标题包含规则
  factory NotificationRule.titleContains({
    required String categoryId,
    required String name,
    required String keyword,
    List<String> actions = const [],
  }) {
    return NotificationRule(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      categoryId: categoryId,
      name: name,
      description: '标题包含"$keyword"的通知',
      conditions: {'title_contains': keyword},
      actions: actions,
      createdAt: DateTime.now(),
    );
  }

  /// 创建类型匹配规则
  factory NotificationRule.typeMatch({
    required String categoryId,
    required String name,
    required NotificationType type,
    List<String> actions = const [],
  }) {
    return NotificationRule(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      categoryId: categoryId,
      name: name,
      description: '类型为${type.name}的通知',
      conditions: {'type': type.name},
      actions: actions,
      createdAt: DateTime.now(),
    );
  }

  /// 创建优先级匹配规则
  factory NotificationRule.priorityMatch({
    required String categoryId,
    required String name,
    required NotificationPriority priority,
    List<String> actions = const [],
  }) {
    return NotificationRule(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      categoryId: categoryId,
      name: name,
      description: '优先级为${priority.name}的通知',
      conditions: {'priority': priority.name},
      actions: actions,
      createdAt: DateTime.now(),
    );
  }
}
