import 'package:fpdart/fpdart.dart';
import '../../../../core/error/failures.dart';
import '../entities/product_specification.dart';

/// 产品规格服务接口
/// 
/// 负责管理产品规格的CRUD操作和业务逻辑
abstract class SpecificationService {
  /// 根据材料ID获取产品规格
  Future<Either<Failure, ProductSpecification?>> getSpecificationByMaterialId(String materialId);
  
  /// 根据规格ID获取产品规格
  Future<Either<Failure, ProductSpecification?>> getSpecificationById(String specificationId);
  
  /// 创建产品规格
  Future<Either<Failure, ProductSpecification>> createSpecification(ProductSpecification specification);
  
  /// 更新产品规格
  Future<Either<Failure, ProductSpecification>> updateSpecification(ProductSpecification specification);
  
  /// 删除产品规格
  Future<Either<Failure, void>> deleteSpecification(String specificationId);
  
  /// 根据分类获取规格模板
  Future<Either<Failure, Map<String, dynamic>>> getSpecificationTemplate(String category);
  
  /// 批量获取规格（用于列表展示）
  Future<Either<Failure, Map<String, ProductSpecification>>> getSpecificationsByMaterialIds(List<String> materialIds);
  
  /// 搜索规格
  Future<Either<Failure, List<ProductSpecification>>> searchSpecifications({
    String? category,
    Map<String, dynamic>? filters,
    int limit = 20,
    int offset = 0,
  });
  
  /// 验证规格数据
  Either<Failure, void> validateSpecification(ProductSpecification specification);
  
  /// 从模板创建规格
  Future<Either<Failure, ProductSpecification>> createFromTemplate(
    String materialId,
    String category,
    Map<String, dynamic> templateValues,
  );
}
