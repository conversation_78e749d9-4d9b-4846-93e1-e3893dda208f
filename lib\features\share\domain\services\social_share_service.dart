import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';

/// 社交媒体平台
enum SocialPlatform {
  wechat,
  wechatMoments,
  qq,
  qzone,
  weibo,
  douyin,
  xiaohongshu,
  system,
  copyLink,
  more,
}

/// 分享内容类型
enum SocialShareContentType {
  text,
  image,
  video,
  link,
  miniProgram,
}

/// 分享内容
class SocialShareContent {
  final SocialShareContentType type;
  final String title;
  final String description;
  final String? url;
  final String? imageUrl;
  final String? videoUrl;
  final List<String>? imagePaths;
  final String? videoPath;
  final Map<String, dynamic>? extraData;

  const SocialShareContent({
    required this.type,
    required this.title,
    required this.description,
    this.url,
    this.imageUrl,
    this.videoUrl,
    this.imagePaths,
    this.videoPath,
    this.extraData,
  });

  /// 创建文本分享内容
  factory SocialShareContent.text({
    required String title,
    required String description,
    String? url,
  }) {
    return SocialShareContent(
      type: SocialShareContentType.text,
      title: title,
      description: description,
      url: url,
    );
  }

  /// 创建图片分享内容
  factory SocialShareContent.image({
    required String title,
    required String description,
    String? imageUrl,
    List<String>? imagePaths,
    String? url,
  }) {
    return SocialShareContent(
      type: SocialShareContentType.image,
      title: title,
      description: description,
      imageUrl: imageUrl,
      imagePaths: imagePaths,
      url: url,
    );
  }

  /// 创建视频分享内容
  factory SocialShareContent.video({
    required String title,
    required String description,
    String? videoUrl,
    String? videoPath,
    String? thumbnailUrl,
    String? url,
  }) {
    return SocialShareContent(
      type: SocialShareContentType.video,
      title: title,
      description: description,
      videoUrl: videoUrl,
      videoPath: videoPath,
      imageUrl: thumbnailUrl,
      url: url,
    );
  }

  /// 创建链接分享内容
  factory SocialShareContent.link({
    required String title,
    required String description,
    required String url,
    String? imageUrl,
  }) {
    return SocialShareContent(
      type: SocialShareContentType.link,
      title: title,
      description: description,
      url: url,
      imageUrl: imageUrl,
    );
  }
}

/// 分享结果
class SocialShareResult {
  final bool success;
  final SocialPlatform platform;
  final String? error;
  final Map<String, dynamic>? data;

  const SocialShareResult({
    required this.success,
    required this.platform,
    this.error,
    this.data,
  });

  factory SocialShareResult.success(SocialPlatform platform, [Map<String, dynamic>? data]) {
    return SocialShareResult(
      success: true,
      platform: platform,
      data: data,
    );
  }

  factory SocialShareResult.failure(SocialPlatform platform, String error) {
    return SocialShareResult(
      success: false,
      platform: platform,
      error: error,
    );
  }
}

/// 社交媒体分享服务接口
abstract class SocialShareService {
  /// 分享到指定平台
  Future<Either<Failure, SocialShareResult>> shareToSocial({
    required SocialPlatform platform,
    required SocialShareContent content,
  });

  /// 显示分享面板
  Future<Either<Failure, SocialShareResult>> showShareSheet({
    required SocialShareContent content,
    List<SocialPlatform>? platforms,
  });

  /// 检查平台是否可用
  Future<Either<Failure, bool>> isPlatformAvailable(SocialPlatform platform);

  /// 获取可用平台列表
  Future<Either<Failure, List<SocialPlatform>>> getAvailablePlatforms();

  /// 复制内容到剪贴板
  Future<Either<Failure, void>> copyToClipboard(String content);

  /// 保存图片到相册
  Future<Either<Failure, void>> saveImageToGallery(String imagePath);

  /// 保存视频到相册
  Future<Either<Failure, void>> saveVideoToGallery(String videoPath);

  /// 生成分享统计
  Future<Either<Failure, void>> recordShareEvent({
    required SocialPlatform platform,
    required String contentId,
    required String contentType,
  });
}

/// 社交平台扩展方法
extension SocialPlatformX on SocialPlatform {
  /// 获取平台名称
  String get displayName {
    switch (this) {
      case SocialPlatform.wechat:
        return '微信';
      case SocialPlatform.wechatMoments:
        return '朋友圈';
      case SocialPlatform.qq:
        return 'QQ';
      case SocialPlatform.qzone:
        return 'QQ空间';
      case SocialPlatform.weibo:
        return '微博';
      case SocialPlatform.douyin:
        return '抖音';
      case SocialPlatform.xiaohongshu:
        return '小红书';
      case SocialPlatform.system:
        return '系统分享';
      case SocialPlatform.copyLink:
        return '复制链接';
      case SocialPlatform.more:
        return '更多';
    }
  }

  /// 获取平台图标
  String get iconAsset {
    switch (this) {
      case SocialPlatform.wechat:
        return 'assets/icons/wechat.png';
      case SocialPlatform.wechatMoments:
        return 'assets/icons/wechat_moments.png';
      case SocialPlatform.qq:
        return 'assets/icons/qq.png';
      case SocialPlatform.qzone:
        return 'assets/icons/qzone.png';
      case SocialPlatform.weibo:
        return 'assets/icons/weibo.png';
      case SocialPlatform.douyin:
        return 'assets/icons/douyin.png';
      case SocialPlatform.xiaohongshu:
        return 'assets/icons/xiaohongshu.png';
      case SocialPlatform.system:
        return 'assets/icons/share.png';
      case SocialPlatform.copyLink:
        return 'assets/icons/copy.png';
      case SocialPlatform.more:
        return 'assets/icons/more.png';
    }
  }

  /// 获取平台颜色
  int get brandColor {
    switch (this) {
      case SocialPlatform.wechat:
      case SocialPlatform.wechatMoments:
        return 0xFF07C160;
      case SocialPlatform.qq:
      case SocialPlatform.qzone:
        return 0xFF12B7F5;
      case SocialPlatform.weibo:
        return 0xFFE6162D;
      case SocialPlatform.douyin:
        return 0xFFFF0050;
      case SocialPlatform.xiaohongshu:
        return 0xFFFF2442;
      case SocialPlatform.system:
        return 0xFF007AFF;
      case SocialPlatform.copyLink:
        return 0xFF34C759;
      case SocialPlatform.more:
        return 0xFF8E8E93;
    }
  }

  /// 是否支持图片分享
  bool get supportsImage {
    switch (this) {
      case SocialPlatform.wechat:
      case SocialPlatform.wechatMoments:
      case SocialPlatform.qq:
      case SocialPlatform.qzone:
      case SocialPlatform.weibo:
      case SocialPlatform.douyin:
      case SocialPlatform.xiaohongshu:
      case SocialPlatform.system:
        return true;
      case SocialPlatform.copyLink:
      case SocialPlatform.more:
        return false;
    }
  }

  /// 是否支持视频分享
  bool get supportsVideo {
    switch (this) {
      case SocialPlatform.wechatMoments:
      case SocialPlatform.qzone:
      case SocialPlatform.weibo:
      case SocialPlatform.douyin:
      case SocialPlatform.xiaohongshu:
      case SocialPlatform.system:
        return true;
      case SocialPlatform.wechat:
      case SocialPlatform.qq:
      case SocialPlatform.copyLink:
      case SocialPlatform.more:
        return false;
    }
  }

  /// 是否支持链接分享
  bool get supportsLink {
    switch (this) {
      case SocialPlatform.wechat:
      case SocialPlatform.wechatMoments:
      case SocialPlatform.qq:
      case SocialPlatform.qzone:
      case SocialPlatform.weibo:
      case SocialPlatform.system:
      case SocialPlatform.copyLink:
        return true;
      case SocialPlatform.douyin:
      case SocialPlatform.xiaohongshu:
      case SocialPlatform.more:
        return false;
    }
  }

  /// 获取分享文本模板
  String getShareTextTemplate(String title, String description, String? url) {
    switch (this) {
      case SocialPlatform.wechat:
        return '$title\n\n$description${url != null ? '\n\n$url' : ''}';
      
      case SocialPlatform.wechatMoments:
        return '$title\n$description${url != null ? '\n$url' : ''} #VanHub改装宝#';
      
      case SocialPlatform.qq:
        return '【$title】\n$description${url != null ? '\n\n查看详情：$url' : ''}';
      
      case SocialPlatform.qzone:
        return '$title\n\n$description${url != null ? '\n\n🔗 $url' : ''}\n\n来自VanHub改装宝';
      
      case SocialPlatform.weibo:
        return '【VanHub改装宝】$title\n$description${url != null ? '\n$url' : ''} #房车改装# #DIY#';
      
      case SocialPlatform.douyin:
        return '$title\n$description\n#房车改装 #DIY #VanHub';
      
      case SocialPlatform.xiaohongshu:
        return '$title\n\n$description\n\n#房车改装 #DIY #VanHub改装宝';
      
      default:
        return '$title\n\n$description${url != null ? '\n\n$url' : ''}';
    }
  }
}

/// 社交媒体分享服务实现
class SocialShareServiceImpl implements SocialShareService {
  @override
  Future<Either<Failure, SocialShareResult>> shareToSocial({
    required SocialPlatform platform,
    required SocialShareContent content,
  }) async {
    try {
      switch (platform) {
        case SocialPlatform.copyLink:
          return await _copyToClipboard(content);
        case SocialPlatform.system:
          return await _shareToSystem(content);
        default:
          return await _shareToThirdParty(platform, content);
      }
    } catch (e) {
      return Left(UnknownFailure(message: '分享失败: $e'));
    }
  }

  @override
  Future<Either<Failure, SocialShareResult>> showShareSheet({
    required SocialShareContent content,
    List<SocialPlatform>? platforms,
  }) async {
    try {
      // TODO: 实现分享面板显示
      // 这里应该显示一个底部弹窗，包含所有可用的分享选项
      return Right(SocialShareResult.success(SocialPlatform.system));
    } catch (e) {
      return Left(UnknownFailure(message: '显示分享面板失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isPlatformAvailable(SocialPlatform platform) async {
    try {
      switch (platform) {
        case SocialPlatform.copyLink:
        case SocialPlatform.system:
        case SocialPlatform.more:
          return const Right(true);
        default:
          // TODO: 检查第三方应用是否已安装
          return const Right(false);
      }
    } catch (e) {
      return Left(UnknownFailure(message: '检查平台可用性失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SocialPlatform>>> getAvailablePlatforms() async {
    try {
      final availablePlatforms = <SocialPlatform>[];

      for (final platform in SocialPlatform.values) {
        final isAvailable = await isPlatformAvailable(platform);
        if (isAvailable.fold((l) => false, (r) => r)) {
          availablePlatforms.add(platform);
        }
      }

      return Right(availablePlatforms);
    } catch (e) {
      return Left(UnknownFailure(message: '获取可用平台失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> copyToClipboard(String content) async {
    try {
      // TODO: 实现剪贴板复制
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '复制到剪贴板失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveImageToGallery(String imagePath) async {
    try {
      // TODO: 实现保存图片到相册
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '保存图片失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveVideoToGallery(String videoPath) async {
    try {
      // TODO: 实现保存视频到相册
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '保存视频失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> recordShareEvent({
    required SocialPlatform platform,
    required String contentId,
    required String contentType,
  }) async {
    try {
      // TODO: 记录分享事件到分析系统
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '记录分享事件失败: $e'));
    }
  }

  /// 复制到剪贴板
  Future<Either<Failure, SocialShareResult>> _copyToClipboard(SocialShareContent content) async {
    try {
      final text = content.url ?? '${content.title}\n${content.description}';
      final result = await copyToClipboard(text);

      return result.fold(
        (failure) => Left(failure),
        (_) => Right(SocialShareResult.success(SocialPlatform.copyLink)),
      );
    } catch (e) {
      return Right(SocialShareResult.failure(SocialPlatform.copyLink, e.toString()));
    }
  }

  /// 分享到系统
  Future<Either<Failure, SocialShareResult>> _shareToSystem(SocialShareContent content) async {
    try {
      // TODO: 使用share_plus插件实现系统分享
      return Right(SocialShareResult.success(SocialPlatform.system));
    } catch (e) {
      return Right(SocialShareResult.failure(SocialPlatform.system, e.toString()));
    }
  }

  /// 分享到第三方平台
  Future<Either<Failure, SocialShareResult>> _shareToThirdParty(
    SocialPlatform platform,
    SocialShareContent content,
  ) async {
    try {
      // TODO: 实现第三方平台分享
      // 这里需要集成各个平台的SDK
      return Right(SocialShareResult.success(platform));
    } catch (e) {
      return Right(SocialShareResult.failure(platform, e.toString()));
    }
  }
}
