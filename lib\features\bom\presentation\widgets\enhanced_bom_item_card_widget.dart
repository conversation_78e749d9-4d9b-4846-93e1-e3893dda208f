import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../domain/entities/bom_item.dart';

/// 增强型BOM项目卡片组件
/// 
/// 用于显示BOM项目的详细信息，包括状态、价格、数量等
/// 支持编辑、删除等操作
class EnhancedBomItemCardWidget extends ConsumerWidget {
  /// BOM项目
  final BomItem item;
  
  /// 是否可编辑
  final bool isEditable;
  
  /// 是否显示成本
  final bool showCost;
  
  /// 编辑回调
  final Function(BomItem)? onEdit;
  
  /// 删除回调
  final Function(BomItem)? onDelete;
  
  /// 状态变更回调
  final Function(BomItem, BomItemStatus)? onStatusChange;

  const EnhancedBomItemCardWidget({
    super.key,
    required this.item,
    this.isEditable = true,
    this.showCost = true,
    this.onEdit,
    this.onDelete,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final status = item.status;
    final statusConfig = _getStatusConfig(status);
    
    return Card(
      margin: EdgeInsets.only(bottom: VanHubSpacing.sm),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
        side: BorderSide(
          color: VanHubColors.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      elevation: 1,
      shadowColor: VanHubColors.shadow.withValues(alpha: 0.1),
      child: InkWell(
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
        onTap: () {
          if (isEditable) {
            onEdit?.call(item);
          }
        },
        child: Padding(
          padding: EdgeInsets.all(VanHubSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态
              Row(
                children: [
                  // 图标
                  Container(
                    padding: EdgeInsets.all(VanHubSpacing.sm),
                    decoration: BoxDecoration(
                      color: statusConfig.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
                    ),
                    child: Icon(
                      Icons.inventory_2_outlined,
                      color: statusConfig.color,
                      size: 16,
                    ),
                  ),
                  
                  SizedBox(width: VanHubSpacing.sm),
                  
                  // 标题
                  Expanded(
                    child: Text(
                      item.materialName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // 状态标签
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: VanHubSpacing.sm,
                      vertical: VanHubSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: statusConfig.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(VanHubSpacing.radiusSmall),
                      border: Border.all(
                        color: statusConfig.color.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          statusConfig.icon,
                          color: statusConfig.color,
                          size: 12,
                        ),
                        SizedBox(width: VanHubSpacing.xs),
                        Text(
                          statusConfig.label,
                          style: TextStyle(
                            color: statusConfig.color,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: VanHubSpacing.md),
              
              // 详细信息
              Row(
                children: [
                  // 数量信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '数量',
                          style: TextStyle(
                            fontSize: 12,
                            color: VanHubColors.textSecondary,
                          ),
                        ),
                        SizedBox(height: VanHubSpacing.xs),
                        Row(
                          children: [
                            Text(
                              '${item.quantity}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            // 单位显示
                            Text(
                              ' 个',
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // 价格信息
                  if (showCost && item.estimatedPrice != null) ...[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '单价',
                            style: TextStyle(
                              fontSize: 12,
                              color: VanHubColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: VanHubSpacing.xs),
                          Text(
                            '¥${item.estimatedPrice!.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              color: VanHubColors.secondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '小计',
                            style: TextStyle(
                              fontSize: 12,
                              color: VanHubColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: VanHubSpacing.xs),
                          Text(
                            '¥${(item.estimatedPrice! * item.quantity).toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: VanHubColors.secondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              
              // 备注信息
              if (item.notes?.isNotEmpty == true) ...[
                SizedBox(height: VanHubSpacing.md),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(VanHubSpacing.sm),
                  decoration: BoxDecoration(
                    color: VanHubColors.surfaceVariant,
                    borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '备注',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: VanHubColors.textSecondary,
                        ),
                      ),
                      SizedBox(height: VanHubSpacing.xs),
                      Text(
                        item.notes!,
                        style: TextStyle(
                          fontSize: 13,
                          color: VanHubColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // 操作按钮
              if (isEditable) ...[
                SizedBox(height: VanHubSpacing.md),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 状态变更按钮
                    if (onStatusChange != null) ...[
                      _buildStatusButton(
                        label: '更新状态',
                        icon: Icons.update,
                        onPressed: () {
                          _showStatusUpdateDialog(context);
                        },
                      ),
                      SizedBox(width: VanHubSpacing.sm),
                    ],
                    
                    // 编辑按钮
                    if (onEdit != null) ...[
                      _buildActionButton(
                        label: '编辑',
                        icon: Icons.edit_outlined,
                        onPressed: () {
                          onEdit?.call(item);
                        },
                      ),
                      SizedBox(width: VanHubSpacing.sm),
                    ],
                    
                    // 删除按钮
                    if (onDelete != null) ...[
                      _buildActionButton(
                        label: '删除',
                        icon: Icons.delete_outline,
                        color: VanHubColors.error,
                        onPressed: () {
                          _showDeleteConfirmDialog(context);
                        },
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required String label,
    required IconData icon,
    Color? color,
    required VoidCallback onPressed,
  }) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color ?? VanHubColors.primary,
        side: BorderSide(
          color: (color ?? VanHubColors.primary).withValues(alpha: 0.5),
          width: 1,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: VanHubSpacing.sm,
          vertical: VanHubSpacing.xs,
        ),
        textStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建状态按钮
  Widget _buildStatusButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: VanHubColors.primary.withValues(alpha: 0.1),
        foregroundColor: VanHubColors.primary,
        elevation: 0,
        padding: EdgeInsets.symmetric(
          horizontal: VanHubSpacing.sm,
          vertical: VanHubSpacing.xs,
        ),
        textStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context) {
    debugPrint('显示删除确认对话框');
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认删除'),
        content: Text('您确定要删除 "${item.materialName}" 吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onDelete?.call(item);
            },
            child: Text(
              '删除',
              style: TextStyle(color: VanHubColors.error),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示状态更新对话框
  void _showStatusUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('更新状态'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusOption(
              context,
              BomItemStatus.pending,
              '待采购',
              Icons.edit_note_outlined,
              VanHubColors.textSecondary,
            ),
            _buildStatusOption(
              context,
              BomItemStatus.ordered,
              '已下单',
              Icons.shopping_cart_outlined,
              VanHubColors.warning,
            ),
            _buildStatusOption(
              context,
              BomItemStatus.received,
              '已收货',
              Icons.inventory_outlined,
              VanHubColors.info,
            ),
            _buildStatusOption(
              context,
              BomItemStatus.installed,
              '已安装',
              Icons.build_outlined,
              VanHubColors.success,
            ),
            _buildStatusOption(
              context,
              BomItemStatus.cancelled,
              '已取消',
              Icons.cancel_outlined,
              VanHubColors.error,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态选项
  Widget _buildStatusOption(
    BuildContext context,
    BomItemStatus status,
    String label,
    IconData icon,
    Color color,
  ) {
    // 将BomItemStatus转换为字符串进行比较，避免类型不匹配
    final isCurrentStatus = _mapBomItemStatusToString(item.status) == _mapDomainStatusToString(status);
    
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(VanHubSpacing.xs),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(icon, color: color, size: 16),
      ),
      title: Text(label),
      trailing: isCurrentStatus
          ? Icon(Icons.check_circle, color: VanHubColors.success)
          : null,
      selected: isCurrentStatus,
      onTap: isCurrentStatus
          ? null
          : () {
              Navigator.of(context).pop();
              onStatusChange?.call(item, status);
            },
    );
  }

  /// 将BomItem中的BomItemStatus转换为字符串
  String _mapBomItemStatusToString(BomItemStatus status) {
    return status.displayName;
  }

  /// 将BomItemStatus转换为字符串
  String _mapDomainStatusToString(BomItemStatus status) {
    return status.displayName;
  }

  /// 获取状态配置
  _StatusConfig _getStatusConfig(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return _StatusConfig(
          label: '待采购',
          icon: Icons.edit_note_outlined,
          color: VanHubColors.textSecondary,
        );
      case BomItemStatus.ordered:
        return _StatusConfig(
          label: '已下单',
          icon: Icons.shopping_cart_outlined,
          color: VanHubColors.warning,
        );
      case BomItemStatus.received:
        return _StatusConfig(
          label: '已收货',
          icon: Icons.inventory_outlined,
          color: VanHubColors.info,
        );
      case BomItemStatus.installed:
        return _StatusConfig(
          label: '已安装',
          icon: Icons.build_outlined,
          color: VanHubColors.success,
        );
      case BomItemStatus.cancelled:
        return _StatusConfig(
          label: '已取消',
          icon: Icons.cancel_outlined,
          color: VanHubColors.error,
        );
    }
  }
}

/// 状态配置
class _StatusConfig {
  final String label;
  final IconData icon;
  final Color color;

  const _StatusConfig({
    required this.label,
    required this.icon,
    required this.color,
  });
}