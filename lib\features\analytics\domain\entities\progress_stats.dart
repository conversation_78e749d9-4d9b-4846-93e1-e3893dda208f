import 'package:freezed_annotation/freezed_annotation.dart';

part 'progress_stats.freezed.dart';
part 'progress_stats.g.dart';

/// 进度统计实体
/// 
/// 用于分析项目进度和任务完成情况
/// 遵循Clean Architecture和.kiro/specs设计规范
@freezed
class ProgressStats with _$ProgressStats {
  const factory ProgressStats({
    /// 唯一标识符
    required String id,
    
    /// 项目ID
    required String projectId,
    
    /// 统计日期
    required DateTime date,
    
    /// 总任务数
    required int totalTasks,
    
    /// 已完成任务数
    required int completedTasks,
    
    /// 进行中任务数
    required int inProgressTasks,
    
    /// 待开始任务数
    required int pendingTasks,
    
    /// 已取消任务数
    @Default(0) int cancelledTasks,
    
    /// 完成百分比
    required double completionPercentage,
    
    /// 总工时（小时）
    required double totalHours,
    
    /// 已用工时（小时）
    required double usedHours,
    
    /// 剩余工时（小时）
    required double remainingHours,
    
    /// 工时使用率
    required double hoursUtilization,
    
    /// 平均任务完成时间（天）
    double? averageTaskDuration,
    
    /// 预计完成日期
    DateTime? estimatedCompletionDate,
    
    /// 里程碑数量
    @Default(0) int totalMilestones,
    
    /// 已完成里程碑数量
    @Default(0) int completedMilestones,
    
    /// 里程碑完成率
    @Default(0.0) double milestoneCompletionRate,
    
    /// 进度状态
    @Default(ProgressStatus.onTrack) ProgressStatus status,
    
    /// 风险等级
    @Default(RiskLevel.low) RiskLevel riskLevel,
    
    /// 效率评分（0-100）
    @Default(0.0) double efficiencyScore,
    
    /// 质量评分（0-100）
    @Default(0.0) double qualityScore,
    
    /// 团队生产力指数
    @Default(0.0) double productivityIndex,
    
    /// 分类进度统计
    @Default({}) Map<String, CategoryProgress> categoryProgress,
    
    /// 每日进度记录
    @Default([]) List<DailyProgress> dailyProgress,
    
    /// 标签
    @Default([]) List<String> tags,
    
    /// 元数据
    @Default({}) Map<String, dynamic> metadata,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    DateTime? updatedAt,
  }) = _ProgressStats;

  factory ProgressStats.fromJson(Map<String, dynamic> json) => _$ProgressStatsFromJson(json);
}

/// 分类进度统计
@freezed
class CategoryProgress with _$CategoryProgress {
  const factory CategoryProgress({
    required String category,
    required int totalTasks,
    required int completedTasks,
    required double completionRate,
    required double totalHours,
    required double usedHours,
    @Default(ProgressStatus.onTrack) ProgressStatus status,
  }) = _CategoryProgress;

  factory CategoryProgress.fromJson(Map<String, dynamic> json) => _$CategoryProgressFromJson(json);
}

/// 每日进度记录
@freezed
class DailyProgress with _$DailyProgress {
  const factory DailyProgress({
    required DateTime date,
    required int tasksCompleted,
    required double hoursWorked,
    required double progressIncrement,
    @Default([]) List<String> completedTaskIds,
    String? notes,
  }) = _DailyProgress;

  factory DailyProgress.fromJson(Map<String, dynamic> json) => _$DailyProgressFromJson(json);
}

/// 进度状态枚举
@JsonEnum()
enum ProgressStatus {
  /// 超前进度
  @JsonValue('ahead')
  ahead,
  
  /// 按计划进行
  @JsonValue('on_track')
  onTrack,
  
  /// 轻微延迟
  @JsonValue('slightly_behind')
  slightlyBehind,
  
  /// 严重延迟
  @JsonValue('behind')
  behind,
  
  /// 停滞
  @JsonValue('stalled')
  stalled;
  
  String get displayName {
    switch (this) {
      case ProgressStatus.ahead:
        return '超前进度';
      case ProgressStatus.onTrack:
        return '按计划进行';
      case ProgressStatus.slightlyBehind:
        return '轻微延迟';
      case ProgressStatus.behind:
        return '严重延迟';
      case ProgressStatus.stalled:
        return '停滞';
    }
  }
  
  String get color {
    switch (this) {
      case ProgressStatus.ahead:
        return '#4CAF50'; // 绿色
      case ProgressStatus.onTrack:
        return '#2196F3'; // 蓝色
      case ProgressStatus.slightlyBehind:
        return '#FF9800'; // 橙色
      case ProgressStatus.behind:
        return '#F44336'; // 红色
      case ProgressStatus.stalled:
        return '#9E9E9E'; // 灰色
    }
  }
}

/// 风险等级枚举
@JsonEnum()
enum RiskLevel {
  /// 低风险
  @JsonValue('low')
  low,
  
  /// 中风险
  @JsonValue('medium')
  medium,
  
  /// 高风险
  @JsonValue('high')
  high,
  
  /// 极高风险
  @JsonValue('critical')
  critical;
  
  String get displayName {
    switch (this) {
      case RiskLevel.low:
        return '低风险';
      case RiskLevel.medium:
        return '中风险';
      case RiskLevel.high:
        return '高风险';
      case RiskLevel.critical:
        return '极高风险';
    }
  }
  
  String get color {
    switch (this) {
      case RiskLevel.low:
        return '#4CAF50'; // 绿色
      case RiskLevel.medium:
        return '#FF9800'; // 橙色
      case RiskLevel.high:
        return '#F44336'; // 红色
      case RiskLevel.critical:
        return '#9C27B0'; // 紫色
    }
  }
}

/// ProgressStats业务方法扩展
extension ProgressStatsX on ProgressStats {
  /// 是否按计划进行
  bool get isOnTrack => status == ProgressStatus.onTrack || status == ProgressStatus.ahead;
  
  /// 是否延迟
  bool get isBehind => status == ProgressStatus.slightlyBehind || status == ProgressStatus.behind;
  
  /// 是否高风险
  bool get isHighRisk => riskLevel == RiskLevel.high || riskLevel == RiskLevel.critical;
  
  /// 剩余任务数
  int get remainingTasks => totalTasks - completedTasks;
  
  /// 任务完成率
  double get taskCompletionRate => totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
  
  /// 工时完成率
  double get hoursCompletionRate => totalHours > 0 ? (usedHours / totalHours) * 100 : 0;
  
  /// 预计剩余天数
  int? get estimatedRemainingDays {
    if (estimatedCompletionDate == null) return null;
    final now = DateTime.now();
    final difference = estimatedCompletionDate!.difference(now);
    return difference.inDays;
  }
  
  /// 格式化完成百分比
  String get formattedCompletionPercentage => '${completionPercentage.toStringAsFixed(1)}%';
  
  /// 格式化工时使用率
  String get formattedHoursUtilization => '${hoursUtilization.toStringAsFixed(1)}%';
  
  /// 获取进度健康度评分
  double get healthScore {
    double score = 0;
    
    // 完成率权重 40%
    score += completionPercentage * 0.4;
    
    // 效率评分权重 30%
    score += efficiencyScore * 0.3;
    
    // 质量评分权重 20%
    score += qualityScore * 0.2;
    
    // 风险等级权重 10%（风险越低分数越高）
    final riskScore = switch (riskLevel) {
      RiskLevel.low => 100.0,
      RiskLevel.medium => 70.0,
      RiskLevel.high => 40.0,
      RiskLevel.critical => 10.0,
    };
    score += riskScore * 0.1;
    
    return score.clamp(0, 100);
  }
  
  /// 获取进度趋势
  ProgressTrend get progressTrend {
    if (dailyProgress.length < 2) return ProgressTrend.steady;
    
    final recent = dailyProgress.length > 7
        ? dailyProgress.sublist(dailyProgress.length - 7)
        : dailyProgress;
    final avgProgress = recent.map((p) => p.progressIncrement).reduce((a, b) => a + b) / recent.length;
    
    if (avgProgress > 2) return ProgressTrend.accelerating;
    if (avgProgress > 0.5) return ProgressTrend.steady;
    if (avgProgress > 0) return ProgressTrend.slow;
    return ProgressTrend.stagnant;
  }
}

/// 进度趋势枚举
enum ProgressTrend {
  accelerating, // 加速
  steady,       // 稳定
  slow,         // 缓慢
  stagnant,     // 停滞
}

extension ProgressTrendX on ProgressTrend {
  String get displayName {
    switch (this) {
      case ProgressTrend.accelerating:
        return '加速进行';
      case ProgressTrend.steady:
        return '稳定推进';
      case ProgressTrend.slow:
        return '缓慢进行';
      case ProgressTrend.stagnant:
        return '进度停滞';
    }
  }
  
  String get icon {
    switch (this) {
      case ProgressTrend.accelerating:
        return '🚀';
      case ProgressTrend.steady:
        return '📈';
      case ProgressTrend.slow:
        return '🐌';
      case ProgressTrend.stagnant:
        return '⏸️';
    }
  }
}
