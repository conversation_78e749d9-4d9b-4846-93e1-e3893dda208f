import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeline_tile/timeline_tile.dart';

import '../../domain/entities/timeline_event.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';

/// 增强时间轴组件
/// 提供多层次、交互式的改装日志时间轴展示
class EnhancedTimelineWidget extends ConsumerStatefulWidget {
  final String projectId;
  final List<TimelineEvent> events;
  final List<LogEntry> logEntries;
  final TimelineViewMode viewMode;
  final VoidCallback? onRefresh;
  final Function(TimelineEvent)? onEventTap;
  final Function(LogEntry)? onLogEntryTap;
  final bool showInteractions;
  final bool showLearningInsights;

  const EnhancedTimelineWidget({
    super.key,
    required this.projectId,
    required this.events,
    required this.logEntries,
    this.viewMode = TimelineViewMode.detailed,
    this.onRefresh,
    this.onEventTap,
    this.onLogEntryTap,
    this.showInteractions = true,
    this.showLearningInsights = true,
  });

  @override
  ConsumerState<EnhancedTimelineWidget> createState() => _EnhancedTimelineWidgetState();
}

class _EnhancedTimelineWidgetState extends ConsumerState<EnhancedTimelineWidget> {
  TimelineViewMode _currentViewMode = TimelineViewMode.detailed;
  String _selectedFilter = 'all';
  bool _showOnlyProblems = false;
  bool _showOnlyMilestones = false;

  @override
  void initState() {
    super.initState();
    _currentViewMode = widget.viewMode;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildTimelineHeader(),
        _buildTimelineFilters(),
        Expanded(
          child: _buildTimelineContent(),
        ),
      ],
    );
  }

  Widget _buildTimelineHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.timeline,
            color: Theme.of(context).primaryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            '改装时间轴',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          _buildViewModeToggle(),
          const SizedBox(width: 8),
          if (widget.onRefresh != null)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: widget.onRefresh,
              tooltip: '刷新',
            ),
        ],
      ),
    );
  }

  Widget _buildViewModeToggle() {
    return SegmentedButton<TimelineViewMode>(
      segments: const [
        ButtonSegment(
          value: TimelineViewMode.compact,
          icon: Icon(Icons.view_list, size: 16),
          label: Text('紧凑', style: TextStyle(fontSize: 12)),
        ),
        ButtonSegment(
          value: TimelineViewMode.detailed,
          icon: Icon(Icons.view_module, size: 16),
          label: Text('详细', style: TextStyle(fontSize: 12)),
        ),
        ButtonSegment(
          value: TimelineViewMode.learning,
          icon: Icon(Icons.school, size: 16),
          label: Text('学习', style: TextStyle(fontSize: 12)),
        ),
      ],
      selected: {_currentViewMode},
      onSelectionChanged: (Set<TimelineViewMode> selection) {
        setState(() {
          _currentViewMode = selection.first;
        });
      },
    );
  }

  Widget _buildTimelineFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('全部', 'all'),
                  const SizedBox(width: 8),
                  _buildFilterChip('电气系统', 'electrical'),
                  const SizedBox(width: 8),
                  _buildFilterChip('水路系统', 'plumbing'),
                  const SizedBox(width: 8),
                  _buildFilterChip('储物系统', 'storage'),
                  const SizedBox(width: 8),
                  _buildFilterChip('问题记录', 'problems'),
                  const SizedBox(width: 8),
                  _buildFilterChip('里程碑', 'milestones'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = selected ? value : 'all';
          _showOnlyProblems = value == 'problems';
          _showOnlyMilestones = value == 'milestones';
        });
      },
      backgroundColor: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.1) : null,
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
    );
  }

  Widget _buildTimelineContent() {
    final filteredEvents = _getFilteredEvents();
    
    if (filteredEvents.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredEvents.length,
        itemBuilder: (context, index) {
          final event = filteredEvents[index];
          final isFirst = index == 0;
          final isLast = index == filteredEvents.length - 1;
          
          return _buildTimelineItem(event, isFirst, isLast);
        },
      ),
    );
  }

  List<TimelineEvent> _getFilteredEvents() {
    var filtered = widget.events.where((event) {
      if (_selectedFilter == 'all') return true;
      if (_selectedFilter == 'problems') return event.isProblem;
      if (_selectedFilter == 'milestones') return event.eventType == TimelineEventType.milestone;
      // 系统过滤逻辑可以根据需要添加
      return true;
    }).toList();

    // 按时间排序
    filtered.sort((a, b) => b.eventTime.compareTo(a.eventTime));
    return filtered;
  }

  Widget _buildTimelineItem(TimelineEvent event, bool isFirst, bool isLast) {
    switch (_currentViewMode) {
      case TimelineViewMode.compact:
        return _buildCompactTimelineItem(event, isFirst, isLast);
      case TimelineViewMode.detailed:
        return _buildDetailedTimelineItem(event, isFirst, isLast);
      case TimelineViewMode.learning:
        return _buildLearningTimelineItem(event, isFirst, isLast);
    }
  }

  Widget _buildCompactTimelineItem(TimelineEvent event, bool isFirst, bool isLast) {
    return TimelineTile(
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        width: 32,
        height: 32,
        indicator: Container(
          decoration: BoxDecoration(
            color: Color(int.parse(event.eventColor.substring(1), radix: 16) + 0xFF000000),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              event.eventIcon,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: Colors.grey.shade300,
        thickness: 2,
      ),
      endChild: Container(
        margin: const EdgeInsets.only(left: 16, bottom: 16),
        child: Card(
          child: ListTile(
            title: Text(
              event.title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(event.timeDescription),
                if (event.cost != null)
                  Text('成本: ${event.costDisplayText}'),
              ],
            ),
            trailing: event.isProblem
                ? Icon(
                    event.isProblemSolved ? Icons.check_circle : Icons.warning,
                    color: event.isProblemSolved ? Colors.green : Colors.orange,
                  )
                : null,
            onTap: () => widget.onEventTap?.call(event),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailedTimelineItem(TimelineEvent event, bool isFirst, bool isLast) {
    return TimelineTile(
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        width: 40,
        height: 40,
        indicator: Container(
          decoration: BoxDecoration(
            color: Color(int.parse(event.eventColor.substring(1), radix: 16) + 0xFF000000),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              event.eventIcon,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: Colors.grey.shade300,
        thickness: 3,
      ),
      endChild: Container(
        margin: const EdgeInsets.only(left: 20, bottom: 20),
        child: Card(
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEventHeader(event),
                const SizedBox(height: 12),
                if (event.description != null && event.description!.isNotEmpty)
                  _buildEventDescription(event),
                if (event.tags.isNotEmpty)
                  _buildEventTags(event),
                if (event.mediaIds.isNotEmpty)
                  _buildEventMedia(event),
                if (widget.showInteractions)
                  _buildEventInteractions(event),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLearningTimelineItem(TimelineEvent event, bool isFirst, bool isLast) {
    // 学习模式下只显示有学习价值的事件
    if (!event.isImportant && !event.isProblem) {
      return const SizedBox.shrink();
    }

    return _buildDetailedTimelineItem(event, isFirst, isLast);
  }

  Widget _buildEventHeader(TimelineEvent event) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                event.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.access_time, size: 14, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    event.timeDescription,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (event.timeSpentMinutes != null) ...[
                    const SizedBox(width: 12),
                    Icon(Icons.timer, size: 14, color: Colors.grey.shade600),
                    const SizedBox(width: 4),
                    Text(
                      event.timeSpentDisplayText,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
        if (event.cost != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Text(
              event.costDisplayText,
              style: TextStyle(
                fontSize: 12,
                color: Colors.green.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildEventDescription(TimelineEvent event) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        event.description!,
        style: const TextStyle(fontSize: 14),
      ),
    );
  }

  Widget _buildEventTags(TimelineEvent event) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Wrap(
        spacing: 6,
        runSpacing: 6,
        children: event.tags.map((tag) => Chip(
          label: Text(
            tag,
            style: const TextStyle(fontSize: 11),
          ),
          backgroundColor: Colors.blue.shade50,
          side: BorderSide(color: Colors.blue.shade200),
        )).toList(),
      ),
    );
  }

  Widget _buildEventMedia(TimelineEvent event) {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Row(
        children: [
          Icon(Icons.photo_library, size: 16, color: Colors.grey.shade600),
          const SizedBox(width: 4),
          Text(
            '${event.mediaCount}张图片',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventInteractions(TimelineEvent event) {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Row(
        children: [
          _buildInteractionButton(
            icon: Icons.thumb_up_outlined,
            count: event.likeCount,
            onTap: () => _handleLike(event),
          ),
          const SizedBox(width: 16),
          _buildInteractionButton(
            icon: Icons.comment_outlined,
            count: event.commentCount,
            onTap: () => _handleComment(event),
          ),
          const SizedBox(width: 16),
          _buildInteractionButton(
            icon: Icons.share_outlined,
            count: 0,
            onTap: () => _handleShare(event),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: Colors.grey.shade600),
            if (count > 0) ...[
              const SizedBox(width: 4),
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.timeline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '暂无时间轴记录',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始记录您的改装过程吧！',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  void _handleLike(TimelineEvent event) {
    // TODO: 实现点赞功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('点赞事件: ${event.title}')),
    );
  }

  void _handleComment(TimelineEvent event) {
    // TODO: 实现评论功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('评论事件: ${event.title}')),
    );
  }

  void _handleShare(TimelineEvent event) {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('分享事件: ${event.title}')),
    );
  }
}

/// 时间轴视图模式枚举
enum TimelineViewMode {
  /// 紧凑模式
  compact,
  /// 详细模式
  detailed,
  /// 学习模式
  learning,
}
