/// VanHub响应式工具类
/// 
/// 提供响应式设计的工具方法和断点判断
/// 支持5个断点的深度响应式设计
library;

import 'package:flutter/material.dart';

/// 响应式断点枚举
enum VanHubBreakpoint {
  xs,  // 0px - 575px (小手机)
  sm,  // 576px - 767px (大手机)
  md,  // 768px - 1023px (平板)
  lg,  // 1024px - 1439px (笔记本)
  xl,  // 1440px+ (桌面)
}

/// 设备类型枚举
enum VanHubDeviceType {
  mobile,
  tablet,
  desktop,
}

/// 响应式工具类
class VanHubResponsiveUtils {
  VanHubResponsiveUtils._();

  // ============ 断点定义 ============
  
  static const double breakpointXs = 0;
  static const double breakpointSm = 576;
  static const double breakpointMd = 768;
  static const double breakpointLg = 1024;
  static const double breakpointXl = 1440;

  // ============ 断点判断方法 ============
  
  /// 获取当前断点
  static VanHubBreakpoint getBreakpoint(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= breakpointXl) {
      return VanHubBreakpoint.xl;
    } else if (width >= breakpointLg) {
      return VanHubBreakpoint.lg;
    } else if (width >= breakpointMd) {
      return VanHubBreakpoint.md;
    } else if (width >= breakpointSm) {
      return VanHubBreakpoint.sm;
    } else {
      return VanHubBreakpoint.xs;
    }
  }

  /// 获取设备类型
  static VanHubDeviceType getDeviceType(BuildContext context) {
    final breakpoint = getBreakpoint(context);
    
    switch (breakpoint) {
      case VanHubBreakpoint.xs:
      case VanHubBreakpoint.sm:
        return VanHubDeviceType.mobile;
      case VanHubBreakpoint.md:
        return VanHubDeviceType.tablet;
      case VanHubBreakpoint.lg:
      case VanHubBreakpoint.xl:
        return VanHubDeviceType.desktop;
    }
  }

  /// 是否为移动端
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == VanHubDeviceType.mobile;
  }

  /// 是否为平板
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == VanHubDeviceType.tablet;
  }

  /// 是否为桌面端
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == VanHubDeviceType.desktop;
  }

  /// 是否为小屏幕 (xs)
  static bool isXs(BuildContext context) {
    return getBreakpoint(context) == VanHubBreakpoint.xs;
  }

  /// 是否为小手机 (sm)
  static bool isSm(BuildContext context) {
    return getBreakpoint(context) == VanHubBreakpoint.sm;
  }

  /// 是否为中等屏幕 (md)
  static bool isMd(BuildContext context) {
    return getBreakpoint(context) == VanHubBreakpoint.md;
  }

  /// 是否为大屏幕 (lg)
  static bool isLg(BuildContext context) {
    return getBreakpoint(context) == VanHubBreakpoint.lg;
  }

  /// 是否为超大屏幕 (xl)
  static bool isXl(BuildContext context) {
    return getBreakpoint(context) == VanHubBreakpoint.xl;
  }

  // ============ 响应式值获取 ============
  
  /// 获取响应式值
  static T getValue<T>(
    BuildContext context, {
    T? xs,
    T? sm,
    T? md,
    T? lg,
    T? xl,
    required T defaultValue,
  }) {
    final breakpoint = getBreakpoint(context);
    
    switch (breakpoint) {
      case VanHubBreakpoint.xl:
        return xl ?? lg ?? md ?? sm ?? xs ?? defaultValue;
      case VanHubBreakpoint.lg:
        return lg ?? md ?? sm ?? xs ?? defaultValue;
      case VanHubBreakpoint.md:
        return md ?? sm ?? xs ?? defaultValue;
      case VanHubBreakpoint.sm:
        return sm ?? xs ?? defaultValue;
      case VanHubBreakpoint.xs:
        return xs ?? defaultValue;
    }
  }

  /// 获取简化响应式值 (移动端/平板/桌面)
  static T getSimpleValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case VanHubDeviceType.mobile:
        return mobile;
      case VanHubDeviceType.tablet:
        return tablet ?? mobile;
      case VanHubDeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  // ============ 屏幕信息获取 ============
  
  /// 获取屏幕宽度
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// 获取屏幕高度
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// 获取屏幕像素密度
  static double getPixelRatio(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部安全区域高度
  static double getBottomSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 是否显示键盘
  static bool isKeyboardVisible(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }

  // ============ 方向判断 ============
  
  /// 是否为横屏
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// 是否为竖屏
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  // ============ 触摸相关 ============
  
  /// 是否支持触摸
  static bool isTouchDevice(BuildContext context) {
    // 在Flutter Web中，可以通过平台检测
    // 这里简化为移动端和平板支持触摸
    final deviceType = getDeviceType(context);
    return deviceType == VanHubDeviceType.mobile || 
           deviceType == VanHubDeviceType.tablet;
  }

  /// 获取推荐的触摸目标大小
  static double getTouchTargetSize(BuildContext context) {
    if (isMobile(context)) {
      return 44.0; // iOS推荐
    } else if (isTablet(context)) {
      return 48.0; // Material推荐
    } else {
      return 40.0; // 桌面可以更小
    }
  }

  // ============ 布局辅助 ============
  
  /// 获取推荐的列数
  static int getRecommendedColumns(BuildContext context, {
    double itemWidth = 300.0,
    double spacing = 16.0,
  }) {
    final screenWidth = getScreenWidth(context);
    final availableWidth = screenWidth - (spacing * 2);
    final columns = (availableWidth / (itemWidth + spacing)).floor();
    return columns.clamp(1, 6);
  }

  /// 获取容器最大宽度
  static double getMaxContainerWidth(BuildContext context) {
    return getValue<double>(
      context,
      xs: double.infinity,
      sm: double.infinity,
      md: 768.0,
      lg: 1200.0,
      xl: 1440.0,
      defaultValue: double.infinity,
    );
  }

  /// 是否应该显示侧边栏
  static bool shouldShowSidebar(BuildContext context) {
    return isDesktop(context);
  }

  /// 是否应该显示底部导航
  static bool shouldShowBottomNav(BuildContext context) {
    return isMobile(context);
  }

  /// 是否应该显示抽屉
  static bool shouldShowDrawer(BuildContext context) {
    return isMobile(context) || isTablet(context);
  }

  // ============ 性能优化 ============
  
  /// 获取推荐的图片质量
  static double getRecommendedImageQuality(BuildContext context) {
    final pixelRatio = getPixelRatio(context);
    
    if (pixelRatio >= 3.0) {
      return 0.8; // 高密度屏幕，降低质量以节省内存
    } else if (pixelRatio >= 2.0) {
      return 0.9; // 中等密度屏幕
    } else {
      return 1.0; // 低密度屏幕，保持高质量
    }
  }

  /// 获取推荐的动画帧率
  static int getRecommendedFrameRate(BuildContext context) {
    if (isMobile(context)) {
      return 60; // 移动端标准帧率
    } else {
      return 120; // 桌面端可以更高
    }
  }
}

/// 响应式构建器组件
class VanHubResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, VanHubBreakpoint breakpoint)? builder;
  final Widget? xs;
  final Widget? sm;
  final Widget? md;
  final Widget? lg;
  final Widget? xl;
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;

  const VanHubResponsiveBuilder({
    super.key,
    this.builder,
    this.xs,
    this.sm,
    this.md,
    this.lg,
    this.xl,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    if (builder != null) {
      return builder!(context, VanHubResponsiveUtils.getBreakpoint(context));
    }

    // 优先使用设备类型
    if (mobile != null || tablet != null || desktop != null) {
      final deviceType = VanHubResponsiveUtils.getDeviceType(context);
      switch (deviceType) {
        case VanHubDeviceType.mobile:
          return mobile ?? Container();
        case VanHubDeviceType.tablet:
          return tablet ?? mobile ?? Container();
        case VanHubDeviceType.desktop:
          return desktop ?? tablet ?? mobile ?? Container();
      }
    }

    // 使用断点
    final breakpoint = VanHubResponsiveUtils.getBreakpoint(context);
    switch (breakpoint) {
      case VanHubBreakpoint.xs:
        return xs ?? Container();
      case VanHubBreakpoint.sm:
        return sm ?? xs ?? Container();
      case VanHubBreakpoint.md:
        return md ?? sm ?? xs ?? Container();
      case VanHubBreakpoint.lg:
        return lg ?? md ?? sm ?? xs ?? Container();
      case VanHubBreakpoint.xl:
        return xl ?? lg ?? md ?? sm ?? xs ?? Container();
    }
  }
}

/// 响应式扩展
extension VanHubResponsiveExtensions on BuildContext {
  /// 获取当前断点
  VanHubBreakpoint get breakpoint => VanHubResponsiveUtils.getBreakpoint(this);

  /// 获取设备类型
  VanHubDeviceType get deviceType => VanHubResponsiveUtils.getDeviceType(this);

  /// 是否为移动端
  bool get isMobile => VanHubResponsiveUtils.isMobile(this);

  /// 是否为平板
  bool get isTablet => VanHubResponsiveUtils.isTablet(this);

  /// 是否为桌面端
  bool get isDesktop => VanHubResponsiveUtils.isDesktop(this);

  /// 获取响应式值
  T responsiveValue<T>({
    T? xs,
    T? sm,
    T? md,
    T? lg,
    T? xl,
    required T defaultValue,
  }) {
    return VanHubResponsiveUtils.getValue<T>(
      this,
      xs: xs,
      sm: sm,
      md: md,
      lg: lg,
      xl: xl,
      defaultValue: defaultValue,
    );
  }

  /// 获取简化响应式值
  T simpleResponsiveValue<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    return VanHubResponsiveUtils.getSimpleValue<T>(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
}
