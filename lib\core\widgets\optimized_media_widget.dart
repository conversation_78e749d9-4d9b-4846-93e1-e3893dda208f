import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// 媒体加载状态
enum MediaLoadState {
  idle,
  loading,
  loaded,
  error,
}

/// 优化的媒体组件
class OptimizedMediaWidget extends ConsumerStatefulWidget {
  final String url;
  final String? thumbnailUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool enableLazyLoading;
  final bool enablePreloading;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Function()? onTap;
  final Function()? onLoad;
  final Function(String)? onError;

  const OptimizedMediaWidget({
    super.key,
    required this.url,
    this.thumbnailUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.enableLazyLoading = true,
    this.enablePreloading = false,
    this.placeholder,
    this.errorWidget,
    this.onTap,
    this.onLoad,
    this.onError,
  });

  @override
  ConsumerState<OptimizedMediaWidget> createState() => _OptimizedMediaWidgetState();
}

class _OptimizedMediaWidgetState extends ConsumerState<OptimizedMediaWidget>
    with AutomaticKeepAliveClientMixin {
  MediaLoadState _loadState = MediaLoadState.idle;
  bool _isVisible = false;
  bool _shouldLoad = false;

  @override
  bool get wantKeepAlive => _loadState == MediaLoadState.loaded;

  @override
  void initState() {
    super.initState();
    
    // 如果不启用懒加载，立即开始加载
    if (!widget.enableLazyLoading) {
      _shouldLoad = true;
      _loadState = MediaLoadState.loading;
    }

    // 预加载
    if (widget.enablePreloading) {
      _preloadMedia();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    Widget child = _buildMediaContent();

    // 如果启用懒加载，使用VisibilityDetector
    if (widget.enableLazyLoading) {
      child = VisibilityDetector(
        key: Key('media_${widget.url}'),
        onVisibilityChanged: _onVisibilityChanged,
        child: child,
      );
    }

    // 如果有点击事件，包装在GestureDetector中
    if (widget.onTap != null) {
      child = GestureDetector(
        onTap: widget.onTap,
        child: child,
      );
    }

    return child;
  }

  /// 构建媒体内容
  Widget _buildMediaContent() {
    // 根据加载状态显示不同内容
    switch (_loadState) {
      case MediaLoadState.idle:
        return _buildPlaceholder();
      
      case MediaLoadState.loading:
        if (_shouldLoad) {
          return _buildLoadingMedia();
        } else {
          return _buildPlaceholder();
        }
      
      case MediaLoadState.loaded:
        return _buildLoadedMedia();
      
      case MediaLoadState.error:
        return _buildErrorWidget();
    }
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.image,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  /// 构建加载中的媒体
  Widget _buildLoadingMedia() {
    if (_isNetworkUrl(widget.url)) {
      return _buildNetworkImage();
    } else {
      return _buildLocalImage();
    }
  }

  /// 构建已加载的媒体
  Widget _buildLoadedMedia() {
    return _buildLoadingMedia(); // 实际上和加载中是一样的，因为CachedNetworkImage会处理状态
  }

  /// 构建网络图片
  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.url,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      placeholder: (context, url) => _buildLoadingPlaceholder(),
      errorWidget: (context, url, error) {
        _onLoadError(error.toString());
        return _buildErrorWidget();
      },
      imageBuilder: (context, imageProvider) {
        _onLoadSuccess();
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: imageProvider,
              fit: widget.fit,
            ),
          ),
        );
      },
      // 使用缩略图作为占位符
      placeholderFadeInDuration: const Duration(milliseconds: 300),
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
    );
  }

  /// 构建本地图片
  Widget _buildLocalImage() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        image: DecorationImage(
          image: FileImage(File(widget.url)),
          fit: widget.fit,
          onError: (error, stackTrace) {
            _onLoadError(error.toString());
          },
        ),
      ),
    );
  }

  /// 构建加载占位符
  Widget _buildLoadingPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade400,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            '加载失败',
            style: TextStyle(
              color: Colors.red.shade600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// 可见性变化回调
  void _onVisibilityChanged(VisibilityInfo info) {
    final isVisible = info.visibleFraction > 0.1; // 10%可见时开始加载
    
    if (isVisible != _isVisible) {
      setState(() {
        _isVisible = isVisible;
        
        // 当组件变为可见且还未开始加载时，开始加载
        if (isVisible && _loadState == MediaLoadState.idle) {
          _shouldLoad = true;
          _loadState = MediaLoadState.loading;
        }
      });
    }
  }

  /// 加载成功回调
  void _onLoadSuccess() {
    if (_loadState != MediaLoadState.loaded) {
      setState(() {
        _loadState = MediaLoadState.loaded;
      });
      widget.onLoad?.call();
    }
  }

  /// 加载失败回调
  void _onLoadError(String error) {
    if (_loadState != MediaLoadState.error) {
      setState(() {
        _loadState = MediaLoadState.error;
      });
      widget.onError?.call(error);
    }
  }

  /// 预加载媒体
  Future<void> _preloadMedia() async {
    try {
      if (_isNetworkUrl(widget.url)) {
        // 预缓存网络图片
        await precacheImage(
          CachedNetworkImageProvider(widget.url),
          context,
        );
      } else {
        // 预缓存本地图片
        await precacheImage(
          FileImage(File(widget.url)),
          context,
        );
      }
    } catch (e) {
      // 预加载失败不影响主流程
      debugPrint('预加载媒体失败: $e');
    }
  }

  /// 判断是否为网络URL
  bool _isNetworkUrl(String url) {
    return url.startsWith('http://') || url.startsWith('https://');
  }
}

/// 媒体网格组件（支持懒加载）
class OptimizedMediaGrid extends ConsumerWidget {
  final List<String> mediaUrls;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;
  final bool enableLazyLoading;
  final bool enablePreloading;
  final Function(String)? onMediaTap;

  const OptimizedMediaGrid({
    super.key,
    required this.mediaUrls,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
    this.enableLazyLoading = true,
    this.enablePreloading = false,
    this.onMediaTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: mediaUrls.length,
      itemBuilder: (context, index) {
        final url = mediaUrls[index];
        
        return OptimizedMediaWidget(
          url: url,
          enableLazyLoading: enableLazyLoading,
          enablePreloading: enablePreloading,
          onTap: () => onMediaTap?.call(url),
          fit: BoxFit.cover,
        );
      },
    );
  }
}

/// 媒体列表组件（支持懒加载）
class OptimizedMediaList extends ConsumerWidget {
  final List<String> mediaUrls;
  final double itemHeight;
  final bool enableLazyLoading;
  final bool enablePreloading;
  final Function(String)? onMediaTap;

  const OptimizedMediaList({
    super.key,
    required this.mediaUrls,
    this.itemHeight = 200.0,
    this.enableLazyLoading = true,
    this.enablePreloading = false,
    this.onMediaTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.builder(
      itemCount: mediaUrls.length,
      itemBuilder: (context, index) {
        final url = mediaUrls[index];
        
        return Container(
          height: itemHeight,
          margin: const EdgeInsets.only(bottom: 8),
          child: OptimizedMediaWidget(
            url: url,
            height: itemHeight,
            enableLazyLoading: enableLazyLoading,
            enablePreloading: enablePreloading,
            onTap: () => onMediaTap?.call(url),
            fit: BoxFit.cover,
          ),
        );
      },
    );
  }
}
