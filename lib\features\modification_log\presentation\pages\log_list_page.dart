import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/widgets/error_display_widget.dart';
import '../../../../core/error/ui_failure.dart';
import '../../domain/entities/log_entry.dart';
import '../providers/log_provider.dart';
import '../widgets/enhanced_log_entry_card.dart';
import 'log_detail_page.dart';
import 'log_editor_page.dart';

/// 日志列表页面
class LogListPage extends ConsumerStatefulWidget {
  final String projectId;
  final String? systemId;
  final String title;

  const LogListPage({
    super.key,
    required this.projectId,
    this.systemId,
    required this.title,
  });

  @override
  ConsumerState<LogListPage> createState() => _LogListPageState();
}

class _LogListPageState extends ConsumerState<LogListPage> {
  @override
  void initState() {
    super.initState();
    // 延迟加载，避免在Widget构建期间修改Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLogs();
    });
  }

  /// 加载日志
  Future<void> _loadLogs() async {
    try {
      if (widget.systemId != null) {
        await ref.read(logProvider.notifier).getSystemLogs(widget.systemId!);
      } else {
        await ref.read(logProvider.notifier).getProjectLogs(widget.projectId);
      }
    } catch (e) {
      // 处理加载错误
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载日志失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final logState = ref.watch(logProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: 实现搜索功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('搜索功能尚未实现')),
              );
            },
          ),
        ],
      ),
      body: _buildBody(logState),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewLog,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// 构建页面主体
  Widget _buildBody(LogState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.failure != null) {
      return ErrorDisplayWidget(
        failure: UIFailure.unknown(
          message: state.failure!.message,
          details: state.failure!.toString(),
        ),
        onRetry: _loadLogs,
      );
    }

    if (state.logs.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadLogs,
      child: ListView.builder(
        itemCount: state.logs.length,
        itemBuilder: (context, index) {
          final log = state.logs[index];
          return EnhancedLogEntryCard(
            log: log,
            onTap: () => _viewLogDetail(log),
            onEdit: () => _editLog(log),
            onDelete: () => _confirmDeleteLog(log),
          );
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.note_alt_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无改装日志',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击下方按钮创建第一条日志',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewLog,
            icon: const Icon(Icons.add),
            label: const Text('创建日志'),
          ),
        ],
      ),
    );
  }

  /// 创建新日志
  void _createNewLog() async {
    // 如果没有指定系统ID，使用默认值或显示选择器
    final systemId = widget.systemId ?? 'default_system';

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => LogEditorPage(
          projectId: widget.projectId,
          systemId: systemId,
        ),
      ),
    );

    if (result == true) {
      _loadLogs();
    }
  }

  /// 查看日志详情
  void _viewLogDetail(LogEntry log) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LogDetailPage(logId: log.id),
      ),
    );
  }

  /// 编辑日志
  void _editLog(LogEntry log) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => LogEditorPage(
          projectId: widget.projectId,
          systemId: widget.systemId ?? '',
          logEntry: log,
        ),
      ),
    );

    if (result == true) {
      _loadLogs();
    }
  }

  /// 确认删除日志
  void _confirmDeleteLog(LogEntry log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除日志"${log.title}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteLog(log.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 删除日志
  Future<void> _deleteLog(String logId) async {
    final result = await ref.read(logProvider.notifier).deleteLogEntry(logId);

    result.fold(
      (failure) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: ${failure.message}')),
        );
      },
      (_) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('日志已删除')),
        );
      },
    );
  }
}