import 'package:freezed_annotation/freezed_annotation.dart';
import '../services/share_service.dart';

part 'share_config.freezed.dart';
part 'share_config.g.dart';

/// 分享配置实体
@freezed
class ShareConfig with _$ShareConfig {
  const factory ShareConfig({
    @Default(SharePermissionLevel.unlisted) SharePermissionLevel permissionLevel,
    DateTime? expiresAt,
    @Default(true) bool allowComments,
    @Default(true) bool allowLikes,
    @Default(false) bool requiresAuth,
    @Default(false) bool trackViews,
    @Default(false) bool notifyOnAccess,
    @Default([]) List<String> allowedUsers,
    @Default([]) List<String> blockedUsers,
    String? customMessage,
    String? accessPassword,
    @Default({}) Map<String, dynamic> metadata,
  }) = _ShareConfig;

  factory ShareConfig.fromJson(Map<String, dynamic> json) => 
      _$ShareConfigFromJson(json);
}

/// 预设分享配置
class ShareConfigPresets {
  /// 公开分享配置
  static const ShareConfig public = ShareConfig(
    permissionLevel: SharePermissionLevel.public,
    allowComments: true,
    allowLikes: true,
    trackViews: true,
  );

  /// 私密分享配置
  static const ShareConfig private = ShareConfig(
    permissionLevel: SharePermissionLevel.private,
    requiresAuth: true,
    allowComments: false,
    allowLikes: false,
    trackViews: true,
    notifyOnAccess: true,
  );

  /// 临时分享配置（24小时）
  static ShareConfig temporary = ShareConfig(
    permissionLevel: SharePermissionLevel.unlisted,
    expiresAt: DateTime.now().add(const Duration(hours: 24)),
    allowComments: false,
    allowLikes: false,
    trackViews: true,
  );

  /// 团队分享配置
  static const ShareConfig team = ShareConfig(
    permissionLevel: SharePermissionLevel.private,
    requiresAuth: true,
    allowComments: true,
    allowLikes: true,
    trackViews: true,
    notifyOnAccess: false,
  );

  /// 只读分享配置
  static const ShareConfig readOnly = ShareConfig(
    permissionLevel: SharePermissionLevel.unlisted,
    allowComments: false,
    allowLikes: false,
    trackViews: true,
  );
}

/// 分享配置扩展方法
extension ShareConfigX on ShareConfig {
  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否需要密码
  bool get needsPassword {
    return accessPassword != null && accessPassword!.isNotEmpty;
  }

  /// 获取权限级别描述
  String get permissionDescription {
    switch (permissionLevel) {
      case SharePermissionLevel.public:
        return '任何人都可以访问和搜索到';
      case SharePermissionLevel.unlisted:
        return '只有拥有链接的人才能访问';
      case SharePermissionLevel.private:
        return '需要权限验证才能访问';
    }
  }

  /// 获取有效期描述
  String get expirationDescription {
    if (expiresAt == null) return '永不过期';
    
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return '已过期';
    
    final difference = expiresAt!.difference(now);
    if (difference.inDays > 0) {
      return '${difference.inDays}天后过期';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时后过期';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟后过期';
    } else {
      return '即将过期';
    }
  }

  /// 获取功能描述列表
  List<String> get featureDescriptions {
    final features = <String>[];
    
    if (allowComments) features.add('允许评论');
    if (allowLikes) features.add('允许点赞');
    if (trackViews) features.add('统计访问量');
    if (notifyOnAccess) features.add('访问通知');
    if (requiresAuth) features.add('需要登录');
    if (needsPassword) features.add('需要密码');
    
    return features;
  }

  /// 验证配置有效性
  bool get isValid {
    // 检查过期时间
    if (isExpired) return false;
    
    // 检查私有权限是否有允许用户
    if (permissionLevel == SharePermissionLevel.private && 
        allowedUsers.isEmpty && 
        !requiresAuth) {
      return false;
    }
    
    return true;
  }

  /// 创建安全的配置副本（移除敏感信息）
  ShareConfig toSafeConfig() {
    return copyWith(
      accessPassword: null,
      allowedUsers: [],
      blockedUsers: [],
    );
  }

  /// 合并配置
  ShareConfig merge(ShareConfig other) {
    return ShareConfig(
      permissionLevel: other.permissionLevel,
      expiresAt: other.expiresAt ?? expiresAt,
      allowComments: other.allowComments,
      allowLikes: other.allowLikes,
      requiresAuth: other.requiresAuth,
      trackViews: other.trackViews,
      notifyOnAccess: other.notifyOnAccess,
      allowedUsers: [...allowedUsers, ...other.allowedUsers].toSet().toList(),
      blockedUsers: [...blockedUsers, ...other.blockedUsers].toSet().toList(),
      customMessage: other.customMessage ?? customMessage,
      accessPassword: other.accessPassword ?? accessPassword,
      metadata: {...metadata, ...other.metadata},
    );
  }

  /// 检查用户是否被阻止
  bool isUserBlocked(String userId) {
    return blockedUsers.contains(userId);
  }

  /// 检查用户是否被允许
  bool isUserAllowed(String userId) {
    if (permissionLevel == SharePermissionLevel.public) return true;
    if (permissionLevel == SharePermissionLevel.unlisted) return true;
    return allowedUsers.contains(userId);
  }

  /// 添加允许用户
  ShareConfig addAllowedUser(String userId) {
    if (allowedUsers.contains(userId)) return this;
    return copyWith(
      allowedUsers: [...allowedUsers, userId],
    );
  }

  /// 移除允许用户
  ShareConfig removeAllowedUser(String userId) {
    return copyWith(
      allowedUsers: allowedUsers.where((id) => id != userId).toList(),
    );
  }

  /// 添加阻止用户
  ShareConfig addBlockedUser(String userId) {
    if (blockedUsers.contains(userId)) return this;
    return copyWith(
      blockedUsers: [...blockedUsers, userId],
      allowedUsers: allowedUsers.where((id) => id != userId).toList(),
    );
  }

  /// 移除阻止用户
  ShareConfig removeBlockedUser(String userId) {
    return copyWith(
      blockedUsers: blockedUsers.where((id) => id != userId).toList(),
    );
  }

  /// 设置过期时间
  ShareConfig setExpiration(Duration duration) {
    return copyWith(
      expiresAt: DateTime.now().add(duration),
    );
  }

  /// 移除过期时间
  ShareConfig removeExpiration() {
    return copyWith(
      expiresAt: null,
    );
  }

  /// 设置密码保护
  ShareConfig setPassword(String password) {
    return copyWith(
      accessPassword: password,
    );
  }

  /// 移除密码保护
  ShareConfig removePassword() {
    return copyWith(
      accessPassword: null,
    );
  }
}
