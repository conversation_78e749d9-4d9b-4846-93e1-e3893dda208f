import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/timeline_event.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';
import '../widgets/enhanced_timeline_widget.dart';

/// 增强时间轴页面
/// 提供完整的改装日志时间轴体验
class EnhancedTimelinePage extends ConsumerStatefulWidget {
  final String projectId;
  final String? projectName;

  const EnhancedTimelinePage({
    super.key,
    required this.projectId,
    this.projectName,
  });

  @override
  ConsumerState<EnhancedTimelinePage> createState() => _EnhancedTimelinePageState();
}

class _EnhancedTimelinePageState extends ConsumerState<EnhancedTimelinePage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  // 模拟数据 - 在实际应用中应该从Provider获取
  List<TimelineEvent> _mockEvents = [];
  List<LogEntry> _mockLogEntries = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _generateMockData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _generateMockData() {
    final now = DateTime.now();
    
    _mockEvents = [
      TimelineEvent(
        id: '1',
        logEntryId: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        eventType: TimelineEventType.startWork,
        title: '开始安装逆变器',
        description: '准备安装3000W逆变器，选择床下位置，开始准备工具和材料。',
        eventTime: now.subtract(const Duration(hours: 4)),
        createdAt: now.subtract(const Duration(hours: 4)),
        updatedAt: now.subtract(const Duration(hours: 4)),
        authorId: 'user1',
        authorName: '改装达人小王',
        cost: 1200.0,
        timeSpentMinutes: 30,
        tags: ['电气系统', '逆变器', '准备工作'],
        isImportant: true,
        mediaIds: ['media1', 'media2', 'media3'],
        likedByUserIds: ['user2', 'user3'],
        commentCount: 2,
      ),
      TimelineEvent(
        id: '2',
        logEntryId: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        eventType: TimelineEventType.encounterProblem,
        title: '发现空间不够',
        description: '原计划的安装位置空间不够，逆变器无法放入，需要重新选择位置。',
        eventTime: now.subtract(const Duration(hours: 3, minutes: 15)),
        createdAt: now.subtract(const Duration(hours: 3, minutes: 15)),
        updatedAt: now.subtract(const Duration(hours: 3, minutes: 15)),
        authorId: 'user1',
        authorName: '改装达人小王',
        isProblem: true,
        problemStatus: ProblemStatus.solving,
        tags: ['问题记录', '空间不足'],
        mediaIds: ['media4', 'media5'],
        likedByUserIds: ['user2'],
        commentCount: 5,
      ),
      TimelineEvent(
        id: '3',
        logEntryId: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        eventType: TimelineEventType.solveProblem,
        title: '找到新的安装位置',
        description: '经过测量，决定将逆变器安装在驾驶室后方，空间充足且散热良好。',
        eventTime: now.subtract(const Duration(hours: 2, minutes: 45)),
        createdAt: now.subtract(const Duration(hours: 2, minutes: 45)),
        updatedAt: now.subtract(const Duration(hours: 2, minutes: 45)),
        authorId: 'user1',
        authorName: '改装达人小王',
        solution: '重新测量空间，选择驾驶室后方位置',
        isProblem: true,
        problemStatus: ProblemStatus.solved,
        tags: ['问题解决', '重新定位'],
        helperIds: ['user4'], // 老司机阿强提供了建议
        mediaIds: ['media6'],
        likedByUserIds: ['user2', 'user3', 'user4'],
        commentCount: 3,
      ),
      TimelineEvent(
        id: '4',
        logEntryId: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        eventType: TimelineEventType.addMaterial,
        title: '添加额外线材',
        description: '由于位置变更，需要额外的电线和线管，增加了150元成本。',
        eventTime: now.subtract(const Duration(hours: 2, minutes: 30)),
        createdAt: now.subtract(const Duration(hours: 2, minutes: 30)),
        updatedAt: now.subtract(const Duration(hours: 2, minutes: 30)),
        authorId: 'user1',
        authorName: '改装达人小王',
        cost: 150.0,
        relatedBomItemIds: ['bom1', 'bom2'],
        tags: ['材料采购', '线材'],
        mediaIds: ['media7'],
        likedByUserIds: ['user2'],
        commentCount: 1,
      ),
      TimelineEvent(
        id: '5',
        logEntryId: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        eventType: TimelineEventType.completeStep,
        title: '逆变器安装完成',
        description: '成功安装逆变器，接线完成，测试正常工作。记得预留散热空间！',
        eventTime: now.subtract(const Duration(minutes: 30)),
        createdAt: now.subtract(const Duration(minutes: 30)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
        authorId: 'user1',
        authorName: '改装达人小王',
        timeSpentMinutes: 180,
        tags: ['安装完成', '测试通过'],
        isImportant: true,
        mediaIds: ['media8', 'media9', 'media10', 'media11', 'media12'],
        likedByUserIds: ['user2', 'user3', 'user4', 'user5'],
        commentCount: 8,
      ),
      TimelineEvent(
        id: '6',
        logEntryId: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        eventType: TimelineEventType.milestone,
        title: '电气系统第一阶段完成',
        description: '逆变器安装完成，标志着电气系统改装进入下一阶段。',
        eventTime: now.subtract(const Duration(minutes: 15)),
        createdAt: now.subtract(const Duration(minutes: 15)),
        updatedAt: now.subtract(const Duration(minutes: 15)),
        authorId: 'user1',
        authorName: '改装达人小王',
        isImportant: true,
        tags: ['里程碑', '阶段完成'],
        likedByUserIds: ['user2', 'user3', 'user4', 'user5', 'user6'],
        commentCount: 12,
      ),
    ];

    _mockLogEntries = [
      LogEntry(
        id: 'log1',
        projectId: widget.projectId,
        systemId: 'electrical',
        title: '3000W逆变器安装记录',
        content: '''
# 逆变器安装完整记录

## 准备工作
- 工具准备：电钻、螺丝刀、万用表、线鼻子压接钳
- 材料清单：3000W逆变器、电线、线管、螺丝、垫片

## 安装过程
1. **位置选择**：原计划床下，发现空间不足
2. **重新定位**：选择驾驶室后方，空间充足
3. **固定安装**：使用螺丝固定，注意散热空间
4. **接线工作**：正负极接线，接地线连接
5. **测试验证**：开机测试，输出电压正常

## 遇到的问题
- 空间测量不准确，导致重新选位置
- 线材长度不够，临时采购了额外线材

## 经验总结
- 安装前一定要精确测量空间
- 预留足够的散热空间
- 线材要选择阻燃型
- 接线要牢固，定期检查

## 成本统计
- 逆变器：¥1,200
- 额外线材：¥150
- 总计：¥1,350

## 时间统计
- 准备工作：30分钟
- 问题解决：45分钟
- 实际安装：180分钟
- 总计：4小时15分钟
        ''',
        logDate: now.subtract(const Duration(hours: 4)),
        authorId: 'user1',
        authorName: '改装达人小王',
        createdAt: now.subtract(const Duration(hours: 4)),
        updatedAt: now.subtract(const Duration(minutes: 15)),
        status: LogStatus.completed,
        difficulty: DifficultyLevel.intermediate,
        timeSpentMinutes: 255,
        mediaIds: ['media1', 'media2', 'media3', 'media4', 'media5'],
        relatedBomItemIds: ['bom1', 'bom2', 'bom3'],
        totalCost: 1350.0,
        budgetCost: 1200.0,
        actualTimeMinutes: 255,
        estimatedTimeMinutes: 240,
        skillRating: 3,
        userRating: 4.5,
        tags: ['电气系统', '逆变器', '安装记录'],
        location: '驾驶室后方',
        weather: '晴天',
        likedByUserIds: ['user2', 'user3', 'user4', 'user5'],
        bookmarkedByUserIds: ['user2', 'user6'],
        viewCount: 156,
        referenceCount: 23,
        isMilestone: true,
        learningValue: 4.2,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTimelineTab(),
                _buildStatsTab(),
                _buildInsightsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('改装时间轴'),
          if (widget.projectName != null)
            Text(
              widget.projectName!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white70,
              ),
            ),
        ],
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showSearchDialog,
          tooltip: '搜索',
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: _showMoreOptions,
          tooltip: '更多选项',
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).primaryColor,
      child: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: const [
          Tab(
            icon: Icon(Icons.timeline),
            text: '时间轴',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: '统计',
          ),
          Tab(
            icon: Icon(Icons.lightbulb),
            text: '经验',
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return EnhancedTimelineWidget(
      projectId: widget.projectId,
      events: _mockEvents,
      logEntries: _mockLogEntries,
      onRefresh: _handleRefresh,
      onEventTap: _handleEventTap,
      onLogEntryTap: _handleLogEntryTap,
    );
  }

  Widget _buildStatsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '项目统计',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatsGrid(),
          const SizedBox(height: 24),
          Text(
            '系统进度',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSystemProgress(),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '学习洞察',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInsightsList(),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard('总事件', '${_mockEvents.length}', Icons.event, Colors.blue),
        _buildStatCard('总成本', '¥1,350', Icons.attach_money, Colors.green),
        _buildStatCard('总耗时', '4小时15分', Icons.access_time, Colors.orange),
        _buildStatCard('问题解决', '100%', Icons.check_circle, Colors.purple),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemProgress() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildProgressItem('电气系统', 0.6, Colors.blue),
            const SizedBox(height: 12),
            _buildProgressItem('水路系统', 0.3, Colors.cyan),
            const SizedBox(height: 12),
            _buildProgressItem('储物系统', 0.1, Colors.green),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(String system, double progress, Color color) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(system),
        ),
        Expanded(
          flex: 3,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8),
        Text('${(progress * 100).toInt()}%'),
      ],
    );
  }

  Widget _buildInsightsList() {
    final insights = [
      '安装前精确测量空间是关键',
      '预留散热空间避免设备过热',
      '选择阻燃线材提高安全性',
      '定期检查接线确保稳定性',
    ];

    return Expanded(
      child: ListView.builder(
        itemCount: insights.length,
        itemBuilder: (context, index) {
          return Card(
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.amber.shade100,
                child: Icon(Icons.lightbulb, color: Colors.amber.shade700),
              ),
              title: Text(insights[index]),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.thumb_up_outlined),
                    onPressed: () {},
                  ),
                  IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _showAddEventDialog,
      tooltip: '添加事件',
      child: const Icon(Icons.add),
    );
  }

  void _handleRefresh() {
    setState(() {
      _isLoading = true;
    });

    // 模拟刷新延迟
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLoading = false;
      });
    });
  }

  void _handleEventTap(TimelineEvent event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(event.title),
        content: Text(event.description ?? '无详细描述'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _handleLogEntryTap(LogEntry logEntry) {
    // TODO: 导航到日志详情页面
  }

  void _showSearchDialog() {
    // TODO: 实现搜索功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('搜索功能开发中...')),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.download),
            title: const Text('导出时间轴'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现导出功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('分享项目'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现分享功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('时间轴设置'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现设置功能
            },
          ),
        ],
      ),
    );
  }

  void _showAddEventDialog() {
    // TODO: 实现添加事件对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加事件功能开发中...')),
    );
  }
}
