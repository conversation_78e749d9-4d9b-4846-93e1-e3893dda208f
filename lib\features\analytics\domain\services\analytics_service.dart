import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/analytics_data.dart';
import '../entities/cost_trend.dart';
import '../entities/progress_stats.dart';

/// 数据分析服务接口
abstract class AnalyticsService {
  /// 获取项目分析数据
  Future<Either<Failure, AnalyticsData>> getProjectAnalytics(String projectId);
  
  /// 获取用户总体分析数据
  Future<Either<Failure, AnalyticsData>> getUserAnalytics(String userId);
  
  /// 获取成本趋势数据
  Future<Either<Failure, List<CostTrend>>> getCostTrends(
    String projectId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// 获取进度统计数据
  Future<Either<Failure, ProgressStats>> getProgressStats(String projectId);
  
  /// 获取分类成本分布
  Future<Either<Failure, Map<String, double>>> getCategoryCostDistribution(String projectId);
  
  /// 获取月度支出统计
  Future<Either<Failure, Map<String, double>>> getMonthlyExpenses(
    String projectId, {
    int months = 12,
  });
  
  /// 获取材料使用统计
  Future<Either<Failure, Map<String, int>>> getMaterialUsageStats(String projectId);
  
  /// 获取预算vs实际对比
  Future<Either<Failure, Map<String, double>>> getBudgetVsActual(String projectId);
  
  /// 获取项目完成度趋势
  Future<Either<Failure, List<Map<String, dynamic>>>> getCompletionTrends(String projectId);

  /// 获取热门材料分析
  Future<Either<Failure, List<Map<String, dynamic>>>> getPopularMaterialsAnalysis({
    String? category,
    int limit = 10,
  });
}
