import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../../project/data/models/project_export_config.dart';

/// 导出类型
enum ExportType {
  project,
  bom,
  report,
  data,
}

/// 导出格式
enum ExportFormat {
  pdf,
  excel,
  csv,
  json,
}

/// 增强的导出对话框
class EnhancedExportDialog extends ConsumerStatefulWidget {
  final String title;
  final ExportType defaultType;
  final List<ExportType> availableTypes;
  final List<ExportFormat> availableFormats;
  final Function(ExportType, ExportFormat, ProjectExportConfig) onExport;
  final Function()? onPreview;

  const EnhancedExportDialog({
    super.key,
    required this.title,
    required this.defaultType,
    required this.availableTypes,
    required this.availableFormats,
    required this.onExport,
    this.onPreview,
  });

  @override
  ConsumerState<EnhancedExportDialog> createState() => _EnhancedExportDialogState();
}

class _EnhancedExportDialogState extends ConsumerState<EnhancedExportDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ExportType _selectedType;
  late ExportFormat _selectedFormat;
  late ProjectExportConfig _config;
  
  bool _isExporting = false;
  double _exportProgress = 0.0;
  String? _exportStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _selectedType = widget.defaultType;
    _selectedFormat = widget.availableFormats.first;
    _config = const ProjectExportConfig();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(VanHubSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: VanHubSpacing.md),
            _buildTabBar(),
            const SizedBox(height: VanHubSpacing.md),
            Expanded(
              child: _buildTabContent(),
            ),
            const SizedBox(height: VanHubSpacing.md),
            if (_isExporting) _buildProgressSection(),
            _buildActionButtons(),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).scale(begin: const Offset(0.8, 0.8));
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.file_download,
          color: VanHubBrandColors.primary,
          size: 28,
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: VanHubTypography.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '选择导出类型和格式，配置导出选项',
                style: VanHubTypography.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  /// 构建标签栏
  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: VanHubBrandColors.primary,
      unselectedLabelColor: Colors.grey,
      indicatorColor: VanHubBrandColors.primary,
      tabs: const [
        Tab(text: '基本设置', icon: Icon(Icons.settings)),
        Tab(text: '内容选择', icon: Icon(Icons.checklist)),
        Tab(text: '高级选项', icon: Icon(Icons.tune)),
      ],
    );
  }

  /// 构建标签内容
  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildBasicSettings(),
        _buildContentSelection(),
        _buildAdvancedOptions(),
      ],
    );
  }

  /// 构建基本设置
  Widget _buildBasicSettings() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('导出类型'),
          const SizedBox(height: VanHubSpacing.sm),
          _buildTypeSelection(),
          
          const SizedBox(height: VanHubSpacing.lg),
          
          _buildSectionTitle('导出格式'),
          const SizedBox(height: VanHubSpacing.sm),
          _buildFormatSelection(),
          
          const SizedBox(height: VanHubSpacing.lg),
          
          _buildSectionTitle('模板选择'),
          const SizedBox(height: VanHubSpacing.sm),
          _buildTemplateSelection(),
          
          const SizedBox(height: VanHubSpacing.lg),
          
          _buildEstimationCard(),
        ],
      ),
    );
  }

  /// 构建内容选择
  Widget _buildContentSelection() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('包含内容'),
          const SizedBox(height: VanHubSpacing.sm),
          
          _buildCheckboxTile(
            title: '项目概览',
            subtitle: '包含项目基本信息和摘要',
            value: _config.includeOverview,
            onChanged: (value) => _updateConfig(_config.copyWith(includeOverview: value)),
          ),
          
          _buildCheckboxTile(
            title: 'BOM清单',
            subtitle: '包含完整的物料清单',
            value: _config.includeBom,
            onChanged: (value) => _updateConfig(_config.copyWith(includeBom: value)),
          ),
          
          _buildCheckboxTile(
            title: '时间轴',
            subtitle: '包含项目进度时间轴',
            value: _config.includeTimeline,
            onChanged: (value) => _updateConfig(_config.copyWith(includeTimeline: value)),
          ),
          
          _buildCheckboxTile(
            title: '进度报告',
            subtitle: '包含详细的进度统计',
            value: _config.includeProgress,
            onChanged: (value) => _updateConfig(_config.copyWith(includeProgress: value)),
          ),
          
          _buildCheckboxTile(
            title: '成本分析',
            subtitle: '包含成本统计和分析',
            value: _config.includeCosts,
            onChanged: (value) => _updateConfig(_config.copyWith(includeCosts: value)),
          ),
          
          _buildCheckboxTile(
            title: '图片和媒体',
            subtitle: '包含项目相关的图片文件',
            value: _config.includeImages,
            onChanged: (value) => _updateConfig(_config.copyWith(includeImages: value)),
          ),
          
          _buildCheckboxTile(
            title: '备注和说明',
            subtitle: '包含详细的备注信息',
            value: _config.includeNotes,
            onChanged: (value) => _updateConfig(_config.copyWith(includeNotes: value)),
          ),
        ],
      ),
    );
  }

  /// 构建高级选项
  Widget _buildAdvancedOptions() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('详细设置'),
          const SizedBox(height: VanHubSpacing.sm),
          
          _buildCheckboxTile(
            title: '显示材料详情',
            subtitle: '包含材料的详细规格信息',
            value: _config.showMaterialDetails,
            onChanged: (value) => _updateConfig(_config.copyWith(showMaterialDetails: value)),
          ),
          
          _buildCheckboxTile(
            title: '显示供应商信息',
            subtitle: '包含供应商和品牌信息',
            value: _config.showSupplierInfo,
            onChanged: (value) => _updateConfig(_config.copyWith(showSupplierInfo: value)),
          ),
          
          _buildCheckboxTile(
            title: '显示价格信息',
            subtitle: '包含详细的价格和成本信息',
            value: _config.showPricing,
            onChanged: (value) => _updateConfig(_config.copyWith(showPricing: value)),
          ),
          
          const SizedBox(height: VanHubSpacing.lg),
          
          _buildSectionTitle('自定义设置'),
          const SizedBox(height: VanHubSpacing.sm),
          
          TextField(
            decoration: const InputDecoration(
              labelText: '自定义标题',
              hintText: '输入自定义的导出标题',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => _updateConfig(_config.copyWith(customTitle: value.isEmpty ? null : value)),
          ),
          
          const SizedBox(height: VanHubSpacing.md),
          
          TextField(
            decoration: const InputDecoration(
              labelText: '公司名称',
              hintText: '输入公司或组织名称',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => _updateConfig(_config.copyWith(companyName: value.isEmpty ? null : value)),
          ),
        ],
      ),
    );
  }

  /// 构建类型选择
  Widget _buildTypeSelection() {
    return Wrap(
      spacing: VanHubSpacing.sm,
      children: widget.availableTypes.map((type) {
        final isSelected = type == _selectedType;
        return FilterChip(
          label: Text(_getTypeText(type)),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedType = type;
              });
            }
          },
          selectedColor: VanHubBrandColors.primary.withValues(alpha: 0.2),
          checkmarkColor: VanHubBrandColors.primary,
        );
      }).toList(),
    );
  }

  /// 构建格式选择
  Widget _buildFormatSelection() {
    return Wrap(
      spacing: VanHubSpacing.sm,
      children: widget.availableFormats.map((format) {
        final isSelected = format == _selectedFormat;
        return FilterChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(_getFormatIcon(format), size: 16),
              const SizedBox(width: 4),
              Text(_getFormatText(format)),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedFormat = format;
              });
            }
          },
          selectedColor: VanHubBrandColors.primary.withValues(alpha: 0.2),
          checkmarkColor: VanHubBrandColors.primary,
        );
      }).toList(),
    );
  }

  /// 构建模板选择
  Widget _buildTemplateSelection() {
    final templates = ['标准模板', '详细模板', '摘要模板', '自定义模板'];
    
    return DropdownButtonFormField<String>(
      value: _config.template,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        labelText: '选择模板',
      ),
      items: templates.map((template) {
        return DropdownMenuItem<String>(
          value: template,
          child: Text(template),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          _updateConfig(_config.copyWith(template: value));
        }
      },
    );
  }

  /// 构建预估卡片
  Widget _buildEstimationCard() {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue.shade600, size: 20),
              const SizedBox(width: VanHubSpacing.xs),
              Text(
                '导出预估',
                style: VanHubTypography.titleSmall.copyWith(
                  color: Colors.blue.shade800,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Row(
            children: [
              Expanded(
                child: _buildEstimationItem('预估大小', '${_config.estimatedFileSizeKB} KB'),
              ),
              Expanded(
                child: _buildEstimationItem('预估时间', '${_config.estimatedExportTimeSeconds} 秒'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建预估项目
  Widget _buildEstimationItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: VanHubTypography.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: VanHubTypography.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 构建进度部分
  Widget _buildProgressSection() {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              const SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: Text(
                  _exportStatus ?? '正在导出...',
                  style: VanHubTypography.bodyMedium,
                ),
              ),
              Text(
                '${(_exportProgress * 100).toInt()}%',
                style: VanHubTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: VanHubSpacing.sm),
          LinearProgressIndicator(
            value: _exportProgress,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(VanHubBrandColors.primary),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        if (widget.onPreview != null) ...[
          OutlinedButton.icon(
            onPressed: _isExporting ? null : widget.onPreview,
            icon: const Icon(Icons.preview),
            label: const Text('预览'),
          ),
          const SizedBox(width: VanHubSpacing.sm),
        ],
        Expanded(
          child: OutlinedButton(
            onPressed: _isExporting ? null : () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isExporting ? null : _handleExport,
            icon: _isExporting 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.download),
            label: Text(_isExporting ? '导出中...' : '开始导出'),
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubBrandColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建部分标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: VanHubTypography.titleMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// 构建复选框项
  Widget _buildCheckboxTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool?) onChanged,
  }) {
    return CheckboxListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// 更新配置
  void _updateConfig(ProjectExportConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
  }

  /// 处理导出
  Future<void> _handleExport() async {
    setState(() {
      _isExporting = true;
      _exportProgress = 0.0;
      _exportStatus = '准备导出...';
    });

    try {
      // 模拟导出进度
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 200));
        setState(() {
          _exportProgress = i / 100;
          _exportStatus = '正在导出... ($i%)';
        });
      }

      // 调用导出回调
      widget.onExport(_selectedType, _selectedFormat, _config);
      
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _isExporting = false;
        _exportStatus = '导出失败: $e';
      });
    }
  }

  /// 获取类型文本
  String _getTypeText(ExportType type) {
    switch (type) {
      case ExportType.project:
        return '项目报告';
      case ExportType.bom:
        return 'BOM清单';
      case ExportType.report:
        return '分析报告';
      case ExportType.data:
        return '数据导出';
    }
  }

  /// 获取格式文本
  String _getFormatText(ExportFormat format) {
    switch (format) {
      case ExportFormat.pdf:
        return 'PDF';
      case ExportFormat.excel:
        return 'Excel';
      case ExportFormat.csv:
        return 'CSV';
      case ExportFormat.json:
        return 'JSON';
    }
  }

  /// 获取格式图标
  IconData _getFormatIcon(ExportFormat format) {
    switch (format) {
      case ExportFormat.pdf:
        return Icons.picture_as_pdf;
      case ExportFormat.excel:
        return Icons.table_chart;
      case ExportFormat.csv:
        return Icons.grid_on;
      case ExportFormat.json:
        return Icons.code;
    }
  }
}
