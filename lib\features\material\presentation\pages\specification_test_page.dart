import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/product_specification.dart';
import '../../domain/entities/category_templates.dart';
import '../widgets/product_specification_widget.dart';
import '../widgets/category_spec_template_viewer.dart';

/// 规格系统测试页面
/// 
/// 用于测试和演示规格系统的各种组件
class SpecificationTestPage extends ConsumerStatefulWidget {
  const SpecificationTestPage({Key? key}) : super(key: key);

  @override
  ConsumerState<SpecificationTestPage> createState() => 
      _SpecificationTestPageState();
}

class _SpecificationTestPageState extends ConsumerState<SpecificationTestPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCategory = 'ELECTRICAL';
  Map<String, dynamic> _templateValues = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('规格系统测试'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '产品规格展示', icon: Icon(Icons.view_list)),
            Tab(text: '分类模板', icon: Icon(Icons.category)),
            Tab(text: '模板编辑', icon: Icon(Icons.edit)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProductSpecificationTab(),
          _buildCategoryTemplateTab(),
          _buildTemplateEditTab(),
        ],
      ),
    );
  }

  /// 构建产品规格展示标签页
  Widget _buildProductSpecificationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '产品规格展示示例',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          ProductSpecificationWidget(
            specification: _createSampleElectricalSpec(),
          ),
          const SizedBox(height: 16),
          ProductSpecificationWidget(
            specification: _createSamplePlumbingSpec(),
          ),
        ],
      ),
    );
  }

  /// 构建分类模板标签页
  Widget _buildCategoryTemplateTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '分类模板展示',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildCategorySelector(),
          const SizedBox(height: 16),
          CategorySpecTemplateViewer(
            categoryCode: _selectedCategory,
            isReadOnly: true,
          ),
        ],
      ),
    );
  }

  /// 构建模板编辑标签页
  Widget _buildTemplateEditTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '模板编辑示例',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildCategorySelector(),
          const SizedBox(height: 16),
          CategorySpecTemplateViewer(
            categoryCode: _selectedCategory,
            initialValues: _templateValues,
            isReadOnly: false,
            onValuesChanged: (values) {
              setState(() {
                _templateValues = values;
              });
            },
          ),
          const SizedBox(height: 16),
          _buildValuesDisplay(),
        ],
      ),
    );
  }

  /// 构建分类选择器
  Widget _buildCategorySelector() {
    final categories = VanHubCategoryTemplates.getCategoryNameMap();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '选择分类',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: categories.entries.map((entry) {
                final isSelected = _selectedCategory == entry.key;
                return FilterChip(
                  label: Text(entry.value),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedCategory = entry.key;
                        _templateValues = {};
                      });
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建值显示
  Widget _buildValuesDisplay() {
    if (_templateValues.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '当前值',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _formatValues(_templateValues),
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化值显示
  String _formatValues(Map<String, dynamic> values) {
    final buffer = StringBuffer();
    values.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString();
  }

  /// 创建电气设备示例规格
  ProductSpecification _createSampleElectricalSpec() {
    return ProductSpecification(
      id: 'sample_battery_001',
      materialId: 'material_battery_001',
      category: 'ELECTRICAL',
      basicSpec: BasicSpecification(
        productName: '磷酸铁锂电池',
        brand: 'CATL',
        model: 'LFP-100Ah',
        manufacturer: '宁德时代',
        countryOfOrigin: '中国',
        warrantyMonths: 60,
        description: '高性能磷酸铁锂电池，适用于房车储能系统',
      ),
      technicalParams: TechnicalParameters(
        electrical: ElectricalSpecs(
          ratedVoltage: 12.8,
          ratedCurrent: 100.0,
          ratedPower: 1280.0,
          capacity: 100.0,
          capacityUnit: 'Ah',
          efficiency: 95.0,
          protectionRating: 'IP65',
        ),
      ),
      physicalProps: PhysicalProperties(
        dimensions: Dimensions(
          length: 330.0,
          width: 173.0,
          height: 220.0,
          unit: 'mm',
        ),
        weight: 13.5,
        color: '黑色',
        material: '磷酸铁锂',
      ),
      performanceMetrics: PerformanceMetrics(
        lifespan: 6000,
        lifespanUnit: '次',
        cycleCount: 6000,
        reliabilityGrade: 'A级',
        performanceGrade: '优秀',
      ),
      certifications: [
        CertificationInfo(
          name: 'CE认证',
          authority: '欧盟',
          certificateNumber: 'CE-2023-001',
          expiryDate: DateTime(2025, 12, 31),
        ),
        CertificationInfo(
          name: 'UL认证',
          authority: '美国UL',
          certificateNumber: 'UL-2023-002',
          expiryDate: DateTime(2025, 12, 31),
        ),
      ],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// 创建水路设备示例规格
  ProductSpecification _createSamplePlumbingSpec() {
    return ProductSpecification(
      id: 'sample_pump_001',
      materialId: 'material_pump_001',
      category: 'PLUMBING',
      basicSpec: BasicSpecification(
        productName: '隔膜水泵',
        brand: 'Seaflo',
        model: 'SFDP1-012-035-21',
        manufacturer: '厦门海流',
        countryOfOrigin: '中国',
        warrantyMonths: 24,
        description: '12V直流隔膜水泵，适用于房车供水系统',
      ),
      technicalParams: TechnicalParameters(
        electrical: ElectricalSpecs(
          ratedVoltage: 12.0,
          ratedCurrent: 3.5,
          ratedPower: 42.0,
          protectionRating: 'IP65',
        ),
        fluid: FluidSpecs(
          flowRate: 13.0,
          head: 17.0,
          pipeDiameter: 12.0,
          connectionType: '快插接头',
        ),
        mechanical: MechanicalSpecs(
          workingPressure: 0.8,
          maxPressure: 1.0,
        ),
      ),
      physicalProps: PhysicalProperties(
        dimensions: Dimensions(
          length: 140.0,
          width: 90.0,
          height: 110.0,
          unit: 'mm',
        ),
        weight: 1.2,
        color: '黑色',
        material: '工程塑料',
      ),
      performanceMetrics: PerformanceMetrics(
        lifespan: 5000,
        lifespanUnit: '小时',
        reliabilityGrade: 'B级',
        performanceGrade: '良好',
      ),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
