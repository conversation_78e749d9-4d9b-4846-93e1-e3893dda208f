import 'dart:convert';
import 'dart:io';
import 'package:fpdart/fpdart.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:crypto/crypto.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/app_settings.dart';
import '../../domain/services/settings_service.dart';

/// 导出格式
enum ExportFormat {
  json,
  encrypted,
  qrCode,
}

/// 导入来源
enum ImportSource {
  file,
  qrCode,
  clipboard,
  url,
}

/// 设置导入导出服务
class SettingsImportExportService {
  static const String _exportFilePrefix = 'vanhub_settings_';
  static const String _encryptionKey = 'VanHub_Settings_2024'; // 实际使用中应该使用更安全的密钥

  /// 导出设置到文件
  Future<Either<Failure, String>> exportToFile(
    AppSettings settings, {
    ExportFormat format = ExportFormat.json,
    String? customFileName,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = customFileName ?? '$_exportFilePrefix$timestamp';
      
      String fileExtension;
      String fileContent;
      
      switch (format) {
        case ExportFormat.json:
          fileExtension = '.json';
          fileContent = _exportToJson(settings);
          break;
        
        case ExportFormat.encrypted:
          fileExtension = '.vhsettings';
          fileContent = _exportToEncrypted(settings);
          break;
        
        case ExportFormat.qrCode:
          fileExtension = '.txt';
          fileContent = _exportToQrCode(settings);
          break;
      }
      
      final file = File('${directory.path}/$fileName$fileExtension');
      await file.writeAsString(fileContent);
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: '导出设置到文件失败: $e'));
    }
  }

  /// 从文件导入设置
  Future<Either<Failure, AppSettings>> importFromFile(
    String userId, {
    String? filePath,
  }) async {
    try {
      String? selectedFilePath = filePath;
      
      if (selectedFilePath == null) {
        final result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['json', 'vhsettings', 'txt'],
        );
        
        if (result == null || result.files.isEmpty) {
          return Left(ValidationFailure(message: '未选择文件'));
        }
        
        selectedFilePath = result.files.first.path;
      }
      
      if (selectedFilePath == null) {
        return Left(ValidationFailure(message: '文件路径无效'));
      }
      
      final file = File(selectedFilePath);
      if (!await file.exists()) {
        return Left(NotFoundFailure(message: '文件不存在'));
      }
      
      final fileContent = await file.readAsString();
      final fileExtension = selectedFilePath.split('.').last.toLowerCase();
      
      Map<String, dynamic> settingsData;
      
      switch (fileExtension) {
        case 'json':
          settingsData = _importFromJson(fileContent);
          break;
        
        case 'vhsettings':
          settingsData = _importFromEncrypted(fileContent);
          break;
        
        case 'txt':
          settingsData = _importFromQrCode(fileContent);
          break;
        
        default:
          return Left(ValidationFailure(message: '不支持的文件格式'));
      }
      
      final importedSettings = AppSettings.importSettings(settingsData, userId);
      return Right(importedSettings);
    } catch (e) {
      return Left(UnknownFailure(message: '从文件导入设置失败: $e'));
    }
  }

  /// 分享设置
  Future<Either<Failure, void>> shareSettings(
    AppSettings settings, {
    ExportFormat format = ExportFormat.json,
    String? customMessage,
  }) async {
    try {
      final exportResult = await exportToFile(settings, format: format);
      
      return await exportResult.fold(
        (failure) async => Left(failure),
        (filePath) async {
          final message = customMessage ?? 'VanHub设置文件分享';
          await Share.shareXFiles(
            [XFile(filePath)],
            text: message,
          );
          return const Right(null);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '分享设置失败: $e'));
    }
  }

  /// 生成设置二维码
  Future<Either<Failure, String>> generateQrCode(AppSettings settings) async {
    try {
      final qrData = _exportToQrCode(settings);
      return Right(qrData);
    } catch (e) {
      return Left(UnknownFailure(message: '生成二维码失败: $e'));
    }
  }

  /// 从二维码导入设置
  Future<Either<Failure, AppSettings>> importFromQrCode(
    String userId,
    String qrData,
  ) async {
    try {
      final settingsData = _importFromQrCode(qrData);
      final importedSettings = AppSettings.importSettings(settingsData, userId);
      return Right(importedSettings);
    } catch (e) {
      return Left(UnknownFailure(message: '从二维码导入设置失败: $e'));
    }
  }

  /// 从剪贴板导入设置
  Future<Either<Failure, AppSettings>> importFromClipboard(String userId) async {
    try {
      // TODO: 实现从剪贴板读取
      // final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      // if (clipboardData?.text == null) {
      //   return Left(ValidationFailure(message: '剪贴板为空'));
      // }
      
      // 暂时返回错误
      return Left(UnknownFailure(message: '剪贴板导入功能暂未实现'));
    } catch (e) {
      return Left(UnknownFailure(message: '从剪贴板导入设置失败: $e'));
    }
  }

  /// 从URL导入设置
  Future<Either<Failure, AppSettings>> importFromUrl(
    String userId,
    String url,
  ) async {
    try {
      // TODO: 实现从URL下载设置文件
      return Left(UnknownFailure(message: 'URL导入功能暂未实现'));
    } catch (e) {
      return Left(UnknownFailure(message: '从URL导入设置失败: $e'));
    }
  }

  /// 批量导出多个设置
  Future<Either<Failure, String>> batchExport(
    List<AppSettings> settingsList, {
    ExportFormat format = ExportFormat.json,
    String? customFileName,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = customFileName ?? '${_exportFilePrefix}batch_$timestamp';
      
      final batchData = {
        'exportedAt': DateTime.now().toIso8601String(),
        'exportVersion': '1.0',
        'settingsCount': settingsList.length,
        'settings': settingsList.map((s) => s.exportSettings()).toList(),
      };
      
      String fileExtension;
      String fileContent;
      
      switch (format) {
        case ExportFormat.json:
          fileExtension = '.json';
          fileContent = jsonEncode(batchData);
          break;
        
        case ExportFormat.encrypted:
          fileExtension = '.vhsettings';
          fileContent = _encryptData(jsonEncode(batchData));
          break;
        
        default:
          return Left(ValidationFailure(message: '批量导出不支持二维码格式'));
      }
      
      final file = File('${directory.path}/$fileName$fileExtension');
      await file.writeAsString(fileContent);
      
      return Right(file.path);
    } catch (e) {
      return Left(UnknownFailure(message: '批量导出设置失败: $e'));
    }
  }

  /// 验证导入文件
  Future<Either<Failure, Map<String, dynamic>>> validateImportFile(
    String filePath,
  ) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return Left(NotFoundFailure(message: '文件不存在'));
      }
      
      final fileContent = await file.readAsString();
      final fileExtension = filePath.split('.').last.toLowerCase();
      
      Map<String, dynamic> data;
      
      switch (fileExtension) {
        case 'json':
          data = jsonDecode(fileContent);
          break;
        
        case 'vhsettings':
          final decryptedContent = _decryptData(fileContent);
          data = jsonDecode(decryptedContent);
          break;
        
        default:
          return Left(ValidationFailure(message: '不支持的文件格式'));
      }
      
      // 验证数据结构
      final validationResult = _validateSettingsData(data);
      if (validationResult.isNotEmpty) {
        return Left(ValidationFailure(message: '文件格式错误: ${validationResult.join(', ')}'));
      }
      
      return Right({
        'isValid': true,
        'settingsCount': data.containsKey('settings') ? (data['settings'] as List).length : 1,
        'exportedAt': data['exportedAt'],
        'exportVersion': data['exportVersion'],
      });
    } catch (e) {
      return Left(UnknownFailure(message: '验证导入文件失败: $e'));
    }
  }

  /// 获取导出历史
  Future<Either<Failure, List<Map<String, dynamic>>>> getExportHistory() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync()
          .where((entity) => entity is File && entity.path.contains(_exportFilePrefix))
          .cast<File>()
          .toList();
      
      final history = <Map<String, dynamic>>[];
      
      for (final file in files) {
        final stat = await file.stat();
        final fileName = file.path.split('/').last;
        
        history.add({
          'fileName': fileName,
          'filePath': file.path,
          'fileSize': stat.size,
          'createdAt': stat.modified.toIso8601String(),
          'format': _getFormatFromFileName(fileName),
        });
      }
      
      // 按创建时间排序
      history.sort((a, b) => DateTime.parse(b['createdAt']).compareTo(DateTime.parse(a['createdAt'])));
      
      return Right(history);
    } catch (e) {
      return Left(UnknownFailure(message: '获取导出历史失败: $e'));
    }
  }

  /// 清理导出文件
  Future<Either<Failure, int>> cleanupExportFiles({
    int retentionDays = 30,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync()
          .where((entity) => entity is File && entity.path.contains(_exportFilePrefix))
          .cast<File>()
          .toList();
      
      final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays));
      int deletedCount = 0;
      
      for (final file in files) {
        final stat = await file.stat();
        if (stat.modified.isBefore(cutoffDate)) {
          await file.delete();
          deletedCount++;
        }
      }
      
      return Right(deletedCount);
    } catch (e) {
      return Left(UnknownFailure(message: '清理导出文件失败: $e'));
    }
  }

  /// 导出为JSON格式
  String _exportToJson(AppSettings settings) {
    final exportData = {
      'exportedAt': DateTime.now().toIso8601String(),
      'exportVersion': '1.0',
      'appVersion': '1.0.0', // TODO: 获取实际应用版本
      'settings': settings.exportSettings(),
    };
    
    return jsonEncode(exportData);
  }

  /// 导出为加密格式
  String _exportToEncrypted(AppSettings settings) {
    final jsonData = _exportToJson(settings);
    return _encryptData(jsonData);
  }

  /// 导出为二维码格式
  String _exportToQrCode(AppSettings settings) {
    final exportData = settings.exportSettings();
    // 压缩数据以适应二维码
    final compressedData = _compressSettingsData(exportData);
    return base64Encode(utf8.encode(jsonEncode(compressedData)));
  }

  /// 从JSON导入
  Map<String, dynamic> _importFromJson(String jsonContent) {
    final data = jsonDecode(jsonContent);
    return data['settings'] ?? data;
  }

  /// 从加密格式导入
  Map<String, dynamic> _importFromEncrypted(String encryptedContent) {
    final decryptedContent = _decryptData(encryptedContent);
    return _importFromJson(decryptedContent);
  }

  /// 从二维码导入
  Map<String, dynamic> _importFromQrCode(String qrData) {
    final decodedData = utf8.decode(base64Decode(qrData));
    final compressedData = jsonDecode(decodedData);
    return _decompressSettingsData(compressedData);
  }

  /// 加密数据
  String _encryptData(String data) {
    // 简单的Base64编码（实际使用中应该使用更安全的加密算法）
    final bytes = utf8.encode(data);
    final key = utf8.encode(_encryptionKey);
    
    // XOR加密
    final encrypted = <int>[];
    for (int i = 0; i < bytes.length; i++) {
      encrypted.add(bytes[i] ^ key[i % key.length]);
    }
    
    return base64Encode(encrypted);
  }

  /// 解密数据
  String _decryptData(String encryptedData) {
    final encrypted = base64Decode(encryptedData);
    final key = utf8.encode(_encryptionKey);
    
    // XOR解密
    final decrypted = <int>[];
    for (int i = 0; i < encrypted.length; i++) {
      decrypted.add(encrypted[i] ^ key[i % key.length]);
    }
    
    return utf8.decode(decrypted);
  }

  /// 压缩设置数据
  Map<String, dynamic> _compressSettingsData(Map<String, dynamic> data) {
    // 移除不必要的字段，只保留核心设置
    return {
      'tm': data['themeMode'],
      'lg': data['language'],
      'ts': data['textScaleFactor'],
      'an': data['enableAnimations'],
      'pn': data['enablePushNotifications'],
      'cs': data['enableCloudSync'],
      // 添加更多需要的字段...
    };
  }

  /// 解压设置数据
  Map<String, dynamic> _decompressSettingsData(Map<String, dynamic> compressedData) {
    // 恢复完整的字段名
    return {
      'themeMode': compressedData['tm'],
      'language': compressedData['lg'],
      'textScaleFactor': compressedData['ts'],
      'enableAnimations': compressedData['an'],
      'enablePushNotifications': compressedData['pn'],
      'enableCloudSync': compressedData['cs'],
      // 恢复更多字段...
    };
  }

  /// 验证设置数据
  List<String> _validateSettingsData(Map<String, dynamic> data) {
    final errors = <String>[];
    
    // 检查必要字段
    if (data.containsKey('settings')) {
      // 批量导出格式
      if (data['settings'] is! List) {
        errors.add('settings字段必须是数组');
      }
    } else {
      // 单个设置格式
      if (!data.containsKey('themeMode')) {
        errors.add('缺少themeMode字段');
      }
    }
    
    return errors;
  }

  /// 从文件名获取格式
  String _getFormatFromFileName(String fileName) {
    if (fileName.endsWith('.json')) return 'JSON';
    if (fileName.endsWith('.vhsettings')) return '加密';
    if (fileName.endsWith('.txt')) return '二维码';
    return '未知';
  }
}
