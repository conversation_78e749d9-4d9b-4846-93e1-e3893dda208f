import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/search_criteria.dart';
import '../../domain/entities/material.dart' as domain;
import '../providers/material_search_provider.dart';

class MaterialSearchDemoWidget extends ConsumerStatefulWidget {
  const MaterialSearchDemoWidget({super.key});

  @override
  ConsumerState<MaterialSearchDemoWidget> createState() => _MaterialSearchDemoWidgetState();
}

class _MaterialSearchDemoWidgetState extends ConsumerState<MaterialSearchDemoWidget> {
  final _searchController = TextEditingController();
  String _selectedCategory = '全部';
  final _categories = ['全部', '电气设备', '水路系统', '储物系统', '床铺系统', '厨房系统', '卫浴系统', '外观系统', '底盘系统'];
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;
    
    final criteria = SearchCriteria(
      query: query,
      category: _selectedCategory == '全部' ? null : _selectedCategory,
      sortBy: 'usage_count',
      ascending: false,
      limit: 20,
    );
    
    ref.read(materialSearchStateProvider.notifier).search(criteria);
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(materialSearchStateProvider);
    final popularTerms = ref.watch(popularSearchTermsProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            '材料搜索',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索材料...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  onSubmitted: (_) => _performSearch(),
                ),
              ),
              const SizedBox(width: 8.0),
              ElevatedButton(
                onPressed: _performSearch,
                child: const Text('搜索'),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8.0),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              const Text('分类:'),
              const SizedBox(width: 8.0),
              DropdownButton<String>(
                value: _selectedCategory,
                items: _categories.map((category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedCategory = value;
                    });
                    _performSearch();
                  }
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 8.0),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: popularTerms.when(
            data: (terms) {
              return Wrap(
                spacing: 8.0,
                children: terms.map((term) {
                  return ActionChip(
                    label: Text(term),
                    onPressed: () {
                      _searchController.text = term;
                      _performSearch();
                    },
                  );
                }).toList(),
              );
            },
            loading: () => const CircularProgressIndicator(),
            error: (_, __) => const Text('加载热门搜索词失败'),
          ),
        ),
        const SizedBox(height: 16.0),
        Expanded(
          child: searchState.when(
            data: (materials) {
              if (materials.isEmpty) {
                return const Center(
                  child: Text('没有找到匹配的材料'),
                );
              }
              
              return ListView.builder(
                itemCount: materials.length,
                itemBuilder: (context, index) {
                  final material = materials[index];
                  return _buildMaterialCard(material);
                },
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, _) => Center(
              child: Text('搜索失败: ${error.toString()}'),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMaterialCard(domain.Material material) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ListTile(
        leading: material.imageUrl != null && material.imageUrl!.isNotEmpty
            ? ClipRRect(
                borderRadius: BorderRadius.circular(4.0),
                child: Image.network(
                  material.imageUrl!,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => const Icon(Icons.image, size: 50),
                ),
              )
            : Container(
                width: 50,
                height: 50,
                color: Colors.grey.shade200,
                child: Icon(
                  Icons.category,
                  color: Colors.grey.shade600,
                ),
              ),
        title: Text(material.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('分类: ${material.category}'),
            if (material.brand != null && material.brand!.isNotEmpty)
              Text('品牌: ${material.brand}'),
            Text('价格: ¥${material.price.toStringAsFixed(2)}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('使用次数: ${material.usageCount}'),
            if (material.lastUsedAt != null)
              Text(
                '最近使用: ${_formatDate(material.lastUsedAt!)}',
                style: const TextStyle(fontSize: 12),
              ),
          ],
        ),
        isThreeLine: true,
        onTap: () {
          // 查看材料详情
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()}周前';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}月前';
    } else {
      return '${(difference.inDays / 365).floor()}年前';
    }
  }
}