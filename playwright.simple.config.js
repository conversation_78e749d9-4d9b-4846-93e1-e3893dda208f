// VanHub改装宝 - 简化的Playwright测试配置
// 专门用于智能联动功能的基础测试

const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './test/playwright',
  
  /* 测试文件匹配 */
  testMatch: '**/simple_test.js',
  
  /* 并行运行测试 */
  fullyParallel: false,
  
  /* 重试配置 */
  retries: 1,
  
  /* Worker数量 */
  workers: 1,
  
  /* 测试报告 */
  reporter: [
    ['html', { outputFolder: 'test-results/simple-report' }],
    ['list']
  ],
  
  /* 全局测试配置 */
  use: {
    /* 基础URL */
    baseURL: 'http://localhost:8080',
    
    /* 截图和视频 */
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    /* 浏览器配置 */
    viewport: { width: 1280, height: 720 },
    
    /* 超时配置 */
    actionTimeout: 15000,
    navigationTimeout: 30000,
    
    /* 忽略HTTPS错误 */
    ignoreHTTPSErrors: true,
  },

  /* 只测试Chrome */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    }
  ],

  /* 测试超时 */
  timeout: 60000,
  expect: {
    timeout: 10000
  },
  
  /* 输出目录 */
  outputDir: 'test-results/simple/',
});
