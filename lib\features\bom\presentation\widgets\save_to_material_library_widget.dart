import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/bom_item.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// BOM项目保存到材料库组件
/// 将BOM中的物料信息保存到用户的材料库中，避免重复录入
class SaveToMaterialLibraryWidget extends ConsumerStatefulWidget {
  final BomItem bomItem;
  final VoidCallback? onSuccess;
  final VoidCallback? onCancel;

  const SaveToMaterialLibraryWidget({
    super.key,
    required this.bomItem,
    this.onSuccess,
    this.onCancel,
  });

  @override
  ConsumerState<SaveToMaterialLibraryWidget> createState() => _SaveToMaterialLibraryWidgetState();
}

class _SaveToMaterialLibraryWidgetState extends ConsumerState<SaveToMaterialLibraryWidget> {
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _specificationController = TextEditingController();
  
  String _selectedCategory = '电气设备';
  bool _isSaving = false;
  bool _addToPersonalLibrary = true;

  final List<String> _categories = [
    '电气设备', '水路系统', '储物系统', '床铺系统', '厨房系统', 
    '卫浴系统', '外观系统', '底盘系统', '安全设备', '其他'
  ];

  @override
  void initState() {
    super.initState();
    _initializeFromBomItem();
  }

  void _initializeFromBomItem() {
    _nameController.text = widget.bomItem.name;
    _brandController.text = widget.bomItem.brand ?? '';
    _modelController.text = widget.bomItem.model ?? '';
    _descriptionController.text = widget.bomItem.description ?? '';
    _priceController.text = widget.bomItem.unitPrice.toStringAsFixed(2) ?? '';
    _specificationController.text = widget.bomItem.specification ?? '';
    
    if (widget.bomItem.category != null && _categories.contains(widget.bomItem.category)) {
      _selectedCategory = widget.bomItem.category!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _specificationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.bookmark_add,
                color: VanHubColors.primary,
                size: 24,
              ),
              SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: Text(
                  '保存到材料库',
                  style: VanHubTypography.titleLarge,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: widget.onCancel,
              ),
            ],
          ),
          
          SizedBox(height: VanHubSpacing.md),
          
          // 提示信息
          Container(
            padding: EdgeInsets.all(VanHubSpacing.md),
            decoration: BoxDecoration(
              color: VanHubColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: VanHubColors.info.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: VanHubColors.info,
                  size: 20,
                ),
                SizedBox(width: VanHubSpacing.sm),
                Expanded(
                  child: Text(
                    '将此BOM项目的信息保存到材料库，方便在其他项目中复用',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: VanHubColors.info,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: VanHubSpacing.lg),
          
          // 表单内容
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 基本信息
                  _buildBasicInfo(),
                  
                  SizedBox(height: VanHubSpacing.lg),
                  
                  // 详细信息
                  _buildDetailedInfo(),
                  
                  SizedBox(height: VanHubSpacing.lg),
                  
                  // 保存选项
                  _buildSaveOptions(),
                ],
              ),
            ),
          ),
          
          SizedBox(height: VanHubSpacing.lg),
          
          // 操作按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '基本信息',
          style: VanHubTypography.titleMedium,
        ),
        SizedBox(height: VanHubSpacing.md),
        
        // 材料名称
        TextField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: '材料名称 *',
            border: OutlineInputBorder(),
          ),
        ),
        
        SizedBox(height: VanHubSpacing.md),
        
        // 品牌和型号
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _brandController,
                decoration: const InputDecoration(
                  labelText: '品牌',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            SizedBox(width: VanHubSpacing.md),
            Expanded(
              child: TextField(
                controller: _modelController,
                decoration: const InputDecoration(
                  labelText: '型号',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ],
        ),
        
        SizedBox(height: VanHubSpacing.md),
        
        // 分类选择
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: '分类',
            border: OutlineInputBorder(),
          ),
          items: _categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCategory = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildDetailedInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '详细信息',
          style: VanHubTypography.titleMedium,
        ),
        SizedBox(height: VanHubSpacing.md),
        
        // 价格
        TextField(
          controller: _priceController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: '参考价格',
            prefixText: '¥',
            border: OutlineInputBorder(),
            helperText: '基于BOM中的单价',
          ),
        ),
        
        SizedBox(height: VanHubSpacing.md),
        
        // 规格
        TextField(
          controller: _specificationController,
          decoration: const InputDecoration(
            labelText: '规格参数',
            border: OutlineInputBorder(),
            helperText: '如：12V 100Ah、1000W等',
          ),
        ),
        
        SizedBox(height: VanHubSpacing.md),
        
        // 描述
        TextField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: '材料描述',
            border: OutlineInputBorder(),
            helperText: '详细描述材料的特点和用途',
          ),
        ),
      ],
    );
  }

  Widget _buildSaveOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '保存选项',
          style: VanHubTypography.titleMedium,
        ),
        SizedBox(height: VanHubSpacing.sm),
        
        CheckboxListTile(
          title: const Text('添加到个人材料库'),
          subtitle: const Text('保存到您的私人材料库中'),
          value: _addToPersonalLibrary,
          onChanged: (value) {
            setState(() {
              _addToPersonalLibrary = value ?? true;
            });
          },
          activeColor: VanHubColors.primary,
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving ? null : widget.onCancel,
            child: const Text('取消'),
          ),
        ),
        SizedBox(width: VanHubSpacing.md),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isSaving ? null : _handleSaveToLibrary,
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('保存到材料库'),
          ),
        ),
      ],
    );
  }

  Future<void> _handleSaveToLibrary() async {
    if (_nameController.text.trim().isEmpty) {
      _showError('请输入材料名称');
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // TODO: 实现材料保存逻辑
      // 这里需要调用正确的材料创建方法
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求

      // 模拟成功结果
      _showSuccess();
      if (widget.onSuccess != null) {
        widget.onSuccess!();
      }
      return;

      // 已在上面处理成功情况
    } catch (e) {
      if (mounted) {
        _showError('保存失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: VanHubColors.error,
      ),
    );
  }

  void _showSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: VanHubSpacing.xs),
            Text('${_nameController.text} 已保存到材料库'),
          ],
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}
