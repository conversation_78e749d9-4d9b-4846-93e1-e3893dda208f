/// VanHub Card Component 2.0
/// 
/// 高端卡片组件系统
/// 支持多种样式、阴影和交互效果
/// 
/// 设计原则：
/// 1. 层次感 - 通过阴影和边框营造深度
/// 2. 内容聚焦 - 清晰的内容区域划分
/// 3. 交互反馈 - 悬停和点击的视觉反馈
/// 4. 响应式 - 适配不同屏幕尺寸
library;

import 'package:flutter/material.dart';
import '../vanhub_design_system.dart';

/// 卡片变体类型
enum VanHubCardVariant {
  elevated,   // 浮起卡片 (有阴影)
  outlined,   // 边框卡片
  filled,     // 填充卡片
  ghost,      // 透明卡片
}

/// 卡片尺寸
enum VanHubCardSize {
  small,   // 小卡片
  medium,  // 中等卡片
  large,   // 大卡片
}

/// VanHub卡片组件
class VanHubCard extends StatefulWidget {
  /// 卡片内容
  final Widget child;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 长按回调
  final VoidCallback? onLongPress;
  
  /// 卡片变体
  final VanHubCardVariant variant;
  
  /// 卡片尺寸
  final VanHubCardSize size;
  
  /// 自定义宽度
  final double? width;
  
  /// 自定义高度
  final double? height;
  
  /// 自定义边距
  final EdgeInsets? margin;
  
  /// 自定义内边距
  final EdgeInsets? padding;
  
  /// 自定义背景色
  final Color? backgroundColor;
  
  /// 自定义边框色
  final Color? borderColor;
  
  /// 是否启用悬停效果
  final bool enableHover;
  
  /// 语义标签 (无障碍访问)
  final String? semanticLabel;

  const VanHubCard({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.variant = VanHubCardVariant.elevated,
    this.size = VanHubCardSize.medium,
    this.width,
    this.height,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.enableHover = true,
    this.semanticLabel,
  });

  /// 创建浮起卡片
  const VanHubCard.elevated({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.size = VanHubCardSize.medium,
    this.width,
    this.height,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.enableHover = true,
    this.semanticLabel,
  }) : variant = VanHubCardVariant.elevated;

  /// 创建边框卡片
  const VanHubCard.outlined({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.size = VanHubCardSize.medium,
    this.width,
    this.height,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.enableHover = true,
    this.semanticLabel,
  }) : variant = VanHubCardVariant.outlined;

  /// 创建填充卡片
  const VanHubCard.filled({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.size = VanHubCardSize.medium,
    this.width,
    this.height,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.enableHover = true,
    this.semanticLabel,
  }) : variant = VanHubCardVariant.filled;

  @override
  State<VanHubCard> createState() => _VanHubCardState();
}

class _VanHubCardState extends State<VanHubCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: VanHubDesignSystem.durationBase,
      vsync: this,
    );
    
    _elevationAnimation = Tween<double>(
      begin: _getBaseElevation(),
      end: _getHoverElevation(),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: VanHubDesignSystem.curveDefault,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: VanHubDesignSystem.curveDefault,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cardStyle = _getCardStyle();
    
    Widget cardContent = Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      padding: widget.padding ?? _getDefaultPadding(),
      decoration: cardStyle.decoration,
      child: widget.child,
    );

    // 添加动画效果
    if (widget.enableHover || widget.onTap != null) {
      cardContent = AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _isPressed ? _scaleAnimation.value : 1.0,
            child: Container(
              width: widget.width,
              height: widget.height,
              margin: widget.margin,
              padding: widget.padding ?? _getDefaultPadding(),
              decoration: cardStyle.decoration.copyWith(
                boxShadow: widget.variant == VanHubCardVariant.elevated
                    ? [_getAnimatedShadow()]
                    : null,
              ),
              child: widget.child,
            ),
          );
        },
      );
    }

    // 添加交互功能
    if (widget.onTap != null || widget.onLongPress != null) {
      cardContent = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          onLongPress: widget.onLongPress,
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          borderRadius: BorderRadius.circular(cardStyle.borderRadius),
          child: cardContent,
        ),
      );
    }

    // 添加悬停效果
    if (widget.enableHover) {
      cardContent = MouseRegion(
        onEnter: _onHoverEnter,
        onExit: _onHoverExit,
        child: cardContent,
      );
    }

    return Semantics(
      label: widget.semanticLabel,
      container: true,
      child: cardContent,
    );
  }

  /// 获取卡片样式
  _CardStyle _getCardStyle() {
    final colorScheme = _getColorScheme();
    final borderRadius = _getBorderRadius();
    
    BoxDecoration decoration;
    
    switch (widget.variant) {
      case VanHubCardVariant.elevated:
        decoration = BoxDecoration(
          color: colorScheme.backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [_getBaseShadow()],
        );
        break;
        
      case VanHubCardVariant.outlined:
        decoration = BoxDecoration(
          color: colorScheme.backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: colorScheme.borderColor,
            width: 1,
          ),
        );
        break;
        
      case VanHubCardVariant.filled:
        decoration = BoxDecoration(
          color: colorScheme.backgroundColor,
          borderRadius: BorderRadius.circular(borderRadius),
        );
        break;
        
      case VanHubCardVariant.ghost:
        decoration = BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(borderRadius),
        );
        break;
    }
    
    return _CardStyle(
      decoration: decoration,
      borderRadius: borderRadius,
    );
  }

  /// 获取颜色方案
  _CardColorScheme _getColorScheme() {
    return _CardColorScheme(
      backgroundColor: widget.backgroundColor ?? VanHubDesignSystem.neutralWhite,
      borderColor: widget.borderColor ?? VanHubDesignSystem.neutralGray200,
    );
  }

  /// 获取默认内边距
  EdgeInsets _getDefaultPadding() {
    return switch (widget.size) {
      VanHubCardSize.small => EdgeInsets.all(VanHubDesignSystem.spacing3),
      VanHubCardSize.medium => EdgeInsets.all(VanHubDesignSystem.spacing4),
      VanHubCardSize.large => EdgeInsets.all(VanHubDesignSystem.spacing5),
    };
  }

  /// 获取边框圆角
  double _getBorderRadius() {
    return switch (widget.size) {
      VanHubCardSize.small => VanHubDesignSystem.radiusBase,
      VanHubCardSize.medium => VanHubDesignSystem.radiusLg,
      VanHubCardSize.large => VanHubDesignSystem.radiusXl,
    };
  }

  /// 获取基础阴影
  BoxShadow _getBaseShadow() {
    return switch (widget.size) {
      VanHubCardSize.small => VanHubDesignSystem.shadowSm,
      VanHubCardSize.medium => VanHubDesignSystem.shadowBase,
      VanHubCardSize.large => VanHubDesignSystem.shadowMd,
    };
  }

  /// 获取基础高度
  double _getBaseElevation() {
    return switch (widget.size) {
      VanHubCardSize.small => 2,
      VanHubCardSize.medium => 4,
      VanHubCardSize.large => 6,
    };
  }

  /// 获取悬停高度
  double _getHoverElevation() {
    return switch (widget.size) {
      VanHubCardSize.small => 4,
      VanHubCardSize.medium => 8,
      VanHubCardSize.large => 12,
    };
  }

  /// 获取动画阴影
  BoxShadow _getAnimatedShadow() {
    final elevation = _elevationAnimation.value;
    return BoxShadow(
      color: VanHubDesignSystem.neutralBlack.withValues(alpha: 0.1),
      offset: Offset(0, elevation / 2),
      blurRadius: elevation,
      spreadRadius: 0,
    );
  }

  // 交互事件处理
  void _onHoverEnter(event) {
    if (!_isHovered && widget.enableHover) {
      setState(() => _isHovered = true);
      _animationController.forward();
    }
  }

  void _onHoverExit(event) {
    if (_isHovered) {
      setState(() => _isHovered = false);
      _animationController.reverse();
    }
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
  }
}

/// 卡片样式配置
class _CardStyle {
  final BoxDecoration decoration;
  final double borderRadius;

  const _CardStyle({
    required this.decoration,
    required this.borderRadius,
  });
}

/// 卡片颜色方案
class _CardColorScheme {
  final Color backgroundColor;
  final Color borderColor;

  const _CardColorScheme({
    required this.backgroundColor,
    required this.borderColor,
  });
}
