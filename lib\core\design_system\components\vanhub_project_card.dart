/// VanHub Project Card Component 2.0
/// 
/// 高端项目卡片组件，支持3D翻转和状态可视化
/// 
/// 特性：
/// - 项目状态可视化
/// - 进度动画效果
/// - 3D翻转交互
/// - 智能标签系统
/// - 情感化设计
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';
import '../utils/responsive_utils.dart';
import 'vanhub_card_v2.dart';

/// 项目状态枚举
enum ProjectStatus {
  planning,     // 规划中
  inProgress,   // 进行中
  paused,       // 暂停
  completed,    // 已完成
  cancelled,    // 已取消
}

/// 项目优先级枚举
enum ProjectPriority {
  low,      // 低优先级
  medium,   // 中等优先级
  high,     // 高优先级
  urgent,   // 紧急
}

/// 项目数据模型
class ProjectData {
  final String id;
  final String name;
  final String description;
  final ProjectStatus status;
  final ProjectPriority priority;
  final double progress; // 0.0 - 1.0
  final DateTime createdAt;
  final DateTime? deadline;
  final String? imageUrl;
  final List<String> tags;
  final double budget;
  final double spent;
  final int teamSize;

  const ProjectData({
    required this.id,
    required this.name,
    required this.description,
    required this.status,
    required this.priority,
    required this.progress,
    required this.createdAt,
    this.deadline,
    this.imageUrl,
    this.tags = const [],
    this.budget = 0.0,
    this.spent = 0.0,
    this.teamSize = 1,
  });

  /// 获取状态颜色
  Color get statusColor {
    switch (status) {
      case ProjectStatus.planning:
        return VanHubSemanticColors.info;
      case ProjectStatus.inProgress:
        return VanHubSemanticColors.warning;
      case ProjectStatus.paused:
        return VanHubSemanticColors.textSecondary;
      case ProjectStatus.completed:
        return VanHubSemanticColors.success;
      case ProjectStatus.cancelled:
        return VanHubSemanticColors.error;
    }
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case ProjectStatus.planning:
        return '规划中';
      case ProjectStatus.inProgress:
        return '进行中';
      case ProjectStatus.paused:
        return '暂停';
      case ProjectStatus.completed:
        return '已完成';
      case ProjectStatus.cancelled:
        return '已取消';
    }
  }

  /// 获取优先级颜色
  Color get priorityColor {
    switch (priority) {
      case ProjectPriority.low:
        return VanHubSemanticColors.success;
      case ProjectPriority.medium:
        return VanHubSemanticColors.warning;
      case ProjectPriority.high:
        return VanHubSemanticColors.error;
      case ProjectPriority.urgent:
        return VanHubBrandColors.secondary;
    }
  }

  /// 获取优先级文本
  String get priorityText {
    switch (priority) {
      case ProjectPriority.low:
        return '低';
      case ProjectPriority.medium:
        return '中';
      case ProjectPriority.high:
        return '高';
      case ProjectPriority.urgent:
        return '紧急';
    }
  }

  /// 是否逾期
  bool get isOverdue {
    if (deadline == null || status == ProjectStatus.completed) return false;
    return DateTime.now().isAfter(deadline!);
  }

  /// 剩余天数
  int? get daysRemaining {
    if (deadline == null) return null;
    final now = DateTime.now();
    final difference = deadline!.difference(now).inDays;
    return difference;
  }
}

/// VanHub项目卡片组件
class VanHubProjectCard extends StatefulWidget {
  final ProjectData project;
  final bool enableFlipAnimation;
  final bool enableProgressAnimation;
  final bool enableStatusIndicator;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;
  
  // 显示配置
  final bool showProgress;
  final bool showTags;
  final bool showStats;
  final bool showActions;
  
  // 动画配置
  final Duration flipDuration;
  final Duration progressAnimationDuration;

  const VanHubProjectCard({
    super.key,
    required this.project,
    this.enableFlipAnimation = true,
    this.enableProgressAnimation = true,
    this.enableStatusIndicator = true,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onShare,
    this.showProgress = true,
    this.showTags = true,
    this.showStats = true,
    this.showActions = true,
    this.flipDuration = VanHubAnimationDurations.slow,
    this.progressAnimationDuration = VanHubAnimationDurations.normal,
  });

  @override
  State<VanHubProjectCard> createState() => _VanHubProjectCardState();
}

class _VanHubProjectCardState extends State<VanHubProjectCard>
    with TickerProviderStateMixin {
  late AnimationController _flipController;
  late AnimationController _progressController;
  late Animation<double> _flipAnimation;
  late Animation<double> _progressAnimation;
  
  bool _isFlipped = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _flipController = AnimationController(
      duration: widget.flipDuration,
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: widget.progressAnimationDuration,
      vsync: this,
    );

    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: VanHubAnimationCurves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.project.progress,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: VanHubAnimationCurves.easeOut,
    ));

    // 启动进度动画
    if (widget.enableProgressAnimation) {
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _flipController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// 处理卡片翻转
  void _handleFlip() {
    if (!widget.enableFlipAnimation) return;
    
    setState(() {
      _isFlipped = !_isFlipped;
    });
    
    if (_isFlipped) {
      _flipController.forward();
    } else {
      _flipController.reverse();
    }
    
    HapticFeedback.lightImpact();
  }

  /// 处理悬停
  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
  }

  @override
  Widget build(BuildContext context) {
    try {
      return MouseRegion(
        onEnter: (_) => _handleHover(true),
        onExit: (_) => _handleHover(false),
        child: GestureDetector(
          onTap: widget.onTap,
          onDoubleTap: widget.enableFlipAnimation ? _handleFlip : null,
          child: AnimatedBuilder(
            animation: _flipAnimation,
            builder: (context, child) {
              try {
                final isShowingFront = _flipAnimation.value < 0.5;

                return Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(_flipAnimation.value * 3.14159),
                  child: isShowingFront ? _buildFrontCard() : _buildBackCard(),
                );
              } catch (e) {
                debugPrint('VanHubProjectCard AnimatedBuilder错误: $e');
                return _buildFallbackCard();
              }
            },
          ),
        ),
      );
    } catch (e) {
      debugPrint('VanHubProjectCard构建错误: $e');
      return _buildFallbackCard();
    }
  }

  /// 构建备选卡片（简化版本）
  Widget _buildFallbackCard() {
    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.project.name,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            widget.project.description,
            style: Theme.of(context).textTheme.bodyMedium,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: widget.project.statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.project.statusText,
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.project.statusColor,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '进度: ${(widget.project.progress * 100).toInt()}%',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建正面卡片
  Widget _buildFrontCard() {
    try {
      return VanHubCardV2.interactive(
        size: VanHubCardSize.lg,
        enable3DEffect: VanHubResponsiveUtils.isDesktop(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部：项目名称和状态
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.project.name,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: VanHubSemanticColors.getTextColor(context),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (widget.enableStatusIndicator) ...[
                  SizedBox(width: VanHubResponsiveSpacing.sm),
                  _buildStatusBadge(),
                ],
              ],
            ),
          
          SizedBox(height: VanHubResponsiveSpacing.sm),
          
          // 描述
          Text(
            widget.project.description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.md),
          
          // 进度条
          if (widget.showProgress) ...[
            _buildProgressSection(),
            SizedBox(height: VanHubResponsiveSpacing.md),
          ],
          
          // 标签
          if (widget.showTags && widget.project.tags.isNotEmpty) ...[
            _buildTagsSection(),
            SizedBox(height: VanHubResponsiveSpacing.md),
          ],
          
          const Spacer(),
          
          // 底部信息
          Row(
            children: [
              // 优先级
              _buildPriorityBadge(),
              const Spacer(),
              // 截止日期
              if (widget.project.deadline != null)
                _buildDeadlineInfo(),
            ],
          ),
        ],
      ),
    );
    } catch (e) {
      debugPrint('VanHubProjectCard _buildFrontCard错误: $e');
      return _buildFallbackCard();
    }
  }

  /// 构建背面卡片（详细信息）
  Widget _buildBackCard() {
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()..rotateY(3.14159),
      child: VanHubCardV2(
        variant: VanHubCardVariant.filled,
        size: VanHubCardSize.lg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Text(
                  '项目详情',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: VanHubSemanticColors.getTextColor(context),
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.flip_to_front),
                  onPressed: _handleFlip,
                  iconSize: 20,
                ),
              ],
            ),
            
            SizedBox(height: VanHubResponsiveSpacing.md),
            
            // 统计信息
            if (widget.showStats) ...[
              _buildStatsSection(),
              SizedBox(height: VanHubResponsiveSpacing.md),
            ],
            
            const Spacer(),
            
            // 操作按钮
            if (widget.showActions) _buildActionsSection(),
          ],
        ),
      ),
    );
  }

  /// 构建状态徽章
  Widget _buildStatusBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.project.statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.project.statusColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        widget.project.statusText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: widget.project.statusColor,
        ),
      ),
    );
  }

  /// 构建进度部分
  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '进度',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
            const Spacer(),
            Text(
              '${(widget.project.progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: VanHubSemanticColors.getTextColor(context),
              ),
            ),
          ],
        ),
        SizedBox(height: VanHubResponsiveSpacing.xs),
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor: VanHubSemanticColors.getBorderColor(context),
              valueColor: AlwaysStoppedAnimation<Color>(
                widget.project.statusColor,
              ),
              minHeight: 6,
            );
          },
        ),
      ],
    );
  }

  /// 构建标签部分
  Widget _buildTagsSection() {
    return Wrap(
      spacing: VanHubResponsiveSpacing.xs,
      runSpacing: VanHubResponsiveSpacing.xs,
      children: widget.project.tags.take(3).map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: VanHubBrandColors.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            tag,
            style: TextStyle(
              fontSize: 10,
              color: VanHubBrandColors.onPrimaryContainer,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建优先级徽章
  Widget _buildPriorityBadge() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.flag,
          size: 14,
          color: widget.project.priorityColor,
        ),
        SizedBox(width: VanHubResponsiveSpacing.xs),
        Text(
          widget.project.priorityText,
          style: TextStyle(
            fontSize: 12,
            color: widget.project.priorityColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建截止日期信息
  Widget _buildDeadlineInfo() {
    final daysRemaining = widget.project.daysRemaining;
    final isOverdue = widget.project.isOverdue;
    
    Color color = VanHubSemanticColors.getTextColor(context, secondary: true);
    if (isOverdue) {
      color = VanHubSemanticColors.error;
    } else if (daysRemaining != null && daysRemaining <= 7) {
      color = VanHubSemanticColors.warning;
    }
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          isOverdue ? Icons.warning : Icons.schedule,
          size: 14,
          color: color,
        ),
        SizedBox(width: VanHubResponsiveSpacing.xs),
        Text(
          isOverdue 
              ? '已逾期'
              : daysRemaining != null 
                  ? '$daysRemaining天'
                  : '无期限',
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建统计部分
  Widget _buildStatsSection() {
    return Column(
      children: [
        _buildStatRow('预算', '¥${widget.project.budget.toStringAsFixed(0)}'),
        _buildStatRow('已花费', '¥${widget.project.spent.toStringAsFixed(0)}'),
        _buildStatRow('团队规模', '${widget.project.teamSize}人'),
        _buildStatRow('创建时间', _formatDate(widget.project.createdAt)),
      ],
    );
  }

  /// 构建统计行
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: VanHubResponsiveSpacing.sm),
      child: Row(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: VanHubSemanticColors.getTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作部分
  Widget _buildActionsSection() {
    return Row(
      children: [
        if (widget.onEdit != null)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: widget.onEdit,
              icon: const Icon(Icons.edit, size: 16),
              label: const Text('编辑'),
              style: OutlinedButton.styleFrom(
                foregroundColor: VanHubBrandColors.primary,
              ),
            ),
          ),
        if (widget.onEdit != null && widget.onShare != null)
          SizedBox(width: VanHubResponsiveSpacing.sm),
        if (widget.onShare != null)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: widget.onShare,
              icon: const Icon(Icons.share, size: 16),
              label: const Text('分享'),
              style: OutlinedButton.styleFrom(
                foregroundColor: VanHubSemanticColors.success,
              ),
            ),
          ),
      ],
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
