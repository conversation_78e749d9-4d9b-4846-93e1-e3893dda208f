import 'dart:math' as math;
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/cost_analysis_service.dart';
import '../../domain/entities/cost_analysis.dart';
import '../../../project/domain/repositories/project_repository.dart';
import '../../../material/domain/repositories/material_repository.dart';

/// 成本分析服务实现
class CostAnalysisServiceImpl implements CostAnalysisService {
  final ProjectRepository projectRepository;
  final MaterialRepository materialRepository;

  const CostAnalysisServiceImpl({
    required this.projectRepository,
    required this.materialRepository,
  });

  @override
  Future<Either<Failure, CostAnalysis>> analyzeProjectCost(String projectId) async {
    try {
      // 获取项目信息
      final projectResult = await projectRepository.getProject(projectId);
      
      return await projectResult.fold(
        (failure) async => Left(failure),
        (project) async {
          // 获取项目材料
          final materialsResult = await materialRepository.getProjectMaterials(projectId);
          
          return await materialsResult.fold(
            (failure) async => Left(failure),
            (materials) async {
              // 计算基础成本数据
              final totalBudget = project.budget ?? 0.0;
              final actualCost = materials.fold<double>(0, (sum, material) => sum + material.price);
              final remainingBudget = totalBudget - actualCost;
              final budgetUtilization = totalBudget > 0 ? (actualCost / totalBudget) * 100 : 0;
              final isOverBudget = actualCost > totalBudget;

              // 分类成本分析
              final categoryBreakdown = <String, double>{};
              for (final material in materials) {
                categoryBreakdown[material.category] = 
                    (categoryBreakdown[material.category] ?? 0) + material.price;
              }

              // 生成成本项目列表
              final topExpenses = materials
                  .map((material) => CostItem(
                        id: material.id,
                        name: material.name,
                        category: material.category,
                        amount: material.price,
                        date: material.createdAt,
                        description: material.description,
                        supplier: material.brand,
                      ))
                  .toList()
                ..sort((a, b) => b.amount.compareTo(a.amount));

              // 生成趋势摘要
              final trendSummary = _generateTrendSummary(materials);

              // 风险评估
              final riskLevel = _assessRiskLevel(budgetUtilization, isOverBudget);
              final warnings = _generateWarnings(budgetUtilization, isOverBudget, categoryBreakdown);

              final analysis = CostAnalysis(
                projectId: projectId,
                totalBudget: totalBudget,
                actualCost: actualCost,
                remainingBudget: remainingBudget,
                budgetUtilization: budgetUtilization,
                isOverBudget: isOverBudget,
                analysisDate: DateTime.now(),
                categoryBreakdown: categoryBreakdown,
                topExpenses: topExpenses.take(10).toList(),
                trendSummary: trendSummary,
                riskLevel: riskLevel,
                warnings: warnings,
              );

              return Right(analysis);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '成本分析失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BudgetAlert>> checkBudgetAlert(String projectId) async {
    try {
      final analysisResult = await analyzeProjectCost(projectId);
      
      return analysisResult.fold(
        (failure) => Left(failure),
        (analysis) {
          final level = _getBudgetAlertLevel(analysis.budgetUtilization, analysis.isOverBudget);
          final message = _getBudgetAlertMessage(level, analysis.budgetUtilization);
          final projectedUtilization = _projectBudgetUtilization(analysis);
          final recommendations = _getBudgetRecommendations(level, analysis);

          final alert = BudgetAlert(
            projectId: projectId,
            level: level,
            message: message,
            currentUtilization: analysis.budgetUtilization,
            projectedUtilization: projectedUtilization,
            alertDate: DateTime.now(),
            recommendations: recommendations,
          );

          return Right(alert);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '预算警告检查失败: $e'));
    }
  }

  @override
  Future<Either<Failure, CostTrendAnalysis>> analyzeCostTrend(
    String projectId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final materialsResult = await materialRepository.getProjectMaterials(projectId);
      
      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          final now = DateTime.now();
          final start = startDate ?? now.subtract(const Duration(days: 180));
          final end = endDate ?? now;

          // 筛选时间范围内的材料
          final filteredMaterials = materials.where((material) {
            return material.createdAt.isAfter(start) && material.createdAt.isBefore(end);
          }).toList();

          // 生成趋势数据点
          final dataPoints = filteredMaterials.map((material) => TrendDataPoint(
            date: material.createdAt,
            amount: material.price,
            category: material.category,
            note: material.name,
          )).toList()
            ..sort((a, b) => a.date.compareTo(b.date));

          // 计算统计数据
          final totalSpend = dataPoints.fold<double>(0, (sum, point) => sum + point.amount);
          final daysDiff = end.difference(start).inDays;
          final averageMonthlySpend = daysDiff > 0 ? (totalSpend / daysDiff) * 30 : 0;

          // 分析整体趋势
          final overallTrend = _analyzeTrend(dataPoints);

          // 生成洞察
          final insights = _generateTrendInsights(dataPoints, overallTrend);

          final trendAnalysis = CostTrendAnalysis(
            projectId: projectId,
            startDate: start,
            endDate: end,
            dataPoints: dataPoints,
            averageMonthlySpend: averageMonthlySpend,
            totalSpend: totalSpend,
            overallTrend: overallTrend,
            insights: insights,
          );

          return Right(trendAnalysis);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '成本趋势分析失败: $e'));
    }
  }

  @override
  Future<Either<Failure, CostStructureAnalysis>> analyzeCostStructure(String projectId) async {
    try {
      final analysisResult = await analyzeProjectCost(projectId);
      
      return analysisResult.fold(
        (failure) => Left(failure),
        (analysis) {
          // 供应商分布分析
          final supplierDistribution = <String, double>{};
          for (final expense in analysis.topExpenses) {
            final supplier = expense.supplier ?? '未知供应商';
            supplierDistribution[supplier] = 
                (supplierDistribution[supplier] ?? 0) + expense.amount;
          }

          // 分类详细分析
          final categories = analysis.categoryBreakdown.entries.map((entry) {
            final categoryExpenses = analysis.topExpenses
                .where((expense) => expense.category == entry.key)
                .toList();
            
            return CostCategory(
              name: entry.key,
              amount: entry.value,
              percentage: (entry.value / analysis.actualCost) * 100,
              itemCount: categoryExpenses.length,
              averageItemCost: categoryExpenses.isNotEmpty 
                  ? entry.value / categoryExpenses.length 
                  : 0,
              trend: _getCategoryTrend(entry.key, categoryExpenses),
            );
          }).toList();

          // 计算集中度指数 (Herfindahl Index)
          final concentrationIndex = _calculateConcentrationIndex(analysis.categoryBreakdown);

          // 生成建议
          final recommendations = _generateStructureRecommendations(categories, concentrationIndex);

          final structureAnalysis = CostStructureAnalysis(
            projectId: projectId,
            categoryDistribution: analysis.categoryBreakdown,
            supplierDistribution: supplierDistribution,
            categories: categories,
            concentrationIndex: concentrationIndex,
            recommendations: recommendations,
          );

          return Right(structureAnalysis);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '成本结构分析失败: $e'));
    }
  }

  @override
  Future<Either<Failure, CostForecast>> forecastCost(
    String projectId, {
    int forecastDays = 30,
  }) async {
    try {
      final trendResult = await analyzeCostTrend(projectId);
      
      return trendResult.fold(
        (failure) => Left(failure),
        (trendAnalysis) {
          final predictions = <ForecastDataPoint>[];
          final now = DateTime.now();
          
          // 使用线性回归进行简单预测
          final dailyAverage = trendAnalysis.averageMonthlySpend / 30;
          
          for (int i = 1; i <= forecastDays; i++) {
            final date = now.add(Duration(days: i));
            final baseAmount = dailyAverage * i;
            
            // 添加一些随机变化和趋势调整
            final trendMultiplier = _getTrendMultiplier(trendAnalysis.overallTrend);
            final predictedAmount = baseAmount * trendMultiplier;
            
            // 计算置信区间
            final variance = predictedAmount * 0.2; // 20% 方差
            final lowerBound = math.max(0, predictedAmount - variance);
            final upperBound = predictedAmount + variance;
            
            predictions.add(ForecastDataPoint(
              date: date,
              predictedAmount: predictedAmount,
              lowerBound: lowerBound,
              upperBound: upperBound,
              category: '总体预测',
            ));
          }

          final totalPredictedCost = predictions.fold<double>(
            0, (sum, point) => sum + point.predictedAmount,
          );

          final forecast = CostForecast(
            projectId: projectId,
            forecastDate: now,
            forecastDays: forecastDays,
            predictions: predictions,
            totalPredictedCost: totalPredictedCost,
            confidenceLevel: 0.8, // 80% 置信度
            methodology: '基于历史趋势的线性回归预测',
            assumptions: [
              '假设当前支出模式持续',
              '不考虑季节性因素',
              '不考虑外部经济因素影响',
            ],
          );

          return Right(forecast);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '成本预测失败: $e'));
    }
  }

  // 辅助方法
  CostTrendSummary _generateTrendSummary(List<dynamic> materials) {
    // 生成月度数据
    final monthlyData = <MonthlySpending>[];
    final now = DateTime.now();
    
    for (int i = 5; i >= 0; i--) {
      final month = now.month - i;
      final year = month <= 0 ? now.year - 1 : now.year;
      final adjustedMonth = month <= 0 ? month + 12 : month;
      
      final monthlyAmount = 1000.0 + (i * 200); // 模拟数据
      
      monthlyData.add(MonthlySpending(
        year: year,
        month: adjustedMonth,
        amount: monthlyAmount,
        itemCount: 5 + i,
      ));
    }

    final monthlyAverage = monthlyData.fold<double>(0, (sum, data) => sum + data.amount) / monthlyData.length;
    final growthRate = monthlyData.length > 1 
        ? ((monthlyData.last.amount - monthlyData.first.amount) / monthlyData.first.amount) * 100
        : 0.0;
    
    String trend;
    if (growthRate > 10) {
      trend = 'increasing';
    } else if (growthRate < -10) {
      trend = 'decreasing';
    } else {
      trend = 'stable';
    }

    return CostTrendSummary(
      monthlyAverage: monthlyAverage,
      growthRate: growthRate,
      trend: trend,
      monthlyData: monthlyData,
    );
  }

  String _assessRiskLevel(double budgetUtilization, bool isOverBudget) {
    if (isOverBudget) return 'critical';
    if (budgetUtilization > 90) return 'high';
    if (budgetUtilization > 70) return 'medium';
    return 'low';
  }

  List<String> _generateWarnings(double budgetUtilization, bool isOverBudget, Map<String, double> categoryBreakdown) {
    final warnings = <String>[];
    
    if (isOverBudget) {
      warnings.add('项目已超出预算');
    } else if (budgetUtilization > 90) {
      warnings.add('预算即将耗尽，请注意控制支出');
    }
    
    // 检查分类集中度
    if (categoryBreakdown.isNotEmpty) {
      final maxCategory = categoryBreakdown.entries.reduce((a, b) => a.value > b.value ? a : b);
      final totalCost = categoryBreakdown.values.fold<double>(0, (sum, value) => sum + value);
      if (maxCategory.value / totalCost > 0.6) {
        warnings.add('${maxCategory.key}分类支出过于集中，占比${((maxCategory.value / totalCost) * 100).toStringAsFixed(1)}%');
      }
    }
    
    return warnings;
  }

  AlertLevel _getBudgetAlertLevel(double budgetUtilization, bool isOverBudget) {
    if (isOverBudget) return AlertLevel.critical;
    if (budgetUtilization > 90) return AlertLevel.high;
    if (budgetUtilization > 70) return AlertLevel.medium;
    return AlertLevel.low;
  }

  String _getBudgetAlertMessage(AlertLevel level, double utilization) {
    switch (level) {
      case AlertLevel.critical:
        return '预算已超支！当前使用率：${utilization.toStringAsFixed(1)}%';
      case AlertLevel.high:
        return '预算紧张！当前使用率：${utilization.toStringAsFixed(1)}%';
      case AlertLevel.medium:
        return '预算正常，当前使用率：${utilization.toStringAsFixed(1)}%';
      case AlertLevel.low:
        return '预算充足，当前使用率：${utilization.toStringAsFixed(1)}%';
    }
  }

  double _projectBudgetUtilization(CostAnalysis analysis) {
    // 简单的线性投影，基于当前趋势
    final growthRate = analysis.trendSummary.growthRate / 100;
    return analysis.budgetUtilization * (1 + growthRate);
  }

  List<String> _getBudgetRecommendations(AlertLevel level, CostAnalysis analysis) {
    final recommendations = <String>[];
    
    switch (level) {
      case AlertLevel.critical:
        recommendations.addAll([
          '立即停止非必要支出',
          '重新评估项目预算',
          '寻找成本削减机会',
        ]);
        break;
      case AlertLevel.high:
        recommendations.addAll([
          '密切监控剩余支出',
          '优先购买必需品',
          '考虑延后非紧急采购',
        ]);
        break;
      case AlertLevel.medium:
        recommendations.addAll([
          '继续按计划执行',
          '定期检查预算状况',
        ]);
        break;
      case AlertLevel.low:
        recommendations.add('预算状况良好，可按计划进行');
        break;
    }
    
    return recommendations;
  }

  String _analyzeTrend(List<TrendDataPoint> dataPoints) {
    if (dataPoints.length < 2) return 'stable';
    
    final firstHalf = dataPoints.take(dataPoints.length ~/ 2).toList();
    final secondHalf = dataPoints.skip(dataPoints.length ~/ 2).toList();
    
    final firstAvg = firstHalf.fold<double>(0, (sum, point) => sum + point.amount) / firstHalf.length;
    final secondAvg = secondHalf.fold<double>(0, (sum, point) => sum + point.amount) / secondHalf.length;
    
    final changeRate = (secondAvg - firstAvg) / firstAvg;
    
    if (changeRate > 0.1) return 'increasing';
    if (changeRate < -0.1) return 'decreasing';
    return 'stable';
  }

  List<TrendInsight> _generateTrendInsights(List<TrendDataPoint> dataPoints, String overallTrend) {
    final insights = <TrendInsight>[];
    
    // 趋势洞察
    switch (overallTrend) {
      case 'increasing':
        insights.add(const TrendInsight(
          type: 'trend',
          title: '支出呈上升趋势',
          description: '项目支出在分析期间呈现上升趋势，需要关注成本控制',
          impact: 0.8,
          recommendation: '建议制定成本控制措施，优化采购策略',
        ));
        break;
      case 'decreasing':
        insights.add(const TrendInsight(
          type: 'trend',
          title: '支出呈下降趋势',
          description: '项目支出在分析期间呈现下降趋势，成本控制效果良好',
          impact: 0.6,
          recommendation: '继续保持当前的成本控制策略',
        ));
        break;
      case 'stable':
        insights.add(const TrendInsight(
          type: 'trend',
          title: '支出相对稳定',
          description: '项目支出在分析期间保持相对稳定',
          impact: 0.4,
          recommendation: '维持当前支出水平，定期监控变化',
        ));
        break;
    }
    
    return insights;
  }

  String _getCategoryTrend(String category, List<CostItem> expenses) {
    if (expenses.length < 2) return 'stable';
    
    expenses.sort((a, b) => a.date.compareTo(b.date));
    final firstHalf = expenses.take(expenses.length ~/ 2).toList();
    final secondHalf = expenses.skip(expenses.length ~/ 2).toList();
    
    final firstAvg = firstHalf.fold<double>(0, (sum, item) => sum + item.amount) / firstHalf.length;
    final secondAvg = secondHalf.fold<double>(0, (sum, item) => sum + item.amount) / secondHalf.length;
    
    if (secondAvg > firstAvg * 1.1) return 'increasing';
    if (secondAvg < firstAvg * 0.9) return 'decreasing';
    return 'stable';
  }

  double _calculateConcentrationIndex(Map<String, double> categoryBreakdown) {
    final totalCost = categoryBreakdown.values.fold<double>(0, (sum, value) => sum + value);
    if (totalCost == 0) return 0;
    
    double herfindahlIndex = 0;
    for (final amount in categoryBreakdown.values) {
      final share = amount / totalCost;
      herfindahlIndex += share * share;
    }
    
    return herfindahlIndex;
  }

  List<String> _generateStructureRecommendations(List<CostCategory> categories, double concentrationIndex) {
    final recommendations = <String>[];
    
    if (concentrationIndex > 0.5) {
      recommendations.add('成本过于集中在少数分类，建议分散风险');
    }
    
    final highCostCategories = categories.where((cat) => cat.percentage > 30).toList();
    for (final category in highCostCategories) {
      recommendations.add('${category.name}分类支出较高，建议寻找优化机会');
    }
    
    return recommendations;
  }

  double _getTrendMultiplier(String trend) {
    switch (trend) {
      case 'increasing':
        return 1.1; // 10% 增长
      case 'decreasing':
        return 0.9; // 10% 下降
      case 'stable':
      default:
        return 1.0; // 保持稳定
    }
  }

  // 其他接口的简化实现
  @override
  Future<Either<Failure, CostComparison>> compareProjectCosts(List<String> projectIds) async {
    // TODO: 实现项目成本比较
    return Left(UnknownFailure(message: '项目成本比较功能开发中'));
  }

  @override
  Future<Either<Failure, List<CostOptimizationSuggestion>>> generateOptimizationSuggestions(String projectId) async {
    // TODO: 实现成本优化建议
    return Left(UnknownFailure(message: '成本优化建议功能开发中'));
  }

  @override
  Future<Either<Failure, ROIAnalysis>> calculateROI(String projectId) async {
    // TODO: 实现ROI计算
    return Left(UnknownFailure(message: 'ROI分析功能开发中'));
  }
}
