import 'dart:io';
import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';
import 'package:path/path.dart' as path;
import '../errors/failures.dart';

/// 视频处理配置
class VideoProcessingConfig {
  final int maxWidth;
  final int maxHeight;
  final int maxBitrate; // kbps
  final int maxFrameRate;
  final String codec;
  final bool generateThumbnail;
  final int thumbnailTimeSeconds;
  final int thumbnailWidth;
  final int thumbnailHeight;
  final bool removeAudio;
  final int maxDurationSeconds;

  const VideoProcessingConfig({
    this.maxWidth = 1920,
    this.maxHeight = 1080,
    this.maxBitrate = 2000, // 2Mbps
    this.maxFrameRate = 30,
    this.codec = 'h264',
    this.generateThumbnail = true,
    this.thumbnailTimeSeconds = 1,
    this.thumbnailWidth = 320,
    this.thumbnailHeight = 240,
    this.removeAudio = false,
    this.maxDurationSeconds = 300, // 5分钟
  });

  /// 高质量配置
  static const VideoProcessingConfig highQuality = VideoProcessingConfig(
    maxWidth: 1920,
    maxHeight: 1080,
    maxBitrate: 5000,
    maxFrameRate: 60,
  );

  /// 中等质量配置
  static const VideoProcessingConfig mediumQuality = VideoProcessingConfig(
    maxWidth: 1280,
    maxHeight: 720,
    maxBitrate: 2000,
    maxFrameRate: 30,
  );

  /// 低质量配置
  static const VideoProcessingConfig lowQuality = VideoProcessingConfig(
    maxWidth: 854,
    maxHeight: 480,
    maxBitrate: 1000,
    maxFrameRate: 24,
  );

  /// Web优化配置
  static const VideoProcessingConfig webOptimized = VideoProcessingConfig(
    maxWidth: 1280,
    maxHeight: 720,
    maxBitrate: 1500,
    maxFrameRate: 30,
    generateThumbnail: true,
    removeAudio: false,
  );
}

/// 视频信息
class VideoInfo {
  final int width;
  final int height;
  final double duration; // 秒
  final double frameRate;
  final int bitrate;
  final String codec;
  final bool hasAudio;
  final int fileSize;
  final String format;

  const VideoInfo({
    required this.width,
    required this.height,
    required this.duration,
    required this.frameRate,
    required this.bitrate,
    required this.codec,
    required this.hasAudio,
    required this.fileSize,
    required this.format,
  });

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'duration': duration,
      'frameRate': frameRate,
      'bitrate': bitrate,
      'codec': codec,
      'hasAudio': hasAudio,
      'fileSize': fileSize,
      'format': format,
      'aspectRatio': width / height,
      'megapixels': (width * height / 1000000).toStringAsFixed(1),
    };
  }
}

/// 视频处理结果
class VideoProcessingResult {
  final File processedVideo;
  final File? thumbnail;
  final VideoInfo originalInfo;
  final VideoInfo processedInfo;
  final double compressionRatio;
  final Duration processingTime;
  final bool wasCompressed;

  const VideoProcessingResult({
    required this.processedVideo,
    this.thumbnail,
    required this.originalInfo,
    required this.processedInfo,
    required this.compressionRatio,
    required this.processingTime,
    required this.wasCompressed,
  });

  /// 压缩百分比
  double get compressionPercentage => (1 - compressionRatio) * 100;

  /// 是否有效压缩
  bool get isEffectiveCompression => processedInfo.fileSize < originalInfo.fileSize;

  Map<String, dynamic> toJson() {
    return {
      'originalInfo': originalInfo.toJson(),
      'processedInfo': processedInfo.toJson(),
      'compressionRatio': compressionRatio,
      'compressionPercentage': compressionPercentage,
      'processingTimeMs': processingTime.inMilliseconds,
      'wasCompressed': wasCompressed,
      'isEffectiveCompression': isEffectiveCompression,
      'hasThumbnail': thumbnail != null,
    };
  }
}

/// 视频处理服务
class VideoProcessingService {
  /// 处理视频
  Future<Either<Failure, VideoProcessingResult>> processVideo({
    required File inputFile,
    VideoProcessingConfig? config,
    String? outputPath,
    Function(double progress)? onProgress,
  }) async {
    final startTime = DateTime.now();
    
    try {
      final processingConfig = config ?? const VideoProcessingConfig();
      
      // 获取原始视频信息
      final originalInfoResult = await getVideoInfo(inputFile);
      final originalInfo = originalInfoResult.fold(
        (failure) => throw Exception('无法获取视频信息: ${failure.message}'),
        (info) => info,
      );

      // 检查视频时长限制
      if (originalInfo.duration > processingConfig.maxDurationSeconds) {
        return Left(ValidationFailure(
          message: '视频时长超过限制 (${processingConfig.maxDurationSeconds}秒)',
        ));
      }

      onProgress?.call(0.1);

      // 检查是否需要处理
      final needsProcessing = _needsProcessing(originalInfo, processingConfig);
      
      File processedVideo;
      VideoInfo processedInfo;
      bool wasCompressed = false;

      if (needsProcessing) {
        // 处理视频
        final processResult = await _processVideoFile(
          inputFile,
          processingConfig,
          outputPath,
          onProgress,
        );
        
        processedVideo = processResult.fold(
          (failure) => throw Exception('视频处理失败: ${failure.message}'),
          (file) => file,
        );

        // 获取处理后的视频信息
        final processedInfoResult = await getVideoInfo(processedVideo);
        processedInfo = processedInfoResult.fold(
          (failure) => originalInfo, // 如果获取失败，使用原始信息
          (info) => info,
        );
        
        wasCompressed = true;
      } else {
        // 不需要处理，直接使用原文件
        processedVideo = inputFile;
        processedInfo = originalInfo;
      }

      onProgress?.call(0.8);

      // 生成缩略图
      File? thumbnail;
      if (processingConfig.generateThumbnail) {
        final thumbnailResult = await generateThumbnail(
          videoFile: processedVideo,
          timeSeconds: processingConfig.thumbnailTimeSeconds,
          width: processingConfig.thumbnailWidth,
          height: processingConfig.thumbnailHeight,
          outputPath: outputPath,
        );
        
        thumbnail = thumbnailResult.fold(
          (failure) => null, // 缩略图生成失败不影响主流程
          (file) => file,
        );
      }

      onProgress?.call(1.0);

      final endTime = DateTime.now();
      final processingTime = endTime.difference(startTime);

      final result = VideoProcessingResult(
        processedVideo: processedVideo,
        thumbnail: thumbnail,
        originalInfo: originalInfo,
        processedInfo: processedInfo,
        compressionRatio: processedInfo.fileSize / originalInfo.fileSize,
        processingTime: processingTime,
        wasCompressed: wasCompressed,
      );

      return Right(result);
    } catch (e) {
      return Left(UnknownFailure(message: '视频处理失败: $e'));
    }
  }

  /// 生成视频缩略图
  Future<Either<Failure, File>> generateThumbnail({
    required File videoFile,
    int timeSeconds = 1,
    int width = 320,
    int height = 240,
    String? outputPath,
  }) async {
    try {
      // TODO: 实现真正的视频缩略图生成
      // 这里需要使用FFmpeg或类似的库
      // 暂时创建一个占位符文件
      
      final outputFile = await _createThumbnailPlaceholder(
        videoFile,
        outputPath,
        width,
        height,
      );
      
      return Right(outputFile);
    } catch (e) {
      return Left(UnknownFailure(message: '生成视频缩略图失败: $e'));
    }
  }

  /// 获取视频信息
  Future<Either<Failure, VideoInfo>> getVideoInfo(File videoFile) async {
    try {
      // TODO: 实现真正的视频信息获取
      // 这里需要使用FFprobe或类似的工具
      // 暂时返回模拟数据
      
      final fileSize = await videoFile.length();
      
      final info = VideoInfo(
        width: 1920,
        height: 1080,
        duration: 60.0, // 60秒
        frameRate: 30.0,
        bitrate: 2000,
        codec: 'h264',
        hasAudio: true,
        fileSize: fileSize,
        format: path.extension(videoFile.path).substring(1),
      );
      
      return Right(info);
    } catch (e) {
      return Left(UnknownFailure(message: '获取视频信息失败: $e'));
    }
  }

  /// 转换视频格式
  Future<Either<Failure, File>> convertFormat({
    required File inputFile,
    required String targetFormat,
    VideoProcessingConfig? config,
    String? outputPath,
  }) async {
    final processingConfig = config ?? const VideoProcessingConfig();
    
    return await processVideo(
      inputFile: inputFile,
      config: processingConfig,
      outputPath: outputPath,
    ).then((result) => result.fold(
      (failure) => Left(failure),
      (success) => Right(success.processedVideo),
    ));
  }

  /// 压缩视频
  Future<Either<Failure, File>> compressVideo({
    required File inputFile,
    int targetSizeKB = 10240, // 10MB
    String? outputPath,
  }) async {
    // 根据目标大小计算合适的比特率
    final originalSize = await inputFile.length();
    final compressionRatio = targetSizeKB * 1024 / originalSize;
    
    final config = VideoProcessingConfig(
      maxBitrate: (2000 * compressionRatio).round().clamp(500, 5000),
      maxWidth: compressionRatio < 0.5 ? 1280 : 1920,
      maxHeight: compressionRatio < 0.5 ? 720 : 1080,
    );

    return await processVideo(
      inputFile: inputFile,
      config: config,
      outputPath: outputPath,
    ).then((result) => result.fold(
      (failure) => Left(failure),
      (success) => Right(success.processedVideo),
    ));
  }

  /// 检查是否需要处理
  bool _needsProcessing(VideoInfo info, VideoProcessingConfig config) {
    return info.width > config.maxWidth ||
           info.height > config.maxHeight ||
           info.bitrate > config.maxBitrate ||
           info.frameRate > config.maxFrameRate;
  }

  /// 处理视频文件
  Future<Either<Failure, File>> _processVideoFile(
    File inputFile,
    VideoProcessingConfig config,
    String? outputPath,
    Function(double)? onProgress,
  ) async {
    try {
      // TODO: 实现真正的视频处理
      // 这里需要使用FFmpeg或类似的库
      // 暂时创建一个处理后的文件占位符
      
      // 模拟处理进度
      for (int i = 20; i <= 70; i += 10) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress?.call(i / 100);
      }
      
      final processedFile = await _createProcessedVideoPlaceholder(
        inputFile,
        outputPath,
      );
      
      return Right(processedFile);
    } catch (e) {
      return Left(UnknownFailure(message: '视频文件处理失败: $e'));
    }
  }

  /// 创建处理后的视频占位符
  Future<File> _createProcessedVideoPlaceholder(
    File originalFile,
    String? outputPath,
  ) async {
    String outputFilePath;
    
    if (outputPath != null) {
      if (outputPath.endsWith('/') || Directory(outputPath).existsSync()) {
        final originalName = path.basenameWithoutExtension(originalFile.path);
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        outputFilePath = '$outputPath/${originalName}_processed_$timestamp.mp4';
      } else {
        outputFilePath = outputPath;
      }
    } else {
      final originalDir = path.dirname(originalFile.path);
      final originalName = path.basenameWithoutExtension(originalFile.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      outputFilePath = '$originalDir/${originalName}_processed_$timestamp.mp4';
    }

    final outputFile = File(outputFilePath);
    
    // 复制原文件作为占位符（实际应用中应该是处理后的视频）
    await originalFile.copy(outputFilePath);
    
    return outputFile;
  }

  /// 创建缩略图占位符
  Future<File> _createThumbnailPlaceholder(
    File videoFile,
    String? outputPath,
    int width,
    int height,
  ) async {
    String outputFilePath;
    
    if (outputPath != null) {
      if (outputPath.endsWith('/') || Directory(outputPath).existsSync()) {
        final originalName = path.basenameWithoutExtension(videoFile.path);
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        outputFilePath = '$outputPath/${originalName}_thumb_$timestamp.jpg';
      } else {
        outputFilePath = outputPath;
      }
    } else {
      final originalDir = path.dirname(videoFile.path);
      final originalName = path.basenameWithoutExtension(videoFile.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      outputFilePath = '$originalDir/${originalName}_thumb_$timestamp.jpg';
    }

    final outputFile = File(outputFilePath);
    
    // 创建一个简单的占位符图片数据
    // 实际应用中应该从视频中提取帧
    final placeholderData = _createPlaceholderImageData(width, height);
    await outputFile.writeAsBytes(placeholderData);
    
    return outputFile;
  }

  /// 创建占位符图片数据
  Uint8List _createPlaceholderImageData(int width, int height) {
    // 创建一个简单的JPEG头部和数据
    // 这只是一个占位符，实际应用中应该生成真正的图片
    return Uint8List.fromList([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      // ... 更多JPEG数据
      0xFF, 0xD9, // JPEG结束标记
    ]);
  }
}
