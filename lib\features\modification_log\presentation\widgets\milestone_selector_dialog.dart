import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/milestone.dart';
import '../../domain/entities/enums.dart';
import '../../../../core/widgets/loading_widget.dart';

/// 里程碑选择对话框
class MilestoneSelectorDialog extends ConsumerStatefulWidget {
  final String projectId;
  final List<String> selectedMilestoneIds;

  const MilestoneSelectorDialog({
    super.key,
    required this.projectId,
    this.selectedMilestoneIds = const [],
  });

  @override
  ConsumerState<MilestoneSelectorDialog> createState() => _MilestoneSelectorDialogState();
}

class _MilestoneSelectorDialogState extends ConsumerState<MilestoneSelectorDialog> {
  final Set<String> _selectedMilestones = {};
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedMilestones.addAll(widget.selectedMilestoneIds);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 临时使用空列表，实际应该从timeline provider获取
    final milestonesAsync = AsyncValue.data(<Milestone>[]);

    return Dialog(
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.deepOrange,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.flag, color: Colors.white),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '选择里程碑',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // 搜索栏
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: '搜索里程碑...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value.toLowerCase();
                  });
                },
              ),
            ),
            
            // 里程碑列表
            Expanded(
              child: milestonesAsync.when(
                data: (milestones) => _buildMilestonesList(milestones),
                loading: () => const LoadingWidget(),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('加载失败: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          // 重试逻辑
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // 底部按钮
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(top: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Text(
                    '已选择 ${_selectedMilestones.length} 个里程碑',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _selectedMilestones.isNotEmpty
                        ? () => Navigator.of(context).pop(_selectedMilestones.toList())
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepOrange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMilestonesList(List<Milestone> milestones) {
    // 过滤搜索结果
    final filteredMilestones = milestones.where((milestone) {
      if (_searchQuery.isEmpty) return true;
      return milestone.title.toLowerCase().contains(_searchQuery) ||
             (milestone.description?.toLowerCase().contains(_searchQuery) ?? false);
    }).toList();

    if (filteredMilestones.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.flag_outlined, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? '该项目还没有里程碑' : '没有找到匹配的里程碑',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredMilestones.length,
      itemBuilder: (context, index) {
        final milestone = filteredMilestones[index];
        final isSelected = _selectedMilestones.contains(milestone.id);

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: CheckboxListTile(
            value: isSelected,
            onChanged: (selected) {
              setState(() {
                if (selected == true) {
                  _selectedMilestones.add(milestone.id);
                } else {
                  _selectedMilestones.remove(milestone.id);
                }
              });
            },
            title: Text(
              milestone.title,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (milestone.description != null) ...[
                  Text(
                    milestone.description!,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                ],
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '目标: ${milestone.date.toString().substring(0, 10)}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getStatusColor(milestone.status),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusText(milestone.status),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            secondary: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getStatusIcon(milestone.status),
                  color: _getStatusColor(milestone.status),
                ),
                const SizedBox(height: 2),
                Text(
                  '0%', // 临时固定值
                  style: const TextStyle(fontSize: 10),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(MilestoneStatus status) {
    switch (status) {
      case MilestoneStatus.planned:
        return Colors.grey;
      case MilestoneStatus.inProgress:
        return Colors.blue;
      case MilestoneStatus.completed:
        return Colors.green;
      case MilestoneStatus.overdue:
        return Colors.red;
      case MilestoneStatus.cancelled:
        return Colors.grey.shade400;
    }
  }

  String _getStatusText(MilestoneStatus status) {
    switch (status) {
      case MilestoneStatus.planned:
        return '待开始';
      case MilestoneStatus.inProgress:
        return '进行中';
      case MilestoneStatus.completed:
        return '已完成';
      case MilestoneStatus.overdue:
        return '已逾期';
      case MilestoneStatus.cancelled:
        return '已取消';
    }
  }

  IconData _getStatusIcon(MilestoneStatus status) {
    switch (status) {
      case MilestoneStatus.planned:
        return Icons.schedule;
      case MilestoneStatus.inProgress:
        return Icons.play_arrow;
      case MilestoneStatus.completed:
        return Icons.check_circle;
      case MilestoneStatus.overdue:
        return Icons.warning;
      case MilestoneStatus.cancelled:
        return Icons.cancel;
    }
  }
}

/// 显示里程碑选择对话框的便捷方法
Future<List<String>?> showMilestoneSelectorDialog({
  required BuildContext context,
  required String projectId,
  List<String> selectedMilestoneIds = const [],
}) {
  return showDialog<List<String>>(
    context: context,
    builder: (context) => MilestoneSelectorDialog(
      projectId: projectId,
      selectedMilestoneIds: selectedMilestoneIds,
    ),
  );
}
