import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/notification.dart';
import '../services/notification_service.dart';

/// 通知Repository接口
abstract class NotificationRepository {
  /// 创建通知
  Future<Either<Failure, Notification>> createNotification(Notification notification);

  /// 批量创建通知
  Future<Either<Failure, List<Notification>>> createNotifications(List<Notification> notifications);

  /// 获取通知详情
  Future<Either<Failure, Notification>> getNotification(String notificationId);

  /// 查询通知列表
  Future<Either<Failure, List<Notification>>> getNotifications(NotificationQuery query);

  /// 更新通知
  Future<Either<Failure, Notification>> updateNotification(Notification notification);

  /// 批量更新通知
  Future<Either<Failure, List<Notification>>> updateNotifications(List<Notification> notifications);

  /// 删除通知
  Future<Either<Failure, void>> deleteNotification(String notificationId);

  /// 批量删除通知
  Future<Either<Failure, void>> deleteNotifications(List<String> notificationIds);

  /// 获取用户通知数量
  Future<Either<Failure, int>> getNotificationCount(String userId);

  /// 获取未读通知数量
  Future<Either<Failure, int>> getUnreadCount(String userId);

  /// 获取通知统计信息
  Future<Either<Failure, NotificationStats>> getNotificationStats(String userId);

  /// 清理过期通知
  Future<Either<Failure, int>> cleanupExpiredNotifications();

  /// 清理已删除通知
  Future<Either<Failure, int>> cleanupDeletedNotifications();

  /// 订阅通知更新
  Stream<List<Notification>> subscribeToNotifications(String userId);

  /// 订阅未读通知数量更新
  Stream<int> subscribeToUnreadCount(String userId);
}
