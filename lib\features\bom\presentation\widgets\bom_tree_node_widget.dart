import 'package:flutter/material.dart';
import '../../domain/entities/tree_node.dart';
import '../../domain/entities/bom_item.dart';

/// BOM专用树节点组件
/// 严格遵循Clean Architecture原则，专门用于显示BOM树形结构的节点
class BomTreeNodeWidget extends StatelessWidget {
  /// 树节点数据
  final TreeNode node;
  
  /// 是否被选中
  final bool isSelected;
  
  /// 是否被高亮（搜索结果）
  final bool isHighlighted;
  
  /// 是否启用拖拽
  final bool isDragEnabled;
  
  /// 是否正在拖拽
  final bool isDragging;
  
  /// 是否为拖拽目标
  final bool isDropTarget;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 双击回调
  final VoidCallback? onDoubleTap;
  
  /// 展开状态变化回调
  final void Function(bool isExpanded)? onExpansionChanged;
  
  /// 编辑回调（仅BOM项目节点）
  final VoidCallback? onEdit;
  
  /// 删除回调（仅BOM项目节点）
  final VoidCallback? onDelete;
  
  /// 拖拽开始回调
  final VoidCallback? onDragStart;
  
  /// 拖拽结束回调
  final VoidCallback? onDragEnd;
  
  /// 拖拽进入回调
  final VoidCallback? onDragEnter;
  
  /// 拖拽离开回调
  final VoidCallback? onDragLeave;

  const BomTreeNodeWidget({
    super.key,
    required this.node,
    this.isSelected = false,
    this.isHighlighted = false,
    this.isDragEnabled = false,
    this.isDragging = false,
    this.isDropTarget = false,
    this.onTap,
    this.onDoubleTap,
    this.onExpansionChanged,
    this.onEdit,
    this.onDelete,
    this.onDragStart,
    this.onDragEnd,
    this.onDragEnter,
    this.onDragLeave,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget nodeWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 节点本身
        _buildNodeItem(theme),
        
        // 子节点（如果展开）
        if (node.isExpanded && node.children.isNotEmpty)
          _buildChildren(theme),
      ],
    );

    // 如果启用拖拽，包装在拖拽组件中
    if (isDragEnabled) {
      nodeWidget = _buildDraggableNode(nodeWidget);
    }

    return nodeWidget;
  }

  /// 构建可拖拽的节点
  Widget _buildDraggableNode(Widget child) {
    return Draggable<TreeNode>(
      data: node,
      feedback: Material(
        elevation: 4,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            node.label,
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: child,
      ),
      onDragStarted: onDragStart,
      onDragEnd: (_) => onDragEnd?.call(),
      child: DragTarget<TreeNode>(
        onWillAcceptWithDetails: (data) => data.id != node.id,
        onAcceptWithDetails: (data) {
          // 处理拖拽接受逻辑
        },
        onMove: (_) => onDragEnter?.call(),
        onLeave: (_) => onDragLeave?.call(),
        builder: (context, candidateData, rejectedData) {
          return Container(
            decoration: BoxDecoration(
              border: isDropTarget
                  ? Border.all(color: Colors.blue, width: 2)
                  : null,
              borderRadius: BorderRadius.circular(8),
            ),
            child: child,
          );
        },
      ),
    );
  }

  /// 构建节点项
  Widget _buildNodeItem(ThemeData theme) {
    return GestureDetector(
      onTap: onTap,
      onDoubleTap: onDoubleTap,
      child: Container(
        margin: EdgeInsets.only(left: node.depth * 16.0),
        decoration: BoxDecoration(
          color: _getBackgroundColor(theme),
          borderRadius: BorderRadius.circular(8),
          border: isHighlighted
              ? Border.all(
                  color: theme.colorScheme.primary,
                  width: 2,
                )
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 展开/折叠图标
              _buildExpansionIcon(theme),
              
              // 节点图标
              _buildNodeIcon(theme),
              
              const SizedBox(width: 8),
              
              // 节点标签
              Expanded(
                child: _buildNodeLabel(theme),
              ),
              
              // 节点统计信息或BOM项目信息
              if (node.type == BomTreeNodeType.category)
                _buildCategoryStats(theme)
              else if (node.type == BomTreeNodeType.bomItem && node.bomItem != null)
                _buildBomItemInfo(theme),
              
              // 操作按钮
              if (node.type == BomTreeNodeType.bomItem)
                _buildActionButtons(theme),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取背景颜色
  Color _getBackgroundColor(ThemeData theme) {
    if (isDragging) {
      return theme.colorScheme.primary.withValues(alpha: 0.3);
    }
    if (isSelected) {
      return theme.colorScheme.primary.withValues(alpha: 0.1);
    }
    if (isDropTarget) {
      return theme.colorScheme.secondary.withValues(alpha: 0.1);
    }
    return Colors.transparent;
  }

  /// 构建展开/折叠图标
  Widget _buildExpansionIcon(ThemeData theme) {
    if (node.children.isEmpty) {
      return const SizedBox(width: 24);
    }

    return GestureDetector(
      onTap: () => onExpansionChanged?.call(!node.isExpanded),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Icon(
          node.isExpanded ? Icons.expand_less : Icons.expand_more,
          size: 16,
          color: theme.colorScheme.onSurface,
        ),
      ),
    );
  }

  /// 构建节点图标
  Widget _buildNodeIcon(ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (node.type) {
      case BomTreeNodeType.category:
        iconData = node.isExpanded ? Icons.folder_open : Icons.folder;
        iconColor = theme.colorScheme.primary;
        break;
      case BomTreeNodeType.subcategory:
        iconData = Icons.folder_outlined;
        iconColor = theme.colorScheme.secondary;
        break;
      case BomTreeNodeType.bomItem:
        iconData = Icons.inventory_2;
        iconColor = theme.colorScheme.tertiary;
        break;
    }

    return Icon(
      iconData,
      size: 20,
      color: iconColor,
    );
  }

  /// 构建节点标签
  Widget _buildNodeLabel(ThemeData theme) {
    return Text(
      node.label,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: isSelected 
            ? theme.colorScheme.primary 
            : theme.colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建分类统计信息
  Widget _buildCategoryStats(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '${node.materialCount}项',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onPrimaryContainer,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建BOM项目信息
  Widget _buildBomItemInfo(ThemeData theme) {
    final bomItem = node.bomItem!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 状态指示器
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Color(int.parse(bomItem.status.colorHex.substring(1), radix: 16) + 0xFF000000),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            bomItem.status.displayName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontSize: 10,
            ),
          ),
        ),
        const SizedBox(height: 2),
        // 价格信息
        Text(
          '¥${bomItem.totalCost.toStringAsFixed(2)}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (onEdit != null)
          IconButton(
            onPressed: onEdit,
            icon: const Icon(Icons.edit, size: 16),
            tooltip: '编辑',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        if (onDelete != null)
          IconButton(
            onPressed: onDelete,
            icon: const Icon(Icons.delete, size: 16),
            tooltip: '删除',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
      ],
    );
  }

  /// 构建子节点
  Widget _buildChildren(ThemeData theme) {
    return Column(
      children: node.children.map((child) {
        return BomTreeNodeWidget(
          node: child,
          isSelected: isSelected,
          isHighlighted: isHighlighted,
          isDragEnabled: isDragEnabled,
          isDragging: isDragging,
          isDropTarget: isDropTarget,
          onTap: onTap,
          onDoubleTap: onDoubleTap,
          onExpansionChanged: onExpansionChanged,
          onEdit: onEdit,
          onDelete: onDelete,
          onDragStart: onDragStart,
          onDragEnd: onDragEnd,
          onDragEnter: onDragEnter,
          onDragLeave: onDragLeave,
        );
      }).toList(),
    );
  }
}
