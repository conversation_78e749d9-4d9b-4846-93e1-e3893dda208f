import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';

/// 注册对话框组件
/// 
/// 提供多步骤注册流程：基本信息→邮箱验证→完成
/// 使用VanHubModal作为基础容器
class RegisterDialog extends ConsumerStatefulWidget {
  /// 注册成功回调
  final VoidCallback? onRegisterSuccess;
  
  /// 切换到登录页面回调
  final VoidCallback? onSwitchToLogin;

  const RegisterDialog({
    super.key,
    this.onRegisterSuccess,
    this.onSwitchToLogin,
  });

  /// 显示注册对话框的静态方法
  static Future<bool?> show({
    required BuildContext context,
    VoidCallback? onRegisterSuccess,
    VoidCallback? onSwitchToLogin,
  }) {
    return VanHubModal.show<bool>(
      context: context,
      title: '注册VanHub账号',
      size: VanHubModalSize.md,
      animation: VanHubModalAnimation.scale,
      child: RegisterDialog(
        onRegisterSuccess: onRegisterSuccess,
        onSwitchToLogin: onSwitchToLogin,
      ),
    );
  }

  @override
  ConsumerState<RegisterDialog> createState() => _RegisterDialogState();
}

class _RegisterDialogState extends ConsumerState<RegisterDialog> {
  final _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  
  // 表单控制器
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  
  int _currentStep = 0;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;
  String? _errorMessage;
  
  // 密码强度
  PasswordStrength _passwordStrength = PasswordStrength.weak;

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 600,
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing6),
      child: Column(
        children: [
          _buildStepIndicator(),
          const SizedBox(height: VanHubDesignSystem.spacing6),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildBasicInfoStep(),
                _buildEmailVerificationStep(),
                _buildCompletionStep(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: [
        _buildStepCircle(0, '基本信息'),
        _buildStepLine(0),
        _buildStepCircle(1, '邮箱验证'),
        _buildStepLine(1),
        _buildStepCircle(2, '完成'),
      ],
    );
  }

  Widget _buildStepCircle(int step, String label) {
    final isActive = step <= _currentStep;
    final isCompleted = step < _currentStep;
    
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isActive 
                  ? VanHubDesignSystem.brandPrimary
                  : VanHubDesignSystem.neutralGray300,
            ),
            child: Center(
              child: isCompleted
                  ? const Icon(Icons.check, color: Colors.white, size: 16)
                  : Text(
                      '${step + 1}',
                      style: TextStyle(
                        color: isActive ? Colors.white : VanHubDesignSystem.neutralGray600,
                        fontWeight: VanHubDesignSystem.fontWeightMedium,
                        fontSize: VanHubDesignSystem.fontSizeSm,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: VanHubDesignSystem.spacing2),
          Text(
            label,
            style: TextStyle(
              fontSize: VanHubDesignSystem.fontSizeXs,
              color: isActive 
                  ? VanHubDesignSystem.brandPrimary
                  : VanHubDesignSystem.neutralGray600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepLine(int step) {
    final isCompleted = step < _currentStep;
    
    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.only(bottom: 24),
        color: isCompleted 
            ? VanHubDesignSystem.brandPrimary
            : VanHubDesignSystem.neutralGray300,
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildErrorMessage(),
            _buildNameField(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildEmailField(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildPasswordField(),
            const SizedBox(height: VanHubDesignSystem.spacing2),
            _buildPasswordStrengthIndicator(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildConfirmPasswordField(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildTermsCheckbox(),
            const SizedBox(height: VanHubDesignSystem.spacing6),
            _buildNextButton(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildLoginPrompt(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmailVerificationStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Icon(
          Icons.email_outlined,
          size: 64,
          color: VanHubDesignSystem.brandPrimary,
        ),
        const SizedBox(height: VanHubDesignSystem.spacing6),
        Text(
          '验证您的邮箱',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        Text(
          '我们已向 ${_emailController.text} 发送了验证码',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: VanHubDesignSystem.spacing6),
        TextFormField(
          controller: _verificationCodeController,
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          decoration: InputDecoration(
            labelText: '验证码',
            hintText: '请输入6位验证码',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入验证码';
            }
            if (value.length != 6) {
              return '验证码应为6位数字';
            }
            return null;
          },
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        TextButton(
          onPressed: _handleResendCode,
          child: const Text('重新发送验证码'),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing6),
        FilledButton(
          onPressed: _isLoading ? null : _handleVerifyEmail,
          child: _isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('验证邮箱'),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        OutlinedButton(
          onPressed: _isLoading ? null : _handlePreviousStep,
          child: const Text('返回上一步'),
        ),
      ],
    );
  }

  Widget _buildCompletionStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Icon(
          Icons.check_circle_outline,
          size: 64,
          color: VanHubDesignSystem.semanticSuccess,
        ),
        const SizedBox(height: VanHubDesignSystem.spacing6),
        Text(
          '注册成功！',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: VanHubDesignSystem.fontWeightSemiBold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: VanHubDesignSystem.spacing4),
        Text(
          '欢迎加入VanHub改装宝社区！\n您现在可以开始创建您的第一个改装项目了。',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: VanHubDesignSystem.spacing6),
        FilledButton(
          onPressed: _handleCompleteRegistration,
          child: const Text('开始使用VanHub'),
        ),
      ],
    );
  }

  Widget _buildErrorMessage() {
    if (_errorMessage == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing4),
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing3),
      decoration: BoxDecoration(
        color: VanHubDesignSystem.semanticError.withOpacity(0.1),
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        border: Border.all(
          color: VanHubDesignSystem.semanticError.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: VanHubDesignSystem.semanticError,
            size: 20,
          ),
          const SizedBox(width: VanHubDesignSystem.spacing2),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: VanHubDesignSystem.semanticError,
                fontSize: VanHubDesignSystem.fontSizeSm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: '姓名',
        hintText: '请输入您的姓名',
        prefixIcon: const Icon(Icons.person_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入姓名';
        }
        if (value.length < 2) {
          return '姓名至少2个字符';
        }
        return null;
      },
      enabled: !_isLoading,
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: '邮箱地址',
        hintText: '请输入您的邮箱地址',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入邮箱地址';
        }
        
        final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
        if (!emailRegex.hasMatch(value)) {
          return '请输入有效的邮箱地址';
        }
        
        return null;
      },
      enabled: !_isLoading,
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: '密码',
        hintText: '请输入密码（至少8位）',
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入密码';
        }
        if (value.length < 8) {
          return '密码长度至少8位';
        }
        return null;
      },
      onChanged: _updatePasswordStrength,
      enabled: !_isLoading,
    );
  }

  Widget _buildPasswordStrengthIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '密码强度：',
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSizeSm,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              _getPasswordStrengthText(),
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSizeSm,
                color: _getPasswordStrengthColor(),
                fontWeight: VanHubDesignSystem.fontWeightMedium,
              ),
            ),
          ],
        ),
        const SizedBox(height: VanHubDesignSystem.spacing2),
        LinearProgressIndicator(
          value: _getPasswordStrengthValue(),
          backgroundColor: VanHubDesignSystem.neutralGray200,
          valueColor: AlwaysStoppedAnimation<Color>(_getPasswordStrengthColor()),
        ),
      ],
    );
  }

  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: _obscureConfirmPassword,
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        labelText: '确认密码',
        hintText: '请再次输入密码',
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscureConfirmPassword = !_obscureConfirmPassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '请确认密码';
        }
        if (value != _passwordController.text) {
          return '两次输入的密码不一致';
        }
        return null;
      },
      enabled: !_isLoading,
    );
  }

  Widget _buildTermsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _agreeToTerms,
          onChanged: _isLoading ? null : (value) {
            setState(() {
              _agreeToTerms = value ?? false;
            });
          },
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _agreeToTerms = !_agreeToTerms;
              });
            },
            child: Text.rich(
              TextSpan(
                text: '我已阅读并同意',
                style: TextStyle(
                  fontSize: VanHubDesignSystem.fontSizeSm,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                children: [
                  TextSpan(
                    text: '《用户协议》',
                    style: TextStyle(
                      color: VanHubDesignSystem.brandPrimary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                  const TextSpan(text: '和'),
                  TextSpan(
                    text: '《隐私政策》',
                    style: TextStyle(
                      color: VanHubDesignSystem.brandPrimary,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNextButton() {
    return FilledButton(
      onPressed: (_isLoading || !_agreeToTerms) ? null : _handleNextStep,
      style: FilledButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: VanHubDesignSystem.spacing4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Text(
              '下一步',
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSizeBase,
                fontWeight: VanHubDesignSystem.fontWeightMedium,
              ),
            ),
    );
  }

  Widget _buildLoginPrompt() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '已有账号？',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: VanHubDesignSystem.fontSizeSm,
          ),
        ),
        TextButton(
          onPressed: _isLoading ? null : widget.onSwitchToLogin,
          child: const Text('立即登录'),
        ),
      ],
    );
  }

  void _updatePasswordStrength(String password) {
    setState(() {
      _passwordStrength = _calculatePasswordStrength(password);
    });
  }

  PasswordStrength _calculatePasswordStrength(String password) {
    if (password.length < 6) return PasswordStrength.weak;
    
    int score = 0;
    if (password.length >= 8) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;
    
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 3) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }

  String _getPasswordStrengthText() {
    switch (_passwordStrength) {
      case PasswordStrength.weak:
        return '弱';
      case PasswordStrength.medium:
        return '中等';
      case PasswordStrength.strong:
        return '强';
    }
  }

  Color _getPasswordStrengthColor() {
    switch (_passwordStrength) {
      case PasswordStrength.weak:
        return VanHubDesignSystem.semanticError;
      case PasswordStrength.medium:
        return VanHubDesignSystem.semanticWarning;
      case PasswordStrength.strong:
        return VanHubDesignSystem.semanticSuccess;
    }
  }

  double _getPasswordStrengthValue() {
    switch (_passwordStrength) {
      case PasswordStrength.weak:
        return 0.33;
      case PasswordStrength.medium:
        return 0.66;
      case PasswordStrength.strong:
        return 1.0;
    }
  }

  Future<void> _handleNextStep() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // TODO: 实际的注册逻辑和发送验证码
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求
      
      if (mounted) {
        setState(() {
          _currentStep = 1;
        });
        _pageController.nextPage(
          duration: VanHubDesignSystem.durationBase,
          curve: VanHubDesignSystem.curveDefault,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '注册失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleVerifyEmail() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // TODO: 实际的邮箱验证逻辑
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求
      
      if (mounted) {
        setState(() {
          _currentStep = 2;
        });
        _pageController.nextPage(
          duration: VanHubDesignSystem.durationBase,
          curve: VanHubDesignSystem.curveDefault,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '验证失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handlePreviousStep() {
    setState(() {
      _currentStep = 0;
    });
    _pageController.previousPage(
      duration: VanHubDesignSystem.durationBase,
      curve: VanHubDesignSystem.curveDefault,
    );
  }

  void _handleResendCode() {
    // TODO: 实现重新发送验证码
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('验证码已重新发送')),
    );
  }

  void _handleCompleteRegistration() {
    widget.onRegisterSuccess?.call();
    Navigator.of(context).pop(true);
  }
}

/// 密码强度枚举
enum PasswordStrength {
  weak,
  medium,
  strong,
}
