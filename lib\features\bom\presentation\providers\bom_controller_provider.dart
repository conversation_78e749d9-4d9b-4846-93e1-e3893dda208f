import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/di/injection_container.dart' as injection_container;
import '../../domain/entities/bom_item.dart';
import '../../domain/entities/create_bom_item_request.dart';
import '../../domain/entities/update_bom_item_request.dart';
import '../../domain/usecases/create_bom_item_usecase.dart';
import '../../domain/usecases/add_material_to_bom_usecase.dart';
import '../../domain/entities/bom_statistics.dart';
import '../../../project/presentation/providers/project_stats_provider.dart';
import 'bom_provider.dart';

part 'bom_controller_provider.g.dart';

/// BOM控制器Provider
@riverpod
class BomController extends _$BomController {
  @override
  FutureOr<void> build() {
    // 初始化状态
  }

  /// 创建BOM项目
  Future<Either<Failure, BomItem>> createBomItem(
    String projectId,
    CreateBomItemRequest request,
  ) async {
    state = const AsyncLoading();

    try {
      // 使用正确的参数
      final params = CreateBomItemParams(
        projectId: projectId,
        request: request,
      );
      
      final result = await ref.read(createBomItemUseCaseProvider).call(params);

      result.fold(
        (failure) => state = AsyncError(failure, StackTrace.current),
        (bomItem) => state = const AsyncData(null),
      );

      // 刷新相关provider
      if (result.isRight()) {
        ref.invalidate(bomItemsProvider);
        ref.invalidate(bomStatisticsProvider);
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '创建BOM项目失败: $e'));
    }
  }

  /// 更新BOM项目
  Future<Either<Failure, BomItem>> updateBomItem(
    UpdateBomItemRequest request,
  ) async {
    state = const AsyncLoading();

    try {
      // 先获取原始BomItem
      final bomItemResult = await ref.read(injection_container.bomRepositoryProvider).getBomItemById(request.id);
      
      return bomItemResult.fold(
        (failure) {
          state = AsyncError(failure, StackTrace.current);
          return Left(failure);
        },
        (bomItem) async {
          // 使用更新后的BomItem进行更新
          final updatedBomItem = bomItem.copyWith(
            materialName: request.name, // 必需字段，不需要??操作符
            description: request.description, // 必需字段，不需要??操作符
            category: request.category, // 必需字段，不需要??操作符
            quantity: request.quantity, // 必需字段，不需要??操作符
            unitPrice: request.unitPrice, // 必需字段，不需要??操作符
            status: request.status, // 必需字段，不需要??操作符
            brand: request.brand ?? bomItem.brand, // 可选字段，需要??操作符
            model: request.model ?? bomItem.model, // 可选字段，需要??操作符
            specifications: request.specifications ?? bomItem.specifications, // 可选字段，需要??操作符
            supplierUrl: request.supplierUrl ?? bomItem.supplierUrl, // 可选字段，需要??操作符
            imageUrl: request.imageUrl ?? bomItem.imageUrl, // 可选字段，需要??操作符
            notes: request.notes ?? bomItem.notes, // 可选字段，需要??操作符
          );
          
          final result = await ref.read(injection_container.bomRepositoryProvider).updateBomItem(updatedBomItem);
          
          result.fold(
            (failure) => state = AsyncError(failure, StackTrace.current),
            (_) => state = const AsyncData(null),
          );
          
          // 刷新相关provider
          if (result.isRight()) {
            ref.invalidate(bomItemsProvider);
            ref.invalidate(bomStatisticsProvider);
          }
          
          // 返回更新后的BomItem或失败
          return result.fold(
            (failure) => Left(failure),
            (_) => Right(updatedBomItem),
          );
        },
      );
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '更新BOM项目失败: $e'));
    }
  }

  /// 删除BOM项目
  Future<Either<Failure, void>> deleteBomItem(String bomItemId) async {
    state = const AsyncLoading();

    try {
      final result = await ref.read(bomRepositoryProvider).deleteBomItem(bomItemId);

      result.fold(
        (failure) => state = AsyncError(failure, StackTrace.current),
        (_) => state = const AsyncData(null),
      );

      // 刷新相关provider
      if (result.isRight()) {
        ref.invalidate(bomItemsProvider);
        ref.invalidate(bomStatisticsProvider);
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '删除BOM项目失败: $e'));
    }
  }

  /// 更新BOM项目状态
  Future<Either<Failure, void>> updateBomItemStatus(
    String bomItemId,
    BomItemStatus status,
  ) async {
    try {
      // 直接使用BomItemStatus
      final bomItemStatus = status;
      
      // 使用BomRepository的updateBomItemStatus方法
      final result = await ref.read(bomRepositoryProvider).updateBomItemStatus(
        bomItemId,
        bomItemStatus,
      );

      // 刷新相关provider
      if (result.isRight()) {
        ref.invalidate(bomItemsProvider);
        ref.invalidate(bomStatisticsProvider);
      }

      return result;
    } catch (e) {
      return Left(ServerFailure(message: '更新BOM项目状态失败: $e'));
    }
  }

  /// 添加材料到BOM - 支持完整参数和智能联动
  Future<Either<Failure, BomItem>> addMaterialToBom({
    required String projectId,
    required String materialId,
    required int quantity,
    double? customPrice,
    DateTime? plannedDate,
    String? notes,
  }) async {
    state = const AsyncLoading();

    try {
      // 直接调用Repository的addMaterialToBom方法，支持完整参数
      final result = await ref.read(bomRepositoryProvider).addMaterialToBom(
        projectId: projectId,
        materialId: materialId,
        quantity: quantity,
        customPrice: customPrice,
        plannedDate: plannedDate,
        notes: notes,
      );

      result.fold(
        (failure) => state = AsyncError(failure, StackTrace.current),
        (bomItem) => state = AsyncData(bomItem),
      );

      // 刷新相关provider - 包括项目统计数据
      if (result.isRight()) {
        // 刷新BOM相关数据
        ref.invalidate(bomItemsProvider);
        ref.invalidate(bomStatisticsProvider);
        ref.invalidate(projectBomItemsProvider(projectId));
        ref.invalidate(realTimeBomStatisticsProvider(projectId));

        // 刷新项目统计数据 - 实现实时更新
        ref.invalidate(projectStatsSummaryProvider(projectId));
        ref.invalidate(projectStatsProvider(projectId));
        ref.invalidate(projectProgressProvider(projectId));
        ref.invalidate(bomStatusCountsProvider(projectId));
        ref.invalidate(budgetAnalysisProvider(projectId));

        debugPrint('✅ BOM添加成功，已刷新所有相关统计数据Provider');
      }

      return result;
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
      return Left(ServerFailure(message: '添加材料到BOM失败: $e'));
    }
  }

  /// 保存BOM项目到材料库
  Future<Either<Failure, void>> saveBomItemToMaterialLibrary(String bomItemId) async {
    try {
      final result = await ref.read(bomRepositoryProvider).saveBomItemToMaterialLibrary(bomItemId);
      return result;
    } catch (e) {
      return Left(ServerFailure(message: '保存BOM项目到材料库失败: $e'));
    }
  }
}

/// BOM项目列表Provider
@riverpod
Future<List<BomItem>> bomItems(Ref ref, String projectId) async {
  final result = await ref.read(getBomItemsUseCaseProvider).call(projectId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItems) => bomItems,
  );
}

/// BOM统计Provider
@riverpod
Future<BomStatistics> bomStatistics(Ref ref, String projectId) async {
  final bomItems = await ref.watch(bomItemsProvider(projectId).future);
  // calculateStatistics直接返回BomStatistics，不是Either类型
  return ref.read(injection_container.bomStatisticsServiceProvider).calculateStatistics(bomItems);
}

/// BOM项目详情Provider
@riverpod
Future<BomItem> bomItemDetail(Ref ref, String bomItemId) async {
  final result = await ref.read(bomRepositoryProvider).getBomItemById(bomItemId);
  return result.fold(
    (failure) => throw Exception(failure.message),
    (bomItem) => bomItem,
  );
}

/// 将BomItemStatus转换为BomItemStatus（实际上不需要转换，因为是同一个类型）
BomItemStatus _convertToBomItemStatus(BomItemStatus status) {
  return status;
}
