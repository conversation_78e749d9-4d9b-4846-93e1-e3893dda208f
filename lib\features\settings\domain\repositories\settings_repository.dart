import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/app_settings.dart';
import '../services/settings_service.dart';

/// 设置Repository接口
abstract class SettingsRepository {
  /// 从本地存储获取设置
  Future<Either<Failure, AppSettings>> getLocalSettings(String userId);

  /// 保存设置到本地存储
  Future<Either<Failure, void>> saveLocalSettings(AppSettings settings);

  /// 从云端获取设置
  Future<Either<Failure, AppSettings>> getCloudSettings(String userId);

  /// 保存设置到云端
  Future<Either<Failure, void>> saveCloudSettings(AppSettings settings);

  /// 删除本地设置
  Future<Either<Failure, void>> deleteLocalSettings(String userId);

  /// 删除云端设置
  Future<Either<Failure, void>> deleteCloudSettings(String userId);

  /// 检查云端设置是否存在
  Future<Either<Failure, bool>> hasCloudSettings(String userId);

  /// 获取设置最后更新时间
  Future<Either<Failure, DateTime?>> getLastUpdateTime(String userId);

  /// 订阅设置变更
  Stream<AppSettings> subscribeToSettings(String userId);

  /// 创建设置备份
  Future<Either<Failure, SettingsBackup>> createBackup(
    String userId,
    String backupName,
    AppSettings settings,
  );

  /// 获取备份列表
  Future<Either<Failure, List<SettingsBackup>>> getBackups(String userId);

  /// 获取备份详情
  Future<Either<Failure, SettingsBackup>> getBackup(String backupId);

  /// 删除备份
  Future<Either<Failure, void>> deleteBackup(String backupId);

  /// 清理过期备份
  Future<Either<Failure, int>> cleanupExpiredBackups(
    String userId,
    int retentionDays,
  );

  /// 保存设置历史记录
  Future<Either<Failure, void>> saveSettingsHistory(AppSettings settings);

  /// 获取设置历史记录
  Future<Either<Failure, List<AppSettings>>> getSettingsHistory(
    String userId,
    {int limit = 10}
  );

  /// 清理设置历史记录
  Future<Either<Failure, int>> cleanupSettingsHistory(
    String userId,
    int retentionDays,
  );
}
