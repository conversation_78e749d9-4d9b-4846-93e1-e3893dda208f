import 'dart:convert';
import 'dart:async';
import 'package:fpdart/fpdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/repositories/settings_repository.dart';
import '../../domain/entities/app_settings.dart';
import '../../domain/services/settings_service.dart';

/// 设置Repository实现
class SettingsRepositoryImpl implements SettingsRepository {
  final SharedPreferences _sharedPreferences;
  final SupabaseClient _supabaseClient;
  
  // 缓存控制器
  final Map<String, StreamController<AppSettings>> _settingsControllers = {};
  
  // 本地存储键
  static const String _settingsPrefix = 'app_settings_';
  static const String _historyPrefix = 'settings_history_';
  static const String _lastSyncPrefix = 'last_sync_';

  SettingsRepositoryImpl({
    required SharedPreferences sharedPreferences,
    required SupabaseClient supabaseClient,
  }) : _sharedPreferences = sharedPreferences,
       _supabaseClient = supabaseClient;

  @override
  Future<Either<Failure, AppSettings>> getLocalSettings(String userId) async {
    try {
      final key = '$_settingsPrefix$userId';
      final settingsJson = _sharedPreferences.getString(key);
      
      if (settingsJson == null) {
        return Left(NotFoundFailure(message: '本地设置不存在'));
      }
      
      final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
      final settings = AppSettings.fromJson(settingsMap);
      
      return Right(settings);
    } catch (e) {
      return Left(CacheFailure(message: '读取本地设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveLocalSettings(AppSettings settings) async {
    try {
      final key = '$_settingsPrefix${settings.userId}';
      final settingsJson = jsonEncode(settings.toJson());
      
      final success = await _sharedPreferences.setString(key, settingsJson);
      if (!success) {
        return Left(CacheFailure(message: '保存本地设置失败'));
      }
      
      // 通知订阅者
      _notifySettingsChange(settings.userId, settings);
      
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: '保存本地设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AppSettings>> getCloudSettings(String userId) async {
    try {
      final response = await _supabaseClient
          .from('user_settings')
          .select()
          .eq('user_id', userId)
          .single();

      final settings = AppSettings.fromJson(response);
      return Right(settings);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure(message: '云端设置不存在'));
      }
      return Left(ServerFailure(message: '获取云端设置失败: ${e.message}'));
    } catch (e) {
      return Left(NetworkFailure(message: '网络连接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveCloudSettings(AppSettings settings) async {
    try {
      final settingsData = settings.toJson();
      
      // 尝试更新现有记录
      final updateResponse = await _supabaseClient
          .from('user_settings')
          .update(settingsData)
          .eq('user_id', settings.userId);

      // 如果更新失败（记录不存在），则插入新记录
      if (updateResponse.isEmpty) {
        await _supabaseClient
            .from('user_settings')
            .insert(settingsData);
      }

      return const Right(null);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '保存云端设置失败: ${e.message}'));
    } catch (e) {
      return Left(NetworkFailure(message: '网络连接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteLocalSettings(String userId) async {
    try {
      final key = '$_settingsPrefix$userId';
      final success = await _sharedPreferences.remove(key);
      
      if (!success) {
        return Left(CacheFailure(message: '删除本地设置失败'));
      }
      
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: '删除本地设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteCloudSettings(String userId) async {
    try {
      await _supabaseClient
          .from('user_settings')
          .delete()
          .eq('user_id', userId);

      return const Right(null);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '删除云端设置失败: ${e.message}'));
    } catch (e) {
      return Left(NetworkFailure(message: '网络连接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> hasCloudSettings(String userId) async {
    try {
      final response = await _supabaseClient
          .from('user_settings')
          .select('id')
          .eq('user_id', userId)
          .limit(1);

      return Right(response.isNotEmpty);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '检查云端设置失败: ${e.message}'));
    } catch (e) {
      return Left(NetworkFailure(message: '网络连接失败: $e'));
    }
  }

  @override
  Future<Either<Failure, DateTime?>> getLastUpdateTime(String userId) async {
    try {
      final key = '$_lastSyncPrefix$userId';
      final timestamp = _sharedPreferences.getInt(key);
      
      if (timestamp == null) {
        return const Right(null);
      }
      
      return Right(DateTime.fromMillisecondsSinceEpoch(timestamp));
    } catch (e) {
      return Left(CacheFailure(message: '获取最后更新时间失败: $e'));
    }
  }

  @override
  Stream<AppSettings> subscribeToSettings(String userId) {
    if (!_settingsControllers.containsKey(userId)) {
      _settingsControllers[userId] = StreamController<AppSettings>.broadcast();
    }
    
    return _settingsControllers[userId]!.stream;
  }

  @override
  Future<Either<Failure, SettingsBackup>> createBackup(
    String userId,
    String backupName,
    AppSettings settings,
  ) async {
    try {
      final backup = SettingsBackup(
        id: 'backup_${DateTime.now().millisecondsSinceEpoch}',
        name: backupName,
        data: settings.toJson(),
        createdAt: DateTime.now(),
        deviceInfo: 'Flutter App', // TODO: 获取实际设备信息
        appVersion: '1.0.0', // TODO: 获取实际应用版本
      );

      await _supabaseClient
          .from('settings_backups')
          .insert(backup.toJson());

      return Right(backup);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '创建备份失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '创建备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<SettingsBackup>>> getBackups(String userId) async {
    try {
      final response = await _supabaseClient
          .from('settings_backups')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final backups = (response as List)
          .map((json) => SettingsBackup.fromJson(json))
          .toList();

      return Right(backups);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '获取备份列表失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取备份列表失败: $e'));
    }
  }

  @override
  Future<Either<Failure, SettingsBackup>> getBackup(String backupId) async {
    try {
      final response = await _supabaseClient
          .from('settings_backups')
          .select()
          .eq('id', backupId)
          .single();

      final backup = SettingsBackup.fromJson(response);
      return Right(backup);
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST116') {
        return Left(NotFoundFailure(message: '备份不存在'));
      }
      return Left(ServerFailure(message: '获取备份失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteBackup(String backupId) async {
    try {
      await _supabaseClient
          .from('settings_backups')
          .delete()
          .eq('id', backupId);

      return const Right(null);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '删除备份失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '删除备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> cleanupExpiredBackups(
    String userId,
    int retentionDays,
  ) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays));
      
      final response = await _supabaseClient
          .from('settings_backups')
          .delete()
          .eq('user_id', userId)
          .lt('created_at', cutoffDate.toIso8601String());

      return Right(response.length);
    } on PostgrestException catch (e) {
      return Left(ServerFailure(message: '清理过期备份失败: ${e.message}'));
    } catch (e) {
      return Left(UnknownFailure(message: '清理过期备份失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveSettingsHistory(AppSettings settings) async {
    try {
      final key = '$_historyPrefix${settings.userId}';
      final existingHistoryJson = _sharedPreferences.getString(key);
      
      List<Map<String, dynamic>> history = [];
      if (existingHistoryJson != null) {
        final existingHistory = jsonDecode(existingHistoryJson) as List;
        history = existingHistory.cast<Map<String, dynamic>>();
      }
      
      // 添加新的历史记录
      history.insert(0, settings.toJson());
      
      // 限制历史记录数量（保留最近20条）
      if (history.length > 20) {
        history = history.take(20).toList();
      }
      
      final historyJson = jsonEncode(history);
      await _sharedPreferences.setString(key, historyJson);
      
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(message: '保存设置历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AppSettings>>> getSettingsHistory(
    String userId,
    {int limit = 10}
  ) async {
    try {
      final key = '$_historyPrefix$userId';
      final historyJson = _sharedPreferences.getString(key);
      
      if (historyJson == null) {
        return const Right([]);
      }
      
      final historyList = jsonDecode(historyJson) as List;
      final history = historyList
          .cast<Map<String, dynamic>>()
          .take(limit)
          .map((json) => AppSettings.fromJson(json))
          .toList();
      
      return Right(history);
    } catch (e) {
      return Left(CacheFailure(message: '获取设置历史失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> cleanupSettingsHistory(
    String userId,
    int retentionDays,
  ) async {
    try {
      final key = '$_historyPrefix$userId';
      final historyJson = _sharedPreferences.getString(key);
      
      if (historyJson == null) {
        return const Right(0);
      }
      
      final historyList = jsonDecode(historyJson) as List;
      final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays));
      
      final filteredHistory = historyList
          .cast<Map<String, dynamic>>()
          .where((json) {
            final updatedAt = DateTime.parse(json['updatedAt']);
            return updatedAt.isAfter(cutoffDate);
          })
          .toList();
      
      final removedCount = historyList.length - filteredHistory.length;
      
      if (removedCount > 0) {
        final newHistoryJson = jsonEncode(filteredHistory);
        await _sharedPreferences.setString(key, newHistoryJson);
      }
      
      return Right(removedCount);
    } catch (e) {
      return Left(CacheFailure(message: '清理设置历史失败: $e'));
    }
  }

  /// 通知设置变更
  void _notifySettingsChange(String userId, AppSettings settings) {
    if (_settingsControllers.containsKey(userId)) {
      _settingsControllers[userId]!.add(settings);
    }
  }

  /// 保存最后同步时间
  Future<void> _saveLastSyncTime(String userId) async {
    final key = '$_lastSyncPrefix$userId';
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    await _sharedPreferences.setInt(key, timestamp);
  }

  /// 释放资源
  void dispose() {
    for (final controller in _settingsControllers.values) {
      controller.close();
    }
    _settingsControllers.clear();
  }
}
