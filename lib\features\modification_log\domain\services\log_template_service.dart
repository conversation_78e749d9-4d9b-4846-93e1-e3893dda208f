import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/log_template.dart';

/// 模板查询参数
class TemplateQuery {
  final String? userId;
  final List<LogTemplateType>? types;
  final List<LogTemplateCategory>? categories;
  final List<String>? tags;
  final String? searchQuery;
  final bool? isPublic;
  final bool? isFeatured;
  final double? minRating;
  final int? minUsageCount;
  final String? sortBy; // 'name', 'rating', 'usage', 'created', 'updated'
  final bool? sortAscending;
  final int limit;
  final int offset;

  const TemplateQuery({
    this.userId,
    this.types,
    this.categories,
    this.tags,
    this.searchQuery,
    this.isPublic,
    this.isFeatured,
    this.minRating,
    this.minUsageCount,
    this.sortBy,
    this.sortAscending,
    this.limit = 20,
    this.offset = 0,
  });
}

/// 模板统计信息
class TemplateStats {
  final int totalTemplates;
  final int systemTemplates;
  final int customTemplates;
  final int sharedTemplates;
  final Map<LogTemplateCategory, int> categoryStats;
  final List<LogTemplate> popularTemplates;
  final List<LogTemplate> recentTemplates;
  final double averageRating;

  const TemplateStats({
    required this.totalTemplates,
    required this.systemTemplates,
    required this.customTemplates,
    required this.sharedTemplates,
    required this.categoryStats,
    required this.popularTemplates,
    required this.recentTemplates,
    required this.averageRating,
  });
}

/// 日志模板服务接口
abstract class LogTemplateService {
  /// 获取模板列表
  Future<Either<Failure, List<LogTemplate>>> getTemplates(TemplateQuery query);

  /// 获取模板详情
  Future<Either<Failure, LogTemplate>> getTemplate(String templateId);

  /// 创建自定义模板
  Future<Either<Failure, LogTemplate>> createTemplate(LogTemplate template);

  /// 更新模板
  Future<Either<Failure, LogTemplate>> updateTemplate(LogTemplate template);

  /// 删除模板
  Future<Either<Failure, void>> deleteTemplate(String templateId);

  /// 复制模板
  Future<Either<Failure, LogTemplate>> duplicateTemplate(
    String templateId,
    String newName,
    String userId,
  );

  /// 获取用户模板
  Future<Either<Failure, List<LogTemplate>>> getUserTemplates(String userId);

  /// 获取系统模板
  Future<Either<Failure, List<LogTemplate>>> getSystemTemplates();

  /// 获取热门模板
  Future<Either<Failure, List<LogTemplate>>> getPopularTemplates({int limit = 10});

  /// 获取推荐模板
  Future<Either<Failure, List<LogTemplate>>> getRecommendedTemplates(
    String userId, {
    int limit = 10,
  });

  /// 搜索模板
  Future<Either<Failure, List<LogTemplate>>> searchTemplates({
    required String query,
    List<LogTemplateCategory>? categories,
    int limit = 20,
    int offset = 0,
  });

  /// 按分类获取模板
  Future<Either<Failure, List<LogTemplate>>> getTemplatesByCategory(
    LogTemplateCategory category, {
    int limit = 20,
    int offset = 0,
  });

  /// 获取模板统计信息
  Future<Either<Failure, TemplateStats>> getTemplateStats(String? userId);

  /// 使用模板
  Future<Either<Failure, TemplateUsage>> useTemplate({
    required String templateId,
    required String userId,
    required String logId,
    required Map<String, dynamic> fieldValues,
  });

  /// 获取模板使用记录
  Future<Either<Failure, List<TemplateUsage>>> getTemplateUsages({
    String? templateId,
    String? userId,
    int limit = 50,
    int offset = 0,
  });

  /// 评价模板
  Future<Either<Failure, void>> rateTemplate({
    required String templateId,
    required String userId,
    required int rating,
    String? feedback,
  });

  /// 分享模板
  Future<Either<Failure, String>> shareTemplate(String templateId);

  /// 导入分享的模板
  Future<Either<Failure, LogTemplate>> importSharedTemplate(
    String shareCode,
    String userId,
  );

  /// 导出模板
  Future<Either<Failure, Map<String, dynamic>>> exportTemplate(String templateId);

  /// 导入模板
  Future<Either<Failure, LogTemplate>> importTemplate(
    Map<String, dynamic> templateData,
    String userId,
  );

  /// 批量导入模板
  Future<Either<Failure, List<LogTemplate>>> importTemplates(
    List<Map<String, dynamic>> templatesData,
    String userId,
  );

  /// 验证模板数据
  Future<Either<Failure, List<String>>> validateTemplate(LogTemplate template);

  /// 验证字段值
  Future<Either<Failure, Map<String, String>>> validateFieldValues(
    String templateId,
    Map<String, dynamic> fieldValues,
  );

  /// 获取字段建议值
  Future<Either<Failure, List<String>>> getFieldSuggestions({
    required String templateId,
    required String fieldName,
    String? query,
    int limit = 10,
  });

  /// 收藏模板
  Future<Either<Failure, void>> favoriteTemplate(String templateId, String userId);

  /// 取消收藏模板
  Future<Either<Failure, void>> unfavoriteTemplate(String templateId, String userId);

  /// 获取收藏的模板
  Future<Either<Failure, List<LogTemplate>>> getFavoriteTemplates(String userId);

  /// 举报模板
  Future<Either<Failure, void>> reportTemplate({
    required String templateId,
    required String userId,
    required String reason,
    String? description,
  });

  /// 获取模板版本历史
  Future<Either<Failure, List<LogTemplate>>> getTemplateVersions(String templateId);

  /// 恢复模板版本
  Future<Either<Failure, LogTemplate>> restoreTemplateVersion(
    String templateId,
    String versionId,
  );

  /// 发布模板到公共库
  Future<Either<Failure, void>> publishTemplate(String templateId);

  /// 取消发布模板
  Future<Either<Failure, void>> unpublishTemplate(String templateId);

  /// 获取模板使用统计
  Future<Either<Failure, Map<String, dynamic>>> getTemplateAnalytics(
    String templateId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 订阅模板更新
  Stream<List<LogTemplate>> subscribeToTemplates(TemplateQuery query);

  /// 订阅模板详情更新
  Stream<LogTemplate> subscribeToTemplate(String templateId);
}
