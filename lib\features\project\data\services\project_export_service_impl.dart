import 'dart:io';
import 'dart:convert';
import 'package:fpdart/fpdart.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/project_export_service.dart';
import '../../domain/entities/project.dart';
import '../../domain/repositories/project_repository.dart';
import '../../../bom/domain/repositories/bom_repository.dart';
import '../../../analytics/domain/services/analytics_service.dart';
import '../models/project_export_config.dart';

/// 项目导出服务实现
class ProjectExportServiceImpl implements ProjectExportService {
  final ProjectRepository projectRepository;
  final BomRepository bomRepository;
  final AnalyticsService analyticsService;

  const ProjectExportServiceImpl({
    required this.projectRepository,
    required this.bomRepository,
    required this.analyticsService,
  });

  @override
  Future<Either<Failure, File>> exportProjectReport({
    required String projectId,
    String? filePath,
    ProjectExportConfig? config,
  }) async {
    try {
      final exportConfig = config ?? const ProjectExportConfig();
      
      // 获取项目数据
      final projectResult = await projectRepository.getProject(projectId);
      
      return await projectResult.fold(
        (failure) async => Left(failure),
        (project) async {
          // 获取相关数据
          final bomResult = await bomRepository.getBomItems(projectId);
          final analyticsResult = await analyticsService.getProjectAnalytics(projectId);
          
          return await bomResult.fold(
            (failure) async => Left(failure),
            (bomItems) async {
              return await analyticsResult.fold(
                (failure) async => Left(failure),
                (analytics) async {
                  // 生成PDF报告
                  final file = await _generateProjectReportPdf(
                    project: project,
                    bomItems: bomItems,
                    analytics: analytics,
                    config: exportConfig,
                    filePath: filePath,
                  );
                  
                  return Right(file);
                },
              );
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '导出项目报告失败: $e'));
    }
  }

  @override
  Future<Either<Failure, File>> exportProjectData({
    required String projectId,
    String? filePath,
    bool includeMedia = false,
  }) async {
    try {
      // 获取项目完整数据
      final projectResult = await projectRepository.getProject(projectId);
      
      return await projectResult.fold(
        (failure) async => Left(failure),
        (project) async {
          final bomResult = await bomRepository.getBomItems(projectId);
          
          return await bomResult.fold(
            (failure) async => Left(failure),
            (bomItems) async {
              // 构建导出数据
              final exportData = {
                'project': project.toJson(),
                'bom_items': bomItems.map((item) => item.toJson()).toList(),
                'export_info': {
                  'exported_at': DateTime.now().toIso8601String(),
                  'version': '1.0',
                  'include_media': includeMedia,
                },
              };
              
              // 保存JSON文件
              final directory = await getApplicationDocumentsDirectory();
              final fileName = filePath ?? '${project.name}_data_${DateTime.now().millisecondsSinceEpoch}.json';
              final file = File(fileName.startsWith('/') ? fileName : '${directory.path}/$fileName');
              
              await file.parent.create(recursive: true);
              await file.writeAsString(
                const JsonEncoder.withIndent('  ').convert(exportData),
                encoding: utf8,
              );
              
              return Right(file);
            },
          );
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '导出项目数据失败: $e'));
    }
  }

  @override
  Future<Either<Failure, File>> exportProjectOverview({
    required String projectId,
    String? filePath,
  }) async {
    final config = const ProjectExportConfig(
      includeOverview: true,
      includeBom: false,
      includeTimeline: false,
      includeProgress: true,
      includeCosts: true,
    );
    
    return await exportProjectReport(
      projectId: projectId,
      filePath: filePath,
      config: config,
    );
  }

  @override
  Future<Either<Failure, File>> exportProgressReport({
    required String projectId,
    String? filePath,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final config = ProjectExportConfig(
      includeOverview: false,
      includeBom: false,
      includeTimeline: true,
      includeProgress: true,
      includeCosts: false,
      dateFrom: startDate,
      dateTo: endDate,
    );
    
    return await exportProjectReport(
      projectId: projectId,
      filePath: filePath,
      config: config,
    );
  }

  @override
  Future<Either<Failure, File>> exportCostAnalysisReport({
    required String projectId,
    String? filePath,
  }) async {
    final config = const ProjectExportConfig(
      includeOverview: false,
      includeBom: true,
      includeTimeline: false,
      includeProgress: false,
      includeCosts: true,
      showPricing: true,
      showSupplierInfo: true,
    );
    
    return await exportProjectReport(
      projectId: projectId,
      filePath: filePath,
      config: config,
    );
  }

  @override
  List<String> getSupportedFormats() {
    return ['pdf', 'json'];
  }

  @override
  Future<String> getDefaultExportPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/exports/projects';
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> previewExportContent({
    required String projectId,
    required String exportType,
  }) async {
    try {
      final projectResult = await projectRepository.getProject(projectId);
      
      return await projectResult.fold(
        (failure) async => Left(failure),
        (project) async {
          final preview = <String, dynamic>{
            'project_name': project.name,
            'project_description': project.description,
            'estimated_sections': [],
            'estimated_size_kb': 0,
            'estimated_time_seconds': 0,
          };
          
          switch (exportType) {
            case 'complete':
              preview['estimated_sections'] = ['概览', 'BOM清单', '时间轴', '进度报告', '成本分析'];
              preview['estimated_size_kb'] = 500;
              preview['estimated_time_seconds'] = 15;
              break;
            case 'overview':
              preview['estimated_sections'] = ['项目概览', '基本信息', '进度摘要'];
              preview['estimated_size_kb'] = 100;
              preview['estimated_time_seconds'] = 5;
              break;
            case 'bom':
              preview['estimated_sections'] = ['BOM清单', '材料详情', '成本统计'];
              preview['estimated_size_kb'] = 200;
              preview['estimated_time_seconds'] = 8;
              break;
            default:
              preview['estimated_sections'] = ['基本导出'];
              preview['estimated_size_kb'] = 50;
              preview['estimated_time_seconds'] = 3;
          }
          
          return Right(preview);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '预览导出内容失败: $e'));
    }
  }

  /// 生成项目报告PDF
  Future<File> _generateProjectReportPdf({
    required Project project,
    required List<dynamic> bomItems,
    required dynamic analytics,
    required ProjectExportConfig config,
    String? filePath,
  }) async {
    final pdf = pw.Document();
    
    // 添加封面页
    if (config.includeHeader) {
      pdf.addPage(_buildCoverPage(project, config));
    }
    
    // 添加项目概览
    if (config.includeOverview) {
      pdf.addPage(_buildOverviewPage(project, analytics, config));
    }
    
    // 添加BOM清单
    if (config.includeBom && bomItems.isNotEmpty) {
      pdf.addPage(_buildBomPage(bomItems, config));
    }
    
    // 添加进度报告
    if (config.includeProgress) {
      pdf.addPage(_buildProgressPage(analytics, config));
    }
    
    // 添加成本分析
    if (config.includeCosts) {
      pdf.addPage(_buildCostAnalysisPage(analytics, config));
    }
    
    // 保存文件
    final directory = await getApplicationDocumentsDirectory();
    final fileName = filePath ?? '${project.name}_report_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File(fileName.startsWith('/') ? fileName : '${directory.path}/$fileName');
    
    await file.parent.create(recursive: true);
    
    final bytes = await pdf.save();
    await file.writeAsBytes(bytes);
    
    return file;
  }

  /// 构建封面页
  pw.Page _buildCoverPage(Project project, ProjectExportConfig config) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Center(
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              if (config.companyLogo != null) ...[
                // TODO: 添加公司Logo
                pw.SizedBox(height: 50),
              ],
              
              pw.Text(
                config.customTitle ?? '${project.name} - 项目报告',
                style: pw.TextStyle(
                  fontSize: 28,
                  fontWeight: pw.FontWeight.bold,
                ),
                textAlign: pw.TextAlign.center,
              ),
              
              pw.SizedBox(height: 20),
              
              if (config.customSubtitle != null) ...[
                pw.Text(
                  config.customSubtitle!,
                  style: const pw.TextStyle(fontSize: 16),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 20),
              ],
              
              if (config.companyName != null) ...[
                pw.Text(
                  config.companyName!,
                  style: const pw.TextStyle(fontSize: 14),
                ),
                pw.SizedBox(height: 10),
              ],
              
              pw.Text(
                '生成时间: ${DateTime.now().toString().substring(0, 19)}',
                style: const pw.TextStyle(fontSize: 12),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建概览页
  pw.Page _buildOverviewPage(Project project, dynamic analytics, ProjectExportConfig config) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(32),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Header(
              level: 0,
              child: pw.Text('项目概览', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
            ),
            
            pw.SizedBox(height: 20),
            
            // 基本信息
            _buildInfoSection('基本信息', [
              '项目名称: ${project.name}',
              '项目描述: ${project.description ?? '无'}',
              '创建时间: ${project.createdAt.toString().substring(0, 10)}',
              '项目状态: ${_getProjectStatusText(project.status)}',
              if (project.budget != null) '预算: ¥${project.budget!.toStringAsFixed(2)}',
            ]),
            
            pw.SizedBox(height: 20),
            
            // 进度摘要
            if (analytics != null) ...[
              _buildInfoSection('进度摘要', [
                '总体进度: ${analytics.completionPercentage?.toStringAsFixed(1) ?? '0'}%',
                '已完成任务: ${analytics.completedTasks ?? 0}',
                '总任务数: ${analytics.totalTasks ?? 0}',
                '材料总数: ${analytics.totalMaterials ?? 0}',
              ]),
            ],
          ],
        );
      },
    );
  }

  /// 构建BOM页面
  pw.Page _buildBomPage(List<dynamic> bomItems, ProjectExportConfig config) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(32),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Header(
              level: 0,
              child: pw.Text('BOM清单', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
            ),
            
            pw.SizedBox(height: 20),
            
            // BOM表格
            pw.Table(
              border: pw.TableBorder.all(),
              children: [
                // 表头
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  children: [
                    _buildTableCell('序号', isHeader: true),
                    _buildTableCell('物料名称', isHeader: true),
                    _buildTableCell('数量', isHeader: true),
                    if (config.showPricing) _buildTableCell('单价', isHeader: true),
                    if (config.showPricing) _buildTableCell('总价', isHeader: true),
                  ],
                ),
                
                // 数据行
                ...bomItems.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  
                  return pw.TableRow(
                    children: [
                      _buildTableCell((index + 1).toString()),
                      _buildTableCell(item.materialName ?? ''),
                      _buildTableCell(item.quantity?.toString() ?? '0'),
                      if (config.showPricing) _buildTableCell('¥${(item.unitPrice ?? 0).toStringAsFixed(2)}'),
                      if (config.showPricing) _buildTableCell('¥${((item.unitPrice ?? 0) * (item.quantity ?? 0)).toStringAsFixed(2)}'),
                    ],
                  );
                }),
              ],
            ),
          ],
        );
      },
    );
  }

  /// 构建进度页面
  pw.Page _buildProgressPage(dynamic analytics, ProjectExportConfig config) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(32),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Header(
              level: 0,
              child: pw.Text('进度报告', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
            ),
            
            pw.SizedBox(height: 20),
            
            if (analytics != null) ...[
              _buildInfoSection('进度统计', [
                '总体完成度: ${analytics.completionPercentage?.toStringAsFixed(1) ?? '0'}%',
                '已完成任务: ${analytics.completedTasks ?? 0}个',
                '进行中任务: ${(analytics.totalTasks ?? 0) - (analytics.completedTasks ?? 0)}个',
                '最后更新: ${analytics.lastUpdated?.toString().substring(0, 19) ?? '未知'}',
              ]),
            ],
          ],
        );
      },
    );
  }

  /// 构建成本分析页面
  pw.Page _buildCostAnalysisPage(dynamic analytics, ProjectExportConfig config) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(32),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Header(
              level: 0,
              child: pw.Text('成本分析', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
            ),
            
            pw.SizedBox(height: 20),
            
            if (analytics != null) ...[
              _buildInfoSection('成本统计', [
                '总预算: ¥${analytics.totalBudget?.toStringAsFixed(2) ?? '0.00'}',
                '实际成本: ¥${analytics.actualCost?.toStringAsFixed(2) ?? '0.00'}',
                '剩余预算: ¥${analytics.remainingBudget?.toStringAsFixed(2) ?? '0.00'}',
                '预算使用率: ${analytics.budgetUtilization?.toStringAsFixed(1) ?? '0'}%',
              ]),
            ],
          ],
        );
      },
    );
  }

  /// 构建信息部分
  pw.Widget _buildInfoSection(String title, List<String> items) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...items.map((item) => pw.Padding(
          padding: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Text('• $item'),
        )),
      ],
    );
  }

  /// 构建表格单元格
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  /// 获取项目状态文本
  String _getProjectStatusText(dynamic status) {
    switch (status.toString()) {
      case 'planning':
        return '规划中';
      case 'in_progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return status.toString();
    }
  }
}
