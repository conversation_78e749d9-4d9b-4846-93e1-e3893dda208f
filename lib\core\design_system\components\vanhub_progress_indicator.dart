/// VanHub Progress Indicator Component
/// 
/// 现代化进度指示器组件
/// 
/// 特性：
/// - 多种样式（圆形、线性、环形）
/// - 动画效果
/// - 自定义颜色和尺寸
/// - 百分比显示
library;

import 'package:flutter/material.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';

/// 进度指示器尺寸枚举
enum VanHubProgressSize {
  sm,   // 小尺寸
  md,   // 中等尺寸
  lg,   // 大尺寸
  xl,   // 超大尺寸
}

/// 进度指示器样式枚举
enum VanHubProgressStyle {
  circular,   // 圆形
  linear,     // 线性
  ring,       // 环形
}

/// VanHub进度指示器组件
class VanHubProgressIndicator extends StatefulWidget {
  final double progress;
  final VanHubProgressSize size;
  final VanHubProgressStyle style;
  final Color? color;
  final Color? backgroundColor;
  final bool showPercentage;
  final String? label;
  final bool enableAnimation;
  final Duration animationDuration;

  const VanHubProgressIndicator({
    super.key,
    required this.progress,
    this.size = VanHubProgressSize.md,
    this.style = VanHubProgressStyle.circular,
    this.color,
    this.backgroundColor,
    this.showPercentage = false,
    this.label,
    this.enableAnimation = true,
    this.animationDuration = const Duration(milliseconds: 1000),
  });

  /// 圆形进度指示器构造函数
  const VanHubProgressIndicator.circular({
    Key? key,
    required double progress,
    VanHubProgressSize size = VanHubProgressSize.md,
    Color? color,
    Color? backgroundColor,
    bool showPercentage = false,
    String? label,
    bool enableAnimation = true,
    Duration animationDuration = const Duration(milliseconds: 1000),
  }) : this(
          key: key,
          progress: progress,
          size: size,
          style: VanHubProgressStyle.circular,
          color: color,
          backgroundColor: backgroundColor,
          showPercentage: showPercentage,
          label: label,
          enableAnimation: enableAnimation,
          animationDuration: animationDuration,
        );

  /// 线性进度指示器构造函数
  const VanHubProgressIndicator.linear({
    Key? key,
    required double progress,
    VanHubProgressSize size = VanHubProgressSize.md,
    Color? color,
    Color? backgroundColor,
    bool showPercentage = false,
    String? label,
    bool enableAnimation = true,
    Duration animationDuration = const Duration(milliseconds: 1000),
  }) : this(
          key: key,
          progress: progress,
          size: size,
          style: VanHubProgressStyle.linear,
          color: color,
          backgroundColor: backgroundColor,
          showPercentage: showPercentage,
          label: label,
          enableAnimation: enableAnimation,
          animationDuration: animationDuration,
        );

  /// 环形进度指示器构造函数
  const VanHubProgressIndicator.ring({
    Key? key,
    required double progress,
    VanHubProgressSize size = VanHubProgressSize.md,
    Color? color,
    Color? backgroundColor,
    bool showPercentage = false,
    String? label,
    bool enableAnimation = true,
    Duration animationDuration = const Duration(milliseconds: 1000),
  }) : this(
          key: key,
          progress: progress,
          size: size,
          style: VanHubProgressStyle.ring,
          color: color,
          backgroundColor: backgroundColor,
          showPercentage: showPercentage,
          label: label,
          enableAnimation: enableAnimation,
          animationDuration: animationDuration,
        );

  @override
  State<VanHubProgressIndicator> createState() => _VanHubProgressIndicatorState();
}

class _VanHubProgressIndicatorState extends State<VanHubProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress.clamp(0.0, 1.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.enableAnimation) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(VanHubProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress.clamp(0.0, 1.0),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      
      if (widget.enableAnimation) {
        _animationController.reset();
        _animationController.forward();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveColor = widget.color ?? VanHubBrandColors.primary;
    final effectiveBackgroundColor = widget.backgroundColor ?? 
        VanHubSemanticColors.getBackgroundColor(context, level: 2);

    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        final progress = widget.enableAnimation 
            ? _progressAnimation.value 
            : widget.progress.clamp(0.0, 1.0);

        switch (widget.style) {
          case VanHubProgressStyle.circular:
            return _buildCircularProgress(progress, effectiveColor, effectiveBackgroundColor);
          case VanHubProgressStyle.linear:
            return _buildLinearProgress(progress, effectiveColor, effectiveBackgroundColor);
          case VanHubProgressStyle.ring:
            return _buildRingProgress(progress, effectiveColor, effectiveBackgroundColor);
        }
      },
    );
  }

  /// 构建圆形进度指示器
  Widget _buildCircularProgress(double progress, Color color, Color backgroundColor) {
    final size = _getProgressSize();
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              // 背景圆环
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: _getStrokeWidth(),
                  valueColor: AlwaysStoppedAnimation<Color>(backgroundColor),
                ),
              ),
              // 进度圆环
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: _getStrokeWidth(),
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              ),
              // 百分比文本
              if (widget.showPercentage)
                Center(
                  child: Text(
                    '${(progress * 100).toInt()}%',
                    style: TextStyle(
                      fontSize: _getPercentageFontSize(),
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        // 标签
        if (widget.label != null) ...[
          SizedBox(height: VanHubResponsiveSpacing.sm),
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// 构建线性进度指示器
  Widget _buildLinearProgress(double progress, Color color, Color backgroundColor) {
    final height = _getLinearHeight();
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签和百分比
        if (widget.label != null || widget.showPercentage)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (widget.label != null)
                Text(
                  widget.label!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              if (widget.showPercentage)
                Text(
                  '${(progress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
            ],
          ),
        
        if (widget.label != null || widget.showPercentage)
          SizedBox(height: VanHubResponsiveSpacing.xs),
        
        // 进度条
        Container(
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(height / 2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(height / 2),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建环形进度指示器
  Widget _buildRingProgress(double progress, Color color, Color backgroundColor) {
    final size = _getProgressSize();
    final strokeWidth = _getStrokeWidth() * 2;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CustomPaint(
            painter: _RingProgressPainter(
              progress: progress,
              color: color,
              backgroundColor: backgroundColor,
              strokeWidth: strokeWidth,
            ),
            child: widget.showPercentage
                ? Center(
                    child: Text(
                      '${(progress * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: _getPercentageFontSize(),
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  )
                : null,
          ),
        ),
        
        // 标签
        if (widget.label != null) ...[
          SizedBox(height: VanHubResponsiveSpacing.sm),
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// 获取进度指示器尺寸
  double _getProgressSize() {
    switch (widget.size) {
      case VanHubProgressSize.sm:
        return 40;
      case VanHubProgressSize.md:
        return 60;
      case VanHubProgressSize.lg:
        return 80;
      case VanHubProgressSize.xl:
        return 120;
    }
  }

  /// 获取线条宽度
  double _getStrokeWidth() {
    switch (widget.size) {
      case VanHubProgressSize.sm:
        return 3;
      case VanHubProgressSize.md:
        return 4;
      case VanHubProgressSize.lg:
        return 6;
      case VanHubProgressSize.xl:
        return 8;
    }
  }

  /// 获取线性进度条高度
  double _getLinearHeight() {
    switch (widget.size) {
      case VanHubProgressSize.sm:
        return 4;
      case VanHubProgressSize.md:
        return 6;
      case VanHubProgressSize.lg:
        return 8;
      case VanHubProgressSize.xl:
        return 12;
    }
  }

  /// 获取百分比字体大小
  double _getPercentageFontSize() {
    switch (widget.size) {
      case VanHubProgressSize.sm:
        return 10;
      case VanHubProgressSize.md:
        return 12;
      case VanHubProgressSize.lg:
        return 16;
      case VanHubProgressSize.xl:
        return 20;
    }
  }
}

/// 环形进度绘制器
class _RingProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;
  final double strokeWidth;

  _RingProgressPainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 绘制背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制进度圆环
    final progressPaint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final sweepAngle = 2 * 3.14159 * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -3.14159 / 2, // 从顶部开始
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}
