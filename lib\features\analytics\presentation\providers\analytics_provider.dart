import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/services/analytics_service.dart';
import '../../domain/entities/analytics_data.dart';
import '../../domain/entities/cost_trend.dart';
import '../../domain/entities/progress_stats.dart';
import '../../data/services/analytics_service_impl.dart';
import '../../../project/domain/repositories/project_repository.dart';
import '../../../material/domain/repositories/material_repository.dart';
import '../../../../core/di/injection_container.dart';

part 'analytics_provider.g.dart';

/// 分析服务Provider
@riverpod
AnalyticsService analyticsService(AnalyticsServiceRef ref) {
  return AnalyticsServiceImpl(
    projectRepository: ref.read(projectRepositoryProvider),
    materialRepository: ref.read(materialRepositoryProvider),
  );
}

/// 项目分析数据Provider
@riverpod
Future<AnalyticsData> projectAnalytics(
  ProjectAnalyticsRef ref,
  String projectId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getProjectAnalytics(projectId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (analytics) => analytics,
  );
}

/// 用户分析数据Provider
@riverpod
Future<AnalyticsData> userAnalytics(
  UserAnalyticsRef ref,
  String userId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getUserAnalytics(userId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (analytics) => analytics,
  );
}

/// 成本趋势Provider
@riverpod
Future<List<CostTrend>> costTrends(
  CostTrendsRef ref,
  String projectId, {
  DateTime? startDate,
  DateTime? endDate,
}) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getCostTrends(
    projectId,
    startDate: startDate,
    endDate: endDate,
  );
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (trends) => trends,
  );
}

/// 进度统计Provider
@riverpod
Future<ProgressStats> progressStats(
  ProgressStatsRef ref,
  String projectId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getProgressStats(projectId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );
}

/// 分类成本分布Provider
@riverpod
Future<Map<String, double>> categoryCostDistribution(
  CategoryCostDistributionRef ref,
  String projectId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getCategoryCostDistribution(projectId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (distribution) => distribution,
  );
}

/// 月度支出Provider
@riverpod
Future<Map<String, double>> monthlyExpenses(
  MonthlyExpensesRef ref,
  String projectId, {
  int months = 12,
}) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getMonthlyExpenses(projectId, months: months);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (expenses) => expenses,
  );
}

/// 材料使用统计Provider
@riverpod
Future<Map<String, int>> materialUsageStats(
  MaterialUsageStatsRef ref,
  String projectId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getMaterialUsageStats(projectId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (stats) => stats,
  );
}

/// 预算vs实际Provider
@riverpod
Future<Map<String, double>> budgetVsActual(
  BudgetVsActualRef ref,
  String projectId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getBudgetVsActual(projectId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (comparison) => comparison,
  );
}

/// 完成度趋势Provider
@riverpod
Future<List<Map<String, dynamic>>> completionTrends(
  CompletionTrendsRef ref,
  String projectId,
) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getCompletionTrends(projectId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (trends) => trends,
  );
}

/// 热门材料分析Provider
@riverpod
Future<List<Map<String, dynamic>>> popularMaterialsAnalysis(
  PopularMaterialsAnalysisRef ref, {
  String? category,
  int limit = 10,
}) async {
  final service = ref.read(analyticsServiceProvider);
  final result = await service.getPopularMaterialsAnalysis(
    category: category,
    limit: limit,
  );
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (analysis) => analysis,
  );
}

/// 实时分析数据状态管理
@riverpod
class AnalyticsState extends _$AnalyticsState {
  @override
  Future<AnalyticsData?> build() async {
    return null;
  }

  /// 加载项目分析数据
  Future<void> loadProjectAnalytics(String projectId) async {
    state = const AsyncLoading();
    
    try {
      final analytics = await ref.read(projectAnalyticsProvider(projectId).future);
      state = AsyncData(analytics);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// 加载用户分析数据
  Future<void> loadUserAnalytics(String userId) async {
    state = const AsyncLoading();
    
    try {
      final analytics = await ref.read(userAnalyticsProvider(userId).future);
      state = AsyncData(analytics);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// 刷新分析数据
  Future<void> refresh() async {
    final currentData = state.value;
    if (currentData != null) {
      if (currentData.projectId.startsWith('user_')) {
        final userId = currentData.projectId.substring(5);
        await loadUserAnalytics(userId);
      } else {
        await loadProjectAnalytics(currentData.projectId);
      }
    }
  }

  /// 清空分析数据
  void clear() {
    state = const AsyncData(null);
  }
}
