import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/entities/log_entry.dart';

/// 日志统计组件
class LogStatsWidget extends StatelessWidget {
  final LogEntry logEntry;

  const LogStatsWidget({
    super.key,
    required this.logEntry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            VanHubBrandColors.primary,
            VanHubBrandColors.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: VanHubBrandColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.analytics,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: Text(
                  '日志统计',
                  style: VanHubTypography.titleMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              _buildStatusBadge(),
            ],
          ),
          
          const SizedBox(height: VanHubSpacing.md),
          
          // 统计数据网格
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.attach_money,
                  label: '总成本',
                  value: '¥${logEntry.totalCost.toStringAsFixed(2)}',
                  color: Colors.green.shade300,
                ),
              ),
              const SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.schedule,
                  label: '工时',
                  value: _formatDuration(logEntry.timeSpentMinutes),
                  color: Colors.blue.shade300,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: VanHubSpacing.sm),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.photo_library,
                  label: '媒体文件',
                  value: '${logEntry.mediaIds.length}',
                  color: Colors.purple.shade300,
                ),
              ),
              const SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.inventory,
                  label: '使用材料',
                  value: '${logEntry.relatedBomItemIds.length}',
                  color: Colors.orange.shade300,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(height: VanHubSpacing.xs),
          Text(
            value,
            style: VanHubTypography.titleSmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    final status = _getLogStatus();
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: VanHubSpacing.sm,
        vertical: VanHubSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: status.color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: status.color.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            status.icon,
            color: Colors.white,
            size: 14,
          ),
          const SizedBox(width: VanHubSpacing.xs),
          Text(
            status.label,
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  LogStatus _getLogStatus() {
    final now = DateTime.now();
    final daysSinceCreated = now.difference(logEntry.createdAt).inDays;
    
    if (logEntry.updatedAt.difference(logEntry.createdAt).inMinutes > 5) {
      return LogStatus(
        label: '已编辑',
        icon: Icons.edit,
        color: Colors.amber,
      );
    }
    
    if (daysSinceCreated == 0) {
      return LogStatus(
        label: '今日',
        icon: Icons.today,
        color: Colors.green,
      );
    } else if (daysSinceCreated <= 7) {
      return LogStatus(
        label: '本周',
        icon: Icons.date_range,
        color: Colors.blue,
      );
    } else if (daysSinceCreated <= 30) {
      return LogStatus(
        label: '本月',
        icon: Icons.calendar_month,
        color: Colors.orange,
      );
    } else {
      return LogStatus(
        label: '较早',
        icon: Icons.history,
        color: Colors.grey,
      );
    }
  }

  String _formatDuration(int minutes) {
    if (minutes == 0) return '0分钟';
    
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    
    if (hours == 0) {
      return '${remainingMinutes}分钟';
    } else if (remainingMinutes == 0) {
      return '${hours}小时';
    } else {
      return '${hours}小时${remainingMinutes}分钟';
    }
  }
}

/// 日志状态
class LogStatus {
  final String label;
  final IconData icon;
  final Color color;

  const LogStatus({
    required this.label,
    required this.icon,
    required this.color,
  });
}

/// 动画统计卡片
class AnimatedStatCard extends StatefulWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;
  final Duration delay;

  const AnimatedStatCard({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
    this.delay = Duration.zero,
  });

  @override
  State<AnimatedStatCard> createState() => _AnimatedStatCardState();
}

class _AnimatedStatCardState extends State<AnimatedStatCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(VanHubSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Transform.rotate(
                  angle: _rotationAnimation.value * 0.1,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: widget.color.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      widget.icon,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
                const SizedBox(height: VanHubSpacing.xs),
                Text(
                  widget.value,
                  style: VanHubTypography.titleSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.label,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
