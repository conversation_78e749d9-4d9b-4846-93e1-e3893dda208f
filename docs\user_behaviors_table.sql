-- 用户行为数据表
CREATE TABLE IF NOT EXISTS user_behaviors (
    id TEXT PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    behavior_type TEXT NOT NULL,
    target_id TEXT NOT NULL,
    context JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    session_id TEXT,
    duration REAL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_behaviors_user_id ON user_behaviors(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behaviors_type ON user_behaviors(behavior_type);
CREATE INDEX IF NOT EXISTS idx_user_behaviors_target_id ON user_behaviors(target_id);
CREATE INDEX IF NOT EXISTS idx_user_behaviors_timestamp ON user_behaviors(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_behaviors_session_id ON user_behaviors(session_id);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_user_behaviors_user_type_time ON user_behaviors(user_id, behavior_type, timestamp DESC);

-- RLS策略
ALTER TABLE user_behaviors ENABLE ROW LEVEL SECURITY;

-- 用户只能查看自己的行为数据
CREATE POLICY "Users can view own behaviors" ON user_behaviors
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能插入自己的行为数据
CREATE POLICY "Users can insert own behaviors" ON user_behaviors
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 管理员可以查看所有数据（用于分析）
CREATE POLICY "Admins can view all behaviors" ON user_behaviors
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' = 'admin'
        )
    );

-- 创建获取材料交互统计的函数
CREATE OR REPLACE FUNCTION get_material_interaction_stats(material_id TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_views', COALESCE(view_stats.total_views, 0),
        'unique_viewers', COALESCE(view_stats.unique_viewers, 0),
        'avg_view_duration', COALESCE(view_stats.avg_duration, 0),
        'total_bom_additions', COALESCE(bom_stats.total_additions, 0),
        'total_favorites', COALESCE(fav_stats.total_favorites, 0),
        'recommendation_clicks', COALESCE(rec_stats.total_clicks, 0)
    ) INTO result
    FROM (
        -- 浏览统计
        SELECT 
            COUNT(*) as total_views,
            COUNT(DISTINCT user_id) as unique_viewers,
            AVG(duration) as avg_duration
        FROM user_behaviors 
        WHERE target_id = material_id 
        AND behavior_type = 'materialView'
    ) view_stats
    CROSS JOIN (
        -- BOM添加统计
        SELECT COUNT(*) as total_additions
        FROM user_behaviors 
        WHERE target_id = material_id 
        AND behavior_type = 'bomAddition'
    ) bom_stats
    CROSS JOIN (
        -- 收藏统计
        SELECT COUNT(*) as total_favorites
        FROM user_behaviors 
        WHERE target_id = material_id 
        AND behavior_type = 'materialFavorite'
        AND context->>'is_favorited' = 'true'
    ) fav_stats
    CROSS JOIN (
        -- 推荐点击统计
        SELECT COUNT(*) as total_clicks
        FROM user_behaviors 
        WHERE target_id = material_id 
        AND behavior_type = 'recommendationClick'
    ) rec_stats;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建获取用户偏好分析的函数
CREATE OR REPLACE FUNCTION get_user_preference_analysis(user_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'favorite_categories', category_prefs.categories,
        'search_patterns', search_prefs.patterns,
        'interaction_frequency', interaction_prefs.frequency,
        'price_range_preference', price_prefs.range
    ) INTO result
    FROM (
        -- 分类偏好分析
        SELECT json_agg(
            json_build_object(
                'category', category,
                'interaction_count', interaction_count,
                'preference_score', preference_score
            )
        ) as categories
        FROM (
            SELECT 
                context->>'category' as category,
                COUNT(*) as interaction_count,
                COUNT(*) * 1.0 / SUM(COUNT(*)) OVER() as preference_score
            FROM user_behaviors ub
            WHERE ub.user_id = get_user_preference_analysis.user_id
            AND context->>'category' IS NOT NULL
            GROUP BY context->>'category'
            ORDER BY interaction_count DESC
            LIMIT 10
        ) cat_data
    ) category_prefs
    CROSS JOIN (
        -- 搜索模式分析
        SELECT json_agg(
            json_build_object(
                'query', query,
                'frequency', frequency
            )
        ) as patterns
        FROM (
            SELECT 
                context->>'query' as query,
                COUNT(*) as frequency
            FROM user_behaviors ub
            WHERE ub.user_id = get_user_preference_analysis.user_id
            AND behavior_type = 'materialSearch'
            AND context->>'query' IS NOT NULL
            GROUP BY context->>'query'
            ORDER BY frequency DESC
            LIMIT 20
        ) search_data
    ) search_prefs
    CROSS JOIN (
        -- 交互频率分析
        SELECT json_build_object(
            'daily_avg', daily_avg,
            'peak_hours', peak_hours
        ) as frequency
        FROM (
            SELECT 
                COUNT(*) / GREATEST(DATE_PART('day', NOW() - MIN(timestamp)), 1) as daily_avg,
                MODE() WITHIN GROUP (ORDER BY EXTRACT(hour FROM timestamp)) as peak_hours
            FROM user_behaviors ub
            WHERE ub.user_id = get_user_preference_analysis.user_id
        ) freq_data
    ) interaction_prefs
    CROSS JOIN (
        -- 价格偏好分析
        SELECT json_build_object(
            'min_price', min_price,
            'max_price', max_price,
            'avg_price', avg_price
        ) as range
        FROM (
            SELECT 
                MIN((context->>'price')::NUMERIC) as min_price,
                MAX((context->>'price')::NUMERIC) as max_price,
                AVG((context->>'price')::NUMERIC) as avg_price
            FROM user_behaviors ub
            WHERE ub.user_id = get_user_preference_analysis.user_id
            AND behavior_type = 'bomAddition'
            AND context->>'price' IS NOT NULL
        ) price_data
    ) price_prefs;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建清理旧行为数据的函数（数据保留策略）
CREATE OR REPLACE FUNCTION cleanup_old_behaviors()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除6个月前的行为数据
    DELETE FROM user_behaviors 
    WHERE timestamp < NOW() - INTERVAL '6 months';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建定时任务清理旧数据（需要pg_cron扩展）
-- SELECT cron.schedule('cleanup-behaviors', '0 2 * * 0', 'SELECT cleanup_old_behaviors();');
