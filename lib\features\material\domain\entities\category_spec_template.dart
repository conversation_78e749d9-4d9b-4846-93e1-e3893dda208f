import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_spec_template.freezed.dart';
part 'category_spec_template.g.dart';

/// 分类规格模板
/// 
/// 定义每个产品分类应该包含的标准规格字段
@freezed
class CategorySpecTemplate with _$CategorySpecTemplate {
  const factory CategorySpecTemplate({
    /// 模板ID
    required String id,
    
    /// 分类名称
    required String categoryName,
    
    /// 分类代码
    required String categoryCode,
    
    /// 模板版本
    @Default('1.0') String version,
    
    /// 必填字段
    required List<SpecField> requiredFields,
    
    /// 可选字段
    required List<SpecField> optionalFields,
    
    /// 分组信息
    required List<SpecFieldGroup> fieldGroups,
    
    /// 验证规则
    List<ValidationRule>? validationRules,
    
    /// 模板描述
    String? description,
    
    /// 创建时间
    required DateTime createdAt,
    
    /// 更新时间
    required DateTime updatedAt,
  }) = _CategorySpecTemplate;

  factory CategorySpecTemplate.fromJson(Map<String, dynamic> json) => 
      _$CategorySpecTemplateFromJson(json);
}

/// 规格字段定义
@freezed
class SpecField with _$SpecField {
  const factory SpecField({
    /// 字段ID
    required String id,
    
    /// 字段名称
    required String name,
    
    /// 字段标签（显示名称）
    required String label,
    
    /// 字段类型
    required SpecFieldType type,
    
    /// 单位
    String? unit,
    
    /// 默认值
    dynamic defaultValue,
    
    /// 字段描述
    String? description,
    
    /// 是否必填
    @Default(false) bool required,
    
    /// 显示顺序
    @Default(0) int order,
    
    /// 字段选项（用于枚举类型）
    List<SpecFieldOption>? options,
    
    /// 数值范围（用于数值类型）
    NumericRange? numericRange,
    
    /// 字符串长度限制
    StringLengthLimit? stringLengthLimit,
    
    /// 字段分组
    String? groupId,
    
    /// 是否在列表中显示
    @Default(false) bool showInList,
    
    /// 是否可搜索
    @Default(false) bool searchable,
    
    /// 是否可筛选
    @Default(false) bool filterable,
  }) = _SpecField;

  factory SpecField.fromJson(Map<String, dynamic> json) => 
      _$SpecFieldFromJson(json);
}

/// 规格字段类型
enum SpecFieldType {
  /// 文本
  text,
  /// 数字
  number,
  /// 小数
  decimal,
  /// 布尔值
  boolean,
  /// 日期
  date,
  /// 日期时间
  datetime,
  /// 枚举（单选）
  enumSingle,
  /// 枚举（多选）
  enumMultiple,
  /// 范围（数值）
  rangeNumeric,
  /// 范围（日期）
  rangeDate,
  /// 尺寸
  dimensions,
  /// 电压
  voltage,
  /// 功率
  power,
  /// 容量
  capacity,
  /// 温度
  temperature,
  /// 压力
  pressure,
  /// 流量
  flowRate,
  /// 重量
  weight,
  /// 颜色
  color,
  /// 图片URL
  imageUrl,
  /// 文件URL
  fileUrl,
  /// JSON对象
  jsonObject,
}

/// 规格字段选项
@freezed
class SpecFieldOption with _$SpecFieldOption {
  const factory SpecFieldOption({
    /// 选项值
    required String value,
    
    /// 选项标签
    required String label,
    
    /// 选项描述
    String? description,
    
    /// 是否默认选中
    @Default(false) bool isDefault,
    
    /// 显示顺序
    @Default(0) int order,
  }) = _SpecFieldOption;

  factory SpecFieldOption.fromJson(Map<String, dynamic> json) => 
      _$SpecFieldOptionFromJson(json);
}

/// 数值范围
@freezed
class NumericRange with _$NumericRange {
  const factory NumericRange({
    /// 最小值
    double? min,
    
    /// 最大值
    double? max,
    
    /// 步长
    double? step,
    
    /// 单位
    String? unit,
  }) = _NumericRange;

  factory NumericRange.fromJson(Map<String, dynamic> json) => 
      _$NumericRangeFromJson(json);
}

/// 字符串长度限制
@freezed
class StringLengthLimit with _$StringLengthLimit {
  const factory StringLengthLimit({
    /// 最小长度
    int? minLength,
    
    /// 最大长度
    int? maxLength,
  }) = _StringLengthLimit;

  factory StringLengthLimit.fromJson(Map<String, dynamic> json) => 
      _$StringLengthLimitFromJson(json);
}

/// 规格字段分组
@freezed
class SpecFieldGroup with _$SpecFieldGroup {
  const factory SpecFieldGroup({
    /// 分组ID
    required String id,
    
    /// 分组名称
    required String name,
    
    /// 分组标签
    required String label,
    
    /// 分组描述
    String? description,
    
    /// 显示顺序
    @Default(0) int order,
    
    /// 是否可折叠
    @Default(false) bool collapsible,
    
    /// 默认是否展开
    @Default(true) bool defaultExpanded,
    
    /// 分组图标
    String? icon,
  }) = _SpecFieldGroup;

  factory SpecFieldGroup.fromJson(Map<String, dynamic> json) => 
      _$SpecFieldGroupFromJson(json);
}

/// 验证规则
@freezed
class ValidationRule with _$ValidationRule {
  const factory ValidationRule({
    /// 规则ID
    required String id,
    
    /// 规则类型
    required ValidationRuleType type,
    
    /// 目标字段
    required String targetField,
    
    /// 规则参数
    Map<String, dynamic>? parameters,
    
    /// 错误消息
    String? errorMessage,
    
    /// 是否启用
    @Default(true) bool enabled,
  }) = _ValidationRule;

  factory ValidationRule.fromJson(Map<String, dynamic> json) => 
      _$ValidationRuleFromJson(json);
}

/// 验证规则类型
enum ValidationRuleType {
  /// 必填验证
  required,
  /// 数值范围验证
  numericRange,
  /// 字符串长度验证
  stringLength,
  /// 正则表达式验证
  regex,
  /// 自定义验证
  custom,
  /// 字段依赖验证
  fieldDependency,
  /// 唯一性验证
  unique,
}

/// 预定义的分类规格模板
class CategorySpecTemplates {
  /// 电气设备模板
  static CategorySpecTemplate get electrical => CategorySpecTemplate(
    id: 'electrical',
    categoryName: '电气设备',
    categoryCode: 'ELECTRICAL',
    requiredFields: [
      SpecField(
        id: 'rated_voltage',
        name: 'ratedVoltage',
        label: '额定电压',
        type: SpecFieldType.voltage,
        unit: 'V',
        required: true,
        order: 1,
        groupId: 'electrical_params',
        showInList: true,
        searchable: true,
        filterable: true,
      ),
      SpecField(
        id: 'rated_power',
        name: 'ratedPower',
        label: '额定功率',
        type: SpecFieldType.power,
        unit: 'W',
        required: true,
        order: 2,
        groupId: 'electrical_params',
        showInList: true,
        searchable: true,
        filterable: true,
      ),
    ],
    optionalFields: [
      SpecField(
        id: 'capacity',
        name: 'capacity',
        label: '容量',
        type: SpecFieldType.capacity,
        unit: 'Ah',
        order: 3,
        groupId: 'electrical_params',
        showInList: true,
        filterable: true,
      ),
      SpecField(
        id: 'efficiency',
        name: 'efficiency',
        label: '效率',
        type: SpecFieldType.decimal,
        unit: '%',
        order: 4,
        groupId: 'performance',
        numericRange: NumericRange(min: 0, max: 100),
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'electrical_params',
        name: 'electricalParams',
        label: '电气参数',
        order: 1,
        icon: 'electrical_services',
      ),
      SpecFieldGroup(
        id: 'performance',
        name: 'performance',
        label: '性能指标',
        order: 2,
        icon: 'speed',
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 水路设备模板
  static CategorySpecTemplate get plumbing => CategorySpecTemplate(
    id: 'plumbing',
    categoryName: '水路设备',
    categoryCode: 'PLUMBING',
    requiredFields: [
      SpecField(
        id: 'flow_rate',
        name: 'flowRate',
        label: '流量',
        type: SpecFieldType.flowRate,
        unit: 'L/min',
        required: true,
        order: 1,
        groupId: 'fluid_params',
        showInList: true,
        searchable: true,
        filterable: true,
      ),
      SpecField(
        id: 'working_pressure',
        name: 'workingPressure',
        label: '工作压力',
        type: SpecFieldType.pressure,
        unit: 'MPa',
        required: true,
        order: 2,
        groupId: 'fluid_params',
        showInList: true,
        filterable: true,
      ),
    ],
    optionalFields: [
      SpecField(
        id: 'material',
        name: 'material',
        label: '材质',
        type: SpecFieldType.enumSingle,
        order: 3,
        groupId: 'material_props',
        options: [
          SpecFieldOption(value: 'stainless_steel', label: '不锈钢'),
          SpecFieldOption(value: 'pvc', label: 'PVC'),
          SpecFieldOption(value: 'brass', label: '黄铜'),
          SpecFieldOption(value: 'plastic', label: '塑料'),
        ],
        filterable: true,
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'fluid_params',
        name: 'fluidParams',
        label: '流体参数',
        order: 1,
        icon: 'water_drop',
      ),
      SpecFieldGroup(
        id: 'material_props',
        name: 'materialProps',
        label: '材质属性',
        order: 2,
        icon: 'construction',
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 获取所有预定义模板
  static List<CategorySpecTemplate> getAllTemplates() {
    return [
      electrical,
      plumbing,
      // 其他模板将在后续添加
    ];
  }

  /// 根据分类代码获取模板
  static CategorySpecTemplate? getTemplate(String categoryCode) {
    switch (categoryCode.toUpperCase()) {
      case 'ELECTRICAL':
        return electrical;
      case 'PLUMBING':
        return plumbing;
      default:
        return null;
    }
  }
}
