import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/material_review.dart';

/// 材料评价摘要组件
/// 显示材料的整体评价统计信息，帮助用户快速了解材料质量
class MaterialReviewSummaryWidget extends ConsumerWidget {
  final MaterialReviewSummary summary;
  final VoidCallback? onViewAllReviews;
  final VoidCallback? onWriteReview;
  final bool showWriteReviewButton;

  const MaterialReviewSummaryWidget({
    super.key,
    required this.summary,
    this.onViewAllReviews,
    this.onWriteReview,
    this.showWriteReviewButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (summary.totalReviews == 0) {
      return _buildNoReviewsState(context, colorScheme);
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和总体评分
            _buildHeader(context, colorScheme),
            
            const SizedBox(height: 16),
            
            // 评分分布
            _buildRatingDistribution(context, colorScheme),
            
            const SizedBox(height: 16),
            
            // 专业评分维度
            _buildDimensionRatings(context, colorScheme),
            
            const SizedBox(height: 16),
            
            // 推荐度和验证购买
            _buildRecommendationInfo(context, colorScheme),
            
            if (summary.topPros.isNotEmpty || summary.topCons.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildTopProsCons(context, colorScheme),
            ],
            
            const SizedBox(height: 16),
            
            // 操作按钮
            _buildActionButtons(context, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildNoReviewsState(BuildContext context, ColorScheme colorScheme) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.rate_review_outlined,
              size: 48,
              color: colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              '暂无评价',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              '成为第一个评价这个材料的用户',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            
            if (showWriteReviewButton) ...[
              const SizedBox(height: 16),
              
              ElevatedButton.icon(
                onPressed: onWriteReview,
                icon: const Icon(Icons.edit),
                label: const Text('写评价'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ColorScheme colorScheme) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '用户评价',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                '${summary.totalReviews}条评价',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
        
        // 总体评分显示
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  summary.averageRating.toStringAsFixed(1),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
                
                const SizedBox(width: 8),
                
                _buildStarRating(summary.averageRating),
              ],
            ),
            
            const SizedBox(height: 4),
            
            Text(
              '推荐度 ${(summary.recommendationScore * 20).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingDistribution(BuildContext context, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '评分分布',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        ...List.generate(5, (index) {
          final stars = 5 - index;
          final count = summary.ratingDistribution[stars] ?? 0;
          final percentage = summary.totalReviews > 0 ? count / summary.totalReviews : 0.0;
          
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text(
                  '$stars星',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                
                const SizedBox(width: 8),
                
                Expanded(
                  child: LinearProgressIndicator(
                    value: percentage,
                    backgroundColor: colorScheme.surfaceContainerHighest,
                    valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                SizedBox(
                  width: 30,
                  child: Text(
                    '$count',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildDimensionRatings(BuildContext context, ColorScheme colorScheme) {
    final dimensions = [
      ('质量', summary.qualityAverage, Icons.verified),
      ('性价比', summary.valueAverage, Icons.attach_money),
      ('耐用性', summary.durabilityAverage, Icons.schedule),
      ('安装难度', summary.installationAverage, Icons.build),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '详细评分',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        ...dimensions.map((dimension) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Icon(
                dimension.$3,
                size: 16,
                color: colorScheme.primary,
              ),
              
              const SizedBox(width: 8),
              
              SizedBox(
                width: 60,
                child: Text(
                  dimension.$1,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
              
              const SizedBox(width: 8),
              
              _buildStarRating(dimension.$2, size: 14),
              
              const SizedBox(width: 8),
              
              Text(
                dimension.$2.toStringAsFixed(1),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildRecommendationInfo(BuildContext context, ColorScheme colorScheme) {
    return Row(
      children: [
        Expanded(
          child: _buildInfoChip(
            context,
            colorScheme,
            '推荐度',
            '${(summary.recommendationScore * 20).toInt()}%',
            Icons.thumb_up,
            Colors.green,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: _buildInfoChip(
            context,
            colorScheme,
            '验证购买',
            '${summary.verifiedPurchaseCount}/${summary.totalReviews}',
            Icons.verified_user,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoChip(
    BuildContext context,
    ColorScheme colorScheme,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          
          const SizedBox(width: 8),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                  ),
                ),
                
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopProsCons(BuildContext context, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '用户反馈',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        if (summary.topPros.isNotEmpty) ...[
          Text(
            '主要优点',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: summary.topPros.take(3).map((pro) => Chip(
              label: Text(pro),
              backgroundColor: Colors.green.withValues(alpha: 0.1),
              labelStyle: TextStyle(
                fontSize: 12,
                color: Colors.green,
              ),
            )).toList(),
          ),
          
          const SizedBox(height: 8),
        ],
        
        if (summary.topCons.isNotEmpty) ...[
          Text(
            '主要缺点',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: summary.topCons.take(3).map((con) => Chip(
              label: Text(con),
              backgroundColor: Colors.orange.withValues(alpha: 0.1),
              labelStyle: TextStyle(
                fontSize: 12,
                color: Colors.orange,
              ),
            )).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, ColorScheme colorScheme) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onViewAllReviews,
            icon: const Icon(Icons.list),
            label: const Text('查看全部评价'),
          ),
        ),
        
        if (showWriteReviewButton) ...[
          const SizedBox(width: 12),
          
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onWriteReview,
              icon: const Icon(Icons.edit),
              label: const Text('写评价'),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStarRating(double rating, {double size = 16}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        final starValue = index + 1;
        return Icon(
          starValue <= rating
              ? Icons.star
              : starValue - 0.5 <= rating
                  ? Icons.star_half
                  : Icons.star_border,
          size: size,
          color: Colors.amber,
        );
      }),
    );
  }
}
