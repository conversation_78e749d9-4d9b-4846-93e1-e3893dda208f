import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics_data.freezed.dart';
part 'analytics_data.g.dart';

/// 分析数据实体
@freezed
class AnalyticsData with _$AnalyticsData {
  const factory AnalyticsData({
    required String id,
    required String projectId,
    required double totalBudget,
    required double actualCost,
    required double remainingBudget,
    required int totalMaterials,
    required int completedTasks,
    required int totalTasks,
    required double completionPercentage,
    required DateTime lastUpdated,
    @Default({}) Map<String, double> categoryCosts,
    @Default({}) Map<String, int> materialCounts,
    @Default([]) List<MonthlyData> monthlyTrends,
  }) = _AnalyticsData;

  factory AnalyticsData.fromJson(Map<String, dynamic> json) => _$AnalyticsDataFromJson(json);
}

/// 月度数据
@freezed
class MonthlyData with _$MonthlyData {
  const factory MonthlyData({
    required int month,
    required int year,
    required double expenses,
    required int completedTasks,
    required int newMaterials,
  }) = _MonthlyData;

  factory MonthlyData.fromJson(Map<String, dynamic> json) => _$MonthlyDataFromJson(json);
}

/// 成本趋势数据（简化版）
@freezed
class SimpleCostTrend with _$SimpleCostTrend {
  const factory SimpleCostTrend({
    required DateTime date,
    required double budgetAmount,
    required double actualAmount,
    required String category,
    String? description,
  }) = _SimpleCostTrend;

  factory SimpleCostTrend.fromJson(Map<String, dynamic> json) => _$SimpleCostTrendFromJson(json);
}

/// 进度统计数据（简化版）
@freezed
class SimpleProgressStats with _$SimpleProgressStats {
  const factory SimpleProgressStats({
    required String projectId,
    required int totalTasks,
    required int completedTasks,
    required int inProgressTasks,
    required int pendingTasks,
    required double overallProgress,
    required DateTime lastUpdated,
    @Default([]) List<TaskProgress> taskProgress,
  }) = _SimpleProgressStats;

  factory SimpleProgressStats.fromJson(Map<String, dynamic> json) => _$SimpleProgressStatsFromJson(json);
}

/// 任务进度
@freezed
class TaskProgress with _$TaskProgress {
  const factory TaskProgress({
    required String taskId,
    required String taskName,
    required String status,
    required double progress,
    required DateTime startDate,
    DateTime? endDate,
    String? category,
  }) = _TaskProgress;

  factory TaskProgress.fromJson(Map<String, dynamic> json) => _$TaskProgressFromJson(json);
}

/// 分析数据扩展
extension AnalyticsDataX on AnalyticsData {
  /// 预算使用率
  double get budgetUtilization => totalBudget > 0 ? (actualCost / totalBudget) * 100 : 0;
  
  /// 是否超预算
  bool get isOverBudget => actualCost > totalBudget;
  
  /// 预算状态
  String get budgetStatus {
    if (isOverBudget) return '超预算';
    if (budgetUtilization > 90) return '预算紧张';
    if (budgetUtilization > 70) return '预算正常';
    return '预算充足';
  }
  
  /// 任务完成率
  double get taskCompletionRate => totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
  
  /// 项目状态
  String get projectStatus {
    if (completionPercentage >= 100) return '已完成';
    if (completionPercentage >= 80) return '即将完成';
    if (completionPercentage >= 50) return '进行中';
    if (completionPercentage >= 20) return '刚开始';
    return '规划中';
  }
  
  /// 获取最大支出分类
  String get topExpenseCategory {
    if (categoryCosts.isEmpty) return '无数据';
    return categoryCosts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
  
  /// 获取月度平均支出
  double get averageMonthlyExpense {
    if (monthlyTrends.isEmpty) return 0;
    final totalExpenses = monthlyTrends.fold<double>(0, (sum, data) => sum + data.expenses);
    return totalExpenses / monthlyTrends.length;
  }
}
