import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';

/// 进度分析服务接口
abstract class ProgressAnalysisService {
  /// 分析项目整体进度
  Future<Either<Failure, ProjectProgressAnalysis>> analyzeProjectProgress(String projectId);

  /// 分析任务完成效率
  Future<Either<Failure, TaskEfficiencyAnalysis>> analyzeTaskEfficiency(String projectId);

  /// 预测项目完成时间
  Future<Either<Failure, ProjectCompletionForecast>> forecastCompletion(String projectId);

  /// 分析进度瓶颈
  Future<Either<Failure, List<ProgressBottleneck>>> identifyBottlenecks(String projectId);

  /// 生成进度报告
  Future<Either<Failure, ProgressReport>> generateProgressReport(String projectId);
}

/// 项目进度分析
class ProjectProgressAnalysis {
  final String projectId;
  final double overallProgress; // 0-100
  final int totalTasks;
  final int completedTasks;
  final int inProgressTasks;
  final int pendingTasks;
  final Map<String, SystemProgress> systemProgress;
  final ProgressVelocity velocity;
  final List<MilestoneProgress> milestones;
  final DateTime analyzedAt;

  const ProjectProgressAnalysis({
    required this.projectId,
    required this.overallProgress,
    required this.totalTasks,
    required this.completedTasks,
    required this.inProgressTasks,
    required this.pendingTasks,
    required this.systemProgress,
    required this.velocity,
    required this.milestones,
    required this.analyzedAt,
  });
}

/// 系统进度
class SystemProgress {
  final String systemName;
  final double progress;
  final int totalTasks;
  final int completedTasks;
  final double estimatedHours;
  final double actualHours;
  final ProgressStatus status;

  const SystemProgress({
    required this.systemName,
    required this.progress,
    required this.totalTasks,
    required this.completedTasks,
    required this.estimatedHours,
    required this.actualHours,
    required this.status,
  });
}

/// 进度速度
class ProgressVelocity {
  final double tasksPerDay;
  final double hoursPerTask;
  final double progressPerDay;
  final List<VelocityDataPoint> historicalData;
  final VelocityTrend trend;

  const ProgressVelocity({
    required this.tasksPerDay,
    required this.hoursPerTask,
    required this.progressPerDay,
    required this.historicalData,
    required this.trend,
  });
}

/// 速度数据点
class VelocityDataPoint {
  final DateTime date;
  final double velocity;
  final int tasksCompleted;
  final double hoursWorked;

  const VelocityDataPoint({
    required this.date,
    required this.velocity,
    required this.tasksCompleted,
    required this.hoursWorked,
  });
}

/// 里程碑进度
class MilestoneProgress {
  final String milestoneId;
  final String name;
  final DateTime plannedDate;
  final DateTime? actualDate;
  final double progress;
  final MilestoneStatus status;
  final List<String> dependencies;
  final List<String> blockers;

  const MilestoneProgress({
    required this.milestoneId,
    required this.name,
    required this.plannedDate,
    this.actualDate,
    required this.progress,
    required this.status,
    required this.dependencies,
    required this.blockers,
  });
}

/// 任务效率分析
class TaskEfficiencyAnalysis {
  final String projectId;
  final double averageTaskDuration;
  final double taskCompletionRate;
  final Map<String, double> systemEfficiency;
  final List<TaskPerformanceMetric> topPerformers;
  final List<TaskPerformanceMetric> underPerformers;
  final EfficiencyTrend trend;
  final DateTime analyzedAt;

  const TaskEfficiencyAnalysis({
    required this.projectId,
    required this.averageTaskDuration,
    required this.taskCompletionRate,
    required this.systemEfficiency,
    required this.topPerformers,
    required this.underPerformers,
    required this.trend,
    required this.analyzedAt,
  });
}

/// 任务性能指标
class TaskPerformanceMetric {
  final String taskId;
  final String taskName;
  final String system;
  final double estimatedHours;
  final double actualHours;
  final double efficiencyRatio;
  final DateTime completedAt;

  const TaskPerformanceMetric({
    required this.taskId,
    required this.taskName,
    required this.system,
    required this.estimatedHours,
    required this.actualHours,
    required this.efficiencyRatio,
    required this.completedAt,
  });
}

/// 项目完成预测
class ProjectCompletionForecast {
  final String projectId;
  final DateTime estimatedCompletionDate;
  final DateTime originalPlannedDate;
  final int daysDelay;
  final double confidenceLevel;
  final List<ForecastScenario> scenarios;
  final List<String> assumptions;
  final List<String> risks;

  const ProjectCompletionForecast({
    required this.projectId,
    required this.estimatedCompletionDate,
    required this.originalPlannedDate,
    required this.daysDelay,
    required this.confidenceLevel,
    required this.scenarios,
    required this.assumptions,
    required this.risks,
  });
}

/// 预测场景
class ForecastScenario {
  final String name;
  final DateTime completionDate;
  final double probability;
  final String description;
  final List<String> conditions;

  const ForecastScenario({
    required this.name,
    required this.completionDate,
    required this.probability,
    required this.description,
    required this.conditions,
  });
}

/// 进度瓶颈
class ProgressBottleneck {
  final String id;
  final String title;
  final String description;
  final BottleneckType type;
  final BottleneckSeverity severity;
  final String affectedSystem;
  final List<String> affectedTasks;
  final double impactOnProgress;
  final List<String> suggestedActions;
  final DateTime identifiedAt;

  const ProgressBottleneck({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.severity,
    required this.affectedSystem,
    required this.affectedTasks,
    required this.impactOnProgress,
    required this.suggestedActions,
    required this.identifiedAt,
  });
}

/// 进度报告
class ProgressReport {
  final String projectId;
  final String projectName;
  final DateTime reportDate;
  final ProjectProgressAnalysis progressAnalysis;
  final TaskEfficiencyAnalysis efficiencyAnalysis;
  final ProjectCompletionForecast forecast;
  final List<ProgressBottleneck> bottlenecks;
  final List<String> achievements;
  final List<String> recommendations;
  final ReportSummary summary;

  const ProgressReport({
    required this.projectId,
    required this.projectName,
    required this.reportDate,
    required this.progressAnalysis,
    required this.efficiencyAnalysis,
    required this.forecast,
    required this.bottlenecks,
    required this.achievements,
    required this.recommendations,
    required this.summary,
  });
}

/// 报告摘要
class ReportSummary {
  final String overallStatus;
  final List<String> keyMetrics;
  final List<String> criticalIssues;
  final List<String> nextSteps;

  const ReportSummary({
    required this.overallStatus,
    required this.keyMetrics,
    required this.criticalIssues,
    required this.nextSteps,
  });
}

/// 进度状态枚举
enum ProgressStatus {
  notStarted,
  inProgress,
  completed,
  delayed,
  blocked,
  cancelled,
}

/// 速度趋势枚举
enum VelocityTrend {
  increasing,
  decreasing,
  stable,
  volatile,
}

/// 里程碑状态枚举
enum MilestoneStatus {
  upcoming,
  inProgress,
  completed,
  delayed,
  atRisk,
}

/// 效率趋势枚举
enum EfficiencyTrend {
  improving,
  declining,
  stable,
  inconsistent,
}

/// 瓶颈类型枚举
enum BottleneckType {
  resource,
  dependency,
  technical,
  communication,
  approval,
  external,
}

/// 瓶颈严重程度枚举
enum BottleneckSeverity {
  low,
  medium,
  high,
  critical,
}
