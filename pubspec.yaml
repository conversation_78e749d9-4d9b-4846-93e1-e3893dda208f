name: vanhub
description: VanHub改装宝 - 房车改装项目管理应用
publish_to: 'none'
version: 2.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 状态管理 & 依赖注入
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # 数据类与不可变性
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  equatable: ^2.0.7

  # 后端服务
  supabase_flutter: ^2.8.0

  # 数据可视化
  fl_chart: ^0.69.0

  # 路由管理
  go_router: ^14.6.2

  # UI & UX
  material_design_icons_flutter: ^7.0.7296
  intl: ^0.20.1
  flutter_html: ^3.0.0-beta.2
  html_editor_enhanced: ^2.5.1
  timeline_tile: ^2.0.0

  # 高端UI/UX重构依赖
  flutter_staggered_grid_view: ^0.7.0  # 瀑布流布局
  flutter_animate: ^4.5.0              # 高级动画系统
  rive: ^0.13.13                       # 矢量动画
  lottie: ^3.1.2                       # Lottie动画
  flutter_staggered_animations: ^1.1.1 # 交错动画
  auto_size_text: ^3.0.0               # 响应式文本
  flutter_screenutil: ^5.9.3           # 屏幕适配
  device_preview: ^1.2.0               # 设备预览
  flutter_svg: ^2.0.10+1               # SVG图标支持

  # 实用工具
  fpdart: ^1.1.0
  http: ^1.2.2
  crypto: ^3.0.6
  flutter_dotenv: ^5.2.1
  uuid: ^4.3.3
  get_it: ^8.0.2

  # 文件处理
  file_picker: ^10.2.0
  image_picker: ^1.1.2
  csv: ^6.0.0
  excel: ^4.0.6
  pdf: ^3.11.1
  path_provider: ^2.1.4

  # UI增强
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1        # 图片缓存
  shared_preferences: ^2.3.2          # 本地存储
  flutter_secure_storage: ^9.2.2      # 安全存储
  
  # 设计系统新增依赖
  dynamic_color: ^1.7.0               # Material 3动态颜色

  # 语音识别 (暂时禁用以解决网络问题)
  # speech_to_text: ^7.0.0              # 语音转文字

  # 分享功能 (暂时禁用以解决网络问题)
  # share_plus: ^10.1.2                   # 分享功能

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成器
  build_runner: ^2.4.13
  riverpod_generator: ^2.6.2
  freezed: ^2.5.7
  json_serializable: ^6.8.0

  # 代码规范
  flutter_lints: ^5.0.0

  # 测试辅助
  mockito: ^5.4.4

flutter:
  uses-material-design: true
  assets:
    - .env.local
    - assets/l10n/
    
