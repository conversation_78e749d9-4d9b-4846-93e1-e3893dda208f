import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../providers/project_provider.dart';
import '../providers/project_stats_provider.dart';
import '../../../bom/presentation/providers/bom_provider.dart';
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/domain/entities/bom_item_status.dart';
import '../../../modification_log/presentation/providers/log_provider.dart';
import '../../../modification_log/presentation/widgets/create_milestone_dialog.dart';

/// 项目仪表盘组件 - 统一展示项目全貌
class ProjectDashboardWidget extends ConsumerWidget {
  final String projectId;

  const ProjectDashboardWidget({
    super.key,
    required this.projectId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 关键指标卡片行
          _buildMetricsRow(context, ref),

          const SizedBox(height: 16),

          // 第二行指标卡片
          _buildSecondMetricsRow(context, ref),

          const SizedBox(height: 24),
          
          // 成本分析区域
          _buildCostAnalysisSection(context, ref),
          
          const SizedBox(height: 24),
          
          // 活动时间线
          _buildActivityTimeline(context, ref),
          
          const SizedBox(height: 24),
          
          // 快速操作区域
          _buildQuickActions(context, ref),
        ],
      ),
    );
  }

  /// 构建关键指标卡片行
  Widget _buildMetricsRow(BuildContext context, WidgetRef ref) {
    final projectAsync = ref.watch(projectDetailProvider(projectId));
    final statsAsync = ref.watch(projectStatsSummaryProvider(projectId));
    final bomItemsAsync = ref.watch(projectBomItemsProvider(projectId));

    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            context,
            title: '项目进度',
            value: statsAsync.when(
              data: (stats) => '${stats.progressPercentage.toStringAsFixed(1)}%',
              loading: () => '...',
              error: (_, __) => 'N/A',
            ),
            subtitle: statsAsync.when(
              data: (stats) => '${stats.completedBomItems}/${stats.totalBomItems} 项完成',
              loading: () => '加载中...',
              error: (_, __) => '加载失败',
            ),
            icon: Icons.trending_up,
            color: Colors.blue,
            progress: statsAsync.when(
              data: (stats) => stats.progressPercentage / 100,
              loading: () => 0.0,
              error: (_, __) => 0.0,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildMetricCard(
            context,
            title: '预算使用',
            value: statsAsync.when(
              data: (stats) => '¥${stats.actualCost.toStringAsFixed(0)}',
              loading: () => '...',
              error: (_, __) => 'N/A',
            ),
            subtitle: statsAsync.when(
              data: (stats) => '预算 ¥${stats.totalBudget.toStringAsFixed(0)}',
              loading: () => '加载中...',
              error: (_, __) => '加载失败',
            ),
            icon: Icons.account_balance_wallet,
            color: _getBudgetColor(statsAsync),
            progress: statsAsync.when(
              data: (stats) => stats.totalBudget > 0 ? (stats.actualCost / stats.totalBudget).clamp(0.0, 1.0) : 0.0,
              loading: () => 0.0,
              error: (_, __) => 0.0,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建第二行指标卡片
  Widget _buildSecondMetricsRow(BuildContext context, WidgetRef ref) {
    final bomItemsAsync = ref.watch(projectBomItemsProvider(projectId));
    final logsAsync = ref.watch(projectLogsProvider(projectId));

    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            context,
            title: '物料状态',
            value: bomItemsAsync.when(
              data: (items) => '${items.length}',
              loading: () => '...',
              error: (_, __) => 'N/A',
            ),
            subtitle: bomItemsAsync.when(
              data: (items) {
                final completed = items.where((item) => item.status == BomItemStatus.installed).length;
                return '$completed 项已完成';
              },
              loading: () => '加载中...',
              error: (_, __) => '加载失败',
            ),
            icon: Icons.inventory,
            color: Colors.orange,
            progress: bomItemsAsync.when(
              data: (items) {
                if (items.isEmpty) return 0.0;
                final completed = items.where((item) => item.status == BomItemStatus.installed).length;
                return completed / items.length;
              },
              loading: () => 0.0,
              error: (_, __) => 0.0,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildMetricCard(
            context,
            title: '改装日志',
            value: logsAsync.when(
              data: (logs) => '${logs.length}',
              loading: () => '...',
              error: (_, __) => 'N/A',
            ),
            subtitle: logsAsync.when(
              data: (logs) {
                final thisMonth = logs.where((log) {
                  final now = DateTime.now();
                  return log.logDate.year == now.year && log.logDate.month == now.month;
                }).length;
                return '本月 $thisMonth 条';
              },
              loading: () => '加载中...',
              error: (_, __) => '加载失败',
            ),
            icon: Icons.edit_note,
            color: Colors.green,
            progress: 1.0, // 日志没有进度概念，显示满进度
          ),
        ),
      ],
    );
  }

  /// 构建单个指标卡片
  Widget _buildMetricCard(
    BuildContext context, {
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required double progress,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建成本分析区域
  Widget _buildCostAnalysisSection(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '成本分析',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: _buildCostChart(context, ref),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建成本图表
  Widget _buildCostChart(BuildContext context, WidgetRef ref) {
    final bomItemsAsync = ref.watch(projectBomItemsProvider(projectId));

    return bomItemsAsync.when(
      data: (bomItems) {
        if (bomItems.isEmpty) {
          return const Center(
            child: Text('暂无成本数据'),
          );
        }

        // 按分类统计成本
        final categoryData = <String, double>{};
        for (final item in bomItems) {
          final category = item.category ?? '未分类';
          final cost = item.quantity * item.unitPrice;
          categoryData[category] = (categoryData[category] ?? 0.0) + cost;
        }

        final sections = categoryData.entries.map((entry) {
          final total = categoryData.values.fold<double>(0, (sum, value) => sum + value);
          final percentage = total > 0 ? (entry.value / total) * 100 : 0.0;
          
          return PieChartSectionData(
            color: _getCategoryColor(entry.key),
            value: entry.value,
            title: '${percentage.toStringAsFixed(1)}%',
            radius: 60,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          );
        }).toList();

        return Row(
          children: [
            Expanded(
              flex: 2,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: categoryData.entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getCategoryColor(entry.key),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            entry.key,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        Text(
                          '¥${entry.value.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        );
      },
      loading: () => const LoadingWidget(message: '加载成本数据...'),
      error: (error, stack) => ErrorDisplayWidget(
        message: '加载成本数据失败: $error',
        onRetry: () => ref.invalidate(projectBomItemsProvider(projectId)),
      ),
    );
  }

  /// 构建活动时间线
  Widget _buildActivityTimeline(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '最近活动',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: 跳转到完整时间线
                  },
                  child: const Text('查看全部'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTimelineContent(context, ref),
          ],
        ),
      ),
    );
  }

  /// 构建时间线内容
  Widget _buildTimelineContent(BuildContext context, WidgetRef ref) {
    final logsAsync = ref.watch(projectLogsProvider(projectId));

    return logsAsync.when(
      data: (logs) {
        if (logs.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text('暂无活动记录'),
            ),
          );
        }

        // 取最近5条日志
        final recentLogs = logs.take(5).toList();

        return Column(
          children: recentLogs.map((log) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: _getLogStatusColor(log.status),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getLogStatusIcon(log.status),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          log.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              _formatDate(log.logDate),
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                            if (log.relatedBomItemIds.isNotEmpty) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '${log.relatedBomItemIds.length} 个物料',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ),
                            ],
                            if (log.totalCost > 0) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '¥${log.totalCost.toStringAsFixed(0)}',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        );
      },
      loading: () => const LoadingWidget(message: '加载活动记录...'),
      error: (error, stack) => ErrorDisplayWidget(
        message: '加载活动记录失败: $error',
        onRetry: () => ref.invalidate(projectLogsProvider(projectId)),
      ),
    );
  }

  /// 构建快速操作区域
  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildQuickActionButton(
                  context,
                  icon: Icons.add_circle,
                  label: '记录日志',
                  color: Colors.green,
                  onPressed: () => _addLog(context, ref),
                ),
                _buildQuickActionButton(
                  context,
                  icon: Icons.add_box,
                  label: '添加物料',
                  color: Colors.blue,
                  onPressed: () => _addMaterial(context, ref),
                ),
                _buildQuickActionButton(
                  context,
                  icon: Icons.flag,
                  label: '设置里程碑',
                  color: Colors.orange,
                  onPressed: () => _addMilestone(context, ref),
                ),
                _buildQuickActionButton(
                  context,
                  icon: Icons.analytics,
                  label: '查看报告',
                  color: Colors.purple,
                  onPressed: () => _viewReport(context, ref),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速操作按钮
  Widget _buildQuickActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  // 辅助方法
  Color _getBudgetColor(AsyncValue statsAsync) {
    return statsAsync.when(
      data: (stats) {
        if (stats.totalBudget <= 0) return Colors.grey;
        final ratio = stats.actualCost / stats.totalBudget;
        if (ratio > 1.0) return Colors.red;
        if (ratio > 0.8) return Colors.orange;
        return Colors.green;
      },
      loading: () => Colors.grey,
      error: (_, __) => Colors.grey,
    );
  }

  Color _getCategoryColor(String category) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[category.hashCode % colors.length];
  }

  Color _getLogStatusColor(status) {
    // TODO: 根据实际的LogStatus枚举实现
    return Colors.blue;
  }

  IconData _getLogStatusIcon(status) {
    // TODO: 根据实际的LogStatus枚举实现
    return Icons.edit_note;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  // 快速操作方法
  void _addLog(BuildContext context, WidgetRef ref) {
    // TODO: 实现添加日志功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加日志功能开发中...')),
    );
  }

  void _addMaterial(BuildContext context, WidgetRef ref) {
    // TODO: 实现添加物料功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加物料功能开发中...')),
    );
  }

  void _addMilestone(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => CreateMilestoneDialog(
        projectId: projectId,
      ),
    ).then((result) {
      if (result != null) {
        // 里程碑创建成功，刷新相关数据
        ref.invalidate(projectLogsProvider(projectId));
        ref.invalidate(projectStatsSummaryProvider(projectId));
      }
    });
  }

  void _viewReport(BuildContext context, WidgetRef ref) {
    // TODO: 实现查看报告功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('查看报告功能开发中...')),
    );
  }
}
