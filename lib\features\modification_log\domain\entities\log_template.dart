import 'package:freezed_annotation/freezed_annotation.dart';

part 'log_template.freezed.dart';
part 'log_template.g.dart';

/// 模板类型
enum LogTemplateType {
  system,      // 系统预设模板
  custom,      // 用户自定义模板
  shared,      // 分享的模板
  imported,    // 导入的模板
}

/// 模板分类
enum LogTemplateCategory {
  electrical,    // 电路改装
  interior,      // 内饰改装
  exterior,      // 外观改装
  mechanical,    // 机械改装
  plumbing,      // 水路改装
  storage,       // 储物改装
  comfort,       // 舒适性改装
  safety,        // 安全改装
  general,       // 通用模板
}

/// 模板字段类型
enum TemplateFieldType {
  text,          // 文本
  richText,      // 富文本
  number,        // 数字
  currency,      // 货币
  date,          // 日期
  time,          // 时间
  duration,      // 时长
  dropdown,      // 下拉选择
  checkbox,      // 复选框
  radio,         // 单选框
  rating,        // 评分
  media,         // 媒体文件
  bomItems,      // BOM物料
  location,      // 位置
  tags,          // 标签
}

/// 模板字段
@freezed
class TemplateField with _$TemplateField {
  const factory TemplateField({
    required String id,
    required String name,
    required String label,
    String? description,
    required TemplateFieldType type,
    @Default(false) bool isRequired,
    @Default(true) bool isVisible,
    @Default(0) int order,
    dynamic defaultValue,
    List<String>? options,
    Map<String, String>? optionLabels,
    String? placeholder,
    String? unit,
    dynamic minValue,
    dynamic maxValue,
    String? validationPattern,
    String? validationMessage,
    @Default({}) Map<String, dynamic> metadata,
  }) = _TemplateField;

  factory TemplateField.fromJson(Map<String, dynamic> json) => 
      _$TemplateFieldFromJson(json);
}

/// 日志模板
@freezed
class LogTemplate with _$LogTemplate {
  const factory LogTemplate({
    required String id,
    required String name,
    required String description,
    String? thumbnail,
    required LogTemplateType type,
    required LogTemplateCategory category,
    required List<TemplateField> fields,
    required String createdBy,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(0) int usageCount,
    @Default(0.0) double rating,
    @Default(0) int ratingCount,
    @Default([]) List<String> tags,
    @Default(true) bool isActive,
    @Default(false) bool isPublic,
    @Default(false) bool isFeatured,
    String? version,
    String? changelog,
    @Default({}) Map<String, dynamic> metadata,
  }) = _LogTemplate;

  factory LogTemplate.fromJson(Map<String, dynamic> json) => 
      _$LogTemplateFromJson(json);
}

/// 模板使用记录
@freezed
class TemplateUsage with _$TemplateUsage {
  const factory TemplateUsage({
    required String id,
    required String templateId,
    required String userId,
    required String logId,
    required DateTime usedAt,
    @Default({}) Map<String, dynamic> fieldValues,
    String? feedback,
    int? rating,
  }) = _TemplateUsage;

  factory TemplateUsage.fromJson(Map<String, dynamic> json) => 
      _$TemplateUsageFromJson(json);
}

/// 模板扩展方法
extension LogTemplateX on LogTemplate {
  /// 获取分类显示名称
  String get categoryDisplayName {
    switch (category) {
      case LogTemplateCategory.electrical:
        return '电路改装';
      case LogTemplateCategory.interior:
        return '内饰改装';
      case LogTemplateCategory.exterior:
        return '外观改装';
      case LogTemplateCategory.mechanical:
        return '机械改装';
      case LogTemplateCategory.plumbing:
        return '水路改装';
      case LogTemplateCategory.storage:
        return '储物改装';
      case LogTemplateCategory.comfort:
        return '舒适性改装';
      case LogTemplateCategory.safety:
        return '安全改装';
      case LogTemplateCategory.general:
        return '通用模板';
    }
  }

  /// 获取类型显示名称
  String get typeDisplayName {
    switch (type) {
      case LogTemplateType.system:
        return '系统模板';
      case LogTemplateType.custom:
        return '自定义模板';
      case LogTemplateType.shared:
        return '分享模板';
      case LogTemplateType.imported:
        return '导入模板';
    }
  }

  /// 获取分类图标
  String get categoryIcon {
    switch (category) {
      case LogTemplateCategory.electrical:
        return 'electrical_services';
      case LogTemplateCategory.interior:
        return 'chair';
      case LogTemplateCategory.exterior:
        return 'directions_car';
      case LogTemplateCategory.mechanical:
        return 'build';
      case LogTemplateCategory.plumbing:
        return 'plumbing';
      case LogTemplateCategory.storage:
        return 'inventory_2';
      case LogTemplateCategory.comfort:
        return 'airline_seat_recline_extra';
      case LogTemplateCategory.safety:
        return 'security';
      case LogTemplateCategory.general:
        return 'description';
    }
  }

  /// 获取分类颜色
  int get categoryColor {
    switch (category) {
      case LogTemplateCategory.electrical:
        return 0xFFFFEB3B; // 黄色
      case LogTemplateCategory.interior:
        return 0xFF8BC34A; // 浅绿色
      case LogTemplateCategory.exterior:
        return 0xFF2196F3; // 蓝色
      case LogTemplateCategory.mechanical:
        return 0xFF9E9E9E; // 灰色
      case LogTemplateCategory.plumbing:
        return 0xFF00BCD4; // 青色
      case LogTemplateCategory.storage:
        return 0xFFFF9800; // 橙色
      case LogTemplateCategory.comfort:
        return 0xFF9C27B0; // 紫色
      case LogTemplateCategory.safety:
        return 0xFFF44336; // 红色
      case LogTemplateCategory.general:
        return 0xFF607D8B; // 蓝灰色
    }
  }

  /// 获取评分显示文本
  String get ratingText {
    if (ratingCount == 0) return '暂无评分';
    return '${rating.toStringAsFixed(1)} ($ratingCount人评价)';
  }

  /// 获取使用次数显示文本
  String get usageText {
    if (usageCount == 0) return '暂未使用';
    if (usageCount < 1000) return '$usageCount次使用';
    return '${(usageCount / 1000).toStringAsFixed(1)}k次使用';
  }

  /// 是否为热门模板
  bool get isPopular => usageCount > 100 || (rating > 4.0 && ratingCount > 10);

  /// 是否为新模板
  bool get isNew {
    final daysSinceCreated = DateTime.now().difference(createdAt).inDays;
    return daysSinceCreated <= 7;
  }

  /// 获取必填字段
  List<TemplateField> get requiredFields {
    return fields.where((field) => field.isRequired).toList();
  }

  /// 获取可见字段
  List<TemplateField> get visibleFields {
    return fields.where((field) => field.isVisible).toList();
  }

  /// 获取排序后的字段
  List<TemplateField> get sortedFields {
    final sorted = List<TemplateField>.from(fields);
    sorted.sort((a, b) => a.order.compareTo(b.order));
    return sorted;
  }

  /// 验证字段值
  Map<String, String> validateFieldValues(Map<String, dynamic> values) {
    final errors = <String, String>{};
    
    for (final field in fields) {
      final value = values[field.name];
      
      // 检查必填字段
      if (field.isRequired && (value == null || value.toString().isEmpty)) {
        errors[field.name] = '${field.label}是必填项';
        continue;
      }
      
      if (value == null) continue;
      
      // 类型验证
      switch (field.type) {
        case TemplateFieldType.number:
        case TemplateFieldType.currency:
          if (value is! num && double.tryParse(value.toString()) == null) {
            errors[field.name] = '${field.label}必须是数字';
          } else {
            final numValue = value is num ? value : double.parse(value.toString());
            if (field.minValue != null && numValue < field.minValue) {
              errors[field.name] = '${field.label}不能小于${field.minValue}';
            }
            if (field.maxValue != null && numValue > field.maxValue) {
              errors[field.name] = '${field.label}不能大于${field.maxValue}';
            }
          }
          break;
          
        case TemplateFieldType.text:
        case TemplateFieldType.richText:
          if (field.validationPattern != null) {
            final regex = RegExp(field.validationPattern!);
            if (!regex.hasMatch(value.toString())) {
              errors[field.name] = field.validationMessage ?? '${field.label}格式不正确';
            }
          }
          break;
          
        case TemplateFieldType.dropdown:
        case TemplateFieldType.radio:
          if (field.options != null && !field.options!.contains(value.toString())) {
            errors[field.name] = '${field.label}选项无效';
          }
          break;
          
        case TemplateFieldType.rating:
          final ratingValue = value is num ? value : double.tryParse(value.toString());
          if (ratingValue == null || ratingValue < 1 || ratingValue > 5) {
            errors[field.name] = '${field.label}必须在1-5之间';
          }
          break;
      }
    }
    
    return errors;
  }

  /// 创建电路改装模板
  factory LogTemplate.electricalTemplate() {
    return LogTemplate(
      id: 'template_electrical_basic',
      name: '电路改装记录',
      description: '记录电路改装的详细过程和注意事项',
      type: LogTemplateType.system,
      category: LogTemplateCategory.electrical,
      createdBy: 'system',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      fields: [
        TemplateField(
          id: 'title',
          name: 'title',
          label: '改装标题',
          type: TemplateFieldType.text,
          isRequired: true,
          order: 1,
          placeholder: '例如：安装逆变器',
        ),
        TemplateField(
          id: 'voltage',
          name: 'voltage',
          label: '工作电压',
          type: TemplateFieldType.dropdown,
          isRequired: true,
          order: 2,
          options: ['12V', '24V', '48V', '220V'],
          defaultValue: '12V',
        ),
        TemplateField(
          id: 'current',
          name: 'current',
          label: '工作电流',
          type: TemplateFieldType.number,
          order: 3,
          unit: 'A',
          minValue: 0,
        ),
        TemplateField(
          id: 'power',
          name: 'power',
          label: '功率',
          type: TemplateFieldType.number,
          order: 4,
          unit: 'W',
          minValue: 0,
        ),
        TemplateField(
          id: 'safety_check',
          name: 'safety_check',
          label: '安全检查',
          type: TemplateFieldType.checkbox,
          isRequired: true,
          order: 5,
          description: '确认已进行安全检查',
        ),
        TemplateField(
          id: 'description',
          name: 'description',
          label: '详细描述',
          type: TemplateFieldType.richText,
          isRequired: true,
          order: 6,
          placeholder: '详细描述改装过程、遇到的问题和解决方案...',
        ),
        TemplateField(
          id: 'cost',
          name: 'cost',
          label: '改装成本',
          type: TemplateFieldType.currency,
          order: 7,
          unit: '元',
          minValue: 0,
        ),
        TemplateField(
          id: 'duration',
          name: 'duration',
          label: '用时',
          type: TemplateFieldType.duration,
          order: 8,
          unit: '小时',
        ),
        TemplateField(
          id: 'media',
          name: 'media',
          label: '相关图片/视频',
          type: TemplateFieldType.media,
          order: 9,
        ),
        TemplateField(
          id: 'materials',
          name: 'materials',
          label: '使用材料',
          type: TemplateFieldType.bomItems,
          order: 10,
        ),
      ],
      tags: ['电路', '改装', '安全', '基础'],
      isFeatured: true,
    );
  }

  /// 创建内饰改装模板
  factory LogTemplate.interiorTemplate() {
    return LogTemplate(
      id: 'template_interior_basic',
      name: '内饰改装记录',
      description: '记录内饰改装的设计思路和施工过程',
      type: LogTemplateType.system,
      category: LogTemplateCategory.interior,
      createdBy: 'system',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      fields: [
        TemplateField(
          id: 'title',
          name: 'title',
          label: '改装标题',
          type: TemplateFieldType.text,
          isRequired: true,
          order: 1,
          placeholder: '例如：制作床铺',
        ),
        TemplateField(
          id: 'area',
          name: 'area',
          label: '改装区域',
          type: TemplateFieldType.dropdown,
          isRequired: true,
          order: 2,
          options: ['驾驶室', '客厅区', '卧室区', '厨房区', '卫生间', '储物区', '其他'],
        ),
        TemplateField(
          id: 'style',
          name: 'style',
          label: '设计风格',
          type: TemplateFieldType.dropdown,
          order: 3,
          options: ['现代简约', '工业风', '北欧风', '日式', '美式', '中式', '其他'],
        ),
        TemplateField(
          id: 'materials_type',
          name: 'materials_type',
          label: '主要材料',
          type: TemplateFieldType.dropdown,
          order: 4,
          options: ['木材', '金属', '塑料', '布艺', '皮革', '复合材料', '其他'],
        ),
        TemplateField(
          id: 'dimensions',
          name: 'dimensions',
          label: '尺寸规格',
          type: TemplateFieldType.text,
          order: 5,
          placeholder: '长x宽x高 (cm)',
        ),
        TemplateField(
          id: 'description',
          name: 'description',
          label: '详细描述',
          type: TemplateFieldType.richText,
          isRequired: true,
          order: 6,
          placeholder: '详细描述设计思路、制作过程、注意事项...',
        ),
        TemplateField(
          id: 'difficulty',
          name: 'difficulty',
          label: '难度评级',
          type: TemplateFieldType.rating,
          order: 7,
          defaultValue: 3,
        ),
        TemplateField(
          id: 'satisfaction',
          name: 'satisfaction',
          label: '满意度',
          type: TemplateFieldType.rating,
          order: 8,
          defaultValue: 5,
        ),
        TemplateField(
          id: 'cost',
          name: 'cost',
          label: '改装成本',
          type: TemplateFieldType.currency,
          order: 9,
          unit: '元',
          minValue: 0,
        ),
        TemplateField(
          id: 'duration',
          name: 'duration',
          label: '用时',
          type: TemplateFieldType.duration,
          order: 10,
          unit: '小时',
        ),
        TemplateField(
          id: 'media',
          name: 'media',
          label: '相关图片/视频',
          type: TemplateFieldType.media,
          order: 11,
        ),
        TemplateField(
          id: 'materials',
          name: 'materials',
          label: '使用材料',
          type: TemplateFieldType.bomItems,
          order: 12,
        ),
      ],
      tags: ['内饰', '设计', '制作', '装饰'],
      isFeatured: true,
    );
  }

  /// 获取所有系统预设模板
  static List<LogTemplate> getSystemTemplates() {
    return [
      LogTemplate.electricalTemplate(),
      LogTemplate.interiorTemplate(),
      // TODO: 添加更多系统模板
    ];
  }
}
