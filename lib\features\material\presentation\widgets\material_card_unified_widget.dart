import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../../domain/entities/product_specification.dart';
import '../providers/specification_provider.dart';
import '../../../../core/design_system/utils/responsive_utils.dart';

/// 统一的材料卡片组件
/// 
/// 特性：
/// - 支持网格和列表两种布局
/// - 游客和登录用户一致的视觉效果
/// - 收藏、分享、快速预览功能
/// - 响应式设计
/// - 无障碍支持
class MaterialCardUnifiedWidget extends ConsumerWidget {
  final domain.Material material;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onAddToBom;
  final VoidCallback? onFavorite;
  final VoidCallback? onShare;
  final bool isListView;
  final bool isGuestMode;
  final bool showActions;

  const MaterialCardUnifiedWidget({
    super.key,
    required this.material,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onAddToBom,
    this.onFavorite,
    this.onShare,
    this.isListView = false,
    this.isGuestMode = false,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return isListView ? _buildListCard(context, ref) : _buildGridCard(context, ref);
  }
  
  Widget _buildGridCard(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 3,
      shadowColor: Colors.black.withValues(alpha: 0.15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildImageSection(context),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(context.responsiveValue(
                  xs: 10,
                  sm: 12,
                  md: 14,
                  lg: 16,
                  xl: 18,
                  defaultValue: 14,
                )),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTitle(context),
                    SizedBox(height: context.responsiveValue(
                      xs: 4,
                      sm: 5,
                      md: 6,
                      lg: 7,
                      xl: 8,
                      defaultValue: 6,
                    )),
                    _buildSubtitle(context),
                    SizedBox(height: context.responsiveValue(
                      xs: 6,
                      sm: 7,
                      md: 8,
                      lg: 9,
                      xl: 10,
                      defaultValue: 8,
                    )),
                    _buildDescription(context),
                    SizedBox(height: context.responsiveValue(
                      xs: 6,
                      sm: 7,
                      md: 8,
                      lg: 9,
                      xl: 10,
                      defaultValue: 8,
                    )),
                    _buildSpecifications(context, ref),
                    SizedBox(height: context.responsiveValue(
                      xs: 6,
                      sm: 7,
                      md: 8,
                      lg: 9,
                      xl: 10,
                      defaultValue: 8,
                    )),
                    _buildSupplierInfo(context),
                    const Spacer(),
                    _buildRatingAndReviews(context),
                    SizedBox(height: context.responsiveValue(
                      xs: 6,
                      sm: 7,
                      md: 8,
                      lg: 9,
                      xl: 10,
                      defaultValue: 8,
                    )),
                    _buildPriceAndActions(context),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildListCard(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              _buildListImage(context),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTitle(context),
                    const SizedBox(height: 4),
                    _buildSubtitle(context),
                    const SizedBox(height: 8),
                    _buildPriceAndActions(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildImageSection(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: _buildImage(context),
          ),
        ),
        _buildCategoryBadge(context),
        _buildStatusBadge(context),
        if (showActions && !isGuestMode) _buildQuickActions(context),
        _buildImageOverlay(context),
      ],
    );
  }
  
  Widget _buildListImage(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: SizedBox(
        width: 80,
        height: 80,
        child: _buildImage(context),
      ),
    );
  }
  
  Widget _buildImage(BuildContext context) {
    if (material.imageUrl?.isNotEmpty == true) {
      return Image.network(
        material.imageUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildPlaceholderImage(context);
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingImage(context);
        },
      );
    }
    return _buildPlaceholderImage(context);
  }
  
  Widget _buildPlaceholderImage(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.inventory_2_outlined,
        size: 32,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }
  
  Widget _buildLoadingImage(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
  
  Widget _buildCategoryBadge(BuildContext context) {
    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getCategoryColor(material.category),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          material.category,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    // 暂时不显示状态标签，因为Material实体中没有status字段
    return const SizedBox.shrink();
  }

  Widget _buildImageOverlay(BuildContext context) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.1),
            ],
            stops: const [0.6, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildSpecifications(BuildContext context, WidgetRef ref) {
    return _buildSpecificationInfo(context, ref);
  }

  /// 构建规格信息
  Widget _buildSpecificationInfo(BuildContext context, WidgetRef ref) {
    // 如果有传统的规格文本，优先显示
    if (material.specifications != null && material.specifications!.isNotEmpty) {
      final specs = _getFormattedSpecifications();
      if (specs.isNotEmpty) {
        return _buildTraditionalSpecifications(context, specs);
      }
    }

    // 如果有规格ID，显示结构化规格信息
    if (material.specificationId != null) {
      return _buildStructuredSpecification(context, ref);
    }

    // 如果有内嵌的规格对象，直接显示
    if (material.productSpecification != null) {
      return _buildSpecificationSummary(context, material.productSpecification!);
    }

    return const SizedBox.shrink();
  }

  /// 构建传统规格显示
  Widget _buildTraditionalSpecifications(BuildContext context, List<String> specs) {
    return Container(
      padding: EdgeInsets.all(context.responsiveValue(
        xs: 6,
        sm: 7,
        md: 8,
        lg: 9,
        xl: 10,
        defaultValue: 8,
      )),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(context.responsiveValue(
          xs: 6,
          sm: 7,
          md: 8,
          lg: 9,
          xl: 10,
          defaultValue: 8,
        )),
        border: Border.all(
          color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings_outlined,
                size: context.responsiveValue(
                  xs: 12,
                  sm: 13,
                  md: 14,
                  lg: 15,
                  xl: 16,
                  defaultValue: 14,
                ),
                color: Theme.of(context).colorScheme.tertiary,
              ),
              SizedBox(width: context.responsiveValue(
                xs: 4,
                sm: 5,
                md: 6,
                lg: 7,
                xl: 8,
                defaultValue: 6,
              )),
              Text(
                '技术规格',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.tertiary,
                  fontSize: context.responsiveValue(
                    xs: 10,
                    sm: 11,
                    md: 12,
                    lg: 13,
                    xl: 14,
                    defaultValue: 12,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: context.responsiveValue(
            xs: 4,
            sm: 5,
            md: 6,
            lg: 7,
            xl: 8,
            defaultValue: 6,
          )),
          Wrap(
            spacing: context.responsiveValue(
              xs: 4,
              sm: 5,
              md: 6,
              lg: 7,
              xl: 8,
              defaultValue: 6,
            ),
            runSpacing: context.responsiveValue(
              xs: 3,
              sm: 4,
              md: 5,
              lg: 6,
              xl: 7,
              defaultValue: 5,
            ),
            children: specs.map((spec) => Container(
              padding: EdgeInsets.symmetric(
                horizontal: context.responsiveValue(
                  xs: 4,
                  sm: 5,
                  md: 6,
                  lg: 7,
                  xl: 8,
                  defaultValue: 6,
                ),
                vertical: context.responsiveValue(
                  xs: 2,
                  sm: 2,
                  md: 3,
                  lg: 3,
                  xl: 4,
                  defaultValue: 3,
                ),
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(context.responsiveValue(
                  xs: 4,
                  sm: 5,
                  md: 6,
                  lg: 7,
                  xl: 8,
                  defaultValue: 6,
                )),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                spec,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: context.responsiveValue(
                    xs: 8,
                    sm: 9,
                    md: 10,
                    lg: 11,
                    xl: 12,
                    defaultValue: 10,
                  ),
                  fontWeight: FontWeight.w500,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }
  
  Widget _buildQuickActions(BuildContext context) {
    return Positioned(
      top: 8,
      right: 8,
      child: Column(
        children: [
          _buildActionButton(
            context,
            icon: Icons.favorite_border, // 暂时使用固定图标，后续可以通过状态管理来控制
            onPressed: onFavorite,
            tooltip: '收藏',
            isActive: false, // 暂时设为false，后续可以通过状态管理来控制
          ),
          const SizedBox(height: 4),
          _buildActionButton(
            context,
            icon: Icons.add_shopping_cart_outlined,
            onPressed: onAddToBom,
            tooltip: '添加到BOM',
          ),
          const SizedBox(height: 4),
          _buildActionButton(
            context,
            icon: Icons.share_outlined,
            onPressed: onShare,
            tooltip: '分享',
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
    bool isActive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.9)
            : Colors.white.withValues(alpha: 0.9),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          icon,
          size: 18,
          color: isActive
              ? Colors.white
              : Theme.of(context).colorScheme.onSurface,
        ),
        onPressed: onPressed,
        tooltip: tooltip,
        constraints: const BoxConstraints(
          minWidth: 32,
          minHeight: 32,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
  
  Widget _buildTitle(BuildContext context) {
    return Text(
      material.name,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.bold,
        fontSize: context.responsiveValue(
          xs: 12,
          sm: 13,
          md: 14,
          lg: 15,
          xl: 16,
          defaultValue: 14,
        ),
      ),
      maxLines: context.isMobile ? 1 : 2,
      overflow: TextOverflow.ellipsis,
    );
  }
  
  Widget _buildSubtitle(BuildContext context) {
    final subtitle = material.brand?.isNotEmpty == true
        ? '${material.brand} • ${material.model ?? '未知型号'}'
        : material.model ?? '未知品牌';

    return Text(
      subtitle,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        fontSize: context.responsiveValue(
          xs: 10,
          sm: 11,
          md: 12,
          lg: 13,
          xl: 14,
          defaultValue: 12,
        ),
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建材料描述
  Widget _buildDescription(BuildContext context) {
    if (material.description?.isEmpty ?? true) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(context.responsiveValue(
        xs: 6,
        sm: 7,
        md: 8,
        lg: 9,
        xl: 10,
        defaultValue: 8,
      )),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(context.responsiveValue(
          xs: 6,
          sm: 7,
          md: 8,
          lg: 9,
          xl: 10,
          defaultValue: 8,
        )),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description_outlined,
                size: context.responsiveValue(
                  xs: 12,
                  sm: 13,
                  md: 14,
                  lg: 15,
                  xl: 16,
                  defaultValue: 14,
                ),
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: context.responsiveValue(
                xs: 4,
                sm: 5,
                md: 6,
                lg: 7,
                xl: 8,
                defaultValue: 6,
              )),
              Text(
                '产品描述',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: context.responsiveValue(
                    xs: 10,
                    sm: 11,
                    md: 12,
                    lg: 13,
                    xl: 14,
                    defaultValue: 12,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: context.responsiveValue(
            xs: 3,
            sm: 4,
            md: 5,
            lg: 6,
            xl: 7,
            defaultValue: 5,
          )),
          Text(
            material.description!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: context.responsiveValue(
                xs: 9,
                sm: 10,
                md: 11,
                lg: 12,
                xl: 13,
                defaultValue: 11,
              ),
              height: 1.3,
            ),
            maxLines: context.responsiveValue(
              xs: 2,
              sm: 2,
              md: 3,
              lg: 3,
              xl: 4,
              defaultValue: 3,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建供应商信息
  Widget _buildSupplierInfo(BuildContext context) {
    // 模拟供应商信息，实际项目中应该从数据库获取
    final supplierName = _getSupplierName();
    final supplierRating = _getSupplierRating();

    return Container(
      padding: EdgeInsets.all(context.responsiveValue(
        xs: 6,
        sm: 7,
        md: 8,
        lg: 9,
        xl: 10,
        defaultValue: 8,
      )),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(context.responsiveValue(
          xs: 6,
          sm: 7,
          md: 8,
          lg: 9,
          xl: 10,
          defaultValue: 8,
        )),
      ),
      child: Row(
        children: [
          Icon(
            Icons.store_outlined,
            size: context.responsiveValue(
              xs: 12,
              sm: 13,
              md: 14,
              lg: 15,
              xl: 16,
              defaultValue: 14,
            ),
            color: Theme.of(context).colorScheme.secondary,
          ),
          SizedBox(width: context.responsiveValue(
            xs: 4,
            sm: 5,
            md: 6,
            lg: 7,
            xl: 8,
            defaultValue: 6,
          )),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  supplierName,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.secondary,
                    fontSize: context.responsiveValue(
                      xs: 9,
                      sm: 10,
                      md: 11,
                      lg: 12,
                      xl: 13,
                      defaultValue: 11,
                    ),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: context.responsiveValue(
                        xs: 10,
                        sm: 11,
                        md: 12,
                        lg: 13,
                        xl: 14,
                        defaultValue: 12,
                      ),
                      color: Colors.amber,
                    ),
                    SizedBox(width: 2),
                    Text(
                      supplierRating.toStringAsFixed(1),
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontSize: context.responsiveValue(
                          xs: 8,
                          sm: 9,
                          md: 10,
                          lg: 11,
                          xl: 12,
                          defaultValue: 10,
                        ),
                      ),
                    ),
                    SizedBox(width: 4),
                    Text(
                      '可信赖',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontSize: context.responsiveValue(
                          xs: 8,
                          sm: 9,
                          md: 10,
                          lg: 11,
                          xl: 12,
                          defaultValue: 10,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建评分和评论信息
  Widget _buildRatingAndReviews(BuildContext context) {
    final rating = _getMaterialRating();
    final reviewCount = _getReviewCount();

    return Row(
      children: [
        // 评分显示
        Row(
          children: List.generate(5, (index) {
            return Icon(
              index < rating.floor() ? Icons.star :
              (index < rating && rating % 1 >= 0.5) ? Icons.star_half : Icons.star_border,
              size: context.responsiveValue(
                xs: 12,
                sm: 13,
                md: 14,
                lg: 15,
                xl: 16,
                defaultValue: 14,
              ),
              color: Colors.amber,
            );
          }),
        ),
        SizedBox(width: context.responsiveValue(
          xs: 4,
          sm: 5,
          md: 6,
          lg: 7,
          xl: 8,
          defaultValue: 6,
        )),
        Text(
          rating.toStringAsFixed(1),
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: context.responsiveValue(
              xs: 10,
              sm: 11,
              md: 12,
              lg: 13,
              xl: 14,
              defaultValue: 12,
            ),
          ),
        ),
        SizedBox(width: context.responsiveValue(
          xs: 4,
          sm: 5,
          md: 6,
          lg: 7,
          xl: 8,
          defaultValue: 6,
        )),
        Text(
          '($reviewCount条评价)',
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: context.responsiveValue(
              xs: 9,
              sm: 10,
              md: 11,
              lg: 12,
              xl: 13,
              defaultValue: 11,
            ),
          ),
        ),
        const Spacer(),
        // 推荐标签
        if (rating >= 4.0)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: context.responsiveValue(
                xs: 4,
                sm: 5,
                md: 6,
                lg: 7,
                xl: 8,
                defaultValue: 6,
              ),
              vertical: context.responsiveValue(
                xs: 2,
                sm: 2,
                md: 3,
                lg: 3,
                xl: 4,
                defaultValue: 3,
              ),
            ),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(context.responsiveValue(
                xs: 4,
                sm: 5,
                md: 6,
                lg: 7,
                xl: 8,
                defaultValue: 6,
              )),
              border: Border.all(
                color: Colors.green.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              '推荐',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Colors.green.shade700,
                fontWeight: FontWeight.bold,
                fontSize: context.responsiveValue(
                  xs: 8,
                  sm: 9,
                  md: 10,
                  lg: 11,
                  xl: 12,
                  defaultValue: 10,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPriceAndActions(BuildContext context) {
    final originalPrice = _getOriginalPrice();
    final hasDiscount = originalPrice > material.price;
    final discountPercent = hasDiscount
        ? ((originalPrice - material.price) / originalPrice * 100).round()
        : 0;

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 当前价格
              Row(
                children: [
                  Text(
                    '¥${material.price.toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: context.responsiveValue(
                        xs: 16,
                        sm: 18,
                        md: 20,
                        lg: 22,
                        xl: 24,
                        defaultValue: 20,
                      ),
                    ),
                  ),
                  if (hasDiscount) ...[
                    SizedBox(width: context.responsiveValue(
                      xs: 4,
                      sm: 5,
                      md: 6,
                      lg: 7,
                      xl: 8,
                      defaultValue: 6,
                    )),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.responsiveValue(
                          xs: 4,
                          sm: 5,
                          md: 6,
                          lg: 7,
                          xl: 8,
                          defaultValue: 6,
                        ),
                        vertical: context.responsiveValue(
                          xs: 2,
                          sm: 2,
                          md: 3,
                          lg: 3,
                          xl: 4,
                          defaultValue: 3,
                        ),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(context.responsiveValue(
                          xs: 4,
                          sm: 5,
                          md: 6,
                          lg: 7,
                          xl: 8,
                          defaultValue: 6,
                        )),
                      ),
                      child: Text(
                        '-$discountPercent%',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: context.responsiveValue(
                            xs: 8,
                            sm: 9,
                            md: 10,
                            lg: 11,
                            xl: 12,
                            defaultValue: 10,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              // 原价和其他价格信息
              if (hasDiscount) ...[
                SizedBox(height: context.responsiveValue(
                  xs: 2,
                  sm: 3,
                  md: 4,
                  lg: 5,
                  xl: 6,
                  defaultValue: 4,
                )),
                Text(
                  '原价 ¥${originalPrice.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    decoration: TextDecoration.lineThrough,
                    fontSize: context.responsiveValue(
                      xs: 10,
                      sm: 11,
                      md: 12,
                      lg: 13,
                      xl: 14,
                      defaultValue: 12,
                    ),
                  ),
                ),
              ],

              SizedBox(height: context.responsiveValue(
                xs: 2,
                sm: 3,
                md: 4,
                lg: 5,
                xl: 6,
                defaultValue: 4,
              )),

              // 价格说明
              Row(
                children: [
                  Icon(
                    Icons.local_shipping_outlined,
                    size: context.responsiveValue(
                      xs: 10,
                      sm: 11,
                      md: 12,
                      lg: 13,
                      xl: 14,
                      defaultValue: 12,
                    ),
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: context.responsiveValue(
                    xs: 2,
                    sm: 3,
                    md: 4,
                    lg: 5,
                    xl: 6,
                    defaultValue: 4,
                  )),
                  Text(
                    material.price >= 500 ? '包邮' : '运费到付',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: material.price >= 500
                          ? Colors.green.shade600
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                      fontSize: context.responsiveValue(
                        xs: 9,
                        sm: 10,
                        md: 11,
                        lg: 12,
                        xl: 13,
                        defaultValue: 11,
                      ),
                      fontWeight: material.price >= 500 ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (showActions && !isGuestMode) _buildActionMenu(context),
      ],
    );
  }

  // 暂时移除评分功能，因为Material实体中没有rating字段
  // Widget _buildRating(BuildContext context) {
  //   // 评分功能待实现
  // }
  
  Widget _buildActionMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        size: 18,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
          case 'addToBom':
            onAddToBom?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'addToBom',
          child: Row(
            children: [
              Icon(Icons.add_shopping_cart),
              SizedBox(width: 8),
              Text('添加到BOM'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit),
              SizedBox(width: 8),
              Text('编辑'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8),
              Text('删除', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  // 辅助方法
  Color _getCategoryColor(String category) {
    switch (category) {
      case '电力系统':
        return Colors.orange;
      case '水系统':
        return Colors.blue;
      case '照明系统':
        return Colors.amber;
      case '储物系统':
        return Colors.brown;
      case '床铺系统':
        return Colors.purple;
      case '厨房系统':
        return Colors.red;
      case '卫浴系统':
        return Colors.cyan;
      case '通风系统':
        return Colors.green;
      case '安全系统':
        return Colors.deepOrange;
      case '娱乐系统':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  // 状态相关方法暂时移除，因为Material实体中没有status字段
  // Color _getStatusColor(String status) { ... }
  // String _getStatusText(String status) { ... }

  /// 获取格式化的规格信息
  List<String> _getFormattedSpecifications() {
    if (material.specifications?.isEmpty ?? true) {
      return [];
    }

    // 基于材料分类生成相关的规格信息
    final specs = <String>[];
    final category = material.category;
    final specText = material.specifications!;

    // 根据分类添加相关规格
    switch (category) {
      case '电力系统':
        specs.addAll([
          '${_extractNumber(specText, 'Ah', '100')}Ah',
          '${_extractNumber(specText, 'V', '12')}V',
          '${_extractNumber(specText, 'W', '1200')}W',
          'LiFePO4',
        ]);
        break;
      case '水系统':
        specs.addAll([
          '${_extractNumber(specText, 'L', '20')}L/min',
          '${_extractNumber(specText, 'bar', '3')}bar',
          '304不锈钢',
          '防冻设计',
        ]);
        break;
      case '照明系统':
        specs.addAll([
          '${_extractNumber(specText, 'W', '24')}W',
          '${_extractNumber(specText, 'lm', '2400')}lm',
          'LED',
          'IP65防水',
        ]);
        break;
      case '储物系统':
        specs.addAll([
          '${_extractNumber(specText, 'kg', '50')}kg承重',
          '铝合金',
          '防滑底面',
          '快装设计',
        ]);
        break;
      default:
        // 通用规格信息
        specs.addAll([
          specText.length > 10 ? specText.substring(0, 10) + '...' : specText,
          '优质材料',
          '质保2年',
        ]);
    }

    return specs.take(4).toList(); // 最多显示4个规格
  }

  /// 从文本中提取数字，如果没有找到则使用默认值
  String _extractNumber(String text, String unit, String defaultValue) {
    final regex = RegExp(r'(\d+)' + unit);
    final match = regex.firstMatch(text);
    return match?.group(1) ?? defaultValue;
  }

  /// 获取供应商名称（模拟数据）
  String _getSupplierName() {
    final suppliers = [
      '改装专家',
      '房车配件城',
      '户外装备店',
      '专业改装厂',
      '优质供应商',
      '信赖商家',
    ];

    // 基于材料ID生成一致的供应商
    final index = material.id.hashCode % suppliers.length;
    return suppliers[index.abs()];
  }

  /// 获取供应商评分（模拟数据）
  double _getSupplierRating() {
    // 基于材料ID生成一致的评分
    final hash = material.id.hashCode;
    return 3.5 + (hash % 15) / 10.0; // 3.5 - 5.0 之间的评分
  }

  /// 获取材料评分（模拟数据）
  double _getMaterialRating() {
    // 基于材料名称和价格生成一致的评分
    final hash = (material.name + material.price.toString()).hashCode;
    return 3.0 + (hash % 20) / 10.0; // 3.0 - 5.0 之间的评分
  }

  /// 获取评论数量（模拟数据）
  int _getReviewCount() {
    // 基于材料价格生成评论数量
    final baseCount = (material.price / 100).round();
    final hash = material.id.hashCode;
    return (baseCount + (hash % 50)).abs(); // 生成合理的评论数量
  }

  /// 获取原价（模拟数据）
  double _getOriginalPrice() {
    // 基于材料ID生成一致的原价
    final hash = material.id.hashCode;
    final discountFactor = 1.1 + (hash % 30) / 100.0; // 1.1 - 1.4 倍的折扣
    return material.price * discountFactor;
  }

  /// 构建结构化规格信息（异步加载）
  Widget _buildStructuredSpecification(BuildContext context, WidgetRef ref) {
    final specificationAsync = ref.watch(specificationByMaterialIdProvider(material.id));

    return specificationAsync.when(
      data: (specification) {
        if (specification == null) return const SizedBox.shrink();
        return _buildSpecificationSummary(context, specification);
      },
      loading: () => Container(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(width: 6),
            Text(
              '加载规格中...',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  /// 构建规格摘要信息
  Widget _buildSpecificationSummary(BuildContext context, ProductSpecification specification) {
    final keySpecs = _extractKeySpecifications(specification);

    if (keySpecs.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getCategoryIcon(specification.category),
                size: 14,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Text(
                '规格参数',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: keySpecs.entries.map((spec) => _buildSpecChip(context, spec)).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建规格标签
  Widget _buildSpecChip(BuildContext context, MapEntry<String, String> spec) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        '${spec.key}: ${spec.value}',
        style: const TextStyle(
          fontSize: 9,
          fontWeight: FontWeight.w500,
          color: Colors.blue,
        ),
      ),
    );
  }

  /// 提取关键规格信息
  Map<String, String> _extractKeySpecifications(ProductSpecification specification) {
    final keySpecs = <String, String>{};

    // 根据分类提取不同的关键规格
    switch (specification.category.toUpperCase()) {
      case 'ELECTRICAL':
        final electrical = specification.technicalParams.electrical;
        if (electrical != null) {
          if (electrical.ratedVoltage != null) {
            keySpecs['电压'] = '${electrical.ratedVoltage}V';
          }
          if (electrical.capacity != null) {
            keySpecs['容量'] = '${electrical.capacity}${electrical.capacityUnit ?? 'Ah'}';
          }
          if (electrical.ratedPower != null) {
            keySpecs['功率'] = '${electrical.ratedPower}W';
          }
        }
        break;

      case 'PLUMBING':
        final fluid = specification.technicalParams.fluid;
        if (fluid != null) {
          if (fluid.flowRate != null) {
            keySpecs['流量'] = '${fluid.flowRate}L/min';
          }
          if (fluid.head != null) {
            keySpecs['扬程'] = '${fluid.head}m';
          }
        }
        break;

      case 'STORAGE':
        final mechanical = specification.technicalParams.mechanical;
        if (mechanical != null) {
          if (mechanical.maxLoad != null) {
            keySpecs['承重'] = '${mechanical.maxLoad}kg';
          }
        }
        break;

      default:
        // 对于其他分类，显示基础物理属性
        final dimensions = specification.physicalProps.dimensions;
        keySpecs['尺寸'] = '${dimensions.length}×${dimensions.width}×${dimensions.height}${dimensions.unit}';
        keySpecs['重量'] = '${specification.physicalProps.weight}kg';
        break;
    }

    // 限制显示的规格数量
    if (keySpecs.length > 3) {
      final entries = keySpecs.entries.take(3).toList();
      return Map.fromEntries(entries);
    }

    return keySpecs;
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String category) {
    switch (category.toUpperCase()) {
      case 'ELECTRICAL':
        return Icons.electrical_services;
      case 'PLUMBING':
        return Icons.water_drop;
      case 'STORAGE':
        return Icons.inventory_2;
      case 'BEDDING':
        return Icons.bed;
      case 'KITCHEN':
        return Icons.kitchen;
      case 'BATHROOM':
        return Icons.bathroom;
      case 'EXTERIOR':
        return Icons.directions_car;
      case 'CHASSIS':
        return Icons.build;
      default:
        return Icons.category;
    }
  }
}
