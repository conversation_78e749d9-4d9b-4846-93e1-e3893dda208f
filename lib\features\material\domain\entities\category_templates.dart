import 'category_spec_template.dart';

/// VanHub房车改装材料分类规格模板集合
/// 
/// 包含8大专业分类的详细规格模板定义
class VanHubCategoryTemplates {
  
  /// 1. 电气设备模板
  static CategorySpecTemplate get electrical => CategorySpecTemplate(
    id: 'electrical',
    categoryName: '电气设备',
    categoryCode: 'ELECTRICAL',
    description: '电池、逆变器、充电器、配电箱等电气设备的专业规格模板',
    requiredFields: [
      // 基础电气参数
      SpecField(
        id: 'rated_voltage',
        name: 'ratedVoltage',
        label: '额定电压',
        type: SpecFieldType.voltage,
        unit: 'V',
        required: true,
        order: 1,
        groupId: 'electrical_basic',
        showInList: true,
        searchable: true,
        filterable: true,
        description: '设备的标准工作电压',
      ),
      SpecField(
        id: 'rated_power',
        name: 'ratedPower',
        label: '额定功率',
        type: SpecFieldType.power,
        unit: 'W',
        required: true,
        order: 2,
        groupId: 'electrical_basic',
        showInList: true,
        searchable: true,
        filterable: true,
        description: '设备的标准功率输出',
      ),
    ],
    optionalFields: [
      // 电池专用参数
      SpecField(
        id: 'battery_capacity',
        name: 'batteryCapacity',
        label: '电池容量',
        type: SpecFieldType.capacity,
        unit: 'Ah',
        order: 3,
        groupId: 'battery_specs',
        showInList: true,
        filterable: true,
        description: '电池的安时容量',
      ),
      SpecField(
        id: 'battery_type',
        name: 'batteryType',
        label: '电池类型',
        type: SpecFieldType.enumSingle,
        order: 4,
        groupId: 'battery_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'lithium_iron_phosphate', label: '磷酸铁锂'),
          SpecFieldOption(value: 'lithium_ternary', label: '三元锂'),
          SpecFieldOption(value: 'lead_acid', label: '铅酸'),
          SpecFieldOption(value: 'gel', label: '胶体'),
          SpecFieldOption(value: 'agm', label: 'AGM'),
        ],
      ),
      SpecField(
        id: 'cycle_life',
        name: 'cycleLife',
        label: '循环寿命',
        type: SpecFieldType.number,
        unit: '次',
        order: 5,
        groupId: 'battery_specs',
        description: '电池的充放电循环次数',
      ),
      // 逆变器专用参数
      SpecField(
        id: 'inverter_type',
        name: 'inverterType',
        label: '逆变器类型',
        type: SpecFieldType.enumSingle,
        order: 6,
        groupId: 'inverter_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'pure_sine_wave', label: '纯正弦波'),
          SpecFieldOption(value: 'modified_sine_wave', label: '修正正弦波'),
          SpecFieldOption(value: 'square_wave', label: '方波'),
        ],
      ),
      SpecField(
        id: 'efficiency',
        name: 'efficiency',
        label: '转换效率',
        type: SpecFieldType.decimal,
        unit: '%',
        order: 7,
        groupId: 'inverter_specs',
        numericRange: NumericRange(min: 0, max: 100),
        description: '逆变器的能量转换效率',
      ),
      // 太阳能板专用参数
      SpecField(
        id: 'solar_cell_type',
        name: 'solarCellType',
        label: '电池片类型',
        type: SpecFieldType.enumSingle,
        order: 8,
        groupId: 'solar_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'monocrystalline', label: '单晶硅'),
          SpecFieldOption(value: 'polycrystalline', label: '多晶硅'),
          SpecFieldOption(value: 'thin_film', label: '薄膜'),
        ],
      ),
      SpecField(
        id: 'peak_power',
        name: 'peakPower',
        label: '峰值功率',
        type: SpecFieldType.power,
        unit: 'Wp',
        order: 9,
        groupId: 'solar_specs',
        showInList: true,
        filterable: true,
        description: '标准测试条件下的最大功率输出',
      ),
      // 安全认证
      SpecField(
        id: 'safety_certification',
        name: 'safetyCertification',
        label: '安全认证',
        type: SpecFieldType.enumMultiple,
        order: 10,
        groupId: 'safety',
        filterable: true,
        options: [
          SpecFieldOption(value: 'ce', label: 'CE认证'),
          SpecFieldOption(value: 'ul', label: 'UL认证'),
          SpecFieldOption(value: 'ccc', label: '3C认证'),
          SpecFieldOption(value: 'rohs', label: 'RoHS认证'),
          SpecFieldOption(value: 'fcc', label: 'FCC认证'),
        ],
      ),
      SpecField(
        id: 'protection_rating',
        name: 'protectionRating',
        label: '防护等级',
        type: SpecFieldType.enumSingle,
        order: 11,
        groupId: 'safety',
        filterable: true,
        options: [
          SpecFieldOption(value: 'ip65', label: 'IP65'),
          SpecFieldOption(value: 'ip67', label: 'IP67'),
          SpecFieldOption(value: 'ip68', label: 'IP68'),
          SpecFieldOption(value: 'ip54', label: 'IP54'),
        ],
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'electrical_basic',
        name: 'electricalBasic',
        label: '基础电气参数',
        description: '设备的基本电气特性',
        order: 1,
        icon: 'electrical_services',
      ),
      SpecFieldGroup(
        id: 'battery_specs',
        name: 'batterySpecs',
        label: '电池规格',
        description: '电池专用技术参数',
        order: 2,
        icon: 'battery_full',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'inverter_specs',
        name: 'inverterSpecs',
        label: '逆变器规格',
        description: '逆变器专用技术参数',
        order: 3,
        icon: 'power',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'solar_specs',
        name: 'solarSpecs',
        label: '太阳能板规格',
        description: '太阳能板专用技术参数',
        order: 4,
        icon: 'wb_sunny',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'safety',
        name: 'safety',
        label: '安全认证',
        description: '产品安全认证和防护等级',
        order: 5,
        icon: 'security',
      ),
    ],
    validationRules: [
      ValidationRule(
        id: 'voltage_power_consistency',
        type: ValidationRuleType.custom,
        targetField: 'rated_power',
        parameters: {
          'dependency_field': 'rated_voltage',
          'validation_function': 'validateVoltagePowerConsistency',
        },
        errorMessage: '功率与电压参数不匹配',
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 2. 水路设备模板
  static CategorySpecTemplate get plumbing => CategorySpecTemplate(
    id: 'plumbing',
    categoryName: '水路设备',
    categoryCode: 'PLUMBING',
    description: '水泵、水箱、净水器、管道等水路系统设备的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'flow_rate',
        name: 'flowRate',
        label: '流量',
        type: SpecFieldType.flowRate,
        unit: 'L/min',
        required: true,
        order: 1,
        groupId: 'fluid_basic',
        showInList: true,
        searchable: true,
        filterable: true,
        description: '设备的标准流量',
      ),
      SpecField(
        id: 'working_pressure',
        name: 'workingPressure',
        label: '工作压力',
        type: SpecFieldType.pressure,
        unit: 'MPa',
        required: true,
        order: 2,
        groupId: 'fluid_basic',
        showInList: true,
        filterable: true,
        description: '设备的正常工作压力',
      ),
    ],
    optionalFields: [
      // 水泵专用参数
      SpecField(
        id: 'pump_type',
        name: 'pumpType',
        label: '水泵类型',
        type: SpecFieldType.enumSingle,
        order: 3,
        groupId: 'pump_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'centrifugal', label: '离心泵'),
          SpecFieldOption(value: 'diaphragm', label: '隔膜泵'),
          SpecFieldOption(value: 'gear', label: '齿轮泵'),
          SpecFieldOption(value: 'submersible', label: '潜水泵'),
        ],
      ),
      SpecField(
        id: 'head',
        name: 'head',
        label: '扬程',
        type: SpecFieldType.decimal,
        unit: 'm',
        order: 4,
        groupId: 'pump_specs',
        description: '水泵的最大扬程',
      ),
      SpecField(
        id: 'suction_head',
        name: 'suctionHead',
        label: '吸程',
        type: SpecFieldType.decimal,
        unit: 'm',
        order: 5,
        groupId: 'pump_specs',
        description: '水泵的最大吸程',
      ),
      // 水箱专用参数
      SpecField(
        id: 'tank_capacity',
        name: 'tankCapacity',
        label: '容量',
        type: SpecFieldType.decimal,
        unit: 'L',
        order: 6,
        groupId: 'tank_specs',
        showInList: true,
        filterable: true,
        description: '水箱的有效容量',
      ),
      SpecField(
        id: 'tank_material',
        name: 'tankMaterial',
        label: '水箱材质',
        type: SpecFieldType.enumSingle,
        order: 7,
        groupId: 'tank_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'food_grade_plastic', label: '食品级塑料'),
          SpecFieldOption(value: 'stainless_steel', label: '不锈钢'),
          SpecFieldOption(value: 'aluminum', label: '铝合金'),
          SpecFieldOption(value: 'fiberglass', label: '玻璃钢'),
        ],
      ),
      // 净水器专用参数
      SpecField(
        id: 'filter_type',
        name: 'filterType',
        label: '过滤类型',
        type: SpecFieldType.enumMultiple,
        order: 8,
        groupId: 'filter_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'sediment', label: '沉淀过滤'),
          SpecFieldOption(value: 'carbon', label: '活性炭'),
          SpecFieldOption(value: 'reverse_osmosis', label: '反渗透'),
          SpecFieldOption(value: 'uv', label: '紫外线杀菌'),
          SpecFieldOption(value: 'ultrafiltration', label: '超滤'),
        ],
      ),
      SpecField(
        id: 'filter_life',
        name: 'filterLife',
        label: '滤芯寿命',
        type: SpecFieldType.number,
        unit: '月',
        order: 9,
        groupId: 'filter_specs',
        description: '滤芯的使用寿命',
      ),
      // 管道配件参数
      SpecField(
        id: 'pipe_diameter',
        name: 'pipeDiameter',
        label: '管径',
        type: SpecFieldType.decimal,
        unit: 'mm',
        order: 10,
        groupId: 'pipe_specs',
        filterable: true,
        description: '管道的内径尺寸',
      ),
      SpecField(
        id: 'connection_type',
        name: 'connectionType',
        label: '连接方式',
        type: SpecFieldType.enumSingle,
        order: 11,
        groupId: 'pipe_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'threaded', label: '螺纹连接'),
          SpecFieldOption(value: 'quick_connect', label: '快插连接'),
          SpecFieldOption(value: 'compression', label: '卡套连接'),
          SpecFieldOption(value: 'welded', label: '焊接连接'),
        ],
      ),
      // 材质和认证
      SpecField(
        id: 'material',
        name: 'material',
        label: '主要材质',
        type: SpecFieldType.enumSingle,
        order: 12,
        groupId: 'material_cert',
        filterable: true,
        options: [
          SpecFieldOption(value: 'stainless_steel_304', label: '304不锈钢'),
          SpecFieldOption(value: 'stainless_steel_316', label: '316不锈钢'),
          SpecFieldOption(value: 'pvc', label: 'PVC'),
          SpecFieldOption(value: 'pex', label: 'PEX'),
          SpecFieldOption(value: 'brass', label: '黄铜'),
          SpecFieldOption(value: 'copper', label: '紫铜'),
        ],
      ),
      SpecField(
        id: 'food_grade',
        name: 'foodGrade',
        label: '食品级认证',
        type: SpecFieldType.boolean,
        order: 13,
        groupId: 'material_cert',
        filterable: true,
        description: '是否通过食品级安全认证',
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'fluid_basic',
        name: 'fluidBasic',
        label: '基础流体参数',
        description: '设备的基本流体特性',
        order: 1,
        icon: 'water_drop',
      ),
      SpecFieldGroup(
        id: 'pump_specs',
        name: 'pumpSpecs',
        label: '水泵规格',
        description: '水泵专用技术参数',
        order: 2,
        icon: 'water_pump',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'tank_specs',
        name: 'tankSpecs',
        label: '水箱规格',
        description: '水箱专用技术参数',
        order: 3,
        icon: 'water_tank',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'filter_specs',
        name: 'filterSpecs',
        label: '净水器规格',
        description: '净水设备专用技术参数',
        order: 4,
        icon: 'filter_alt',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'pipe_specs',
        name: 'pipeSpecs',
        label: '管道规格',
        description: '管道配件专用技术参数',
        order: 5,
        icon: 'plumbing',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'material_cert',
        name: 'materialCert',
        label: '材质认证',
        description: '产品材质和安全认证',
        order: 6,
        icon: 'verified',
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 3. 储物系统模板
  static CategorySpecTemplate get storage => CategorySpecTemplate(
    id: 'storage',
    categoryName: '储物系统',
    categoryCode: 'STORAGE',
    description: '储物箱、收纳架、抽屉、挂钩等储物设备的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'storage_capacity',
        name: 'storageCapacity',
        label: '储物容量',
        type: SpecFieldType.decimal,
        unit: 'L',
        required: true,
        order: 1,
        groupId: 'capacity_basic',
        showInList: true,
        searchable: true,
        filterable: true,
        description: '储物空间的有效容量',
      ),
      SpecField(
        id: 'max_load',
        name: 'maxLoad',
        label: '最大承重',
        type: SpecFieldType.weight,
        unit: 'kg',
        required: true,
        order: 2,
        groupId: 'capacity_basic',
        showInList: true,
        filterable: true,
        description: '储物设备的最大承重能力',
      ),
    ],
    optionalFields: [
      SpecField(
        id: 'storage_type',
        name: 'storageType',
        label: '储物类型',
        type: SpecFieldType.enumSingle,
        order: 3,
        groupId: 'storage_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'cabinet', label: '储物柜'),
          SpecFieldOption(value: 'drawer', label: '抽屉'),
          SpecFieldOption(value: 'shelf', label: '置物架'),
          SpecFieldOption(value: 'box', label: '储物箱'),
          SpecFieldOption(value: 'hook', label: '挂钩'),
          SpecFieldOption(value: 'net', label: '储物网'),
        ],
      ),
      SpecField(
        id: 'opening_method',
        name: 'openingMethod',
        label: '开启方式',
        type: SpecFieldType.enumSingle,
        order: 4,
        groupId: 'storage_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'hinged_door', label: '铰链门'),
          SpecFieldOption(value: 'sliding_door', label: '推拉门'),
          SpecFieldOption(value: 'drawer_slide', label: '抽屉滑轨'),
          SpecFieldOption(value: 'flip_top', label: '翻盖式'),
          SpecFieldOption(value: 'removable', label: '可拆卸'),
        ],
      ),
      SpecField(
        id: 'lock_type',
        name: 'lockType',
        label: '锁具类型',
        type: SpecFieldType.enumSingle,
        order: 5,
        groupId: 'security_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'key_lock', label: '钥匙锁'),
          SpecFieldOption(value: 'combination_lock', label: '密码锁'),
          SpecFieldOption(value: 'push_button', label: '按压锁'),
          SpecFieldOption(value: 'magnetic_lock', label: '磁吸锁'),
          SpecFieldOption(value: 'no_lock', label: '无锁'),
        ],
      ),
      SpecField(
        id: 'material_frame',
        name: 'materialFrame',
        label: '框架材质',
        type: SpecFieldType.enumSingle,
        order: 6,
        groupId: 'material_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'aluminum_alloy', label: '铝合金'),
          SpecFieldOption(value: 'steel', label: '钢材'),
          SpecFieldOption(value: 'stainless_steel', label: '不锈钢'),
          SpecFieldOption(value: 'wood', label: '木材'),
          SpecFieldOption(value: 'plastic', label: '塑料'),
        ],
      ),
      SpecField(
        id: 'waterproof_rating',
        name: 'waterproofRating',
        label: '防水等级',
        type: SpecFieldType.enumSingle,
        order: 7,
        groupId: 'protection_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'ip65', label: 'IP65'),
          SpecFieldOption(value: 'ip67', label: 'IP67'),
          SpecFieldOption(value: 'waterproof', label: '防水'),
          SpecFieldOption(value: 'water_resistant', label: '防潮'),
          SpecFieldOption(value: 'not_waterproof', label: '不防水'),
        ],
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'capacity_basic',
        name: 'capacityBasic',
        label: '基础容量参数',
        description: '储物设备的基本容量和承重',
        order: 1,
        icon: 'inventory_2',
      ),
      SpecFieldGroup(
        id: 'storage_specs',
        name: 'storageSpecs',
        label: '储物规格',
        description: '储物设备的结构和功能特性',
        order: 2,
        icon: 'storage',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'security_specs',
        name: 'securitySpecs',
        label: '安全规格',
        description: '储物设备的安全和锁具配置',
        order: 3,
        icon: 'lock',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'material_specs',
        name: 'materialSpecs',
        label: '材质规格',
        description: '储物设备的材质和工艺',
        order: 4,
        icon: 'construction',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'protection_specs',
        name: 'protectionSpecs',
        label: '防护规格',
        description: '储物设备的防护等级',
        order: 5,
        icon: 'shield',
        collapsible: true,
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 4. 床铺系统模板
  static CategorySpecTemplate get bedding => CategorySpecTemplate(
    id: 'bedding',
    categoryName: '床铺系统',
    categoryCode: 'BEDDING',
    description: '床垫、床架、寝具等床铺系统设备的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'bed_size',
        name: 'bedSize',
        label: '床铺尺寸',
        type: SpecFieldType.enumSingle,
        required: true,
        order: 1,
        groupId: 'size_basic',
        showInList: true,
        filterable: true,
        options: [
          SpecFieldOption(value: 'single', label: '单人床 (90x190cm)'),
          SpecFieldOption(value: 'double', label: '双人床 (135x190cm)'),
          SpecFieldOption(value: 'queen', label: '大床 (150x200cm)'),
          SpecFieldOption(value: 'king', label: '特大床 (180x200cm)'),
          SpecFieldOption(value: 'custom', label: '定制尺寸'),
        ],
      ),
    ],
    optionalFields: [
      SpecField(
        id: 'mattress_type',
        name: 'mattressType',
        label: '床垫类型',
        type: SpecFieldType.enumSingle,
        order: 2,
        groupId: 'mattress_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'memory_foam', label: '记忆棉'),
          SpecFieldOption(value: 'latex', label: '乳胶'),
          SpecFieldOption(value: 'spring', label: '弹簧'),
          SpecFieldOption(value: 'hybrid', label: '混合型'),
          SpecFieldOption(value: 'air', label: '充气'),
        ],
      ),
      SpecField(
        id: 'mattress_thickness',
        name: 'mattressThickness',
        label: '床垫厚度',
        type: SpecFieldType.decimal,
        unit: 'cm',
        order: 3,
        groupId: 'mattress_specs',
        filterable: true,
        description: '床垫的厚度尺寸',
      ),
      SpecField(
        id: 'firmness_level',
        name: 'firmnessLevel',
        label: '硬度等级',
        type: SpecFieldType.enumSingle,
        order: 4,
        groupId: 'comfort_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'soft', label: '软'),
          SpecFieldOption(value: 'medium_soft', label: '偏软'),
          SpecFieldOption(value: 'medium', label: '中等'),
          SpecFieldOption(value: 'medium_firm', label: '偏硬'),
          SpecFieldOption(value: 'firm', label: '硬'),
        ],
      ),
      SpecField(
        id: 'bed_frame_type',
        name: 'bedFrameType',
        label: '床架类型',
        type: SpecFieldType.enumSingle,
        order: 5,
        groupId: 'frame_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'fixed', label: '固定床'),
          SpecFieldOption(value: 'folding', label: '折叠床'),
          SpecFieldOption(value: 'murphy', label: '墨菲床'),
          SpecFieldOption(value: 'bunk', label: '上下铺'),
          SpecFieldOption(value: 'sofa_bed', label: '沙发床'),
        ],
      ),
      SpecField(
        id: 'frame_material',
        name: 'frameMaterial',
        label: '床架材质',
        type: SpecFieldType.enumSingle,
        order: 6,
        groupId: 'frame_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'aluminum', label: '铝合金'),
          SpecFieldOption(value: 'steel', label: '钢材'),
          SpecFieldOption(value: 'wood', label: '木材'),
          SpecFieldOption(value: 'composite', label: '复合材料'),
        ],
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'size_basic',
        name: 'sizeBasic',
        label: '基础尺寸',
        description: '床铺的基本尺寸规格',
        order: 1,
        icon: 'bed',
      ),
      SpecFieldGroup(
        id: 'mattress_specs',
        name: 'mattressSpecs',
        label: '床垫规格',
        description: '床垫的类型和特性',
        order: 2,
        icon: 'airline_seat_flat',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'comfort_specs',
        name: 'comfortSpecs',
        label: '舒适度规格',
        description: '床铺的舒适度和支撑性',
        order: 3,
        icon: 'hotel',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'frame_specs',
        name: 'frameSpecs',
        label: '床架规格',
        description: '床架的结构和材质',
        order: 4,
        icon: 'construction',
        collapsible: true,
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 5. 厨房系统模板
  static CategorySpecTemplate get kitchen => CategorySpecTemplate(
    id: 'kitchen',
    categoryName: '厨房系统',
    categoryCode: 'KITCHEN',
    description: '炉灶、冰箱、水槽、橱柜等厨房设备的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'appliance_type',
        name: 'applianceType',
        label: '设备类型',
        type: SpecFieldType.enumSingle,
        required: true,
        order: 1,
        groupId: 'basic_info',
        showInList: true,
        filterable: true,
        options: [
          SpecFieldOption(value: 'stove', label: '炉灶'),
          SpecFieldOption(value: 'refrigerator', label: '冰箱'),
          SpecFieldOption(value: 'sink', label: '水槽'),
          SpecFieldOption(value: 'cabinet', label: '橱柜'),
          SpecFieldOption(value: 'microwave', label: '微波炉'),
          SpecFieldOption(value: 'oven', label: '烤箱'),
        ],
      ),
    ],
    optionalFields: [
      // 炉灶专用参数
      SpecField(
        id: 'fuel_type',
        name: 'fuelType',
        label: '燃料类型',
        type: SpecFieldType.enumSingle,
        order: 2,
        groupId: 'stove_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'lpg', label: '液化气'),
          SpecFieldOption(value: 'natural_gas', label: '天然气'),
          SpecFieldOption(value: 'electric', label: '电力'),
          SpecFieldOption(value: 'induction', label: '电磁'),
          SpecFieldOption(value: 'alcohol', label: '酒精'),
        ],
      ),
      SpecField(
        id: 'burner_count',
        name: 'burnerCount',
        label: '炉头数量',
        type: SpecFieldType.number,
        unit: '个',
        order: 3,
        groupId: 'stove_specs',
        filterable: true,
        numericRange: NumericRange(min: 1, max: 6),
      ),
      SpecField(
        id: 'heat_output',
        name: 'heatOutput',
        label: '热功率',
        type: SpecFieldType.power,
        unit: 'kW',
        order: 4,
        groupId: 'stove_specs',
        description: '炉灶的总热功率输出',
      ),
      // 冰箱专用参数
      SpecField(
        id: 'cooling_type',
        name: 'coolingType',
        label: '制冷方式',
        type: SpecFieldType.enumSingle,
        order: 5,
        groupId: 'fridge_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'compressor', label: '压缩机'),
          SpecFieldOption(value: 'absorption', label: '吸收式'),
          SpecFieldOption(value: 'thermoelectric', label: '半导体'),
        ],
      ),
      SpecField(
        id: 'fridge_capacity',
        name: 'fridgeCapacity',
        label: '冰箱容量',
        type: SpecFieldType.decimal,
        unit: 'L',
        order: 6,
        groupId: 'fridge_specs',
        showInList: true,
        filterable: true,
        description: '冰箱的有效容量',
      ),
      SpecField(
        id: 'energy_consumption',
        name: 'energyConsumption',
        label: '能耗',
        type: SpecFieldType.decimal,
        unit: 'W',
        order: 7,
        groupId: 'fridge_specs',
        description: '冰箱的平均功耗',
      ),
      // 水槽专用参数
      SpecField(
        id: 'sink_material',
        name: 'sinkMaterial',
        label: '水槽材质',
        type: SpecFieldType.enumSingle,
        order: 8,
        groupId: 'sink_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'stainless_steel', label: '不锈钢'),
          SpecFieldOption(value: 'granite', label: '花岗岩'),
          SpecFieldOption(value: 'ceramic', label: '陶瓷'),
          SpecFieldOption(value: 'composite', label: '复合材料'),
        ],
      ),
      SpecField(
        id: 'basin_count',
        name: 'basinCount',
        label: '水槽数量',
        type: SpecFieldType.enumSingle,
        order: 9,
        groupId: 'sink_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'single', label: '单槽'),
          SpecFieldOption(value: 'double', label: '双槽'),
          SpecFieldOption(value: 'triple', label: '三槽'),
        ],
      ),
      // 橱柜专用参数
      SpecField(
        id: 'cabinet_material',
        name: 'cabinetMaterial',
        label: '柜体材质',
        type: SpecFieldType.enumSingle,
        order: 10,
        groupId: 'cabinet_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'plywood', label: '多层板'),
          SpecFieldOption(value: 'particle_board', label: '刨花板'),
          SpecFieldOption(value: 'mdf', label: '密度板'),
          SpecFieldOption(value: 'solid_wood', label: '实木'),
          SpecFieldOption(value: 'aluminum', label: '铝合金'),
        ],
      ),
      SpecField(
        id: 'door_style',
        name: 'doorStyle',
        label: '门板样式',
        type: SpecFieldType.enumSingle,
        order: 11,
        groupId: 'cabinet_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'flat', label: '平板'),
          SpecFieldOption(value: 'raised_panel', label: '凸起面板'),
          SpecFieldOption(value: 'shaker', label: '摇摆门'),
          SpecFieldOption(value: 'glass', label: '玻璃门'),
        ],
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'basic_info',
        name: 'basicInfo',
        label: '基础信息',
        description: '厨房设备的基本类型',
        order: 1,
        icon: 'kitchen',
      ),
      SpecFieldGroup(
        id: 'stove_specs',
        name: 'stoveSpecs',
        label: '炉灶规格',
        description: '炉灶设备的专用参数',
        order: 2,
        icon: 'local_fire_department',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'fridge_specs',
        name: 'fridgeSpecs',
        label: '冰箱规格',
        description: '冰箱设备的专用参数',
        order: 3,
        icon: 'kitchen',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'sink_specs',
        name: 'sinkSpecs',
        label: '水槽规格',
        description: '水槽设备的专用参数',
        order: 4,
        icon: 'wash',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'cabinet_specs',
        name: 'cabinetSpecs',
        label: '橱柜规格',
        description: '橱柜设备的专用参数',
        order: 5,
        icon: 'cabinet',
        collapsible: true,
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 6. 卫浴系统模板
  static CategorySpecTemplate get bathroom => CategorySpecTemplate(
    id: 'bathroom',
    categoryName: '卫浴系统',
    categoryCode: 'BATHROOM',
    description: '马桶、洗手盆、淋浴、浴室柜等卫浴设备的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'bathroom_type',
        name: 'bathroomType',
        label: '设备类型',
        type: SpecFieldType.enumSingle,
        required: true,
        order: 1,
        groupId: 'basic_info',
        showInList: true,
        filterable: true,
        options: [
          SpecFieldOption(value: 'toilet', label: '马桶'),
          SpecFieldOption(value: 'sink', label: '洗手盆'),
          SpecFieldOption(value: 'shower', label: '淋浴'),
          SpecFieldOption(value: 'cabinet', label: '浴室柜'),
          SpecFieldOption(value: 'mirror', label: '浴室镜'),
          SpecFieldOption(value: 'faucet', label: '水龙头'),
        ],
      ),
    ],
    optionalFields: [
      // 马桶专用参数
      SpecField(
        id: 'toilet_type',
        name: 'toiletType',
        label: '马桶类型',
        type: SpecFieldType.enumSingle,
        order: 2,
        groupId: 'toilet_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'cassette', label: '盒式马桶'),
          SpecFieldOption(value: 'composting', label: '堆肥马桶'),
          SpecFieldOption(value: 'portable', label: '便携马桶'),
          SpecFieldOption(value: 'rv_toilet', label: '房车专用马桶'),
        ],
      ),
      SpecField(
        id: 'flush_type',
        name: 'flushType',
        label: '冲水方式',
        type: SpecFieldType.enumSingle,
        order: 3,
        groupId: 'toilet_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'gravity', label: '重力冲水'),
          SpecFieldOption(value: 'pressure', label: '压力冲水'),
          SpecFieldOption(value: 'electric', label: '电动冲水'),
          SpecFieldOption(value: 'manual', label: '手动冲水'),
        ],
      ),
      SpecField(
        id: 'waste_capacity',
        name: 'wasteCapacity',
        label: '废料容量',
        type: SpecFieldType.decimal,
        unit: 'L',
        order: 4,
        groupId: 'toilet_specs',
        description: '马桶废料箱的容量',
      ),
      // 淋浴专用参数
      SpecField(
        id: 'shower_type',
        name: 'showerType',
        label: '淋浴类型',
        type: SpecFieldType.enumSingle,
        order: 5,
        groupId: 'shower_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'fixed', label: '固定淋浴'),
          SpecFieldOption(value: 'portable', label: '便携淋浴'),
          SpecFieldOption(value: 'outdoor', label: '户外淋浴'),
          SpecFieldOption(value: 'combo', label: '组合淋浴'),
        ],
      ),
      SpecField(
        id: 'water_heater_type',
        name: 'waterHeaterType',
        label: '热水器类型',
        type: SpecFieldType.enumSingle,
        order: 6,
        groupId: 'shower_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'gas', label: '燃气热水器'),
          SpecFieldOption(value: 'electric', label: '电热水器'),
          SpecFieldOption(value: 'solar', label: '太阳能热水器'),
          SpecFieldOption(value: 'instant', label: '即热式'),
        ],
      ),
      // 洗手盆专用参数
      SpecField(
        id: 'basin_material',
        name: 'basinMaterial',
        label: '盆体材质',
        type: SpecFieldType.enumSingle,
        order: 7,
        groupId: 'basin_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'ceramic', label: '陶瓷'),
          SpecFieldOption(value: 'stainless_steel', label: '不锈钢'),
          SpecFieldOption(value: 'glass', label: '玻璃'),
          SpecFieldOption(value: 'stone', label: '石材'),
          SpecFieldOption(value: 'composite', label: '复合材料'),
        ],
      ),
      SpecField(
        id: 'installation_type',
        name: 'installationType',
        label: '安装方式',
        type: SpecFieldType.enumSingle,
        order: 8,
        groupId: 'basin_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'wall_mounted', label: '壁挂式'),
          SpecFieldOption(value: 'countertop', label: '台上盆'),
          SpecFieldOption(value: 'undermount', label: '台下盆'),
          SpecFieldOption(value: 'pedestal', label: '立柱盆'),
        ],
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'basic_info',
        name: 'basicInfo',
        label: '基础信息',
        description: '卫浴设备的基本类型',
        order: 1,
        icon: 'bathroom',
      ),
      SpecFieldGroup(
        id: 'toilet_specs',
        name: 'toiletSpecs',
        label: '马桶规格',
        description: '马桶设备的专用参数',
        order: 2,
        icon: 'wc',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'shower_specs',
        name: 'showerSpecs',
        label: '淋浴规格',
        description: '淋浴设备的专用参数',
        order: 3,
        icon: 'shower',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'basin_specs',
        name: 'basinSpecs',
        label: '洗手盆规格',
        description: '洗手盆设备的专用参数',
        order: 4,
        icon: 'wash',
        collapsible: true,
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 7. 外观系统模板
  static CategorySpecTemplate get exterior => CategorySpecTemplate(
    id: 'exterior',
    categoryName: '外观系统',
    categoryCode: 'EXTERIOR',
    description: '车身贴纸、装饰条、车顶架、车灯等外观装饰的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'exterior_type',
        name: 'exteriorType',
        label: '外观类型',
        type: SpecFieldType.enumSingle,
        required: true,
        order: 1,
        groupId: 'basic_info',
        showInList: true,
        filterable: true,
        options: [
          SpecFieldOption(value: 'decal', label: '车身贴纸'),
          SpecFieldOption(value: 'trim', label: '装饰条'),
          SpecFieldOption(value: 'roof_rack', label: '车顶架'),
          SpecFieldOption(value: 'awning', label: '遮阳篷'),
          SpecFieldOption(value: 'light', label: '车灯'),
          SpecFieldOption(value: 'bumper', label: '保险杠'),
        ],
      ),
    ],
    optionalFields: [
      // 贴纸专用参数
      SpecField(
        id: 'decal_material',
        name: 'decalMaterial',
        label: '贴纸材质',
        type: SpecFieldType.enumSingle,
        order: 2,
        groupId: 'decal_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'vinyl', label: '乙烯基'),
          SpecFieldOption(value: 'reflective', label: '反光材料'),
          SpecFieldOption(value: 'carbon_fiber', label: '碳纤维纹'),
          SpecFieldOption(value: 'chrome', label: '镀铬'),
        ],
      ),
      SpecField(
        id: 'weather_resistance',
        name: 'weatherResistance',
        label: '耐候性',
        type: SpecFieldType.enumSingle,
        order: 3,
        groupId: 'decal_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'uv_resistant', label: '抗紫外线'),
          SpecFieldOption(value: 'waterproof', label: '防水'),
          SpecFieldOption(value: 'fade_resistant', label: '抗褪色'),
          SpecFieldOption(value: 'all_weather', label: '全天候'),
        ],
      ),
      // 车顶架专用参数
      SpecField(
        id: 'rack_type',
        name: 'rackType',
        label: '车顶架类型',
        type: SpecFieldType.enumSingle,
        order: 4,
        groupId: 'rack_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'roof_box', label: '车顶箱'),
          SpecFieldOption(value: 'bike_rack', label: '自行车架'),
          SpecFieldOption(value: 'kayak_rack', label: '皮划艇架'),
          SpecFieldOption(value: 'cargo_basket', label: '行李筐'),
          SpecFieldOption(value: 'ladder_rack', label: '梯子架'),
        ],
      ),
      SpecField(
        id: 'load_capacity',
        name: 'loadCapacity',
        label: '载重能力',
        type: SpecFieldType.weight,
        unit: 'kg',
        order: 5,
        groupId: 'rack_specs',
        showInList: true,
        filterable: true,
        description: '车顶架的最大载重能力',
      ),
      SpecField(
        id: 'mounting_system',
        name: 'mountingSystem',
        label: '安装系统',
        type: SpecFieldType.enumSingle,
        order: 6,
        groupId: 'rack_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'clamp_on', label: '夹具式'),
          SpecFieldOption(value: 'track_mount', label: '导轨式'),
          SpecFieldOption(value: 'permanent', label: '永久安装'),
          SpecFieldOption(value: 'magnetic', label: '磁吸式'),
        ],
      ),
      // 遮阳篷专用参数
      SpecField(
        id: 'awning_type',
        name: 'awningType',
        label: '遮阳篷类型',
        type: SpecFieldType.enumSingle,
        order: 7,
        groupId: 'awning_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'retractable', label: '可收缩'),
          SpecFieldOption(value: 'fixed', label: '固定式'),
          SpecFieldOption(value: 'pop_up', label: '弹出式'),
          SpecFieldOption(value: 'slide_out', label: '滑出式'),
        ],
      ),
      SpecField(
        id: 'fabric_material',
        name: 'fabricMaterial',
        label: '面料材质',
        type: SpecFieldType.enumSingle,
        order: 8,
        groupId: 'awning_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'vinyl', label: '乙烯基'),
          SpecFieldOption(value: 'acrylic', label: '丙烯酸'),
          SpecFieldOption(value: 'polyester', label: '聚酯纤维'),
          SpecFieldOption(value: 'canvas', label: '帆布'),
        ],
      ),
      SpecField(
        id: 'coverage_area',
        name: 'coverageArea',
        label: '遮蔽面积',
        type: SpecFieldType.decimal,
        unit: 'm²',
        order: 9,
        groupId: 'awning_specs',
        showInList: true,
        description: '遮阳篷的有效遮蔽面积',
      ),
      // 车灯专用参数
      SpecField(
        id: 'light_type',
        name: 'lightType',
        label: '灯具类型',
        type: SpecFieldType.enumSingle,
        order: 10,
        groupId: 'light_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'led_bar', label: 'LED灯条'),
          SpecFieldOption(value: 'spotlight', label: '射灯'),
          SpecFieldOption(value: 'work_light', label: '工作灯'),
          SpecFieldOption(value: 'marker_light', label: '示廓灯'),
          SpecFieldOption(value: 'backup_light', label: '倒车灯'),
        ],
      ),
      SpecField(
        id: 'light_output',
        name: 'lightOutput',
        label: '光通量',
        type: SpecFieldType.number,
        unit: 'lm',
        order: 11,
        groupId: 'light_specs',
        showInList: true,
        description: '灯具的光通量输出',
      ),
      SpecField(
        id: 'beam_pattern',
        name: 'beamPattern',
        label: '光束模式',
        type: SpecFieldType.enumSingle,
        order: 12,
        groupId: 'light_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'spot', label: '聚光'),
          SpecFieldOption(value: 'flood', label: '泛光'),
          SpecFieldOption(value: 'combo', label: '组合'),
          SpecFieldOption(value: 'driving', label: '行车'),
        ],
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'basic_info',
        name: 'basicInfo',
        label: '基础信息',
        description: '外观装饰的基本类型',
        order: 1,
        icon: 'directions_car',
      ),
      SpecFieldGroup(
        id: 'decal_specs',
        name: 'decalSpecs',
        label: '贴纸规格',
        description: '车身贴纸的专用参数',
        order: 2,
        icon: 'palette',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'rack_specs',
        name: 'rackSpecs',
        label: '车顶架规格',
        description: '车顶架的专用参数',
        order: 3,
        icon: 'luggage',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'awning_specs',
        name: 'awningSpecs',
        label: '遮阳篷规格',
        description: '遮阳篷的专用参数',
        order: 4,
        icon: 'beach_access',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'light_specs',
        name: 'lightSpecs',
        label: '车灯规格',
        description: '车灯的专用参数',
        order: 5,
        icon: 'lightbulb',
        collapsible: true,
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 8. 底盘系统模板
  static CategorySpecTemplate get chassis => CategorySpecTemplate(
    id: 'chassis',
    categoryName: '底盘系统',
    categoryCode: 'CHASSIS',
    description: '悬挂、轮胎、刹车、减震器等底盘系统的专业规格模板',
    requiredFields: [
      SpecField(
        id: 'chassis_type',
        name: 'chassisType',
        label: '底盘类型',
        type: SpecFieldType.enumSingle,
        required: true,
        order: 1,
        groupId: 'basic_info',
        showInList: true,
        filterable: true,
        options: [
          SpecFieldOption(value: 'suspension', label: '悬挂系统'),
          SpecFieldOption(value: 'tire', label: '轮胎'),
          SpecFieldOption(value: 'brake', label: '刹车系统'),
          SpecFieldOption(value: 'shock_absorber', label: '减震器'),
          SpecFieldOption(value: 'stabilizer', label: '稳定杆'),
          SpecFieldOption(value: 'differential', label: '差速器'),
        ],
      ),
    ],
    optionalFields: [
      // 轮胎专用参数
      SpecField(
        id: 'tire_size',
        name: 'tireSize',
        label: '轮胎规格',
        type: SpecFieldType.text,
        order: 2,
        groupId: 'tire_specs',
        showInList: true,
        filterable: true,
        description: '轮胎的规格尺寸，如 225/75R16',
      ),
      SpecField(
        id: 'tire_type',
        name: 'tireType',
        label: '轮胎类型',
        type: SpecFieldType.enumSingle,
        order: 3,
        groupId: 'tire_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'all_terrain', label: '全地形'),
          SpecFieldOption(value: 'highway', label: '公路'),
          SpecFieldOption(value: 'mud_terrain', label: '泥地'),
          SpecFieldOption(value: 'winter', label: '冬季'),
          SpecFieldOption(value: 'commercial', label: '商用'),
        ],
      ),
      SpecField(
        id: 'load_rating',
        name: 'loadRating',
        label: '载重等级',
        type: SpecFieldType.text,
        order: 4,
        groupId: 'tire_specs',
        filterable: true,
        description: '轮胎的载重等级代码',
      ),
      SpecField(
        id: 'speed_rating',
        name: 'speedRating',
        label: '速度等级',
        type: SpecFieldType.text,
        order: 5,
        groupId: 'tire_specs',
        filterable: true,
        description: '轮胎的速度等级代码',
      ),
      // 悬挂专用参数
      SpecField(
        id: 'suspension_type',
        name: 'suspensionType',
        label: '悬挂类型',
        type: SpecFieldType.enumSingle,
        order: 6,
        groupId: 'suspension_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'leaf_spring', label: '钢板弹簧'),
          SpecFieldOption(value: 'coil_spring', label: '螺旋弹簧'),
          SpecFieldOption(value: 'air_suspension', label: '空气悬挂'),
          SpecFieldOption(value: 'torsion_bar', label: '扭杆悬挂'),
        ],
      ),
      SpecField(
        id: 'spring_rate',
        name: 'springRate',
        label: '弹簧刚度',
        type: SpecFieldType.decimal,
        unit: 'N/mm',
        order: 7,
        groupId: 'suspension_specs',
        description: '弹簧的刚度系数',
      ),
      // 刹车专用参数
      SpecField(
        id: 'brake_type',
        name: 'brakeType',
        label: '刹车类型',
        type: SpecFieldType.enumSingle,
        order: 8,
        groupId: 'brake_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'disc', label: '盘式刹车'),
          SpecFieldOption(value: 'drum', label: '鼓式刹车'),
          SpecFieldOption(value: 'electric', label: '电子刹车'),
          SpecFieldOption(value: 'hydraulic', label: '液压刹车'),
        ],
      ),
      SpecField(
        id: 'brake_material',
        name: 'brakeMaterial',
        label: '刹车片材质',
        type: SpecFieldType.enumSingle,
        order: 9,
        groupId: 'brake_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'ceramic', label: '陶瓷'),
          SpecFieldOption(value: 'semi_metallic', label: '半金属'),
          SpecFieldOption(value: 'organic', label: '有机'),
          SpecFieldOption(value: 'carbon', label: '碳纤维'),
        ],
      ),
      // 减震器专用参数
      SpecField(
        id: 'shock_type',
        name: 'shockType',
        label: '减震器类型',
        type: SpecFieldType.enumSingle,
        order: 10,
        groupId: 'shock_specs',
        filterable: true,
        options: [
          SpecFieldOption(value: 'gas', label: '气压式'),
          SpecFieldOption(value: 'oil', label: '油压式'),
          SpecFieldOption(value: 'adjustable', label: '可调式'),
          SpecFieldOption(value: 'electronic', label: '电子式'),
        ],
      ),
      SpecField(
        id: 'damping_force',
        name: 'dampingForce',
        label: '阻尼力',
        type: SpecFieldType.decimal,
        unit: 'N',
        order: 11,
        groupId: 'shock_specs',
        description: '减震器的阻尼力',
      ),
    ],
    fieldGroups: [
      SpecFieldGroup(
        id: 'basic_info',
        name: 'basicInfo',
        label: '基础信息',
        description: '底盘系统的基本类型',
        order: 1,
        icon: 'build',
      ),
      SpecFieldGroup(
        id: 'tire_specs',
        name: 'tireSpecs',
        label: '轮胎规格',
        description: '轮胎的专用参数',
        order: 2,
        icon: 'tire_repair',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'suspension_specs',
        name: 'suspensionSpecs',
        label: '悬挂规格',
        description: '悬挂系统的专用参数',
        order: 3,
        icon: 'settings_input_component',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'brake_specs',
        name: 'brakeSpecs',
        label: '刹车规格',
        description: '刹车系统的专用参数',
        order: 4,
        icon: 'speed',
        collapsible: true,
      ),
      SpecFieldGroup(
        id: 'shock_specs',
        name: 'shockSpecs',
        label: '减震器规格',
        description: '减震器的专用参数',
        order: 5,
        icon: 'waves',
        collapsible: true,
      ),
    ],
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// 获取所有模板
  static List<CategorySpecTemplate> getAllTemplates() {
    return [
      electrical,
      plumbing,
      storage,
      bedding,
      kitchen,
      bathroom,
      exterior,
      chassis,
    ];
  }

  /// 根据分类代码获取模板
  static CategorySpecTemplate? getTemplate(String categoryCode) {
    switch (categoryCode.toUpperCase()) {
      case 'ELECTRICAL':
        return electrical;
      case 'PLUMBING':
        return plumbing;
      case 'STORAGE':
        return storage;
      case 'BEDDING':
        return bedding;
      case 'KITCHEN':
        return kitchen;
      case 'BATHROOM':
        return bathroom;
      case 'EXTERIOR':
        return exterior;
      case 'CHASSIS':
        return chassis;
      default:
        return null;
    }
  }

  /// 获取分类名称映射
  static Map<String, String> getCategoryNameMap() {
    return {
      'ELECTRICAL': '电气设备',
      'PLUMBING': '水路设备',
      'STORAGE': '储物系统',
      'BEDDING': '床铺系统',
      'KITCHEN': '厨房系统',
      'BATHROOM': '卫浴系统',
      'EXTERIOR': '外观系统',
      'CHASSIS': '底盘系统',
    };
  }

  /// 获取分类描述映射
  static Map<String, String> getCategoryDescriptionMap() {
    return {
      'ELECTRICAL': '电池、逆变器、充电器、配电箱等电气设备',
      'PLUMBING': '水泵、水箱、净水器、管道等水路系统设备',
      'STORAGE': '储物箱、收纳架、抽屉、挂钩等储物设备',
      'BEDDING': '床垫、床架、寝具等床铺系统设备',
      'KITCHEN': '炉灶、冰箱、水槽、橱柜等厨房设备',
      'BATHROOM': '马桶、洗手盆、淋浴、浴室柜等卫浴设备',
      'EXTERIOR': '车身贴纸、装饰条、车顶架、车灯等外观装饰',
      'CHASSIS': '悬挂、轮胎、刹车、减震器等底盘系统',
    };
  }
}
