import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';

/// VanHub表单系统
/// 提供统一的表单验证、自动保存和动态表单生成功能
class VanHubForm extends StatefulWidget {
  final List<VanHubFormField> fields;
  final VanHubFormSubmitCallback onSubmit;
  final VanHubFormChangeCallback? onChange;
  final VanHubFormSaveCallback? onSave;
  final bool autoSave;
  final Duration autoSaveDuration;
  final bool showSubmitButton;
  final String submitButtonText;
  final bool showCancelButton;
  final String cancelButtonText;
  final VoidCallback? onCancel;
  final EdgeInsetsGeometry padding;
  final bool scrollable;
  final Map<String, dynamic>? initialValues;

  const VanHubForm({
    super.key,
    required this.fields,
    required this.onSubmit,
    this.onChange,
    this.onSave,
    this.autoSave = false,
    this.autoSaveDuration = const Duration(seconds: 5),
    this.showSubmitButton = true,
    this.submitButtonText = '提交',
    this.showCancelButton = false,
    this.cancelButtonText = '取消',
    this.onCancel,
    this.padding = const EdgeInsets.all(16.0),
    this.scrollable = true,
    this.initialValues,
  });

  @override
  State<VanHubForm> createState() => _VanHubFormState();
}

class _VanHubFormState extends State<VanHubForm> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> _formValues = {};
  Timer? _autoSaveTimer;
  bool _isDirty = false;

  @override
  void initState() {
    super.initState();
    
    // 初始化表单值
    if (widget.initialValues != null) {
      _formValues.addAll(widget.initialValues!);
    }
    
    // 设置自动保存定时器
    if (widget.autoSave && widget.onSave != null) {
      _autoSaveTimer = Timer.periodic(widget.autoSaveDuration, (_) {
        if (_isDirty) {
          widget.onSave!(_formValues);
          _isDirty = false;
        }
      });
    }
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    super.dispose();
  }

  void _handleFieldChange(String name, dynamic value) {
    _formValues[name] = value;
    _isDirty = true;
    
    if (widget.onChange != null) {
      widget.onChange!(_formValues);
    }
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      widget.onSubmit(_formValues);
    }
  }

  @override
  Widget build(BuildContext context) {
    final formContent = Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...widget.fields.map((field) {
            // 处理字段依赖关系
            bool isVisible = true;
            if (field.visibleWhen != null) {
              isVisible = field.visibleWhen!(_formValues);
            }
            
            if (!isVisible) {
              return const SizedBox.shrink();
            }
            
            // 构建表单字段
            return Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: _buildFormField(field),
            );
          }),
          
          if (widget.showSubmitButton || widget.showCancelButton)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (widget.showCancelButton)
                    Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: TextButton(
                        onPressed: widget.onCancel,
                        child: Text(widget.cancelButtonText),
                      ),
                    ),
                  if (widget.showSubmitButton)
                    ElevatedButton(
                      onPressed: _handleSubmit,
                      child: Text(widget.submitButtonText),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
    
    if (widget.scrollable) {
      return SingleChildScrollView(
        padding: widget.padding,
        child: formContent,
      );
    } else {
      return Padding(
        padding: widget.padding,
        child: formContent,
      );
    }
  }

  Widget _buildFormField(VanHubFormField field) {
    // 获取初始值
    final initialValue = _formValues[field.name] ?? field.initialValue;
    
    switch (field.type) {
      case VanHubFormFieldType.text:
        return TextFormField(
          initialValue: initialValue?.toString(),
          decoration: InputDecoration(
            labelText: field.label,
            hintText: field.hint,
            prefixIcon: field.prefixIcon,
            suffixIcon: field.suffixIcon,
            helperText: field.helperText,
          ),
          validator: field.validator,
          onChanged: (value) => _handleFieldChange(field.name, value),
          onSaved: (value) => _formValues[field.name] = value,
          keyboardType: field.keyboardType,
          textInputAction: field.textInputAction,
          obscureText: field.obscureText,
          maxLines: field.maxLines,
          minLines: field.minLines,
          maxLength: field.maxLength,
          enabled: field.enabled,
          readOnly: field.readOnly,
          inputFormatters: field.inputFormatters,
        );
        
      case VanHubFormFieldType.number:
        return TextFormField(
          initialValue: initialValue?.toString(),
          decoration: InputDecoration(
            labelText: field.label,
            hintText: field.hint,
            prefixIcon: field.prefixIcon,
            suffixIcon: field.suffixIcon,
            helperText: field.helperText,
          ),
          validator: field.validator,
          onChanged: (value) {
            final numValue = double.tryParse(value);
            _handleFieldChange(field.name, numValue);
          },
          onSaved: (value) {
            final numValue = double.tryParse(value ?? '');
            _formValues[field.name] = numValue;
          },
          keyboardType: TextInputType.number,
          textInputAction: field.textInputAction,
          enabled: field.enabled,
          readOnly: field.readOnly,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ...?field.inputFormatters,
          ],
        );
        
      case VanHubFormFieldType.checkbox:
        return CheckboxListTile(
          title: Text(field.label),
          subtitle: field.hint != null ? Text(field.hint!) : null,
          value: initialValue ?? false,
          onChanged: field.enabled
              ? (value) => _handleFieldChange(field.name, value)
              : null,
          controlAffinity: ListTileControlAffinity.leading,
          secondary: field.suffixIcon,
        );
        
      case VanHubFormFieldType.radio:
        if (field.options == null || field.options!.isEmpty) {
          return const Text('错误：单选框需要提供选项');
        }
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              field.label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (field.hint != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  field.hint!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            const SizedBox(height: 8),
            ...field.options!.map((option) {
              return RadioListTile<dynamic>(
                title: Text(option.label),
                value: option.value,
                groupValue: initialValue,
                onChanged: field.enabled
                    ? (value) => _handleFieldChange(field.name, value)
                    : null,
                dense: true,
              );
            }),
          ],
        );
        
      case VanHubFormFieldType.dropdown:
        if (field.options == null || field.options!.isEmpty) {
          return const Text('错误：下拉框需要提供选项');
        }
        
        return DropdownButtonFormField<dynamic>(
          decoration: InputDecoration(
            labelText: field.label,
            hintText: field.hint,
            prefixIcon: field.prefixIcon,
            suffixIcon: field.suffixIcon,
            helperText: field.helperText,
          ),
          value: initialValue,
          items: field.options!.map((option) {
            return DropdownMenuItem<dynamic>(
              value: option.value,
              child: Text(option.label),
            );
          }).toList(),
          onChanged: field.enabled
              ? (value) => _handleFieldChange(field.name, value)
              : null,
          validator: field.validator != null
              ? (value) => field.validator!(value?.toString())
              : null,
          onSaved: (value) => _formValues[field.name] = value,
        );
        
      case VanHubFormFieldType.date:
        return _buildDateField(field, initialValue);
        
      case VanHubFormFieldType.time:
        return _buildTimeField(field, initialValue);
        
      case VanHubFormFieldType.file:
        return _buildFileField(field, initialValue);
        
      case VanHubFormFieldType.slider:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              field.label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (field.hint != null)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  field.hint!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            Slider(
              value: (initialValue ?? field.min ?? 0.0).toDouble(),
              min: (field.min ?? 0.0).toDouble(),
              max: (field.max ?? 100.0).toDouble(),
              divisions: field.divisions,
              label: initialValue?.toString(),
              onChanged: field.enabled
                  ? (value) => _handleFieldChange(field.name, value)
                  : null,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text((field.min ?? 0.0).toString()),
                Text((field.max ?? 100.0).toString()),
              ],
            ),
          ],
        );
        
      case VanHubFormFieldType.custom:
        if (field.customBuilder == null) {
          return const Text('错误：自定义字段需要提供构建器');
        }
        
        return field.customBuilder!(
          context,
          initialValue,
          (value) => _handleFieldChange(field.name, value),
        );
        
      default:
        return const Text('不支持的字段类型');
    }
  }

  Widget _buildDateField(VanHubFormField field, dynamic initialValue) {
    final TextEditingController controller = TextEditingController(
      text: initialValue != null
          ? DateFormat('yyyy-MM-dd').format(initialValue)
          : '',
    );
    
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: field.label,
        hintText: field.hint ?? 'YYYY-MM-DD',
        prefixIcon: field.prefixIcon ?? const Icon(Icons.calendar_today),
        suffixIcon: field.suffixIcon,
        helperText: field.helperText,
      ),
      readOnly: true,
      onTap: field.enabled
          ? () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: initialValue ?? DateTime.now(),
                firstDate: field.min != null
                    ? DateTime.fromMillisecondsSinceEpoch(field.min!.toInt())
                    : DateTime(1900),
                lastDate: field.max != null
                    ? DateTime.fromMillisecondsSinceEpoch(field.max!.toInt())
                    : DateTime(2100),
              );
              
              if (picked != null) {
                controller.text = DateFormat('yyyy-MM-dd').format(picked);
                _handleFieldChange(field.name, picked);
              }
            }
          : null,
      validator: field.validator,
      onSaved: (value) {
        if (value != null && value.isNotEmpty) {
          try {
            final date = DateFormat('yyyy-MM-dd').parse(value);
            _formValues[field.name] = date;
          } catch (e) {
            // 解析失败，保持原值
          }
        }
      },
    );
  }

  Widget _buildTimeField(VanHubFormField field, dynamic initialValue) {
    final TextEditingController controller = TextEditingController(
      text: initialValue != null
          ? initialValue is TimeOfDay
              ? '${initialValue.hour.toString().padLeft(2, '0')}:${initialValue.minute.toString().padLeft(2, '0')}'
              : ''
          : '',
    );
    
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: field.label,
        hintText: field.hint ?? 'HH:MM',
        prefixIcon: field.prefixIcon ?? const Icon(Icons.access_time),
        suffixIcon: field.suffixIcon,
        helperText: field.helperText,
      ),
      readOnly: true,
      onTap: field.enabled
          ? () async {
              final TimeOfDay? picked = await showTimePicker(
                context: context,
                initialTime: initialValue is TimeOfDay
                    ? initialValue
                    : TimeOfDay.now(),
              );
              
              if (picked != null) {
                controller.text =
                    '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
                _handleFieldChange(field.name, picked);
              }
            }
          : null,
      validator: field.validator,
      onSaved: (value) {
        if (value != null && value.isNotEmpty) {
          try {
            final parts = value.split(':');
            if (parts.length == 2) {
              final hour = int.parse(parts[0]);
              final minute = int.parse(parts[1]);
              _formValues[field.name] = TimeOfDay(hour: hour, minute: minute);
            }
          } catch (e) {
            // 解析失败，保持原值
          }
        }
      },
    );
  }

  Widget _buildFileField(VanHubFormField field, dynamic initialValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          field.label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (field.hint != null)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              field.hint!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton.icon(
              icon: const Icon(Icons.upload_file),
              label: Text(initialValue != null ? '更改文件' : '选择文件'),
              onPressed: field.enabled
                  ? () async {
                      // 文件选择逻辑，需要根据实际情况实现
                      // 这里仅作为示例
                      final result = await FilePicker.platform.pickFiles(
                        type: FileType.any,
                        allowMultiple: field.allowMultiple ?? false,
                      );
                      
                      if (result != null) {
                        _handleFieldChange(
                          field.name,
                          field.allowMultiple ?? false
                              ? result.files
                              : result.files.first,
                        );
                      }
                    }
                  : null,
            ),
            const SizedBox(width: 16),
            if (initialValue != null)
              Expanded(
                child: Text(
                  initialValue is PlatformFile
                      ? initialValue.name
                      : initialValue is List<PlatformFile>
                          ? '${initialValue.length} 个文件已选择'
                          : '文件已选择',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
          ],
        ),
      ],
    );
  }
}

/// 表单字段类型
enum VanHubFormFieldType {
  text,
  number,
  checkbox,
  radio,
  dropdown,
  date,
  time,
  file,
  slider,
  custom,
}

/// 表单选项
class VanHubFormOption {
  final String label;
  final dynamic value;

  const VanHubFormOption({
    required this.label,
    required this.value,
  });
}

/// 表单字段
class VanHubFormField {
  final String name;
  final String label;
  final VanHubFormFieldType type;
  final String? hint;
  final String? helperText;
  final dynamic initialValue;
  final FormFieldValidator<String>? validator;
  final List<VanHubFormOption>? options;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final List<TextInputFormatter>? inputFormatters;
  final num? min;
  final num? max;
  final int? divisions;
  final bool? allowMultiple;
  final VanHubFormFieldVisibilityCondition? visibleWhen;
  final VanHubFormCustomFieldBuilder? customBuilder;

  const VanHubFormField({
    required this.name,
    required this.label,
    required this.type,
    this.hint,
    this.helperText,
    this.initialValue,
    this.validator,
    this.options,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.inputFormatters,
    this.min,
    this.max,
    this.divisions,
    this.allowMultiple,
    this.visibleWhen,
    this.customBuilder,
  });
}

/// 表单提交回调
typedef VanHubFormSubmitCallback = void Function(Map<String, dynamic> values);

/// 表单变更回调
typedef VanHubFormChangeCallback = void Function(Map<String, dynamic> values);

/// 表单保存回调
typedef VanHubFormSaveCallback = void Function(Map<String, dynamic> values);

/// 表单字段可见性条件
typedef VanHubFormFieldVisibilityCondition = bool Function(Map<String, dynamic> values);

/// 自定义表单字段构建器
typedef VanHubFormCustomFieldBuilder = Widget Function(
  BuildContext context,
  dynamic value,
  ValueChanged<dynamic> onChanged,
);

/// 动态表单生成器
class VanHubDynamicFormGenerator {
  /// 从JSON配置生成表单字段
  static List<VanHubFormField> generateFieldsFromJson(List<Map<String, dynamic>> fieldsConfig) {
    return fieldsConfig.map((config) {
      return VanHubFormField(
        name: config['name'],
        label: config['label'],
        type: _parseFieldType(config['type']),
        hint: config['hint'],
        helperText: config['helperText'],
        initialValue: config['initialValue'],
        validator: _createValidator(config),
        options: _parseOptions(config['options']),
        prefixIcon: config['prefixIcon'] != null
            ? Icon(IconData(config['prefixIcon'], fontFamily: 'MaterialIcons'))
            : null,
        suffixIcon: config['suffixIcon'] != null
            ? Icon(IconData(config['suffixIcon'], fontFamily: 'MaterialIcons'))
            : null,
        keyboardType: _parseKeyboardType(config['keyboardType']),
        textInputAction: _parseTextInputAction(config['textInputAction']),
        obscureText: config['obscureText'] ?? false,
        maxLines: config['maxLines'],
        minLines: config['minLines'],
        maxLength: config['maxLength'],
        enabled: config['enabled'] ?? true,
        readOnly: config['readOnly'] ?? false,
        min: config['min'],
        max: config['max'],
        divisions: config['divisions'],
        allowMultiple: config['allowMultiple'],
      );
    }).toList();
  }

  /// 解析字段类型
  static VanHubFormFieldType _parseFieldType(String? typeStr) {
    switch (typeStr) {
      case 'text':
        return VanHubFormFieldType.text;
      case 'number':
        return VanHubFormFieldType.number;
      case 'checkbox':
        return VanHubFormFieldType.checkbox;
      case 'radio':
        return VanHubFormFieldType.radio;
      case 'dropdown':
        return VanHubFormFieldType.dropdown;
      case 'date':
        return VanHubFormFieldType.date;
      case 'time':
        return VanHubFormFieldType.time;
      case 'file':
        return VanHubFormFieldType.file;
      case 'slider':
        return VanHubFormFieldType.slider;
      case 'custom':
        return VanHubFormFieldType.custom;
      default:
        return VanHubFormFieldType.text;
    }
  }

  /// 解析选项
  static List<VanHubFormOption>? _parseOptions(List? options) {
    if (options == null) return null;
    
    return options.map((option) {
      return VanHubFormOption(
        label: option['label'],
        value: option['value'],
      );
    }).toList();
  }

  /// 解析键盘类型
  static TextInputType? _parseKeyboardType(String? type) {
    switch (type) {
      case 'text':
        return TextInputType.text;
      case 'number':
        return TextInputType.number;
      case 'phone':
        return TextInputType.phone;
      case 'email':
        return TextInputType.emailAddress;
      case 'url':
        return TextInputType.url;
      case 'multiline':
        return TextInputType.multiline;
      default:
        return null;
    }
  }

  /// 解析文本输入动作
  static TextInputAction? _parseTextInputAction(String? action) {
    switch (action) {
      case 'done':
        return TextInputAction.done;
      case 'next':
        return TextInputAction.next;
      case 'search':
        return TextInputAction.search;
      case 'send':
        return TextInputAction.send;
      case 'go':
        return TextInputAction.go;
      default:
        return null;
    }
  }

  /// 创建验证器
  static FormFieldValidator<String>? _createValidator(Map<String, dynamic> config) {
    final validations = config['validations'];
    if (validations == null) return null;
    
    return (value) {
      if (validations['required'] == true && (value == null || value.isEmpty)) {
        return validations['requiredMessage'] ?? '此字段为必填项';
      }
      
      if (validations['minLength'] != null &&
          value != null &&
          value.length < validations['minLength']) {
        return validations['minLengthMessage'] ??
            '最小长度为 ${validations['minLength']}';
      }
      
      if (validations['maxLength'] != null &&
          value != null &&
          value.length > validations['maxLength']) {
        return validations['maxLengthMessage'] ??
            '最大长度为 ${validations['maxLength']}';
      }
      
      if (validations['pattern'] != null &&
          value != null &&
          !RegExp(validations['pattern']).hasMatch(value)) {
        return validations['patternMessage'] ?? '格式不正确';
      }
      
      if (validations['email'] == true &&
          value != null &&
          value.isNotEmpty &&
          !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
        return validations['emailMessage'] ?? '请输入有效的电子邮件地址';
      }
      
      return null;
    };
  }
}
