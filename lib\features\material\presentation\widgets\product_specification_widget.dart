import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/product_specification.dart';
import '../../domain/entities/category_spec_template.dart';

/// 产品规格展示组件
/// 
/// 用于展示产品的详细技术规格信息
class ProductSpecificationWidget extends ConsumerWidget {
  final ProductSpecification specification;
  final bool showAllFields;
  final bool isCompactMode;

  const ProductSpecificationWidget({
    Key? key,
    required this.specification,
    this.showAllFields = true,
    this.isCompactMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildBasicSpecification(context),
            if (showAllFields) ...[
              const SizedBox(height: 16),
              _buildTechnicalParameters(context),
              const SizedBox(height: 16),
              _buildPhysicalProperties(context),
              if (specification.performanceMetrics != null) ...[
                const SizedBox(height: 16),
                _buildPerformanceMetrics(context),
              ],
              if (specification.certifications != null && 
                  specification.certifications!.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildCertifications(context),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          _getCategoryIcon(specification.category),
          size: 24,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                specification.basicSpec.productName,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${specification.basicSpec.brand} - ${specification.basicSpec.model}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        _buildCategoryChip(context),
      ],
    );
  }

  /// 构建分类标签
  Widget _buildCategoryChip(BuildContext context) {
    return Chip(
      label: Text(
        _getCategoryDisplayName(specification.category),
        style: const TextStyle(fontSize: 12),
      ),
      backgroundColor: _getCategoryColor(specification.category).withOpacity(0.1),
      side: BorderSide(
        color: _getCategoryColor(specification.category),
        width: 1,
      ),
    );
  }

  /// 构建基础规格信息
  Widget _buildBasicSpecification(BuildContext context) {
    return _buildSection(
      context,
      title: '基础信息',
      icon: Icons.info_outline,
      children: [
        if (specification.basicSpec.manufacturer != null)
          _buildSpecItem('制造商', specification.basicSpec.manufacturer!),
        if (specification.basicSpec.countryOfOrigin != null)
          _buildSpecItem('产地', specification.basicSpec.countryOfOrigin!),
        if (specification.basicSpec.warrantyMonths != null)
          _buildSpecItem('保修期', '${specification.basicSpec.warrantyMonths}个月'),
        if (specification.basicSpec.description != null)
          _buildSpecItem('描述', specification.basicSpec.description!),
      ],
    );
  }

  /// 构建技术参数
  Widget _buildTechnicalParameters(BuildContext context) {
    final params = specification.technicalParams;
    List<Widget> children = [];

    // 电气参数
    if (params.electrical != null) {
      children.addAll(_buildElectricalSpecs(params.electrical!));
    }

    // 机械参数
    if (params.mechanical != null) {
      children.addAll(_buildMechanicalSpecs(params.mechanical!));
    }

    // 流体参数
    if (params.fluid != null) {
      children.addAll(_buildFluidSpecs(params.fluid!));
    }

    // 热力参数
    if (params.thermal != null) {
      children.addAll(_buildThermalSpecs(params.thermal!));
    }

    // 化学参数
    if (params.chemical != null) {
      children.addAll(_buildChemicalSpecs(params.chemical!));
    }

    if (children.isEmpty) return const SizedBox.shrink();

    return _buildSection(
      context,
      title: '技术参数',
      icon: Icons.engineering,
      children: children,
    );
  }

  /// 构建电气参数
  List<Widget> _buildElectricalSpecs(ElectricalSpecs electrical) {
    List<Widget> specs = [];
    
    if (electrical.ratedVoltage != null) {
      specs.add(_buildSpecItem('额定电压', '${electrical.ratedVoltage}V'));
    }
    if (electrical.ratedCurrent != null) {
      specs.add(_buildSpecItem('额定电流', '${electrical.ratedCurrent}A'));
    }
    if (electrical.ratedPower != null) {
      specs.add(_buildSpecItem('额定功率', '${electrical.ratedPower}W'));
    }
    if (electrical.capacity != null) {
      specs.add(_buildSpecItem(
        '容量', 
        '${electrical.capacity}${electrical.capacityUnit ?? 'Ah'}'
      ));
    }
    if (electrical.efficiency != null) {
      specs.add(_buildSpecItem('效率', '${electrical.efficiency}%'));
    }
    if (electrical.protectionRating != null) {
      specs.add(_buildSpecItem('防护等级', electrical.protectionRating!));
    }

    return specs;
  }

  /// 构建机械参数
  List<Widget> _buildMechanicalSpecs(MechanicalSpecs mechanical) {
    List<Widget> specs = [];
    
    if (mechanical.maxLoad != null) {
      specs.add(_buildSpecItem('最大承重', '${mechanical.maxLoad}kg'));
    }
    if (mechanical.workingPressure != null) {
      specs.add(_buildSpecItem('工作压力', '${mechanical.workingPressure}MPa'));
    }
    if (mechanical.maxPressure != null) {
      specs.add(_buildSpecItem('最大压力', '${mechanical.maxPressure}MPa'));
    }
    if (mechanical.rotationSpeed != null) {
      specs.add(_buildSpecItem('转速', '${mechanical.rotationSpeed}rpm'));
    }
    if (mechanical.strengthGrade != null) {
      specs.add(_buildSpecItem('强度等级', mechanical.strengthGrade!));
    }

    return specs;
  }

  /// 构建流体参数
  List<Widget> _buildFluidSpecs(FluidSpecs fluid) {
    List<Widget> specs = [];
    
    if (fluid.flowRate != null) {
      specs.add(_buildSpecItem('流量', '${fluid.flowRate}L/min'));
    }
    if (fluid.head != null) {
      specs.add(_buildSpecItem('扬程', '${fluid.head}m'));
    }
    if (fluid.pipeDiameter != null) {
      specs.add(_buildSpecItem('管径', '${fluid.pipeDiameter}mm'));
    }
    if (fluid.connectionType != null) {
      specs.add(_buildSpecItem('接口类型', fluid.connectionType!));
    }

    return specs;
  }

  /// 构建热力参数
  List<Widget> _buildThermalSpecs(ThermalSpecs thermal) {
    List<Widget> specs = [];
    
    if (thermal.workingTempRange != null) {
      specs.add(_buildSpecItem(
        '工作温度', 
        '${thermal.workingTempRange!.min}~${thermal.workingTempRange!.max}${thermal.workingTempRange!.unit}'
      ));
    }
    if (thermal.thermalConductivity != null) {
      specs.add(_buildSpecItem('导热系数', '${thermal.thermalConductivity}W/m·K'));
    }
    if (thermal.flameRetardantGrade != null) {
      specs.add(_buildSpecItem('阻燃等级', thermal.flameRetardantGrade!));
    }

    return specs;
  }

  /// 构建化学参数
  List<Widget> _buildChemicalSpecs(ChemicalSpecs chemical) {
    List<Widget> specs = [];
    
    if (chemical.material != null) {
      specs.add(_buildSpecItem('材质', chemical.material!));
    }
    if (chemical.corrosionResistance != null) {
      specs.add(_buildSpecItem('耐腐蚀性', chemical.corrosionResistance!));
    }

    return specs;
  }

  /// 构建物理属性
  Widget _buildPhysicalProperties(BuildContext context) {
    final props = specification.physicalProps;
    
    return _buildSection(
      context,
      title: '物理属性',
      icon: Icons.straighten,
      children: [
        _buildSpecItem(
          '尺寸', 
          '${props.dimensions.length}×${props.dimensions.width}×${props.dimensions.height}${props.dimensions.unit}'
        ),
        _buildSpecItem('重量', '${props.weight}kg'),
        if (props.color != null)
          _buildSpecItem('颜色', props.color!),
        if (props.material != null)
          _buildSpecItem('材质', props.material!),
        if (props.volume != null)
          _buildSpecItem('体积', '${props.volume}L'),
      ],
    );
  }

  /// 构建性能指标
  Widget _buildPerformanceMetrics(BuildContext context) {
    final metrics = specification.performanceMetrics!;
    
    return _buildSection(
      context,
      title: '性能指标',
      icon: Icons.speed,
      children: [
        if (metrics.lifespan != null)
          _buildSpecItem(
            '使用寿命', 
            '${metrics.lifespan}${metrics.lifespanUnit ?? '小时'}'
          ),
        if (metrics.cycleCount != null)
          _buildSpecItem('循环次数', '${metrics.cycleCount}次'),
        if (metrics.reliabilityGrade != null)
          _buildSpecItem('可靠性等级', metrics.reliabilityGrade!),
        if (metrics.performanceGrade != null)
          _buildSpecItem('性能等级', metrics.performanceGrade!),
      ],
    );
  }

  /// 构建认证信息
  Widget _buildCertifications(BuildContext context) {
    return _buildSection(
      context,
      title: '认证信息',
      icon: Icons.verified,
      children: specification.certifications!.map((cert) => 
        _buildSpecItem(cert.name, '${cert.authority} - ${cert.certificateNumber ?? ''}')
      ).toList(),
    );
  }

  /// 构建分组区域
  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    if (children.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  /// 构建规格项目
  Widget _buildSpecItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String category) {
    switch (category.toUpperCase()) {
      case 'ELECTRICAL':
        return Icons.electrical_services;
      case 'PLUMBING':
        return Icons.water_drop;
      case 'STORAGE':
        return Icons.inventory_2;
      case 'BEDDING':
        return Icons.bed;
      case 'KITCHEN':
        return Icons.kitchen;
      case 'BATHROOM':
        return Icons.bathroom;
      case 'EXTERIOR':
        return Icons.directions_car;
      case 'CHASSIS':
        return Icons.build;
      default:
        return Icons.category;
    }
  }

  /// 获取分类显示名称
  String _getCategoryDisplayName(String category) {
    switch (category.toUpperCase()) {
      case 'ELECTRICAL':
        return '电气设备';
      case 'PLUMBING':
        return '水路设备';
      case 'STORAGE':
        return '储物系统';
      case 'BEDDING':
        return '床铺系统';
      case 'KITCHEN':
        return '厨房系统';
      case 'BATHROOM':
        return '卫浴系统';
      case 'EXTERIOR':
        return '外观系统';
      case 'CHASSIS':
        return '底盘系统';
      default:
        return category;
    }
  }

  /// 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category.toUpperCase()) {
      case 'ELECTRICAL':
        return Colors.amber;
      case 'PLUMBING':
        return Colors.blue;
      case 'STORAGE':
        return Colors.green;
      case 'BEDDING':
        return Colors.purple;
      case 'KITCHEN':
        return Colors.orange;
      case 'BATHROOM':
        return Colors.cyan;
      case 'EXTERIOR':
        return Colors.red;
      case 'CHASSIS':
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }
}
