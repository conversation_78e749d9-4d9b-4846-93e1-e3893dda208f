import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 增强版富文本编辑器
class EnhancedRichTextEditor extends ConsumerStatefulWidget {
  final String? initialText;
  final Function(String)? onChanged;
  final Function(List<String>)? onMediaAdded;
  final String? hintText;
  final int? maxLines;
  final bool enabled;
  final bool showToolbar;
  final bool allowMediaUpload;
  final List<String> initialMediaIds;

  const EnhancedRichTextEditor({
    super.key,
    this.initialText,
    this.onChanged,
    this.onMediaAdded,
    this.hintText,
    this.maxLines,
    this.enabled = true,
    this.showToolbar = true,
    this.allowMediaUpload = true,
    this.initialMediaIds = const [],
  });

  @override
  ConsumerState<EnhancedRichTextEditor> createState() => _EnhancedRichTextEditorState();
}

class _EnhancedRichTextEditorState extends ConsumerState<EnhancedRichTextEditor>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _toolbarController;
  late Animation<double> _toolbarAnimation;
  
  bool _isPreviewMode = false;
  bool _isToolbarExpanded = false;
  final List<String> _mediaIds = [];
  final ImagePicker _imagePicker = ImagePicker();

  // 编辑器状态
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;
  String _currentFontSize = 'normal';
  String _currentAlignment = 'left';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText ?? '');
    _focusNode = FocusNode();
    _mediaIds.addAll(widget.initialMediaIds);
    
    _toolbarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _toolbarAnimation = CurvedAnimation(
      parent: _toolbarController,
      curve: Curves.easeInOut,
    );
    
    _controller.addListener(() {
      widget.onChanged?.call(_controller.text);
      _updateFormattingState();
    });
    
    _focusNode.addListener(() {
      if (_focusNode.hasFocus && widget.showToolbar) {
        _toolbarController.forward();
      } else {
        _toolbarController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _toolbarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 增强工具栏
        if (widget.showToolbar) _buildEnhancedToolbar(),
        
        // 编辑器容器
        _buildEditorContainer(),
        
        // 媒体预览区域
        if (_mediaIds.isNotEmpty) _buildMediaPreview(),
        
        // 状态栏
        _buildStatusBar(),
      ],
    );
  }

  Widget _buildEnhancedToolbar() {
    return AnimatedBuilder(
      animation: _toolbarAnimation,
      builder: (context, child) {
        return Container(
          height: _toolbarAnimation.value * (_isToolbarExpanded ? 120 : 60),
          margin: const EdgeInsets.only(bottom: VanHubSpacing.sm),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // 主工具栏
                _buildMainToolbar(),
                
                // 扩展工具栏
                if (_isToolbarExpanded) _buildExtendedToolbar(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMainToolbar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.sm),
      child: Row(
        children: [
          // 格式化按钮组
          _buildToolbarButtonGroup([
            _buildFormatButton(
              icon: Icons.format_bold,
              tooltip: '粗体 (Ctrl+B)',
              isActive: _isBold,
              onPressed: () => _toggleFormat('**', '**'),
            ),
            _buildFormatButton(
              icon: Icons.format_italic,
              tooltip: '斜体 (Ctrl+I)',
              isActive: _isItalic,
              onPressed: () => _toggleFormat('*', '*'),
            ),
            _buildFormatButton(
              icon: Icons.format_underlined,
              tooltip: '下划线',
              isActive: _isUnderline,
              onPressed: () => _toggleFormat('<u>', '</u>'),
            ),
          ]),
          
          const VerticalDivider(width: 1),
          
          // 列表按钮组
          _buildToolbarButtonGroup([
            _buildFormatButton(
              icon: Icons.format_list_bulleted,
              tooltip: '无序列表',
              onPressed: () => _insertList('- '),
            ),
            _buildFormatButton(
              icon: Icons.format_list_numbered,
              tooltip: '有序列表',
              onPressed: () => _insertList('1. '),
            ),
          ]),
          
          const VerticalDivider(width: 1),
          
          // 媒体按钮组
          if (widget.allowMediaUpload) ...[
            _buildToolbarButtonGroup([
              _buildFormatButton(
                icon: Icons.image,
                tooltip: '插入图片',
                onPressed: _insertImage,
              ),
              _buildFormatButton(
                icon: Icons.videocam,
                tooltip: '插入视频',
                onPressed: _insertVideo,
              ),
            ]),
            const VerticalDivider(width: 1),
          ],
          
          const Spacer(),
          
          // 模式切换和扩展按钮
          _buildFormatButton(
            icon: _isPreviewMode ? Icons.edit : Icons.preview,
            tooltip: _isPreviewMode ? '编辑模式' : '预览模式',
            onPressed: _togglePreviewMode,
          ),
          _buildFormatButton(
            icon: _isToolbarExpanded ? Icons.expand_less : Icons.expand_more,
            tooltip: '更多选项',
            onPressed: _toggleToolbarExpansion,
          ),
        ],
      ),
    );
  }

  Widget _buildExtendedToolbar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.sm),
      child: Row(
        children: [
          // 字体大小选择
          _buildFontSizeSelector(),
          
          const VerticalDivider(width: 1),
          
          // 对齐方式
          _buildToolbarButtonGroup([
            _buildFormatButton(
              icon: Icons.format_align_left,
              tooltip: '左对齐',
              isActive: _currentAlignment == 'left',
              onPressed: () => _setAlignment('left'),
            ),
            _buildFormatButton(
              icon: Icons.format_align_center,
              tooltip: '居中对齐',
              isActive: _currentAlignment == 'center',
              onPressed: () => _setAlignment('center'),
            ),
            _buildFormatButton(
              icon: Icons.format_align_right,
              tooltip: '右对齐',
              isActive: _currentAlignment == 'right',
              onPressed: () => _setAlignment('right'),
            ),
          ]),
          
          const VerticalDivider(width: 1),
          
          // 其他格式化选项
          _buildToolbarButtonGroup([
            _buildFormatButton(
              icon: Icons.format_strikethrough,
              tooltip: '删除线',
              onPressed: () => _toggleFormat('~~', '~~'),
            ),
            _buildFormatButton(
              icon: Icons.code,
              tooltip: '代码',
              onPressed: () => _toggleFormat('`', '`'),
            ),
            _buildFormatButton(
              icon: Icons.format_quote,
              tooltip: '引用',
              onPressed: () => _insertQuote(),
            ),
          ]),
          
          const Spacer(),
          
          // 清除格式按钮
          _buildFormatButton(
            icon: Icons.format_clear,
            tooltip: '清除格式',
            onPressed: _clearFormatting,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButtonGroup(List<Widget> buttons) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: buttons,
      ),
    );
  }

  Widget _buildFormatButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: isActive ? VanHubBrandColors.primary.withValues(alpha: 0.1) : null,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 18,
            color: isActive ? VanHubBrandColors.primary : Colors.grey.shade700,
          ),
        ),
      ),
    );
  }

  Widget _buildFontSizeSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<String>(
        value: _currentFontSize,
        underline: const SizedBox(),
        items: const [
          DropdownMenuItem(value: 'small', child: Text('小')),
          DropdownMenuItem(value: 'normal', child: Text('正常')),
          DropdownMenuItem(value: 'large', child: Text('大')),
          DropdownMenuItem(value: 'xlarge', child: Text('特大')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _currentFontSize = value;
            });
            _applyFontSize(value);
          }
        },
      ),
    );
  }

  Widget _buildEditorContainer() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 模式切换标签
          _buildModeToggle(),
          
          // 编辑器内容
          Container(
            constraints: BoxConstraints(
              minHeight: 200,
              maxHeight: widget.maxLines != null ? widget.maxLines! * 24.0 : 400,
            ),
            child: _isPreviewMode ? _buildEnhancedPreview() : _buildEnhancedEditor(),
          ),
        ],
      ),
    );
  }

  Widget _buildModeToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          _buildModeTab('编辑', !_isPreviewMode),
          _buildModeTab('预览', _isPreviewMode),
          const Spacer(),
          // 字数统计
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.sm),
            child: Text(
              '${_controller.text.length} 字符',
              style: VanHubTypography.bodySmall.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeTab(String title, bool isActive) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isPreviewMode = title == '预览';
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md, vertical: VanHubSpacing.sm),
        decoration: BoxDecoration(
          color: isActive ? Colors.white : Colors.transparent,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          border: isActive ? Border.all(color: Colors.grey.shade300) : null,
        ),
        child: Text(
          title,
          style: VanHubTypography.bodyMedium.copyWith(
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            color: isActive ? VanHubBrandColors.primary : Colors.grey.shade600,
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedEditor() {
    return Padding(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top,
        decoration: InputDecoration(
          hintText: widget.hintText ?? '开始编写你的改装日志...\n\n支持丰富的格式化选项和媒体插入',
          border: InputBorder.none,
          hintStyle: VanHubTypography.bodyMedium.copyWith(
            color: Colors.grey.shade500,
          ),
        ),
        style: VanHubTypography.bodyMedium.copyWith(
          height: 1.6,
        ),
        onChanged: (text) {
          widget.onChanged?.call(text);
        },
      ),
    );
  }

  Widget _buildEnhancedPreview() {
    return Padding(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      child: SingleChildScrollView(
        child: _buildMarkdownPreview(_controller.text),
      ),
    );
  }

  Widget _buildMarkdownPreview(String text) {
    // TODO: 实现更完整的Markdown渲染
    return Text(
      text.isEmpty ? '预览内容将在这里显示...' : text,
      style: VanHubTypography.bodyMedium.copyWith(
        height: 1.6,
      ),
    );
  }

  Widget _buildMediaPreview() {
    return Container(
      margin: const EdgeInsets.only(top: VanHubSpacing.sm),
      padding: const EdgeInsets.all(VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '已添加的媒体文件 (${_mediaIds.length})',
            style: VanHubTypography.titleSmall,
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Wrap(
            spacing: VanHubSpacing.sm,
            runSpacing: VanHubSpacing.sm,
            children: _mediaIds.map((mediaId) => _buildMediaChip(mediaId)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaChip(String mediaId) {
    return Chip(
      label: Text('媒体 $mediaId'),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: () {
        setState(() {
          _mediaIds.remove(mediaId);
        });
        widget.onMediaAdded?.call(_mediaIds);
      },
    );
  }

  Widget _buildStatusBar() {
    return Container(
      margin: const EdgeInsets.only(top: VanHubSpacing.xs),
      child: Row(
        children: [
          Text(
            '支持Markdown语法和富文本格式',
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const Spacer(),
          if (_mediaIds.isNotEmpty)
            Text(
              '${_mediaIds.length} 个媒体文件',
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubBrandColors.primary,
              ),
            ),
        ],
      ),
    );
  }

  // 格式化方法
  void _toggleFormat(String before, String after) {
    final selection = _controller.selection;
    final text = _controller.text;
    
    if (selection.isValid && !selection.isCollapsed) {
      final selectedText = selection.textInside(text);
      final newText = before + selectedText + after;
      
      _controller.text = text.replaceRange(selection.start, selection.end, newText);
      _controller.selection = TextSelection.collapsed(
        offset: selection.start + before.length + selectedText.length + after.length,
      );
    } else {
      final cursorPos = selection.baseOffset;
      final newText = before + after;
      
      _controller.text = text.replaceRange(cursorPos, cursorPos, newText);
      _controller.selection = TextSelection.collapsed(
        offset: cursorPos + before.length,
      );
    }
    
    _focusNode.requestFocus();
  }

  void _insertList(String prefix) {
    final selection = _controller.selection;
    final text = _controller.text;
    final cursorPos = selection.baseOffset;
    
    // 找到当前行的开始
    int lineStart = text.lastIndexOf('\n', cursorPos - 1) + 1;
    
    _controller.text = text.replaceRange(lineStart, lineStart, prefix);
    _controller.selection = TextSelection.collapsed(
      offset: cursorPos + prefix.length,
    );
    
    _focusNode.requestFocus();
  }

  void _insertQuote() {
    _insertList('> ');
  }

  void _setAlignment(String alignment) {
    setState(() {
      _currentAlignment = alignment;
    });
    // TODO: 实现对齐功能
  }

  void _applyFontSize(String size) {
    // TODO: 实现字体大小功能
  }

  void _clearFormatting() {
    // TODO: 实现清除格式功能
  }

  void _togglePreviewMode() {
    setState(() {
      _isPreviewMode = !_isPreviewMode;
    });
  }

  void _toggleToolbarExpansion() {
    setState(() {
      _isToolbarExpanded = !_isToolbarExpanded;
    });
  }

  void _updateFormattingState() {
    // TODO: 根据光标位置更新格式化状态
  }

  Future<void> _insertImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        // TODO: 上传图片并获取ID
        final mediaId = 'img_${DateTime.now().millisecondsSinceEpoch}';
        setState(() {
          _mediaIds.add(mediaId);
        });
        widget.onMediaAdded?.call(_mediaIds);
        
        // 在编辑器中插入图片标记
        _toggleFormat('![图片描述](', ')');
      }
    } catch (e) {
      // 处理错误
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('插入图片失败: $e')),
      );
    }
  }

  Future<void> _insertVideo() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        // TODO: 上传视频并获取ID
        final mediaId = 'vid_${DateTime.now().millisecondsSinceEpoch}';
        setState(() {
          _mediaIds.add(mediaId);
        });
        widget.onMediaAdded?.call(_mediaIds);
        
        // 在编辑器中插入视频标记
        _toggleFormat('[视频描述](', ')');
      }
    } catch (e) {
      // 处理错误
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('插入视频失败: $e')),
      );
    }
  }
}
