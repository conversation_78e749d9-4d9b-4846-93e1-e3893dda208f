import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/entities/log_media.dart';
import '../../domain/entities/enums.dart';

/// 上传进度信息
class UploadProgress {
  final String fileName;
  final double progress;
  final String status;
  final bool isCompleted;
  final String? error;

  const UploadProgress({
    required this.fileName,
    required this.progress,
    required this.status,
    required this.isCompleted,
    this.error,
  });
}

/// 增强的媒体上传组件
class EnhancedMediaUploadWidget extends ConsumerStatefulWidget {
  final List<LogMedia> initialMedia;
  final Function(List<LogMedia>) onMediaChanged;
  final Function(File, MediaType)? onFileSelected;
  final bool isEditable;
  final int maxMediaCount;
  final List<MediaType> allowedTypes;
  final bool enableDragDrop;
  final bool showProgress;
  final bool enablePreview;
  final bool enableReorder;

  const EnhancedMediaUploadWidget({
    super.key,
    this.initialMedia = const [],
    required this.onMediaChanged,
    this.onFileSelected,
    this.isEditable = true,
    this.maxMediaCount = 10,
    this.allowedTypes = const [MediaType.image, MediaType.video],
    this.enableDragDrop = true,
    this.showProgress = true,
    this.enablePreview = true,
    this.enableReorder = true,
  });

  @override
  ConsumerState<EnhancedMediaUploadWidget> createState() => _EnhancedMediaUploadWidgetState();
}

class _EnhancedMediaUploadWidgetState extends ConsumerState<EnhancedMediaUploadWidget>
    with TickerProviderStateMixin {
  final ImagePicker _imagePicker = ImagePicker();
  List<LogMedia> _mediaList = [];
  final Map<String, UploadProgress> _uploadProgress = {};
  bool _isDragOver = false;
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();
    _mediaList = List.from(widget.initialMedia);
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: VanHubSpacing.md),
        if (widget.enableDragDrop && widget.isEditable)
          _buildDragDropArea(),
        const SizedBox(height: VanHubSpacing.md),
        _buildMediaGrid(),
        if (_uploadProgress.isNotEmpty) ...[
          const SizedBox(height: VanHubSpacing.md),
          _buildProgressSection(),
        ],
        if (widget.isEditable && _mediaList.length < widget.maxMediaCount) ...[
          const SizedBox(height: VanHubSpacing.md),
          _buildUploadButtons(),
        ],
      ],
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.perm_media,
          color: VanHubBrandColors.primary,
          size: 24,
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '媒体文件',
                style: VanHubTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '支持图片、视频上传，最多${widget.maxMediaCount}个文件',
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        if (_mediaList.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: VanHubSpacing.sm,
              vertical: VanHubSpacing.xs,
            ),
            decoration: BoxDecoration(
              color: VanHubBrandColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_mediaList.length}/${widget.maxMediaCount}',
              style: VanHubTypography.bodySmall.copyWith(
                color: VanHubBrandColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建拖拽上传区域
  Widget _buildDragDropArea() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: DragTarget<List<File>>(
        onWillAcceptWithDetails: (data) => widget.isEditable,
        onAcceptWithDetails: (files) {
          setState(() {
            _isDragOver = false;
          });
          _handleMultipleFiles(files);
        },
        onMove: (details) {
          if (!_isDragOver) {
            setState(() {
              _isDragOver = true;
            });
          }
        },
        onLeave: (data) {
          setState(() {
            _isDragOver = false;
          });
        },
        builder: (context, candidateData, rejectedData) {
          return AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: _isDragOver ? 1.02 : 1.0,
                child: DottedBorder(
                  borderType: BorderType.RRect,
                  radius: const Radius.circular(12),
                  dashPattern: const [8, 4],
                  strokeWidth: 2,
                  color: _isDragOver 
                      ? VanHubBrandColors.primary
                      : Colors.grey.shade400,
                  child: Container(
                    width: double.infinity,
                    height: 120,
                    decoration: BoxDecoration(
                      color: _isDragOver 
                          ? VanHubBrandColors.primary.withValues(alpha: 0.05)
                          : Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AnimatedBuilder(
                          animation: _pulseController,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _isDragOver 
                                  ? 1.0 + (_pulseController.value * 0.1)
                                  : 1.0,
                              child: Icon(
                                Icons.cloud_upload,
                                size: 40,
                                color: _isDragOver 
                                    ? VanHubBrandColors.primary
                                    : Colors.grey.shade400,
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: VanHubSpacing.sm),
                        Text(
                          _isDragOver ? '释放文件以上传' : '拖拽文件到此处上传',
                          style: VanHubTypography.bodyMedium.copyWith(
                            color: _isDragOver 
                                ? VanHubBrandColors.primary
                                : Colors.grey.shade600,
                            fontWeight: _isDragOver ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                        const SizedBox(height: VanHubSpacing.xs),
                        Text(
                          '或点击下方按钮选择文件',
                          style: VanHubTypography.bodySmall.copyWith(
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  /// 构建媒体网格
  Widget _buildMediaGrid() {
    if (_mediaList.isEmpty) {
      return _buildEmptyState();
    }

    return ReorderableListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      onReorder: widget.enableReorder ? _onReorder : null,
      itemCount: _mediaList.length,
      itemBuilder: (context, index) {
        final media = _mediaList[index];
        return _buildMediaItem(media, index);
      },
    );
  }

  /// 构建媒体项
  Widget _buildMediaItem(LogMedia media, int index) {
    return Container(
      key: ValueKey(media.id),
      margin: const EdgeInsets.only(bottom: VanHubSpacing.sm),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: widget.enablePreview ? () => _showPreview(media) : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(VanHubSpacing.sm),
            child: Row(
              children: [
                _buildMediaThumbnail(media),
                const SizedBox(width: VanHubSpacing.md),
                Expanded(
                  child: _buildMediaInfo(media),
                ),
                if (widget.isEditable) ...[
                  const SizedBox(width: VanHubSpacing.sm),
                  _buildMediaActions(media, index),
                ],
              ],
            ),
          ),
        ),
      ),
    ).animate(delay: (index * 100).ms)
        .fadeIn(duration: 300.ms)
        .slideX(begin: 0.2, end: 0);
  }

  /// 构建媒体缩略图
  Widget _buildMediaThumbnail(LogMedia media) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade200,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: _buildThumbnailContent(media),
      ),
    );
  }

  /// 构建缩略图内容
  Widget _buildThumbnailContent(LogMedia media) {
    if (media.type == MediaType.image) {
      if (media.url.startsWith('http')) {
        return CachedNetworkImage(
          imageUrl: media.url,
          fit: BoxFit.cover,
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        );
      } else {
        return Image.file(
          File(media.url),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => const Icon(Icons.error),
        );
      }
    } else if (media.type == MediaType.video) {
      return Stack(
        alignment: Alignment.center,
        children: [
          Container(
            color: Colors.black26,
            child: const Icon(
              Icons.videocam,
              color: Colors.white,
              size: 24,
            ),
          ),
          const Positioned(
            bottom: 4,
            right: 4,
            child: Icon(
              Icons.play_circle_filled,
              color: Colors.white,
              size: 16,
            ),
          ),
        ],
      );
    } else {
      return const Icon(Icons.insert_drive_file);
    }
  }

  /// 构建媒体信息
  Widget _buildMediaInfo(LogMedia media) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          media.filename,
          style: VanHubTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Text(
          _getMediaTypeText(media.type),
          style: VanHubTypography.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
        ),
        if (media.caption?.isNotEmpty == true) ...[
          const SizedBox(height: 2),
          Text(
            media.caption!,
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey[500],
              fontStyle: FontStyle.italic,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 构建媒体操作
  Widget _buildMediaActions(LogMedia media, int index) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: () => _editCaption(media, index),
          icon: const Icon(Icons.edit_note),
          iconSize: 20,
          tooltip: '编辑说明',
        ),
        IconButton(
          onPressed: () => _removeMedia(index),
          icon: const Icon(Icons.delete),
          iconSize: 20,
          color: Colors.red,
          tooltip: '删除',
        ),
      ],
    );
  }

  /// 构建进度部分
  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '上传进度',
          style: VanHubTypography.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: VanHubSpacing.sm),
        ..._uploadProgress.entries.map((entry) {
          final progress = entry.value;
          return Container(
            margin: const EdgeInsets.only(bottom: VanHubSpacing.sm),
            padding: const EdgeInsets.all(VanHubSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        progress.fileName,
                        style: VanHubTypography.bodySmall.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      '${(progress.progress * 100).toInt()}%',
                      style: VanHubTypography.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: VanHubSpacing.xs),
                LinearProgressIndicator(
                  value: progress.progress,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progress.error != null 
                        ? Colors.red 
                        : VanHubBrandColors.primary,
                  ),
                ),
                if (progress.error != null) ...[
                  const SizedBox(height: VanHubSpacing.xs),
                  Text(
                    progress.error!,
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.red,
                    ),
                  ),
                ],
              ],
            ),
          );
        }),
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(VanHubSpacing.xl),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: VanHubSpacing.md),
          Text(
            '还没有添加媒体文件',
            style: VanHubTypography.bodyLarge.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: VanHubSpacing.xs),
          Text(
            '点击下方按钮或拖拽文件到上方区域来添加',
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建上传按钮
  Widget _buildUploadButtons() {
    return Row(
      children: [
        if (widget.allowedTypes.contains(MediaType.image)) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.photo),
              label: const Text('选择图片'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubBrandColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: VanHubSpacing.sm),
        ],
        if (widget.allowedTypes.contains(MediaType.video)) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _pickVideo,
              icon: const Icon(Icons.videocam),
              label: const Text('选择视频'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 选择图片
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        await _addMedia(File(image.path), MediaType.image);
      }
    } catch (e) {
      _showError('选择图片失败: $e');
    }
  }

  /// 选择视频
  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5),
      );

      if (video != null) {
        await _addMedia(File(video.path), MediaType.video);
      }
    } catch (e) {
      _showError('选择视频失败: $e');
    }
  }

  /// 添加媒体
  Future<void> _addMedia(File file, MediaType type) async {
    final fileName = file.path.split('/').last;
    
    // 添加上传进度
    setState(() {
      _uploadProgress[fileName] = const UploadProgress(
        fileName: '',
        progress: 0.0,
        status: '准备上传...',
        isCompleted: false,
      );
    });

    try {
      // 模拟上传进度
      for (int i = 0; i <= 100; i += 20) {
        await Future.delayed(const Duration(milliseconds: 200));
        setState(() {
          _uploadProgress[fileName] = UploadProgress(
            fileName: fileName,
            progress: i / 100,
            status: i == 100 ? '上传完成' : '上传中...',
            isCompleted: i == 100,
          );
        });
      }

      // 创建媒体对象
      final media = LogMedia(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        logId: '',
        type: type,
        url: file.path,
        filename: fileName,
        caption: '',
        sortOrder: _mediaList.length,
        uploadedAt: DateTime.now(),
        uploadedBy: '',
      );

      setState(() {
        _mediaList.add(media);
        _uploadProgress.remove(fileName);
      });

      widget.onMediaChanged(_mediaList);
      widget.onFileSelected?.call(file, type);
    } catch (e) {
      setState(() {
        _uploadProgress[fileName] = UploadProgress(
          fileName: fileName,
          progress: 0.0,
          status: '上传失败',
          isCompleted: false,
          error: e.toString(),
        );
      });
    }
  }

  /// 处理多个文件
  Future<void> _handleMultipleFiles(List<File> files) async {
    for (final file in files) {
      final extension = file.path.split('.').last.toLowerCase();
      MediaType? type;
      
      if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(extension)) {
        type = MediaType.image;
      } else if (['mp4', 'mov', 'avi'].contains(extension)) {
        type = MediaType.video;
      }
      
      if (type != null && widget.allowedTypes.contains(type)) {
        await _addMedia(file, type);
      }
    }
  }

  /// 重新排序
  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _mediaList.removeAt(oldIndex);
      _mediaList.insert(newIndex, item);
      
      // 更新排序
      for (int i = 0; i < _mediaList.length; i++) {
        _mediaList[i] = _mediaList[i].copyWith(sortOrder: i);
      }
    });
    
    widget.onMediaChanged(_mediaList);
  }

  /// 删除媒体
  void _removeMedia(int index) {
    setState(() {
      _mediaList.removeAt(index);
    });
    widget.onMediaChanged(_mediaList);
  }

  /// 编辑说明
  void _editCaption(LogMedia media, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑说明'),
        content: TextField(
          controller: TextEditingController(text: media.caption),
          decoration: const InputDecoration(
            hintText: '输入图片或视频说明...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          onChanged: (value) {
            setState(() {
              _mediaList[index] = media.copyWith(caption: value);
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onMediaChanged(_mediaList);
              Navigator.of(context).pop();
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 显示预览
  void _showPreview(LogMedia media) {
    // TODO: 实现媒体预览功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('预览功能开发中: ${media.filename}'),
      ),
    );
  }

  /// 显示错误
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// 获取媒体类型文本
  String _getMediaTypeText(MediaType type) {
    switch (type) {
      case MediaType.image:
        return '图片';
      case MediaType.video:
        return '视频';
      case MediaType.document:
        return '文档';
      default:
        return '文件';
    }
  }
}
