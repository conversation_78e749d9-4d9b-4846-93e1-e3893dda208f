import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_category.freezed.dart';
part 'settings_category.g.dart';

/// 设置项类型
enum SettingItemType {
  toggle,       // 开关
  dropdown,     // 下拉选择
  slider,       // 滑块
  textInput,    // 文本输入
  numberInput,  // 数字输入
  colorPicker,  // 颜色选择
  action,       // 操作按钮
  navigation,   // 导航项
  info,         // 信息显示
}

/// 设置项实体
@freezed
class SettingItem with _$SettingItem {
  const factory SettingItem({
    required String id,
    required String title,
    String? subtitle,
    String? description,
    required SettingItemType type,
    String? iconName,
    int? iconColor,
    @Default(true) bool isEnabled,
    @Default(false) bool isVisible,
    @Default(false) bool requiresRestart,
    @Default(false) bool isPremiumFeature,
    
    // 值相关
    dynamic defaultValue,
    dynamic currentValue,
    dynamic minValue,
    dynamic maxValue,
    List<dynamic>? options,
    Map<String, String>? optionLabels,
    
    // 验证相关
    String? validationPattern,
    String? validationMessage,
    List<String>? dependencies,
    
    // UI相关
    String? unit,
    int? decimalPlaces,
    @Default({}) Map<String, dynamic> metadata,
  }) = _SettingItem;

  factory SettingItem.fromJson(Map<String, dynamic> json) => 
      _$SettingItemFromJson(json);
}

/// 设置分类实体
@freezed
class SettingsCategory with _$SettingsCategory {
  const factory SettingsCategory({
    required String id,
    required String title,
    String? subtitle,
    String? description,
    String? iconName,
    int? iconColor,
    @Default(0) int order,
    @Default(true) bool isVisible,
    @Default(false) bool isPremiumCategory,
    required List<SettingItem> items,
    @Default({}) Map<String, dynamic> metadata,
  }) = _SettingsCategory;

  factory SettingsCategory.fromJson(Map<String, dynamic> json) => 
      _$SettingsCategoryFromJson(json);
}

/// 设置项扩展方法
extension SettingItemX on SettingItem {
  /// 是否为布尔类型
  bool get isBooleanType => type == SettingItemType.toggle;

  /// 是否为数值类型
  bool get isNumericType => type == SettingItemType.slider || type == SettingItemType.numberInput;

  /// 是否为选择类型
  bool get isSelectionType => type == SettingItemType.dropdown;

  /// 是否为文本类型
  bool get isTextType => type == SettingItemType.textInput;

  /// 是否为操作类型
  bool get isActionType => type == SettingItemType.action || type == SettingItemType.navigation;

  /// 获取显示值
  String get displayValue {
    if (currentValue == null) return '';
    
    switch (type) {
      case SettingItemType.toggle:
        return (currentValue as bool) ? '开启' : '关闭';
      
      case SettingItemType.dropdown:
        if (optionLabels != null && optionLabels!.containsKey(currentValue.toString())) {
          return optionLabels![currentValue.toString()]!;
        }
        return currentValue.toString();
      
      case SettingItemType.slider:
      case SettingItemType.numberInput:
        final value = currentValue as num;
        if (decimalPlaces != null && decimalPlaces! > 0) {
          return value.toStringAsFixed(decimalPlaces!);
        }
        return value.toString();
      
      case SettingItemType.colorPicker:
        return '#${(currentValue as int).toRadixString(16).padLeft(8, '0').toUpperCase()}';
      
      default:
        return currentValue.toString();
    }
  }

  /// 获取带单位的显示值
  String get displayValueWithUnit {
    final value = displayValue;
    return unit != null ? '$value $unit' : value;
  }

  /// 验证当前值
  bool isValidValue(dynamic value) {
    if (value == null && defaultValue != null) return false;
    
    switch (type) {
      case SettingItemType.toggle:
        return value is bool;
      
      case SettingItemType.dropdown:
        return options?.contains(value) ?? false;
      
      case SettingItemType.slider:
      case SettingItemType.numberInput:
        if (value is! num) return false;
        if (minValue != null && value < minValue) return false;
        if (maxValue != null && value > maxValue) return false;
        return true;
      
      case SettingItemType.textInput:
        if (value is! String) return false;
        if (validationPattern != null) {
          final regex = RegExp(validationPattern!);
          return regex.hasMatch(value);
        }
        return true;
      
      case SettingItemType.colorPicker:
        return value is int && value >= 0 && value <= 0xFFFFFFFF;
      
      default:
        return true;
    }
  }

  /// 获取验证错误信息
  String? getValidationError(dynamic value) {
    if (isValidValue(value)) return null;
    
    if (validationMessage != null) {
      return validationMessage!;
    }
    
    switch (type) {
      case SettingItemType.toggle:
        return '请选择开启或关闭';
      
      case SettingItemType.dropdown:
        return '请选择有效的选项';
      
      case SettingItemType.slider:
      case SettingItemType.numberInput:
        if (minValue != null && maxValue != null) {
          return '请输入 $minValue 到 $maxValue 之间的数值';
        } else if (minValue != null) {
          return '请输入大于等于 $minValue 的数值';
        } else if (maxValue != null) {
          return '请输入小于等于 $maxValue 的数值';
        }
        return '请输入有效的数值';
      
      case SettingItemType.textInput:
        return '请输入有效的文本';
      
      case SettingItemType.colorPicker:
        return '请选择有效的颜色';
      
      default:
        return '输入值无效';
    }
  }

  /// 创建开关设置项
  factory SettingItem.toggle({
    required String id,
    required String title,
    String? subtitle,
    String? iconName,
    bool defaultValue = false,
    bool? currentValue,
    bool requiresRestart = false,
  }) {
    return SettingItem(
      id: id,
      title: title,
      subtitle: subtitle,
      iconName: iconName,
      type: SettingItemType.toggle,
      defaultValue: defaultValue,
      currentValue: currentValue ?? defaultValue,
      requiresRestart: requiresRestart,
      isVisible: true,
    );
  }

  /// 创建下拉选择设置项
  factory SettingItem.dropdown({
    required String id,
    required String title,
    String? subtitle,
    String? iconName,
    required List<dynamic> options,
    Map<String, String>? optionLabels,
    dynamic defaultValue,
    dynamic currentValue,
  }) {
    return SettingItem(
      id: id,
      title: title,
      subtitle: subtitle,
      iconName: iconName,
      type: SettingItemType.dropdown,
      options: options,
      optionLabels: optionLabels,
      defaultValue: defaultValue ?? options.first,
      currentValue: currentValue ?? defaultValue ?? options.first,
      isVisible: true,
    );
  }

  /// 创建滑块设置项
  factory SettingItem.slider({
    required String id,
    required String title,
    String? subtitle,
    String? iconName,
    required double minValue,
    required double maxValue,
    double defaultValue = 0.0,
    double? currentValue,
    String? unit,
    int decimalPlaces = 0,
  }) {
    return SettingItem(
      id: id,
      title: title,
      subtitle: subtitle,
      iconName: iconName,
      type: SettingItemType.slider,
      minValue: minValue,
      maxValue: maxValue,
      defaultValue: defaultValue,
      currentValue: currentValue ?? defaultValue,
      unit: unit,
      decimalPlaces: decimalPlaces,
      isVisible: true,
    );
  }

  /// 创建操作设置项
  factory SettingItem.action({
    required String id,
    required String title,
    String? subtitle,
    String? iconName,
    bool isPremiumFeature = false,
  }) {
    return SettingItem(
      id: id,
      title: title,
      subtitle: subtitle,
      iconName: iconName,
      type: SettingItemType.action,
      isPremiumFeature: isPremiumFeature,
      isVisible: true,
    );
  }
}

/// 设置分类扩展方法
extension SettingsCategoryX on SettingsCategory {
  /// 获取可见的设置项
  List<SettingItem> get visibleItems {
    return items.where((item) => item.isVisible).toList();
  }

  /// 获取启用的设置项
  List<SettingItem> get enabledItems {
    return items.where((item) => item.isEnabled).toList();
  }

  /// 获取需要重启的设置项
  List<SettingItem> get restartRequiredItems {
    return items.where((item) => item.requiresRestart).toList();
  }

  /// 获取高级功能设置项
  List<SettingItem> get premiumItems {
    return items.where((item) => item.isPremiumFeature).toList();
  }

  /// 根据ID查找设置项
  SettingItem? findItemById(String itemId) {
    try {
      return items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  /// 更新设置项值
  SettingsCategory updateItemValue(String itemId, dynamic value) {
    final updatedItems = items.map((item) {
      if (item.id == itemId) {
        return item.copyWith(currentValue: value);
      }
      return item;
    }).toList();
    
    return copyWith(items: updatedItems);
  }

  /// 验证所有设置项
  Map<String, String> validateAllItems() {
    final errors = <String, String>{};
    
    for (final item in items) {
      final error = item.getValidationError(item.currentValue);
      if (error != null) {
        errors[item.id] = error;
      }
    }
    
    return errors;
  }

  /// 获取已更改的设置项
  List<SettingItem> getChangedItems() {
    return items.where((item) => item.currentValue != item.defaultValue).toList();
  }

  /// 重置所有设置项为默认值
  SettingsCategory resetToDefaults() {
    final resetItems = items.map((item) {
      return item.copyWith(currentValue: item.defaultValue);
    }).toList();
    
    return copyWith(items: resetItems);
  }

  /// 创建外观设置分类
  factory SettingsCategory.appearance() {
    return SettingsCategory(
      id: 'appearance',
      title: '外观设置',
      subtitle: '主题、语言和显示选项',
      iconName: 'palette',
      order: 1,
      isVisible: true,
      items: [
        SettingItem.dropdown(
          id: 'theme_mode',
          title: '主题模式',
          subtitle: '选择应用主题',
          iconName: 'brightness_6',
          options: ['light', 'dark', 'system'],
          optionLabels: {
            'light': '浅色模式',
            'dark': '深色模式',
            'system': '跟随系统',
          },
          defaultValue: 'system',
        ),
        SettingItem.dropdown(
          id: 'language',
          title: '语言设置',
          subtitle: '选择应用语言',
          iconName: 'language',
          options: ['chinese', 'english'],
          optionLabels: {
            'chinese': '中文',
            'english': 'English',
          },
          defaultValue: 'chinese',
        ),
        SettingItem.slider(
          id: 'text_scale_factor',
          title: '文字大小',
          subtitle: '调整应用文字大小',
          iconName: 'text_fields',
          minValue: 0.8,
          maxValue: 2.0,
          defaultValue: 1.0,
          unit: 'x',
          decimalPlaces: 1,
        ),
        SettingItem.toggle(
          id: 'enable_animations',
          title: '动画效果',
          subtitle: '启用界面动画和过渡效果',
          iconName: 'animation',
          defaultValue: true,
        ),
      ],
    );
  }

  /// 创建通知设置分类
  factory SettingsCategory.notifications() {
    return SettingsCategory(
      id: 'notifications',
      title: '通知设置',
      subtitle: '推送通知和提醒选项',
      iconName: 'notifications',
      order: 2,
      isVisible: true,
      items: [
        SettingItem.toggle(
          id: 'enable_push_notifications',
          title: '推送通知',
          subtitle: '接收应用推送消息',
          iconName: 'notifications_active',
          defaultValue: true,
        ),
        SettingItem.toggle(
          id: 'enable_sound_notifications',
          title: '通知声音',
          subtitle: '播放通知提示音',
          iconName: 'volume_up',
          defaultValue: true,
        ),
        SettingItem.toggle(
          id: 'enable_vibration_notifications',
          title: '振动提醒',
          subtitle: '接收通知时振动',
          iconName: 'vibration',
          defaultValue: true,
        ),
        SettingItem.toggle(
          id: 'enable_project_notifications',
          title: '项目通知',
          subtitle: '项目相关的通知提醒',
          iconName: 'folder',
          defaultValue: true,
        ),
      ],
    );
  }

  /// 创建数据同步设置分类
  factory SettingsCategory.dataSync() {
    return SettingsCategory(
      id: 'data_sync',
      title: '数据同步',
      subtitle: '云端同步和备份设置',
      iconName: 'sync',
      order: 3,
      isVisible: true,
      items: [
        SettingItem.toggle(
          id: 'enable_cloud_sync',
          title: '云端同步',
          subtitle: '自动同步数据到云端',
          iconName: 'cloud_sync',
          defaultValue: true,
        ),
        SettingItem.dropdown(
          id: 'sync_frequency',
          title: '同步频率',
          subtitle: '设置自动同步的频率',
          iconName: 'schedule',
          options: ['realtime', 'hourly', 'daily', 'manual'],
          optionLabels: {
            'realtime': '实时同步',
            'hourly': '每小时',
            'daily': '每天',
            'manual': '手动同步',
          },
          defaultValue: 'realtime',
        ),
        SettingItem.toggle(
          id: 'sync_on_wifi_only',
          title: '仅WiFi同步',
          subtitle: '只在WiFi网络下进行同步',
          iconName: 'wifi',
          defaultValue: true,
        ),
        SettingItem.toggle(
          id: 'enable_auto_backup',
          title: '自动备份',
          subtitle: '定期自动备份数据',
          iconName: 'backup',
          defaultValue: true,
        ),
      ],
    );
  }

  /// 创建隐私安全设置分类
  factory SettingsCategory.privacy() {
    return SettingsCategory(
      id: 'privacy',
      title: '隐私安全',
      subtitle: '数据隐私和安全选项',
      iconName: 'security',
      order: 4,
      isVisible: true,
      items: [
        SettingItem.toggle(
          id: 'enable_analytics',
          title: '使用统计',
          subtitle: '帮助改进应用体验',
          iconName: 'analytics',
          defaultValue: true,
        ),
        SettingItem.toggle(
          id: 'enable_crash_reporting',
          title: '崩溃报告',
          subtitle: '自动发送崩溃报告',
          iconName: 'bug_report',
          defaultValue: false,
        ),
        SettingItem.toggle(
          id: 'enable_biometric_auth',
          title: '生物识别',
          subtitle: '使用指纹或面部识别',
          iconName: 'fingerprint',
          defaultValue: false,
        ),
        SettingItem.slider(
          id: 'auto_lock_minutes',
          title: '自动锁定',
          subtitle: '应用自动锁定时间',
          iconName: 'lock_clock',
          minValue: 1,
          maxValue: 60,
          defaultValue: 5,
          unit: '分钟',
        ),
      ],
    );
  }

  /// 获取所有默认分类
  static List<SettingsCategory> getDefaultCategories() {
    return [
      SettingsCategory.appearance(),
      SettingsCategory.notifications(),
      SettingsCategory.dataSync(),
      SettingsCategory.privacy(),
    ];
  }
}
