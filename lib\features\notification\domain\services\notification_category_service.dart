import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/notification.dart';
import '../entities/notification_category.dart';

/// 通知分类服务接口
abstract class NotificationCategoryService {
  /// 获取所有通知分类
  Future<Either<Failure, List<NotificationCategory>>> getAllCategories();

  /// 获取用户启用的分类
  Future<Either<Failure, List<NotificationCategory>>> getEnabledCategories(String userId);

  /// 创建自定义分类
  Future<Either<Failure, NotificationCategory>> createCustomCategory({
    required String userId,
    required String name,
    required String description,
    String? iconName,
    int? color,
    NotificationPriority defaultPriority = NotificationPriority.normal,
  });

  /// 更新分类设置
  Future<Either<Failure, NotificationCategory>> updateCategory(NotificationCategory category);

  /// 删除自定义分类
  Future<Either<Failure, void>> deleteCustomCategory(String categoryId);

  /// 启用/禁用分类
  Future<Either<Failure, void>> toggleCategory({
    required String userId,
    required String categoryId,
    required bool enabled,
  });

  /// 设置分类优先级
  Future<Either<Failure, void>> setCategoryPriority({
    required String userId,
    required String categoryId,
    required NotificationPriority priority,
  });

  /// 获取分类统计信息
  Future<Either<Failure, Map<String, int>>> getCategoryStats(String userId);

  /// 按分类筛选通知
  Future<Either<Failure, List<Notification>>> getNotificationsByCategory({
    required String userId,
    required String categoryId,
    int limit = 50,
    int offset = 0,
  });

  /// 自动分类通知
  Future<Either<Failure, NotificationCategory>> categorizeNotification(Notification notification);

  /// 批量分类通知
  Future<Either<Failure, Map<String, List<Notification>>>> categorizeNotifications(
    List<Notification> notifications,
  );

  /// 获取分类规则
  Future<Either<Failure, List<NotificationRule>>> getCategoryRules(String categoryId);

  /// 创建分类规则
  Future<Either<Failure, NotificationRule>> createCategoryRule({
    required String categoryId,
    required String name,
    required Map<String, dynamic> conditions,
    required List<String> actions,
  });

  /// 更新分类规则
  Future<Either<Failure, NotificationRule>> updateCategoryRule(NotificationRule rule);

  /// 删除分类规则
  Future<Either<Failure, void>> deleteCategoryRule(String ruleId);

  /// 应用分类规则
  Future<Either<Failure, List<Notification>>> applyRules(List<Notification> notifications);
}
