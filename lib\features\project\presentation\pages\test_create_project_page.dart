import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/create_project_dialog_widget.dart';
import '../providers/project_provider.dart';
import '../../domain/entities/create_project_request.dart';
import '../../../../core/providers/auth_state_provider.dart';

/// 测试创建项目功能的页面
class TestCreateProjectPage extends ConsumerStatefulWidget {
  const TestCreateProjectPage({super.key});

  @override
  ConsumerState<TestCreateProjectPage> createState() => _TestCreateProjectPageState();
}

class _TestCreateProjectPageState extends ConsumerState<TestCreateProjectPage> {
  String? _lastResult;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final currentUserId = ref.watch(currentUserIdProvider);
    final projectController = ref.watch(projectControllerProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('测试创建项目'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '用户状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    currentUserId != null
                        ? Text('已登录: $currentUserId', style: const TextStyle(color: Colors.green))
                        : const Text('未登录', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Provider状态
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Provider状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    projectController.when(
                      data: (_) => const Text('就绪', style: TextStyle(color: Colors.green)),
                      loading: () => const Text('加载中...', style: TextStyle(color: Colors.orange)),
                      error: (error, stack) => Text('错误: $error', style: const TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 测试按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testCreateProjectDialog,
                    icon: const Icon(Icons.add_road),
                    label: const Text('测试创建项目对话框'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testDirectCreate,
                    icon: const Icon(Icons.code),
                    label: const Text('测试直接创建'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 结果显示
            if (_lastResult != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '最后结果',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(_lastResult!),
                    ],
                  ),
                ),
              ),
            ],

            // 加载状态
            if (_isLoading) ...[
              const SizedBox(height: 16),
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _testCreateProjectDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const CreateProjectDialogWidget(),
    ).then((result) {
      setState(() {
        if (result != null) {
          _lastResult = '对话框创建成功: ${result.toString()}';
        } else {
          _lastResult = '对话框被取消';
        }
      });
    }).catchError((error) {
      setState(() {
        _lastResult = '对话框错误: $error';
      });
    });
  }

  void _testDirectCreate() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final request = CreateProjectRequest(
        title: '测试项目 ${DateTime.now().millisecondsSinceEpoch}',
        description: '这是一个测试项目，用于验证创建功能',
        vehicleType: '自行式房车',
        vehicleModel: '测试车型',
        vehicleBrand: '测试品牌',
        budget: 50000.0,
        isPublic: true,
        tags: ['电路系统', '水路系统'],
      );

      final result = await ref.read(projectControllerProvider.notifier).createProject(request);

      result.fold(
        (failure) {
          setState(() {
            _lastResult = '直接创建失败: ${failure.message}';
          });
        },
        (project) {
          setState(() {
            _lastResult = '直接创建成功: ${project.title} (ID: ${project.id})';
          });
        },
      );
    } catch (e) {
      setState(() {
        _lastResult = '直接创建异常: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
