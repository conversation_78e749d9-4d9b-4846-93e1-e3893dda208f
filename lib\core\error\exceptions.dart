/// 异常类定义
/// 
/// 根据Clean Architecture原则，定义应用中使用的各种异常类型
/// 这些异常主要在Data层使用，用于表示不同类型的错误情况
library;

/// 服务器异常
class ServerException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  const ServerException({
    required this.message,
    this.statusCode,
    this.data,
  });

  @override
  String toString() => 'ServerException: $message';
}

/// 缓存异常
class CacheException implements Exception {
  final String message;

  const CacheException({required this.message});

  @override
  String toString() => 'CacheException: $message';
}

/// 网络异常
class NetworkException implements Exception {
  final String message;

  const NetworkException({required this.message});

  @override
  String toString() => 'NetworkException: $message';
}

/// 认证异常
class AuthException implements Exception {
  final String message;

  const AuthException({required this.message});

  @override
  String toString() => 'AuthException: $message';
}

/// 权限异常
class PermissionException implements Exception {
  final String message;

  const PermissionException({required this.message});

  @override
  String toString() => 'PermissionException: $message';
}

/// 数据格式异常
class FormatException implements Exception {
  final String message;

  const FormatException({required this.message});

  @override
  String toString() => 'FormatException: $message';
}

/// 验证异常
class ValidationException implements Exception {
  final String message;
  final Map<String, String>? errors;

  const ValidationException({
    required this.message,
    this.errors,
  });

  @override
  String toString() => 'ValidationException: $message';
}
