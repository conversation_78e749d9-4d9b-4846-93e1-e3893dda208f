import 'package:flutter/material.dart';
import 'complete_test_runner.dart';

/// 测试功能入口页面
class TestEntryPage extends StatelessWidget {
  const TestEntryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('VanHub测试中心'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 页面标题
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Icon(
                      Icons.science,
                      size: 48,
                      color: Colors.purple,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'VanHub改装宝测试中心',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '提供完整的功能测试和数据验证工具',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 测试选项
            _buildTestOption(
              context,
              title: '完整工作流程测试',
              description: '测试从用户注册到项目完成的完整流程',
              icon: Icons.timeline,
              color: Colors.green,
              onTap: () => _navigateToCompleteTest(context),
            ),
            
            const SizedBox(height: 12),
            
            _buildTestOption(
              context,
              title: '数据初始化工具',
              description: '快速创建测试数据，包括项目、材料、BOM等',
              icon: Icons.data_object,
              color: Colors.blue,
              onTap: () => _navigateToDataInitializer(context),
            ),
            
            const SizedBox(height: 12),
            
            _buildTestOption(
              context,
              title: '单元测试套件',
              description: '运行所有单元测试和集成测试',
              icon: Icons.check_circle,
              color: Colors.orange,
              onTap: () => _showComingSoon(context),
            ),
            
            const SizedBox(height: 12),
            
            _buildTestOption(
              context,
              title: '性能测试工具',
              description: '测试应用性能和内存使用情况',
              icon: Icons.speed,
              color: Colors.red,
              onTap: () => _showComingSoon(context),
            ),
            
            const Spacer(),
            
            // 底部说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber[50],
                border: Border.all(color: Colors.amber),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.amber),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '注意事项',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          '• 测试功能仅在开发环境中可用\n'
                          '• 测试数据会写入真实数据库\n'
                          '• 建议在测试完成后清理数据',
                          style: TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestOption(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCompleteTest(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CompleteTestRunnerPage(),
      ),
    );
  }

  void _navigateToDataInitializer(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CompleteTestRunnerPage(),
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('功能开发中'),
        content: const Text('此功能正在开发中，敬请期待！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

/// 快速测试按钮组件
class QuickTestButton extends StatelessWidget {
  const QuickTestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _navigateToTestCenter(context),
      backgroundColor: Colors.purple,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.science),
      label: const Text('测试'),
    );
  }

  void _navigateToTestCenter(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TestEntryPage(),
      ),
    );
  }
}
