import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vanhub/features/material/data/services/specification_service_impl.dart';
import 'package:vanhub/features/material/domain/entities/product_specification.dart';
import 'package:vanhub/features/material/presentation/providers/specification_provider.dart';
import 'package:vanhub/features/material/presentation/widgets/product_specification_widget.dart';
import 'package:vanhub/features/material/presentation/widgets/category_spec_template_viewer.dart';
import 'package:vanhub/features/material/presentation/pages/specification_test_page.dart';

void main() {
  group('Specification System Integration Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: [
          specificationServiceProvider.overrideWithValue(SpecificationServiceImpl()),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    Widget createTestApp({Widget? home}) {
      return UncontrolledProviderScope(
        container: container,
        child: MaterialApp(
          home: home ?? const Scaffold(body: Text('Test App')),
        ),
      );
    }

    group('Service Integration Tests', () {
      testWidgets('should load sample data automatically', (tester) async {
        // Arrange
        final service = container.read(specificationServiceProvider);

        // Act
        final result = await service.searchSpecifications();

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications, isNotEmpty);
            expect(specifications.length, greaterThan(5));
            
            // 验证包含不同分类的数据
            final categories = specifications.map((s) => s.category).toSet();
            expect(categories.contains('ELECTRICAL'), true);
            expect(categories.contains('PLUMBING'), true);
            expect(categories.contains('KITCHEN'), true);
          },
        );
      });

      testWidgets('should retrieve specification by material id', (tester) async {
        // Arrange
        const materialId = 'material_battery_001';

        // Act
        final future = container.read(specificationByMaterialIdProvider(materialId).future);
        final specification = await future;

        // Assert
        expect(specification, isNotNull);
        expect(specification!.materialId, equals(materialId));
        expect(specification.category, equals('ELECTRICAL'));
        expect(specification.basicSpec.productName, equals('磷酸铁锂电池'));
      });

      testWidgets('should batch retrieve specifications', (tester) async {
        // Arrange
        const materialIds = ['material_battery_001', 'material_pump_001', 'material_fridge_001'];

        // Act
        final future = container.read(specificationsByMaterialIdsProvider(materialIds).future);
        final specifications = await future;

        // Assert
        expect(specifications.length, equals(3));
        expect(specifications.containsKey('material_battery_001'), true);
        expect(specifications.containsKey('material_pump_001'), true);
        expect(specifications.containsKey('material_fridge_001'), true);
      });
    });

    group('Provider Integration Tests', () {
      testWidgets('should handle provider state changes correctly', (tester) async {
        // Arrange
        final manager = container.read(specificationManagerProvider.notifier);
        
        // Create a test specification
        final testSpec = ProductSpecification(
          id: 'integration_test_001',
          materialId: 'integration_material_001',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '集成测试产品',
            brand: '测试品牌',
            model: 'INT-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act & Assert - Create
        final createResult = await manager.createSpecification(testSpec);
        expect(createResult.isRight(), true);
        
        final state = container.read(specificationManagerProvider);
        expect(state.currentSpecification, isNotNull);
        expect(state.currentSpecification!.id, equals('integration_test_001'));
        expect(state.cache.containsKey('integration_test_001'), true);

        // Act & Assert - Update
        final updatedSpec = testSpec.copyWith(
          basicSpec: testSpec.basicSpec.copyWith(productName: '更新后的产品'),
        );
        final updateResult = await manager.updateSpecification(updatedSpec);
        expect(updateResult.isRight(), true);
        
        final updatedState = container.read(specificationManagerProvider);
        expect(updatedState.currentSpecification!.basicSpec.productName, equals('更新后的产品'));

        // Act & Assert - Delete
        final deleteResult = await manager.deleteSpecification('integration_test_001');
        expect(deleteResult.isRight(), true);
        
        final finalState = container.read(specificationManagerProvider);
        expect(finalState.cache.containsKey('integration_test_001'), false);
      });
    });

    group('Widget Integration Tests', () {
      testWidgets('should display ProductSpecificationWidget with real data', (tester) async {
        // Arrange
        const materialId = 'material_battery_001';
        final specificationFuture = container.read(specificationByMaterialIdProvider(materialId).future);
        final specification = await specificationFuture;

        // Act
        await tester.pumpWidget(createTestApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: ProductSpecificationWidget(specification: specification!),
            ),
          ),
        ));

        // Assert
        expect(find.text('磷酸铁锂电池'), findsOneWidget);
        expect(find.text('CATL - LFP-100Ah'), findsOneWidget);
        expect(find.text('电气设备'), findsOneWidget);
        expect(find.text('基础信息'), findsOneWidget);
        expect(find.text('技术参数'), findsOneWidget);
        expect(find.text('物理属性'), findsOneWidget);
        expect(find.text('性能指标'), findsOneWidget);
        expect(find.text('认证信息'), findsOneWidget);
      });

      testWidgets('should display CategorySpecTemplateViewer', (tester) async {
        // Act
        await tester.pumpWidget(createTestApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: CategorySpecTemplateViewer(
                categoryCode: 'ELECTRICAL',
                isReadOnly: true,
              ),
            ),
          ),
        ));

        // Assert
        expect(find.text('电气设备'), findsOneWidget);
        expect(find.text('必填字段'), findsOneWidget);
      });

      testWidgets('should display SpecificationTestPage', (tester) async {
        // Act
        await tester.pumpWidget(createTestApp(
          home: const SpecificationTestPage(),
        ));

        // Assert
        expect(find.text('规格系统测试'), findsOneWidget);
        expect(find.text('产品规格展示'), findsOneWidget);
        expect(find.text('分类模板'), findsOneWidget);
        expect(find.text('模板编辑'), findsOneWidget);
      });
    });

    group('End-to-End Workflow Tests', () {
      testWidgets('should complete full specification workflow', (tester) async {
        // Arrange
        await tester.pumpWidget(createTestApp(
          home: const SpecificationTestPage(),
        ));

        // Act & Assert - Navigate through tabs
        expect(find.text('产品规格展示'), findsOneWidget);
        
        // Tap on category template tab
        await tester.tap(find.text('分类模板'));
        await tester.pumpAndSettle();
        
        expect(find.text('分类模板展示'), findsOneWidget);
        
        // Tap on template edit tab
        await tester.tap(find.text('模板编辑'));
        await tester.pumpAndSettle();
        
        expect(find.text('模板编辑示例'), findsOneWidget);
      });

      testWidgets('should handle category selection in template viewer', (tester) async {
        // Arrange
        await tester.pumpWidget(createTestApp(
          home: const SpecificationTestPage(),
        ));

        // Navigate to template edit tab
        await tester.tap(find.text('模板编辑'));
        await tester.pumpAndSettle();

        // Act - Select different category
        await tester.tap(find.text('水路设备'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('水路设备'), findsAtLeastNWidgets(1));
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should handle non-existent material id gracefully', (tester) async {
        // Arrange
        const nonExistentMaterialId = 'non_existent_material_999';

        // Act
        final future = container.read(specificationByMaterialIdProvider(nonExistentMaterialId).future);
        final specification = await future;

        // Assert
        expect(specification, isNull);
      });

      testWidgets('should handle empty search results', (tester) async {
        // Arrange
        final service = container.read(specificationServiceProvider);

        // Act
        final result = await service.searchSpecifications(
          category: 'NON_EXISTENT_CATEGORY',
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications, isEmpty);
          },
        );
      });
    });

    group('Performance Tests', () {
      testWidgets('should handle large batch requests efficiently', (tester) async {
        // Arrange
        final materialIds = List.generate(100, (index) => 'material_$index');

        // Act
        final stopwatch = Stopwatch()..start();
        final future = container.read(specificationsByMaterialIdsProvider(materialIds).future);
        final specifications = await future;
        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete within 1 second
        expect(specifications, isA<Map<String, ProductSpecification>>());
      });

      testWidgets('should cache specifications efficiently', (tester) async {
        // Arrange
        const materialId = 'material_battery_001';

        // Act - First call
        final spec1 = await container.read(specificationByMaterialIdProvider(materialId).future);

        // Act - Second call (should return same instance due to provider caching)
        final spec2 = await container.read(specificationByMaterialIdProvider(materialId).future);

        // Assert
        expect(spec1, isNotNull);
        expect(spec2, isNotNull);
        expect(spec1!.id, equals(spec2!.id));
        expect(spec1.materialId, equals(spec2.materialId));
        expect(spec1.basicSpec.productName, equals(spec2.basicSpec.productName));
      });
    });

    group('Data Consistency Tests', () {
      testWidgets('should maintain data consistency across operations', (tester) async {
        // Arrange
        final manager = container.read(specificationManagerProvider.notifier);
        final service = container.read(specificationServiceProvider);
        
        final testSpec = ProductSpecification(
          id: 'consistency_test_001',
          materialId: 'consistency_material_001',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '一致性测试产品',
            brand: '测试品牌',
            model: 'CON-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act - Create through manager
        await manager.createSpecification(testSpec);

        // Assert - Verify through service
        final serviceResult = await service.getSpecificationById('consistency_test_001');
        expect(serviceResult.isRight(), true);
        serviceResult.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specification) {
            expect(specification, isNotNull);
            expect(specification!.id, equals('consistency_test_001'));
            expect(specification.basicSpec.productName, equals('一致性测试产品'));
          },
        );

        // Assert - Verify through provider
        final providerResult = await container.read(
          specificationByMaterialIdProvider('consistency_material_001').future,
        );
        expect(providerResult, isNotNull);
        expect(providerResult!.id, equals('consistency_test_001'));
      });
    });
  });
}
