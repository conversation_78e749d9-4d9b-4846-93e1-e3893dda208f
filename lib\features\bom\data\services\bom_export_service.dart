import 'dart:io';
import 'dart:convert';
import 'package:excel/excel.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/bom_item.dart';
import '../../domain/services/bom_export_service.dart';

/// 导出模板类型
enum ExportTemplate {
  standard,
  detailed,
  summary,
  custom,
}

/// 导出配置
class ExportConfig {
  final ExportTemplate template;
  final bool includeImages;
  final bool includeCosts;
  final bool includeSuppliers;
  final bool includeNotes;
  final String? customTitle;
  final Map<String, dynamic>? customFields;

  const ExportConfig({
    this.template = ExportTemplate.standard,
    this.includeImages = false,
    this.includeCosts = true,
    this.includeSuppliers = true,
    this.includeNotes = true,
    this.customTitle,
    this.customFields,
  });
}

/// BOM导出服务实现
class BomExportServiceImpl implements BomExportService {
  const BomExportServiceImpl();
  @override
  Future<Either<Failure, File>> exportToExcel({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
  }) async {
    try {
      return Right(await _exportToExcelInternal(
        bomItems: bomItems,
        projectName: projectName,
        filePath: filePath,
        config: const ExportConfig(),
      ));
    } catch (e) {
      return Left(UnknownFailure(message: '导出Excel失败: $e'));
    }
  }

  @override
  Future<Either<Failure, File>> exportToPdf({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
  }) async {
    try {
      return Right(await _exportToPdfInternal(
        bomItems: bomItems,
        projectName: projectName,
        filePath: filePath,
        config: const ExportConfig(),
      ));
    } catch (e) {
      return Left(UnknownFailure(message: '导出PDF失败: $e'));
    }
  }

  @override
  Future<Either<Failure, File>> exportToCsv({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
  }) async {
    try {
      return Right(await _exportToCsvInternal(
        bomItems: bomItems,
        projectName: projectName,
        filePath: filePath,
        config: const ExportConfig(),
      ));
    } catch (e) {
      return Left(UnknownFailure(message: '导出CSV失败: $e'));
    }
  }

  @override
  List<String> getSupportedFormats() {
    return ['excel', 'pdf', 'csv'];
  }

  @override
  Future<String> getDefaultExportPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/exports';
  }

  /// 带配置的Excel导出
  Future<File> exportToExcelWithConfig({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
    ExportConfig config = const ExportConfig(),
  }) async {
    return await _exportToExcelInternal(
      bomItems: bomItems,
      projectName: projectName,
      filePath: filePath,
      config: config,
    );
  }

  /// 内部Excel导出实现
  Future<File> _exportToExcelInternal({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
    required ExportConfig config,
  }) async {
    final excel = Excel.createExcel();
    final sheet = excel['BOM清单'];
    
    // 设置表头
    final headers = [
      '序号', '物料名称', '分类', '规格', '数量', '单位', 
      '单价', '总价', '品牌', '型号', '供应商', '状态', '备注'
    ];
    
    for (int i = 0; i < headers.length; i++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
    }
    
    // 填充数据
    for (int i = 0; i < bomItems.length; i++) {
      final item = bomItems[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = IntCellValue(i + 1);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = TextCellValue(item.materialName);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = TextCellValue(item.category ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = TextCellValue(item.specifications ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = DoubleCellValue(item.quantity.toDouble());
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = TextCellValue('个'); // 默认单位
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = DoubleCellValue(item.unitPrice.toDouble() ?? 0);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = DoubleCellValue((item.unitPrice.toDouble() ?? 0) * item.quantity.toDouble());
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = TextCellValue(item.brand ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row))
          .value = TextCellValue(item.model ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row))
          .value = TextCellValue(item.supplier ?? '');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 11, rowIndex: row))
          .value = TextCellValue(_getStatusText(item.status.toString()));
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 12, rowIndex: row))
          .value = TextCellValue(item.notes ?? '');
    }
    
    // 保存文件
    final directory = await getApplicationDocumentsDirectory();
    final fileName = '${projectName}_BOM_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');
    
    final bytes = excel.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }
    
    return file;
  }

  /// 获取不同模板的表头
  List<String> _getHeadersForTemplate(ExportTemplate template, ExportConfig config) {
    switch (template) {
      case ExportTemplate.summary:
        return ['序号', '物料名称', '数量', '总价'];
      case ExportTemplate.detailed:
        final headers = ['序号', '物料名称', '分类', '规格', '数量', '单位'];
        if (config.includeCosts) headers.addAll(['单价', '总价']);
        if (config.includeSuppliers) headers.addAll(['品牌', '型号', '供应商']);
        headers.addAll(['状态']);
        if (config.includeNotes) headers.add('备注');
        return headers;
      case ExportTemplate.custom:
        // 自定义模板可以通过config.customFields定义
        return config.customFields?['headers'] ?? ['序号', '物料名称', '数量', '总价'];
      case ExportTemplate.standard:
      default:
        return ['序号', '物料名称', '分类', '规格', '数量', '单位', '单价', '总价', '品牌', '型号', '供应商', '状态', '备注'];
    }
  }

  /// 填充行数据
  void _fillRowData(Sheet sheet, BomItem item, int row, ExportConfig config, int index) {
    int colIndex = 0;

    // 序号
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
        .value = IntCellValue(index);

    // 物料名称
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
        .value = TextCellValue(item.materialName);

    if (config.template != ExportTemplate.summary) {
      // 分类
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(item.category ?? '');

      // 规格
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(item.specifications ?? '');
    }

    // 数量
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
        .value = DoubleCellValue(item.quantity.toDouble());

    if (config.template != ExportTemplate.summary) {
      // 单位
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue('个');
    }

    if (config.includeCosts) {
      if (config.template != ExportTemplate.summary) {
        // 单价
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
            .value = DoubleCellValue(item.unitPrice.toDouble() ?? 0);
      }

      // 总价
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = DoubleCellValue((item.unitPrice.toDouble() ?? 0) * item.quantity.toDouble());
    }

    if (config.includeSuppliers && config.template == ExportTemplate.detailed) {
      // 品牌
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(item.brand ?? '');

      // 型号
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(item.model ?? '');

      // 供应商
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(item.supplier ?? '');
    }

    if (config.template != ExportTemplate.summary) {
      // 状态
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(_getStatusText(item.status.toString()));
    }

    if (config.includeNotes && config.template == ExportTemplate.detailed) {
      // 备注
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex++, rowIndex: row))
          .value = TextCellValue(item.notes ?? '');
    }
  }

  /// 获取成本列的索引
  int _getCostColumnIndex(ExportConfig config) {
    final headers = _getHeadersForTemplate(config.template, config);
    return headers.indexOf('总价');
  }

  /// 内部PDF导出实现
  Future<File> _exportToPdfInternal({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
    required ExportConfig config,
  }) async {
    final pdf = pw.Document();

    // 计算总价
    final totalCost = bomItems.fold<double>(
      0,
      (sum, item) => sum + ((item.unitPrice.toDouble() ?? 0) * item.quantity.toDouble()),
    );

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // 标题
            pw.Header(
              level: 0,
              child: pw.Text(
                config.customTitle ?? '$projectName - BOM清单',
                style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
            ),
            pw.SizedBox(height: 20),

            // 项目信息
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('项目名称: $projectName'),
                pw.Text('导出时间: ${DateTime.now().toString().substring(0, 19)}'),
              ],
            ),
            pw.SizedBox(height: 10),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('物料总数: ${bomItems.length}'),
                if (config.includeCosts) pw.Text('总成本: ¥${totalCost.toStringAsFixed(2)}'),
              ],
            ),
            pw.SizedBox(height: 20),

            // BOM表格
            _buildPdfTable(bomItems, config),
          ];
        },
      ),
    );

    // 保存文件
    final directory = await getApplicationDocumentsDirectory();
    final fileName = filePath ?? '${projectName}_BOM_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File(fileName.startsWith('/') ? fileName : '${directory.path}/$fileName');

    // 确保目录存在
    await file.parent.create(recursive: true);

    final bytes = await pdf.save();
    await file.writeAsBytes(bytes);

    return file;
  }

  /// 构建PDF表格
  pw.Widget _buildPdfTable(List<BomItem> bomItems, ExportConfig config) {
    final headers = _getHeadersForTemplate(config.template, config);

    return pw.Table(
      border: pw.TableBorder.all(),
      columnWidths: _getPdfColumnWidths(headers),
      children: [
        // 表头
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey300),
          children: headers.map((header) => pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              header,
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          )).toList(),
        ),

        // 数据行
        ...bomItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;

          return pw.TableRow(
            children: _buildPdfRowCells(item, config, index + 1),
          );
        }),
      ],
    );
  }

  /// 获取PDF列宽
  Map<int, pw.TableColumnWidth> _getPdfColumnWidths(List<String> headers) {
    final widths = <int, pw.TableColumnWidth>{};
    for (int i = 0; i < headers.length; i++) {
      switch (headers[i]) {
        case '序号':
          widths[i] = const pw.FixedColumnWidth(30);
          break;
        case '物料名称':
          widths[i] = const pw.FlexColumnWidth(3);
          break;
        case '数量':
        case '单价':
        case '总价':
          widths[i] = const pw.FixedColumnWidth(60);
          break;
        default:
          widths[i] = const pw.FlexColumnWidth(2);
      }
    }
    return widths;
  }

  /// 构建PDF行单元格
  List<pw.Widget> _buildPdfRowCells(BomItem item, ExportConfig config, int index) {
    final cells = <pw.Widget>[];

    // 序号
    cells.add(pw.Padding(
      padding: const pw.EdgeInsets.all(4),
      child: pw.Text(index.toString()),
    ));

    // 物料名称
    cells.add(pw.Padding(
      padding: const pw.EdgeInsets.all(4),
      child: pw.Text(item.materialName),
    ));

    if (config.template != ExportTemplate.summary) {
      // 分类
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(item.category ?? ''),
      ));

      // 规格
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(item.specifications ?? ''),
      ));
    }

    // 数量
    cells.add(pw.Padding(
      padding: const pw.EdgeInsets.all(4),
      child: pw.Text(item.quantity.toString()),
    ));

    if (config.template != ExportTemplate.summary) {
      // 单位
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text('个'),
      ));
    }

    if (config.includeCosts) {
      if (config.template != ExportTemplate.summary) {
        // 单价
        cells.add(pw.Padding(
          padding: const pw.EdgeInsets.all(4),
          child: pw.Text('¥${(item.unitPrice.toDouble() ?? 0).toStringAsFixed(2)}'),
        ));
      }

      // 总价
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text('¥${((item.unitPrice.toDouble() ?? 0) * item.quantity.toDouble()).toStringAsFixed(2)}'),
      ));
    }

    if (config.includeSuppliers && config.template == ExportTemplate.detailed) {
      // 品牌
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(item.brand ?? ''),
      ));

      // 型号
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(item.model ?? ''),
      ));

      // 供应商
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(item.supplier ?? ''),
      ));
    }

    if (config.template != ExportTemplate.summary) {
      // 状态
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(_getStatusText(item.status.toString())),
      ));
    }

    if (config.includeNotes && config.template == ExportTemplate.detailed) {
      // 备注
      cells.add(pw.Padding(
        padding: const pw.EdgeInsets.all(4),
        child: pw.Text(item.notes ?? ''),
      ));
    }

    return cells;
  }

  /// 内部CSV导出实现
  Future<File> _exportToCsvInternal({
    required List<BomItem> bomItems,
    required String projectName,
    String? filePath,
    required ExportConfig config,
  }) async {
    final headers = _getHeadersForTemplate(config.template, config);
    final csvData = StringBuffer();

    // 添加BOM标识
    csvData.writeln('# $projectName BOM清单');
    csvData.writeln('# 导出时间: ${DateTime.now()}');
    csvData.writeln('# 物料总数: ${bomItems.length}');
    csvData.writeln();

    // 添加表头
    csvData.writeln(headers.join(','));

    // 添加数据行
    for (int i = 0; i < bomItems.length; i++) {
      final item = bomItems[i];
      final row = _buildCsvRow(item, config, i + 1);
      csvData.writeln(row.join(','));
    }

    // 保存文件
    final directory = await getApplicationDocumentsDirectory();
    final fileName = filePath ?? '${projectName}_BOM_${DateTime.now().millisecondsSinceEpoch}.csv';
    final file = File(fileName.startsWith('/') ? fileName : '${directory.path}/$fileName');

    // 确保目录存在
    await file.parent.create(recursive: true);

    await file.writeAsString(csvData.toString(), encoding: utf8);

    return file;
  }

  /// 构建CSV行数据
  List<String> _buildCsvRow(BomItem item, ExportConfig config, int index) {
    final row = <String>[];

    // 序号
    row.add(index.toString());

    // 物料名称
    row.add('"${item.materialName}"');

    if (config.template != ExportTemplate.summary) {
      // 分类
      row.add('"${item.category ?? ''}"');

      // 规格
      row.add('"${item.specifications ?? ''}"');
    }

    // 数量
    row.add(item.quantity.toString());

    if (config.template != ExportTemplate.summary) {
      // 单位
      row.add('"个"');
    }

    if (config.includeCosts) {
      if (config.template != ExportTemplate.summary) {
        // 单价
        row.add((item.unitPrice.toDouble() ?? 0).toStringAsFixed(2));
      }

      // 总价
      row.add(((item.unitPrice.toDouble() ?? 0) * item.quantity.toDouble()).toStringAsFixed(2));
    }

    if (config.includeSuppliers && config.template == ExportTemplate.detailed) {
      // 品牌
      row.add('"${item.brand ?? ''}"');

      // 型号
      row.add('"${item.model ?? ''}"');

      // 供应商
      row.add('"${item.supplier ?? ''}"');
    }

    if (config.template != ExportTemplate.summary) {
      // 状态
      row.add('"${_getStatusText(item.status.toString())}"');
    }

    if (config.includeNotes && config.template == ExportTemplate.detailed) {
      // 备注
      row.add('"${item.notes ?? ''}"');
    }

    return row;
  }

  /// 获取状态文本
  String _getStatusText(String status) {
    switch (status) {
      case 'planned':
        return '计划中';
      case 'purchased':
        return '已采购';
      case 'installed':
        return '已安装';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  }
}
