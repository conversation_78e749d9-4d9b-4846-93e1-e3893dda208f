import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/core/design_system/components/atoms/vanhub_badge.dart';

void main() {
  group('VanHubBadge', () {
    testWidgets('应该显示数字徽章', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: <PERSON><PERSON>ubBadge(
              label: '5',
              child: Icon(Icons.notifications),
            ),
          ),
        ),
      );

      expect(find.text('5'), findsOneWidget);
      expect(find.byIcon(Icons.notifications), findsOneWidget);
    });

    testWidgets('应该显示99+当数字大于99', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubBadge(
              label: '150',
              child: Icon(Icons.notifications),
            ),
          ),
        ),
      );

      expect(find.text('99+'), findsOneWidget);
    });

    testWidgets('应该显示点状徽章', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: <PERSON><PERSON><PERSON>Badge(
              showDot: true,
              child: Icon(Icons.notifications),
            ),
          ),
        ),
      );

      expect(find.byType(Container), findsWidgets);
      expect(find.byIcon(Icons.notifications), findsOneWidget);
    });

    testWidgets('当count为0时不应该显示徽章', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubBadge(
              label: '0',
              isVisible: false,
              child: Icon(Icons.notifications),
            ),
          ),
        ),
      );

      expect(find.text('0'), findsNothing);
      expect(find.byIcon(Icons.notifications), findsOneWidget);
    });

    testWidgets('应该支持不同颜色主题', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                VanHubBadge(
                  label: '1',
                  color: VanHubBadgeColor.primary,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '2',
                  color: VanHubBadgeColor.secondary,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '3',
                  color: VanHubBadgeColor.success,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '4',
                  color: VanHubBadgeColor.warning,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '5',
                  color: VanHubBadgeColor.error,
                  child: Icon(Icons.notifications),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(VanHubBadge), findsNWidgets(5));
    });

    testWidgets('应该支持不同位置', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                VanHubBadge(
                  label: '1',
                  position: VanHubBadgePosition.topRight,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '2',
                  position: VanHubBadgePosition.topLeft,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '3',
                  position: VanHubBadgePosition.bottomRight,
                  child: Icon(Icons.notifications),
                ),
                VanHubBadge(
                  label: '4',
                  position: VanHubBadgePosition.bottomLeft,
                  child: Icon(Icons.notifications),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(VanHubBadge), findsNWidgets(4));
    });

    testWidgets('应该支持自定义最大值', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubBadge(
              label: '1000',
              maxCount: 999,
              child: Icon(Icons.notifications),
            ),
          ),
        ),
      );

      expect(find.text('999+'), findsOneWidget);
    });

    testWidgets('应该支持动画', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: VanHubBadge(
              label: '5',
              showAnimation: true,
              child: Icon(Icons.notifications),
            ),
          ),
        ),
      );

      expect(find.byType(AnimatedScale), findsOneWidget);
    });
  });
}