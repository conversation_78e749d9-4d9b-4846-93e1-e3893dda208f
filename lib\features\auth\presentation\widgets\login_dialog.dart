import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';

/// 登录对话框组件
/// 
/// 提供邮箱/密码登录、记住登录状态、忘记密码等功能
/// 使用VanHubModal作为基础容器
class LoginDialog extends ConsumerStatefulWidget {
  /// 登录成功回调
  final VoidCallback? onLoginSuccess;
  
  /// 切换到注册页面回调
  final VoidCallback? onSwitchToRegister;

  const LoginDialog({
    super.key,
    this.onLoginSuccess,
    this.onSwitchToRegister,
  });

  /// 显示登录对话框的静态方法
  static Future<bool?> show({
    required BuildContext context,
    VoidCallback? onLoginSuccess,
    VoidCallback? onSwitchToRegister,
  }) {
    return VanHubModal.show<bool>(
      context: context,
      title: '登录到VanHub',
      size: VanHubModalSize.sm,
      animation: VanHubModalAnimation.scale,
      child: LoginDialog(
        onLoginSuccess: onLoginSuccess,
        onSwitchToRegister: onSwitchToRegister,
      ),
    );
  }

  @override
  ConsumerState<LoginDialog> createState() => _LoginDialogState();
}

class _LoginDialogState extends ConsumerState<LoginDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isLoading = false;
  bool _rememberMe = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing6),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildErrorMessage(),
            _buildEmailField(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildPasswordField(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildRememberMeRow(),
            const SizedBox(height: VanHubDesignSystem.spacing6),
            _buildLoginButton(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildForgotPasswordButton(),
            const SizedBox(height: VanHubDesignSystem.spacing6),
            _buildDivider(),
            const SizedBox(height: VanHubDesignSystem.spacing4),
            _buildSocialLoginButtons(),
            const SizedBox(height: VanHubDesignSystem.spacing6),
            _buildRegisterPrompt(),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorMessage() {
    if (_errorMessage == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubDesignSystem.spacing4),
      padding: const EdgeInsets.all(VanHubDesignSystem.spacing3),
      decoration: BoxDecoration(
        color: VanHubDesignSystem.semanticError.withOpacity(0.1),
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        border: Border.all(
          color: VanHubDesignSystem.semanticError.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: VanHubDesignSystem.semanticError,
            size: 20,
          ),
          const SizedBox(width: VanHubDesignSystem.spacing2),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: VanHubDesignSystem.semanticError,
                fontSize: VanHubDesignSystem.fontSizeSm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: '邮箱地址',
        hintText: '请输入您的邮箱地址',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      validator: _validateEmail,
      enabled: !_isLoading,
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      textInputAction: TextInputAction.done,
      decoration: InputDecoration(
        labelText: '密码',
        hintText: '请输入您的密码',
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      validator: _validatePassword,
      enabled: !_isLoading,
      onFieldSubmitted: (_) => _handleLogin(),
    );
  }

  Widget _buildRememberMeRow() {
    return Row(
      children: [
        Checkbox(
          value: _rememberMe,
          onChanged: _isLoading ? null : (value) {
            setState(() {
              _rememberMe = value ?? false;
            });
          },
        ),
        const Text('记住登录状态'),
      ],
    );
  }

  Widget _buildLoginButton() {
    return FilledButton(
      onPressed: _isLoading ? null : _handleLogin,
      style: FilledButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: VanHubDesignSystem.spacing4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        ),
      ),
      child: _isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Text(
              '登录',
              style: TextStyle(
                fontSize: VanHubDesignSystem.fontSizeBase,
                fontWeight: VanHubDesignSystem.fontWeightMedium,
              ),
            ),
    );
  }

  Widget _buildForgotPasswordButton() {
    return TextButton(
      onPressed: _isLoading ? null : _handleForgotPassword,
      child: const Text('忘记密码？'),
    );
  }

  Widget _buildDivider() {
    return Row(
      children: [
        const Expanded(child: Divider()),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: VanHubDesignSystem.spacing4),
          child: Text(
            '或',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: VanHubDesignSystem.fontSizeSm,
            ),
          ),
        ),
        const Expanded(child: Divider()),
      ],
    );
  }

  Widget _buildSocialLoginButtons() {
    return Column(
      children: [
        OutlinedButton.icon(
          onPressed: _isLoading ? null : _handleGoogleLogin,
          icon: const Icon(Icons.g_mobiledata), // 临时图标，实际应使用Google图标
          label: const Text('使用Google登录'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: VanHubDesignSystem.spacing3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
        ),
        const SizedBox(height: VanHubDesignSystem.spacing3),
        OutlinedButton.icon(
          onPressed: _isLoading ? null : _handleAppleLogin,
          icon: const Icon(Icons.apple), // 临时图标，实际应使用Apple图标
          label: const Text('使用Apple登录'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: VanHubDesignSystem.spacing3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterPrompt() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '还没有账号？',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: VanHubDesignSystem.fontSizeSm,
          ),
        ),
        TextButton(
          onPressed: _isLoading ? null : widget.onSwitchToRegister,
          child: const Text('立即注册'),
        ),
      ],
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入邮箱地址';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value)) {
      return '请输入有效的邮箱地址';
    }
    
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    
    if (value.length < 6) {
      return '密码长度至少6位';
    }
    
    return null;
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // TODO: 实现实际的登录逻辑
      await Future.delayed(const Duration(seconds: 2)); // 模拟网络请求
      
      // 模拟登录成功
      if (mounted) {
        widget.onLoginSuccess?.call();
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '登录失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleForgotPassword() {
    // TODO: 实现忘记密码功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('忘记密码功能开发中...')),
    );
  }

  void _handleGoogleLogin() {
    // TODO: 实现Google登录
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Google登录功能开发中...')),
    );
  }

  void _handleAppleLogin() {
    // TODO: 实现Apple登录
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Apple登录功能开发中...')),
    );
  }
}
