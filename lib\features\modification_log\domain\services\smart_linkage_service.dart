import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/log_entry.dart';
import '../entities/enums.dart';
import '../repositories/log_repository.dart';
import '../repositories/timeline_repository.dart';
import '../../../bom/domain/repositories/bom_repository.dart';
import '../../../bom/domain/entities/bom_item.dart';

/// 智能联动服务
/// 实现改装日志与BOM、时间轴的智能联动功能
class SmartLinkageService {
  final LogRepository logRepository;
  final TimelineRepository timelineRepository;
  final BomRepository bomRepository;

  SmartLinkageService({
    required this.logRepository,
    required this.timelineRepository,
    required this.bomRepository,
  });

  /// 保存日志并执行智能联动
  /// 
  /// 功能：
  /// 1. 保存日志基本信息
  /// 2. 自动关联到相关BOM物料
  /// 3. 自动更新时间轴任务状态
  /// 4. 自动累加工时到项目进度
  Future<Either<Failure, LogEntry>> saveLogWithSmartLinkage({
    required LogEntry logEntry,
    List<String>? relatedBomItemIds,
    List<String>? relatedMilestoneIds,
    bool autoUpdateTaskStatus = true,
    bool autoUpdateBomStatus = true,
  }) async {
    try {
      // 1. 保存日志基本信息
      final logResult = await logRepository.createLogEntry(logEntry);
      if (logResult.isLeft()) {
        return logResult;
      }
      
      final savedLog = logResult.getRight().getOrElse(() => logEntry);

      // 2. 关联BOM物料
      if (relatedBomItemIds != null && relatedBomItemIds.isNotEmpty && autoUpdateBomStatus) {
        await _linkToBomItems(savedLog, relatedBomItemIds);
      }

      // 3. 关联时间轴里程碑
      if (relatedMilestoneIds != null && relatedMilestoneIds.isNotEmpty && autoUpdateTaskStatus) {
        await _linkToMilestones(savedLog, relatedMilestoneIds);
      }

      // 4. 自动更新项目进度
      await _updateProjectProgress(savedLog);

      return Right(savedLog);
    } catch (e) {
      return Left(ServerFailure(message: '智能联动保存失败: $e'));
    }
  }

  /// 关联到BOM物料
  Future<void> _linkToBomItems(LogEntry logEntry, List<String> bomItemIds) async {
    for (final bomItemId in bomItemIds) {
      try {
        // 获取BOM物料详情
        final bomResult = await bomRepository.getBomItemById(bomItemId);
        if (bomResult.isRight()) {
          final bomItem = bomResult.getRight().getOrElse(() => throw Exception('BOM item not found'));
          
          // 根据日志状态自动更新BOM物料状态
          BomItemStatus newStatus = bomItem.status;
          switch (logEntry.status) {
            case LogStatus.inProgress:
              newStatus = BomItemStatus.ordered;
              break;
            case LogStatus.completed:
              newStatus = BomItemStatus.installed;
              break;
            default:
              // 保持原状态
              break;
          }

          // 更新BOM物料状态
          if (newStatus != bomItem.status) {
            final updates = {
              'status': newStatus.name,
              'notes': '${bomItem.notes ?? ''}\n[自动更新] 关联日志: ${logEntry.title}',
              'updated_at': DateTime.now().toIso8601String(),
            };

            await bomRepository.updateBomItemById(bomItemId, updates);
          }
        }
      } catch (e) {
        // 记录错误但不中断流程
        print('关联BOM物料失败: $bomItemId, 错误: $e');
      }
    }
  }

  /// 关联到时间轴里程碑
  Future<void> _linkToMilestones(LogEntry logEntry, List<String> milestoneIds) async {
    for (final milestoneId in milestoneIds) {
      try {
        // 将日志链接到里程碑
        await timelineRepository.linkLogToMilestone(milestoneId, logEntry.id);
        
        // 如果日志已完成，自动更新里程碑状态
        if (logEntry.status == LogStatus.completed) {
          await timelineRepository.updateMilestoneStatus(
            milestoneId, 
            MilestoneStatus.completed,
          );
        }
      } catch (e) {
        // 记录错误但不中断流程
        print('关联时间轴里程碑失败: $milestoneId, 错误: $e');
      }
    }
  }

  /// 更新项目进度
  Future<void> _updateProjectProgress(LogEntry logEntry) async {
    try {
      // TODO: 实现项目进度自动计算
      // 1. 获取项目所有日志的工时
      // 2. 计算总体完成度
      // 3. 更新项目状态
      
      print('项目进度更新: 项目${logEntry.projectId}新增工时${logEntry.timeSpentMinutes}分钟');
    } catch (e) {
      print('更新项目进度失败: $e');
    }
  }

  /// 从材料库添加到BOM的智能联动
  /// 
  /// 当用户从材料库添加物料到BOM时，自动：
  /// 1. 创建BOM条目
  /// 2. 更新材料使用统计
  /// 3. 触发智能推荐
  Future<Either<Failure, BomItem>> addMaterialToBomWithLinkage({
    required String projectId,
    required String materialId,
    required int quantity,
    double? customPrice,
    String? notes,
  }) async {
    try {
      // TODO: 实现材料库到BOM的智能联动
      // 1. 从材料库获取材料信息
      // 2. 创建BOM条目
      // 3. 更新材料使用统计
      // 4. 触发相关材料推荐
      
      return Left(ServerFailure(message: '材料库到BOM联动功能待实现'));
    } catch (e) {
      return Left(ServerFailure(message: '材料库到BOM联动失败: $e'));
    }
  }

  /// 智能推荐相关材料
  /// 
  /// 基于当前BOM和项目类型，推荐相关材料
  Future<Either<Failure, List<String>>> getSmartRecommendations({
    required String projectId,
    required String systemType,
  }) async {
    try {
      // TODO: 实现智能推荐算法
      // 1. 分析当前项目的BOM构成
      // 2. 基于系统类型推荐配套材料
      // 3. 基于其他用户的项目推荐热门材料
      
      return const Right([]);
    } catch (e) {
      return Left(ServerFailure(message: '智能推荐失败: $e'));
    }
  }
}


