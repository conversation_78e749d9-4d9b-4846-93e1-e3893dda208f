#!/usr/bin/env dart

import 'dart:io';

/// 规格系统测试运行脚本
/// 
/// 运行所有与规格系统相关的测试
void main(List<String> args) async {
  print('🚀 开始运行规格系统测试套件...\n');

  final testSuites = [
    TestSuite(
      name: '单元测试 - 规格服务',
      path: 'test/features/material/domain/services/specification_service_test.dart',
      description: '测试规格服务的核心业务逻辑',
    ),
    TestSuite(
      name: 'Widget测试 - 规格展示组件',
      path: 'test/features/material/presentation/widgets/product_specification_widget_test.dart',
      description: '测试产品规格展示组件的UI渲染',
    ),
    TestSuite(
      name: '集成测试 - 规格系统',
      path: 'test/features/material/integration/specification_system_integration_test.dart',
      description: '测试规格系统的端到端功能',
    ),
  ];

  var totalTests = 0;
  var passedTests = 0;
  var failedTests = 0;
  final failedSuites = <String>[];

  for (final suite in testSuites) {
    print('📋 运行: ${suite.name}');
    print('   ${suite.description}');
    print('   路径: ${suite.path}');
    
    final result = await runTestSuite(suite.path);
    
    if (result.success) {
      print('   ✅ 通过: ${result.passedCount}/${result.totalCount} 测试');
      totalTests += result.totalCount;
      passedTests += result.passedCount;
    } else {
      print('   ❌ 失败: ${result.failedCount}/${result.totalCount} 测试失败');
      totalTests += result.totalCount;
      passedTests += result.passedCount;
      failedTests += result.failedCount;
      failedSuites.add(suite.name);
      
      if (result.errorOutput.isNotEmpty) {
        print('   错误信息:');
        print('   ${result.errorOutput}');
      }
    }
    print('');
  }

  // 打印总结
  print('📊 测试总结');
  print('=' * 50);
  print('总测试数: $totalTests');
  print('通过: $passedTests');
  print('失败: $failedTests');
  print('成功率: ${(passedTests / totalTests * 100).toStringAsFixed(1)}%');
  
  if (failedSuites.isNotEmpty) {
    print('\n❌ 失败的测试套件:');
    for (final suite in failedSuites) {
      print('   - $suite');
    }
    exit(1);
  } else {
    print('\n🎉 所有测试都通过了！');
    exit(0);
  }
}

/// 测试套件信息
class TestSuite {
  final String name;
  final String path;
  final String description;

  TestSuite({
    required this.name,
    required this.path,
    required this.description,
  });
}

/// 测试结果
class TestResult {
  final bool success;
  final int totalCount;
  final int passedCount;
  final int failedCount;
  final String output;
  final String errorOutput;

  TestResult({
    required this.success,
    required this.totalCount,
    required this.passedCount,
    required this.failedCount,
    required this.output,
    required this.errorOutput,
  });
}

/// 运行单个测试套件
Future<TestResult> runTestSuite(String testPath) async {
  try {
    final process = await Process.run(
      'flutter',
      ['test', testPath],
      workingDirectory: Directory.current.path,
    );

    final output = process.stdout as String;
    final errorOutput = process.stderr as String;
    
    // 解析测试结果
    final result = parseTestOutput(output);
    
    return TestResult(
      success: process.exitCode == 0,
      totalCount: result['total'] ?? 0,
      passedCount: result['passed'] ?? 0,
      failedCount: result['failed'] ?? 0,
      output: output,
      errorOutput: errorOutput,
    );
  } catch (e) {
    return TestResult(
      success: false,
      totalCount: 0,
      passedCount: 0,
      failedCount: 0,
      output: '',
      errorOutput: e.toString(),
    );
  }
}

/// 解析测试输出
Map<String, int> parseTestOutput(String output) {
  final lines = output.split('\n');
  var totalTests = 0;
  var passedTests = 0;
  var failedTests = 0;

  for (final line in lines) {
    // 查找类似 "00:02 +14: All tests passed!" 的行
    final allPassedMatch = RegExp(r'\+(\d+):\s+All tests passed!').firstMatch(line);
    if (allPassedMatch != null) {
      totalTests = int.parse(allPassedMatch.group(1)!);
      passedTests = totalTests;
      break;
    }

    // 查找类似 "00:02 +5 -2: Some tests failed." 的行
    final someFailedMatch = RegExp(r'\+(\d+)\s+-(\d+):').firstMatch(line);
    if (someFailedMatch != null) {
      passedTests = int.parse(someFailedMatch.group(1)!);
      failedTests = int.parse(someFailedMatch.group(2)!);
      totalTests = passedTests + failedTests;
      break;
    }

    // 查找单个测试结果
    final testMatch = RegExp(r'\+(\d+):').firstMatch(line);
    if (testMatch != null) {
      final currentTotal = int.parse(testMatch.group(1)!);
      if (currentTotal > totalTests) {
        totalTests = currentTotal;
        passedTests = currentTotal;
      }
    }
  }

  return {
    'total': totalTests,
    'passed': passedTests,
    'failed': failedTests,
  };
}
