import 'package:freezed_annotation/freezed_annotation.dart';

part 'cost_analysis.freezed.dart';
part 'cost_analysis.g.dart';

/// 成本分析结果
@freezed
class CostAnalysis with _$CostAnalysis {
  const factory CostAnalysis({
    required String projectId,
    required double totalBudget,
    required double actualCost,
    required double remainingBudget,
    required double budgetUtilization,
    required bool isOverBudget,
    required DateTime analysisDate,
    required Map<String, double> categoryBreakdown,
    required List<CostItem> topExpenses,
    required CostTrendSummary trendSummary,
    String? riskLevel,
    List<String>? warnings,
  }) = _CostAnalysis;

  factory CostAnalysis.fromJson(Map<String, dynamic> json) => _$CostAnalysisFromJson(json);
}

/// 成本项目
@freezed
class CostItem with _$CostItem {
  const factory CostItem({
    required String id,
    required String name,
    required String category,
    required double amount,
    required DateTime date,
    String? description,
    String? supplier,
  }) = _CostItem;

  factory CostItem.fromJson(Map<String, dynamic> json) => _$CostItemFromJson(json);
}

/// 成本趋势摘要
@freezed
class CostTrendSummary with _$CostTrendSummary {
  const factory CostTrendSummary({
    required double monthlyAverage,
    required double growthRate,
    required String trend, // 'increasing', 'decreasing', 'stable'
    required List<MonthlySpending> monthlyData,
  }) = _CostTrendSummary;

  factory CostTrendSummary.fromJson(Map<String, dynamic> json) => _$CostTrendSummaryFromJson(json);
}

/// 月度支出
@freezed
class MonthlySpending with _$MonthlySpending {
  const factory MonthlySpending({
    required int year,
    required int month,
    required double amount,
    required int itemCount,
  }) = _MonthlySpending;

  factory MonthlySpending.fromJson(Map<String, dynamic> json) => _$MonthlySpendingFromJson(json);
}

/// 预算警告
@freezed
class BudgetAlert with _$BudgetAlert {
  const factory BudgetAlert({
    required String projectId,
    required AlertLevel level,
    required String message,
    required double currentUtilization,
    required double projectedUtilization,
    required DateTime alertDate,
    List<String>? recommendations,
  }) = _BudgetAlert;

  factory BudgetAlert.fromJson(Map<String, dynamic> json) => _$BudgetAlertFromJson(json);
}

/// 警告级别
enum AlertLevel {
  low,
  medium,
  high,
  critical,
}

/// 成本趋势分析
@freezed
class CostTrendAnalysis with _$CostTrendAnalysis {
  const factory CostTrendAnalysis({
    required String projectId,
    required DateTime startDate,
    required DateTime endDate,
    required List<TrendDataPoint> dataPoints,
    required double averageMonthlySpend,
    required double totalSpend,
    required String overallTrend,
    required List<TrendInsight> insights,
  }) = _CostTrendAnalysis;

  factory CostTrendAnalysis.fromJson(Map<String, dynamic> json) => _$CostTrendAnalysisFromJson(json);
}

/// 趋势数据点
@freezed
class TrendDataPoint with _$TrendDataPoint {
  const factory TrendDataPoint({
    required DateTime date,
    required double amount,
    required String category,
    String? note,
  }) = _TrendDataPoint;

  factory TrendDataPoint.fromJson(Map<String, dynamic> json) => _$TrendDataPointFromJson(json);
}

/// 趋势洞察
@freezed
class TrendInsight with _$TrendInsight {
  const factory TrendInsight({
    required String type,
    required String title,
    required String description,
    required double impact,
    String? recommendation,
  }) = _TrendInsight;

  factory TrendInsight.fromJson(Map<String, dynamic> json) => _$TrendInsightFromJson(json);
}

/// 成本结构分析
@freezed
class CostStructureAnalysis with _$CostStructureAnalysis {
  const factory CostStructureAnalysis({
    required String projectId,
    required Map<String, double> categoryDistribution,
    required Map<String, double> supplierDistribution,
    required List<CostCategory> categories,
    required double concentrationIndex,
    required List<String> recommendations,
  }) = _CostStructureAnalysis;

  factory CostStructureAnalysis.fromJson(Map<String, dynamic> json) => _$CostStructureAnalysisFromJson(json);
}

/// 成本分类
@freezed
class CostCategory with _$CostCategory {
  const factory CostCategory({
    required String name,
    required double amount,
    required double percentage,
    required int itemCount,
    required double averageItemCost,
    String? trend,
  }) = _CostCategory;

  factory CostCategory.fromJson(Map<String, dynamic> json) => _$CostCategoryFromJson(json);
}

/// 成本预测
@freezed
class CostForecast with _$CostForecast {
  const factory CostForecast({
    required String projectId,
    required DateTime forecastDate,
    required int forecastDays,
    required List<ForecastDataPoint> predictions,
    required double totalPredictedCost,
    required double confidenceLevel,
    required String methodology,
    List<String>? assumptions,
  }) = _CostForecast;

  factory CostForecast.fromJson(Map<String, dynamic> json) => _$CostForecastFromJson(json);
}

/// 预测数据点
@freezed
class ForecastDataPoint with _$ForecastDataPoint {
  const factory ForecastDataPoint({
    required DateTime date,
    required double predictedAmount,
    required double lowerBound,
    required double upperBound,
    String? category,
  }) = _ForecastDataPoint;

  factory ForecastDataPoint.fromJson(Map<String, dynamic> json) => _$ForecastDataPointFromJson(json);
}

/// 成本比较
@freezed
class CostComparison with _$CostComparison {
  const factory CostComparison({
    required List<String> projectIds,
    required Map<String, ProjectCostSummary> projectSummaries,
    required List<ComparisonInsight> insights,
    required DateTime comparisonDate,
  }) = _CostComparison;

  factory CostComparison.fromJson(Map<String, dynamic> json) => _$CostComparisonFromJson(json);
}

/// 项目成本摘要
@freezed
class ProjectCostSummary with _$ProjectCostSummary {
  const factory ProjectCostSummary({
    required String projectId,
    required String projectName,
    required double totalCost,
    required double budgetUtilization,
    required Map<String, double> categoryBreakdown,
    required String status,
  }) = _ProjectCostSummary;

  factory ProjectCostSummary.fromJson(Map<String, dynamic> json) => _$ProjectCostSummaryFromJson(json);
}

/// 比较洞察
@freezed
class ComparisonInsight with _$ComparisonInsight {
  const factory ComparisonInsight({
    required String type,
    required String title,
    required String description,
    required List<String> affectedProjects,
    String? recommendation,
  }) = _ComparisonInsight;

  factory ComparisonInsight.fromJson(Map<String, dynamic> json) => _$ComparisonInsightFromJson(json);
}

/// 成本优化建议
@freezed
class CostOptimizationSuggestion with _$CostOptimizationSuggestion {
  const factory CostOptimizationSuggestion({
    required String id,
    required String title,
    required String description,
    required double potentialSavings,
    required String category,
    required String priority,
    required String difficulty,
    required List<String> actionSteps,
    String? timeframe,
  }) = _CostOptimizationSuggestion;

  factory CostOptimizationSuggestion.fromJson(Map<String, dynamic> json) => _$CostOptimizationSuggestionFromJson(json);
}

/// ROI分析
@freezed
class ROIAnalysis with _$ROIAnalysis {
  const factory ROIAnalysis({
    required String projectId,
    required double totalInvestment,
    required double expectedReturn,
    required double roi,
    required int paybackPeriodMonths,
    required double npv,
    required double irr,
    required List<ROIScenario> scenarios,
    required DateTime analysisDate,
  }) = _ROIAnalysis;

  factory ROIAnalysis.fromJson(Map<String, dynamic> json) => _$ROIAnalysisFromJson(json);
}

/// ROI场景
@freezed
class ROIScenario with _$ROIScenario {
  const factory ROIScenario({
    required String name,
    required double probability,
    required double expectedReturn,
    required double roi,
    String? description,
  }) = _ROIScenario;

  factory ROIScenario.fromJson(Map<String, dynamic> json) => _$ROIScenarioFromJson(json);
}

/// 成本分析扩展
extension CostAnalysisX on CostAnalysis {
  /// 获取风险级别颜色
  String get riskLevelColor {
    switch (riskLevel) {
      case 'low':
        return '#4CAF50';
      case 'medium':
        return '#FF9800';
      case 'high':
        return '#F44336';
      case 'critical':
        return '#D32F2F';
      default:
        return '#9E9E9E';
    }
  }

  /// 获取预算状态描述
  String get budgetStatusDescription {
    if (isOverBudget) {
      return '预算超支 ${((actualCost - totalBudget) / totalBudget * 100).toStringAsFixed(1)}%';
    } else if (budgetUtilization > 90) {
      return '预算紧张 (已使用${budgetUtilization.toStringAsFixed(1)}%)';
    } else if (budgetUtilization > 70) {
      return '预算正常 (已使用${budgetUtilization.toStringAsFixed(1)}%)';
    } else {
      return '预算充足 (已使用${budgetUtilization.toStringAsFixed(1)}%)';
    }
  }

  /// 获取最大支出分类
  String get topExpenseCategory {
    if (categoryBreakdown.isEmpty) return '无数据';
    return categoryBreakdown.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
}
