import 'package:fpdart/fpdart.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/product_specification.dart';
import '../../domain/entities/category_templates.dart';
import '../../domain/services/specification_service.dart';
import '../datasources/specification_data_initializer.dart';

/// 产品规格服务实现
///
/// 目前使用内存存储，后续可扩展为数据库存储
class SpecificationServiceImpl implements SpecificationService {
  // 内存存储（演示用）
  final Map<String, ProductSpecification> _specifications = {};
  final Map<String, String> _materialSpecMap = {}; // materialId -> specificationId

  // 初始化标志
  bool _isInitialized = false;

  /// 初始化示例数据
  void _initializeData() {
    if (_isInitialized) return;

    final sampleSpecs = SpecificationDataInitializer.getAllSampleSpecifications();
    for (final spec in sampleSpecs) {
      _specifications[spec.id] = spec;
      _materialSpecMap[spec.materialId] = spec.id;
    }

    _isInitialized = true;
  }

  @override
  Future<Either<Failure, ProductSpecification?>> getSpecificationByMaterialId(String materialId) async {
    try {
      _initializeData();

      final specId = _materialSpecMap[materialId];
      if (specId == null) {
        return const Right(null);
      }

      final specification = _specifications[specId];
      return Right(specification);
    } catch (e) {
      return Left(UnknownFailure(message: '获取规格失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ProductSpecification?>> getSpecificationById(String specificationId) async {
    try {
      _initializeData();

      final specification = _specifications[specificationId];
      return Right(specification);
    } catch (e) {
      return Left(UnknownFailure(message: '获取规格失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ProductSpecification>> createSpecification(ProductSpecification specification) async {
    try {
      _specifications[specification.id] = specification;
      _materialSpecMap[specification.materialId] = specification.id;
      return Right(specification);
    } catch (e) {
      return Left(UnknownFailure(message: '创建规格失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ProductSpecification>> updateSpecification(ProductSpecification specification) async {
    try {
      if (!_specifications.containsKey(specification.id)) {
        return Left(NotFoundFailure(message: '规格不存在'));
      }
      
      _specifications[specification.id] = specification;
      return Right(specification);
    } catch (e) {
      return Left(UnknownFailure(message: '更新规格失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteSpecification(String specificationId) async {
    try {
      final specification = _specifications[specificationId];
      if (specification == null) {
        return Left(NotFoundFailure(message: '规格不存在'));
      }
      
      _specifications.remove(specificationId);
      _materialSpecMap.remove(specification.materialId);
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '删除规格失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getSpecificationTemplate(String category) async {
    try {
      final template = VanHubCategoryTemplates.getTemplate(category);
      if (template == null) {
        return Left(NotFoundFailure(message: '未找到分类模板: $category'));
      }
      
      // 转换为简化的模板格式
      final templateData = {
        'category': template.categoryCode,
        'categoryName': template.categoryName,
        'version': template.version,
        'requiredFields': template.requiredFields.map((field) => {
          'name': field.name,
          'label': field.label,
          'type': field.type.toString(),
          'required': field.required,
          'defaultValue': field.defaultValue,
        }).toList(),
        'optionalFields': template.optionalFields.map((field) => {
          'name': field.name,
          'label': field.label,
          'type': field.type.toString(),
          'required': field.required,
          'defaultValue': field.defaultValue,
          'groupId': field.groupId,
        }).toList(),
        'fieldGroups': template.fieldGroups.map((group) => {
          'id': group.id,
          'label': group.label,
          'description': group.description,
          'icon': group.icon,
          'defaultExpanded': group.defaultExpanded,
        }).toList(),
      };
      
      return Right(templateData);
    } catch (e) {
      return Left(UnknownFailure(message: '获取模板失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, ProductSpecification>>> getSpecificationsByMaterialIds(List<String> materialIds) async {
    try {
      _initializeData();

      final result = <String, ProductSpecification>{};

      for (final materialId in materialIds) {
        final specId = _materialSpecMap[materialId];
        if (specId != null) {
          final specification = _specifications[specId];
          if (specification != null) {
            result[materialId] = specification;
          }
        }
      }

      return Right(result);
    } catch (e) {
      return Left(UnknownFailure(message: '批量获取规格失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ProductSpecification>>> searchSpecifications({
    String? category,
    Map<String, dynamic>? filters,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _initializeData();

      var results = _specifications.values.toList();

      // 按分类过滤
      if (category != null) {
        results = results.where((spec) => spec.category == category).toList();
      }

      // 应用其他过滤器
      if (filters != null) {
        // 这里可以实现更复杂的过滤逻辑
        // 例如按品牌、价格范围、技术参数等过滤
      }

      // 分页
      final startIndex = offset;
      final endIndex = (startIndex + limit).clamp(0, results.length);

      if (startIndex >= results.length) {
        return const Right([]);
      }

      final paginatedResults = results.sublist(startIndex, endIndex);
      return Right(paginatedResults);
    } catch (e) {
      return Left(UnknownFailure(message: '搜索规格失败: $e'));
    }
  }

  @override
  Either<Failure, void> validateSpecification(ProductSpecification specification) {
    try {
      // 基础验证
      if (specification.id.isEmpty) {
        return Left(ValidationFailure(message: '规格ID不能为空'));
      }
      
      if (specification.materialId.isEmpty) {
        return Left(ValidationFailure(message: '材料ID不能为空'));
      }
      
      if (specification.category.isEmpty) {
        return Left(ValidationFailure(message: '分类不能为空'));
      }
      
      if (specification.basicSpec.productName.isEmpty) {
        return Left(ValidationFailure(message: '产品名称不能为空'));
      }
      
      if (specification.basicSpec.brand.isEmpty) {
        return Left(ValidationFailure(message: '品牌不能为空'));
      }
      
      // 验证物理属性
      final dimensions = specification.physicalProps.dimensions;
      if (dimensions.length <= 0 || dimensions.width <= 0 || dimensions.height <= 0) {
        return Left(ValidationFailure(message: '尺寸必须大于0'));
      }
      
      if (specification.physicalProps.weight <= 0) {
        return Left(ValidationFailure(message: '重量必须大于0'));
      }
      
      // 验证技术参数（根据分类）
      final validationResult = _validateTechnicalParams(specification);
      if (validationResult.isLeft()) {
        return validationResult;
      }
      
      return const Right(null);
    } catch (e) {
      return Left(ValidationFailure(message: '验证失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ProductSpecification>> createFromTemplate(
    String materialId,
    String category,
    Map<String, dynamic> templateValues,
  ) async {
    try {
      final template = VanHubCategoryTemplates.getTemplate(category);
      if (template == null) {
        return Left(NotFoundFailure(message: '未找到分类模板: $category'));
      }
      
      // 从模板值创建规格
      final specId = 'spec_${DateTime.now().millisecondsSinceEpoch}';
      
      // 构建基础规格
      final basicSpec = BasicSpecification(
        productName: templateValues['product_name'] ?? '',
        brand: templateValues['brand'] ?? '',
        model: templateValues['model'] ?? '',
        manufacturer: templateValues['manufacturer'],
        countryOfOrigin: templateValues['country_of_origin'],
        warrantyMonths: templateValues['warranty_months'],
        description: templateValues['description'],
      );
      
      // 构建物理属性（使用默认值）
      final physicalProps = PhysicalProperties(
        dimensions: Dimensions(
          length: templateValues['length']?.toDouble() ?? 100.0,
          width: templateValues['width']?.toDouble() ?? 100.0,
          height: templateValues['height']?.toDouble() ?? 100.0,
          unit: templateValues['dimension_unit'] ?? 'mm',
        ),
        weight: templateValues['weight']?.toDouble() ?? 1.0,
        color: templateValues['color'],
        material: templateValues['material'],
        volume: templateValues['volume']?.toDouble(),
      );
      
      // 构建技术参数（根据分类）
      final technicalParams = _buildTechnicalParamsFromTemplate(category, templateValues);
      
      final specification = ProductSpecification(
        id: specId,
        materialId: materialId,
        category: category,
        basicSpec: basicSpec,
        technicalParams: technicalParams,
        physicalProps: physicalProps,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // 验证并保存
      final validationResult = validateSpecification(specification);
      if (validationResult.isLeft()) {
        return validationResult.fold(
          (failure) => Left(failure),
          (_) => throw Exception('Unexpected validation result'),
        );
      }
      
      return createSpecification(specification);
    } catch (e) {
      return Left(UnknownFailure(message: '从模板创建规格失败: $e'));
    }
  }

  /// 验证技术参数
  Either<Failure, void> _validateTechnicalParams(ProductSpecification specification) {
    // 根据分类验证不同的技术参数
    switch (specification.category.toUpperCase()) {
      case 'ELECTRICAL':
        return _validateElectricalParams(specification.technicalParams.electrical);
      case 'PLUMBING':
        return _validateFluidParams(specification.technicalParams.fluid);
      default:
        return const Right(null);
    }
  }

  /// 验证电气参数
  Either<Failure, void> _validateElectricalParams(ElectricalSpecs? electrical) {
    if (electrical == null) return const Right(null);
    
    if (electrical.ratedVoltage != null && electrical.ratedVoltage! <= 0) {
      return Left(ValidationFailure(message: '额定电压必须大于0'));
    }
    
    if (electrical.ratedCurrent != null && electrical.ratedCurrent! <= 0) {
      return Left(ValidationFailure(message: '额定电流必须大于0'));
    }
    
    return const Right(null);
  }

  /// 验证流体参数
  Either<Failure, void> _validateFluidParams(FluidSpecs? fluid) {
    if (fluid == null) return const Right(null);
    
    if (fluid.flowRate != null && fluid.flowRate! <= 0) {
      return Left(ValidationFailure(message: '流量必须大于0'));
    }
    
    if (fluid.head != null && fluid.head! < 0) {
      return Left(ValidationFailure(message: '扬程不能为负数'));
    }
    
    return const Right(null);
  }

  /// 从模板构建技术参数
  TechnicalParameters _buildTechnicalParamsFromTemplate(String category, Map<String, dynamic> values) {
    switch (category.toUpperCase()) {
      case 'ELECTRICAL':
        return TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: values['rated_voltage']?.toDouble(),
            ratedCurrent: values['rated_current']?.toDouble(),
            ratedPower: values['rated_power']?.toDouble(),
            capacity: values['capacity']?.toDouble(),
            capacityUnit: values['capacity_unit'],
            efficiency: values['efficiency']?.toDouble(),
            protectionRating: values['protection_rating'],
          ),
        );
      case 'PLUMBING':
        return TechnicalParameters(
          fluid: FluidSpecs(
            flowRate: values['flow_rate']?.toDouble(),
            head: values['head']?.toDouble(),
            pipeDiameter: values['pipe_diameter']?.toDouble(),
            connectionType: values['connection_type'],
          ),
        );
      default:
        return const TechnicalParameters();
    }
  }
}
