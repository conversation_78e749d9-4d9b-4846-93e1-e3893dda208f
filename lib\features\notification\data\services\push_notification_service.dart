import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/notification.dart' as domain;

/// 推送通知服务
class PushNotificationService {
  static final PushNotificationService _instance = PushNotificationService._internal();
  factory PushNotificationService() => _instance;
  PushNotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  String? _fcmToken;
  bool _isInitialized = false;

  /// 初始化推送通知服务
  Future<Either<Failure, void>> initialize() async {
    try {
      if (_isInitialized) return const Right(null);

      // 初始化本地通知
      await _initializeLocalNotifications();

      // 请求通知权限
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        return Left(ValidationFailure(message: '用户拒绝了通知权限'));
      }

      // 获取FCM Token
      _fcmToken = await _firebaseMessaging.getToken();
      
      // 监听Token刷新
      _firebaseMessaging.onTokenRefresh.listen((token) {
        _fcmToken = token;
        _onTokenRefresh(token);
      });

      // 设置前台消息处理
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // 设置后台消息处理
      FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

      // 处理应用终止状态下的消息
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleBackgroundMessage(initialMessage);
      }

      _isInitialized = true;
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '初始化推送通知服务失败: $e'));
    }
  }

  /// 获取FCM Token
  String? get fcmToken => _fcmToken;

  /// 订阅主题
  Future<Either<Failure, void>> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '订阅主题失败: $e'));
    }
  }

  /// 取消订阅主题
  Future<Either<Failure, void>> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '取消订阅主题失败: $e'));
    }
  }

  /// 显示本地通知
  Future<Either<Failure, void>> showLocalNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    String? imageUrl,
    domain.NotificationPriority priority = domain.NotificationPriority.normal,
  }) async {
    try {
      final androidDetails = AndroidNotificationDetails(
        'vanhub_notifications',
        'VanHub通知',
        channelDescription: 'VanHub改装宝应用通知',
        importance: _getAndroidImportance(priority),
        priority: _getAndroidPriority(priority),
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
        largeIcon: imageUrl != null ? NetworkAndroidBitmap(imageUrl) : null,
        styleInformation: imageUrl != null 
            ? BigPictureStyleInformation(
                NetworkAndroidBitmap(imageUrl),
                contentTitle: title,
                summaryText: body,
              )
            : BigTextStyleInformation(body),
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '显示本地通知失败: $e'));
    }
  }

  /// 取消本地通知
  Future<Either<Failure, void>> cancelLocalNotification(int id) async {
    try {
      await _localNotifications.cancel(id);
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '取消本地通知失败: $e'));
    }
  }

  /// 取消所有本地通知
  Future<Either<Failure, void>> cancelAllLocalNotifications() async {
    try {
      await _localNotifications.cancelAll();
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '取消所有本地通知失败: $e'));
    }
  }

  /// 定时显示本地通知
  Future<Either<Failure, void>> scheduleLocalNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    String? imageUrl,
    domain.NotificationPriority priority = domain.NotificationPriority.normal,
  }) async {
    try {
      final androidDetails = AndroidNotificationDetails(
        'vanhub_scheduled',
        'VanHub定时通知',
        channelDescription: 'VanHub改装宝定时通知',
        importance: _getAndroidImportance(priority),
        priority: _getAndroidPriority(priority),
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.zonedSchedule(
        id,
        title,
        body,
        scheduledDate,
        notificationDetails,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );

      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '定时通知失败: $e'));
    }
  }

  /// 获取待处理的通知
  Future<Either<Failure, List<PendingNotificationRequest>>> getPendingNotifications() async {
    try {
      final pendingNotifications = await _localNotifications.pendingNotificationRequests();
      return Right(pendingNotifications);
    } catch (e) {
      return Left(UnknownFailure(message: '获取待处理通知失败: $e'));
    }
  }

  /// 初始化本地通知
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initializationSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // 创建通知渠道
    await _createNotificationChannels();
  }

  /// 创建通知渠道
  Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'vanhub_notifications',
        'VanHub通知',
        description: 'VanHub改装宝应用通知',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      ),
      AndroidNotificationChannel(
        'vanhub_scheduled',
        'VanHub定时通知',
        description: 'VanHub改装宝定时通知',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      ),
      AndroidNotificationChannel(
        'vanhub_reminders',
        'VanHub提醒',
        description: 'VanHub改装宝提醒通知',
        importance: Importance.max,
        enableVibration: true,
        playSound: true,
      ),
    ];

    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }

  /// 处理前台消息
  void _handleForegroundMessage(RemoteMessage message) {
    final notification = message.notification;
    if (notification != null) {
      showLocalNotification(
        id: message.hashCode,
        title: notification.title ?? '新通知',
        body: notification.body ?? '',
        payload: jsonEncode(message.data),
        imageUrl: notification.android?.imageUrl ?? notification.apple?.imageUrl,
      );
    }
  }

  /// 处理后台消息
  void _handleBackgroundMessage(RemoteMessage message) {
    // TODO: 处理后台消息点击事件
    // 可以导航到特定页面或执行特定操作
    print('Background message: ${message.data}');
  }

  /// 处理通知点击事件
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        // TODO: 根据数据导航到相应页面
        print('Notification tapped: $data');
      } catch (e) {
        print('Error parsing notification payload: $e');
      }
    }
  }

  /// Token刷新回调
  void _onTokenRefresh(String token) {
    // TODO: 将新Token发送到服务器
    print('FCM Token refreshed: $token');
  }

  /// 获取Android重要性级别
  Importance _getAndroidImportance(domain.NotificationPriority priority) {
    switch (priority) {
      case domain.NotificationPriority.low:
        return Importance.low;
      case domain.NotificationPriority.normal:
        return Importance.defaultImportance;
      case domain.NotificationPriority.high:
        return Importance.high;
      case domain.NotificationPriority.urgent:
        return Importance.max;
    }
  }

  /// 获取Android优先级
  Priority _getAndroidPriority(domain.NotificationPriority priority) {
    switch (priority) {
      case domain.NotificationPriority.low:
        return Priority.low;
      case domain.NotificationPriority.normal:
        return Priority.defaultPriority;
      case domain.NotificationPriority.high:
        return Priority.high;
      case domain.NotificationPriority.urgent:
        return Priority.max;
    }
  }
}

/// 后台消息处理器
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // 处理后台接收到的消息
  print('Handling a background message: ${message.messageId}');
}
