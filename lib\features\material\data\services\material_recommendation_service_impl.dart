import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/services/user_behavior_tracker.dart';
import '../../../../core/services/similarity_engine.dart';
import '../../domain/entities/material.dart';
import '../../domain/entities/material_recommendation.dart';
import '../../domain/services/material_recommendation_service.dart';
import '../../domain/repositories/material_repository.dart';

/// 材料推荐服务实现
/// 遵循Clean Architecture原则，实现智能材料推荐功能
class MaterialRecommendationServiceImpl implements MaterialRecommendationService {
  final MaterialRepository materialRepository;
  final UserBehaviorTracker behaviorTracker;
  final SimilarityEngine similarityEngine;

  const MaterialRecommendationServiceImpl({
    required this.materialRepository,
    required this.behaviorTracker,
    required this.similarityEngine,
  });

  /// 判断两个类别是否互补
  bool _isComplementaryCategory(String category1, String category2) {
    // 定义互补类别映射
    const complementaryMap = {
      '电池系统': ['充电系统', '电源管理', '逆变器'],
      '充电系统': ['电池系统', '电源管理'],
      '水电系统': ['储水设备', '净水设备', '管道配件'],
      '储水设备': ['水电系统', '净水设备'],
      '照明系统': ['电池系统', '开关控制'],
      '通风系统': ['温控设备', '空调系统'],
      '厨房设备': ['燃气系统', '储水设备'],
      '燃气系统': ['厨房设备', '通风系统'],
    };

    final complementaries = complementaryMap[category1];
    return complementaries?.contains(category2) ?? false;
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForProject(
    String projectId, {
    int limit = 10,
  }) async {
    try {
      // 1. 获取项目信息和用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) async {
          // 2. 获取用户行为数据进行个性化推荐
          final userBehaviors = await behaviorTracker.getUserBehaviors(
            projectId, // 临时使用projectId作为userId
            type: UserBehaviorType.materialView,
            limit: 50,
          );

          // 3. 分析用户偏好
          final userPreferences = _analyzeUserPreferences(userBehaviors);

          // 4. 计算每个材料的推荐分数
          final scoredMaterials = <({Material material, double score})>[];

          for (final material in materials) {
            double score = 0.0;

            // 基础热门度分数
            score += material.usageCount * 2.0;

            // 用户偏好匹配分数
            if (userPreferences['categories']?.contains(material.category) == true) {
              score += 30.0;
            }

            // 价格偏好匹配分数
            final preferredPriceRange = userPreferences['price_range'] as Map<String, double>?;
            if (preferredPriceRange != null) {
              final minPrice = preferredPriceRange['min'] ?? 0.0;
              final maxPrice = preferredPriceRange['max'] ?? double.infinity;
              if (material.price >= minPrice && material.price <= maxPrice) {
                score += 20.0;
              }
            }

            // 最近使用加分
            if (material.lastUsedAt != null) {
              final daysSinceLastUse = DateTime.now().difference(material.lastUsedAt!).inDays;
              if (daysSinceLastUse < 30) {
                score += 15.0 * (30 - daysSinceLastUse) / 30;
              }
            }

            // 评分加分
            if (material.averageRating > 4.0) {
              score += (material.averageRating - 4.0) * 10.0;
            }

            scoredMaterials.add((material: material, score: score));
          }

          // 5. 排序并生成推荐
          scoredMaterials.sort((a, b) => b.score.compareTo(a.score));

          final recommendations = scoredMaterials
              .take(limit)
              .map((item) => MaterialRecommendation(
                    material: item.material,
                    relevanceScore: item.score,
                    reason: _generateRecommendationReason(item.material, userPreferences),
                    type: RecommendationType.projectBased,
                  ))
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '为项目推荐材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendForSystem(
    String projectId,
    String systemType, {
    int limit = 10,
  }) async {
    try {
      // 实现基于系统类型的材料推荐逻辑
      // 1. 获取用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 基于系统类型过滤相关材料
          final filteredMaterials = materials.where((material) {
            // 简单的系统类型匹配逻辑
            final category = material.category.toLowerCase();
            final systemTypeLower = systemType.toLowerCase();

            return category.contains(systemTypeLower) ||
                   material.name.toLowerCase().contains(systemTypeLower);
          }).toList();

          // 3. 按相关性排序并生成推荐
          final recommendations = filteredMaterials
              .take(limit)
              .map((material) => MaterialRecommendation(
                    material: material,
                    relevanceScore: 90.0, // 系统匹配推荐分数更高
                    reason: '适用于$systemType系统',
                    type: RecommendationType.systemBased,
                  ))
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '为系统推荐材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendSimilarMaterials(
    String materialId, {
    int limit = 10,
  }) async {
    try {
      // 实现相似材料推荐逻辑
      // 1. 获取指定材料信息
      final targetMaterialResult = await materialRepository.getMaterialById(materialId);

      return targetMaterialResult.fold(
        (failure) => Left(failure),
        (targetMaterial) async {
          // 2. 获取用户材料进行相似性比较
          final allMaterialsResult = await materialRepository.getUserMaterials('');

          return allMaterialsResult.fold(
            (failure) => Left(failure),
            (allMaterials) {
              // 3. 使用相似度引擎计算材料相似性
              final scoredMaterials = <({Material material, double similarity})>[];

              for (final material in allMaterials) {
                if (material.id == materialId) continue; // 排除自己

                // 计算综合相似度
                final similarity = similarityEngine.calculateMaterialSimilarity(
                  targetMaterial,
                  material,
                );

                if (similarity > 0.1) { // 只保留相似度大于0.1的材料
                  scoredMaterials.add((material: material, similarity: similarity));
                }
              }

              // 4. 按相似度排序
              scoredMaterials.sort((a, b) => b.similarity.compareTo(a.similarity));

              // 5. 生成推荐
              final recommendations = scoredMaterials
                  .take(limit)
                  .map((item) => MaterialRecommendation(
                        material: item.material,
                        relevanceScore: item.similarity * 100, // 转换为0-100分数
                        reason: _generateSimilarityReason(targetMaterial, item.material, item.similarity),
                        type: RecommendationType.similarMaterial,
                      ))
                  .toList();

              return Right(recommendations);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐相似材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendComplementaryMaterials(
    String materialId, {
    int limit = 10,
  }) async {
    try {
      // 实现搭配材料推荐逻辑
      // 1. 获取指定材料信息
      final targetMaterialResult = await materialRepository.getMaterialById(materialId);

      return targetMaterialResult.fold(
        (failure) => Left(failure),
        (targetMaterial) async {
          // 2. 获取用户材料进行搭配分析
          final allMaterialsResult = await materialRepository.getUserMaterials('');

          return allMaterialsResult.fold(
            (failure) => Left(failure),
            (allMaterials) {
              // 3. 基于类别互补性查找搭配材料
              final complementaryMaterials = allMaterials
                  .where((material) => material.id != materialId) // 排除自己
                  .where((material) {
                    // 简单的搭配逻辑：不同类别但相关的材料
                    return material.category != targetMaterial.category &&
                           _isComplementaryCategory(targetMaterial.category, material.category);
                  })
                  .take(limit)
                  .map((material) => MaterialRecommendation(
                        material: material,
                        relevanceScore: 60.0, // 搭配材料推荐分数
                        reason: '与${targetMaterial.name}搭配使用',
                        type: RecommendationType.complementary,
                      ))
                  .toList();

              return Right(complementaryMaterials);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐搭配材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendPopularMaterials(
    String userId, {
    String? category,
    int limit = 10,
  }) async {
    try {
      // 实现热门材料推荐逻辑
      // 1. 获取用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 按类别过滤（如果指定）
          var filteredMaterials = materials;
          if (category != null && category.isNotEmpty && category != '全部') {
            filteredMaterials = materials.where((material) =>
                material.category == category).toList();
          }

          // 3. 按使用频率排序（usageCount）
          filteredMaterials.sort((a, b) => b.usageCount.compareTo(a.usageCount));

          // 4. 生成热门材料推荐
          final recommendations = filteredMaterials
              .take(limit)
              .map((material) => MaterialRecommendation(
                    material: material,
                    relevanceScore: 80.0 + (material.usageCount * 1.0), // 基于使用次数的动态分数
                    reason: '热门材料 (使用${material.usageCount}次)',
                    type: RecommendationType.popular,
                  ))
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐热门材料失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<MaterialRecommendation>>> recommendValueForMoneyMaterials(
    String userId, {
    String? category,
    int limit = 10,
  }) async {
    try {
      // 实现性价比材料推荐逻辑
      // 1. 获取用户材料
      final materialsResult = await materialRepository.getUserMaterials('');

      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 2. 按类别过滤（如果指定）
          var filteredMaterials = materials;
          if (category != null && category.isNotEmpty && category != '全部') {
            filteredMaterials = materials.where((material) =>
                material.category == category).toList();
          }

          // 3. 过滤有价格的材料并计算性价比
          final materialsWithPrice = filteredMaterials
              .where((material) => material.price > 0)
              .toList();

          // 4. 按性价比排序（使用次数/价格比）
          materialsWithPrice.sort((a, b) {
            final ratioA = a.usageCount / a.price;
            final ratioB = b.usageCount / b.price;
            return ratioB.compareTo(ratioA);
          });

          // 5. 生成性价比推荐
          final recommendations = materialsWithPrice
              .take(limit)
              .map((material) {
                final ratio = material.usageCount / material.price;
                return MaterialRecommendation(
                  material: material,
                  relevanceScore: 70.0 + (ratio * 10.0).clamp(0.0, 30.0), // 基于性价比的动态分数
                  reason: '高性价比 (¥${material.price.toStringAsFixed(2)})',
                  type: RecommendationType.valueForMoney,
                );
              })
              .toList();

          return Right(recommendations);
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '推荐性价比材料失败: $e'));
    }
  }

  /// 分析用户偏好
  Map<String, dynamic> _analyzeUserPreferences(List<UserBehaviorData> behaviors) {
    final preferences = <String, dynamic>{};

    // 分析分类偏好
    final categoryCount = <String, int>{};
    final priceList = <double>[];

    for (final behavior in behaviors) {
      final category = behavior.context['category'] as String?;
      if (category != null) {
        categoryCount[category] = (categoryCount[category] ?? 0) + 1;
      }

      final price = behavior.context['price'] as double?;
      if (price != null) {
        priceList.add(price);
      }
    }

    // 获取偏好分类（按频次排序）
    final sortedCategories = categoryCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    preferences['categories'] = sortedCategories.map((e) => e.key).take(3).toList();

    // 计算价格偏好范围
    if (priceList.isNotEmpty) {
      priceList.sort();
      final q1 = priceList[(priceList.length * 0.25).floor()];
      final q3 = priceList[(priceList.length * 0.75).floor()];
      preferences['price_range'] = {'min': q1, 'max': q3};
    }

    return preferences;
  }

  /// 生成推荐理由
  String _generateRecommendationReason(Material material, Map<String, dynamic> preferences) {
    final reasons = <String>[];

    // 基于分类偏好
    final preferredCategories = preferences['categories'] as List<String>?;
    if (preferredCategories?.contains(material.category) == true) {
      reasons.add('符合您的${material.category}偏好');
    }

    // 基于热门度
    if (material.usageCount > 10) {
      reasons.add('热门材料(${material.usageCount}次使用)');
    }

    // 基于评分
    if (material.averageRating > 4.0) {
      reasons.add('高评分材料(${material.averageRating.toStringAsFixed(1)}分)');
    }

    // 基于价格
    final priceRange = preferences['price_range'] as Map<String, double>?;
    if (priceRange != null &&
        material.price >= priceRange['min']! &&
        material.price <= priceRange['max']!) {
      reasons.add('价格符合预期');
    }

    return reasons.isNotEmpty ? reasons.join('，') : '推荐材料';
  }

  /// 生成相似性推荐理由
  String _generateSimilarityReason(Material target, Material similar, double similarity) {
    final reasons = <String>[];

    // 基于相似度分数
    if (similarity > 0.8) {
      reasons.add('高度相似');
    } else if (similarity > 0.6) {
      reasons.add('较为相似');
    } else {
      reasons.add('部分相似');
    }

    // 基于具体相似点
    if (target.category == similar.category) {
      reasons.add('同类别(${target.category})');
    }

    if (target.brand != null && target.brand == similar.brand) {
      reasons.add('同品牌(${target.brand})');
    }

    // 基于价格相似性
    final priceDiff = (target.price - similar.price).abs();
    final priceRatio = priceDiff / target.price;
    if (priceRatio < 0.2) {
      reasons.add('价格相近');
    }

    return reasons.join('，');
  }
}