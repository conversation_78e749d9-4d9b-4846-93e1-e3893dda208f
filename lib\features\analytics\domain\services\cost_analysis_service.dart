import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/cost_analysis.dart';

/// 成本分析服务接口
abstract class CostAnalysisService {
  /// 分析项目成本
  Future<Either<Failure, CostAnalysis>> analyzeProjectCost(String projectId);
  
  /// 检测预算超支风险
  Future<Either<Failure, BudgetAlert>> checkBudgetAlert(String projectId);
  
  /// 分析成本趋势
  Future<Either<Failure, CostTrendAnalysis>> analyzeCostTrend(
    String projectId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// 分析成本结构
  Future<Either<Failure, CostStructureAnalysis>> analyzeCostStructure(String projectId);
  
  /// 预测未来成本
  Future<Either<Failure, CostForecast>> forecastCost(
    String projectId, {
    int forecastDays = 30,
  });
  
  /// 比较项目成本
  Future<Either<Failure, CostComparison>> compareProjectCosts(
    List<String> projectIds,
  );
  
  /// 生成成本优化建议
  Future<Either<Failure, List<CostOptimizationSuggestion>>> generateOptimizationSuggestions(
    String projectId,
  );
  
  /// 计算投资回报率
  Future<Either<Failure, ROIAnalysis>> calculateROI(String projectId);
}
