import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

// Core services
import '../services/user_behavior_tracker.dart';
import '../services/similarity_engine.dart';
import '../services/file_upload_service.dart';

// Log imports
import '../../features/modification_log/domain/repositories/log_repository.dart';
import '../../features/modification_log/data/repositories/log_repository_impl.dart';
import '../../features/modification_log/data/datasources/log_remote_datasource.dart';
import '../../features/modification_log/domain/repositories/timeline_repository.dart';
import '../../features/modification_log/data/repositories/timeline_repository_impl.dart';
import '../../features/modification_log/data/datasources/timeline_remote_datasource.dart';
import '../../features/modification_log/domain/usecases/create_log_entry_usecase.dart';
import '../../features/modification_log/domain/usecases/update_log_entry_usecase.dart';
import '../../features/modification_log/domain/usecases/delete_log_entry_usecase.dart';
import '../../features/modification_log/domain/usecases/get_log_entry_usecase.dart';
import '../../features/modification_log/domain/usecases/get_project_logs_usecase.dart';
import '../../features/modification_log/domain/usecases/get_system_logs_usecase.dart';
import '../../features/modification_log/domain/usecases/search_logs_usecase.dart';
import '../../features/modification_log/domain/usecases/get_project_timeline_usecase.dart';
import '../../features/modification_log/domain/usecases/get_project_milestones_usecase.dart';
import '../../features/modification_log/domain/usecases/add_milestone_usecase.dart';

// Auth imports
import '../../features/auth/data/datasources/auth_remote_datasource.dart';
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/domain/usecases/register_usecase.dart';
import '../../features/auth/domain/usecases/logout_usecase.dart';

// Project imports
import '../../features/project/data/datasources/project_remote_datasource.dart';
import '../../features/project/data/repositories/project_repository_impl.dart';
import '../../features/project/domain/repositories/project_repository.dart';
import '../../features/project/domain/usecases/create_project_usecase.dart';
import '../../features/project/domain/usecases/get_projects_usecase.dart';
import '../../features/project/domain/usecases/fork_project_usecase.dart';

// Material imports
import '../../features/material/data/datasources/material_remote_datasource.dart';
import '../../features/material/data/repositories/material_repository_impl.dart';
import '../../features/material/domain/repositories/material_repository.dart';
import '../../features/material/domain/services/material_search_service.dart';
import '../../features/material/data/services/material_search_service_impl.dart';
import '../../features/material/domain/services/material_recommendation_service.dart';
import '../../features/material/data/services/material_recommendation_service_impl.dart';
import '../../features/material/domain/services/data_sync_service.dart';
import '../../features/material/data/services/data_sync_service_impl.dart';
import '../../features/project/domain/services/fork_permission_service.dart';
import '../../features/project/data/services/fork_permission_service_impl.dart';
import '../../features/project/domain/services/project_fork_service.dart';
import '../../features/project/data/services/project_fork_service_impl.dart';
import '../../features/project/domain/services/project_stats_service.dart';
import '../../features/project/data/services/project_stats_service_impl.dart';

// BOM imports
import '../../features/bom/data/datasources/bom_remote_datasource.dart';
import '../../features/bom/data/repositories/bom_repository_impl.dart';
import '../../features/bom/domain/repositories/bom_repository.dart';
import '../../features/bom/domain/usecases/get_bom_items_usecase.dart';
import '../../features/bom/domain/usecases/create_bom_item_usecase.dart';
import '../../features/bom/domain/usecases/add_material_to_bom_usecase.dart';
import '../../features/bom/domain/services/bom_statistics_service.dart';
import '../../features/bom/domain/services/bom_statistics_service_impl.dart';
import '../../features/bom/presentation/providers/bom_controller.dart';

part 'injection_container.g.dart';

// Core providers
final supabaseClientProvider = Provider<supabase.SupabaseClient>((ref) {
  try {
    final client = supabase.Supabase.instance.client;
    if (client.auth.currentUser == null) {
      // 如果没有当前用户，这是正常的（游客模式）
    }
    return client;
  } catch (e) {
    // 如果Supabase未初始化，抛出更明确的错误
    throw Exception('Supabase客户端未初始化，请确保在main()中正确初始化Supabase');
  }
});

// Core service providers
final fileUploadServiceProvider = Provider<FileUploadService>((ref) {
  return FileUploadService(
    supabaseClient: ref.read(supabaseClientProvider),
  );
});

// Auth module providers
final authRemoteDataSourceProvider = Provider((ref) {
  return AuthRemoteDataSourceImpl(
    supabaseClient: ref.read(supabaseClientProvider),
  );
});

final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    remoteDataSource: ref.read(authRemoteDataSourceProvider),
  );
});

// Auth UseCase providers
final loginUseCaseProvider = Provider<LoginUseCase>((ref) {
  return LoginUseCase(ref.read(authRepositoryProvider));
});

final registerUseCaseProvider = Provider<RegisterUseCase>((ref) {
  return RegisterUseCase(ref.read(authRepositoryProvider));
});

final logoutUseCaseProvider = Provider<LogoutUseCase>((ref) {
  return LogoutUseCase(ref.read(authRepositoryProvider));
});

// Project module providers
@riverpod
ProjectRemoteDataSource projectRemoteDataSource(Ref ref) {
  return ProjectRemoteDataSourceImpl(
    supabaseClient: ref.read(supabaseClientProvider),
  );
}

@riverpod
ProjectRepository projectRepository(Ref ref) {
  return ProjectRepositoryImpl(
    remoteDataSource: ref.read(projectRemoteDataSourceProvider),
    ref: ref,
  );
}

// Project UseCase providers
final createProjectUseCaseProvider = Provider<CreateProjectUseCase>((ref) {
  return CreateProjectUseCase(ref.read(projectRepositoryProvider));
});

final getProjectsUseCaseProvider = Provider<GetProjectsUseCase>((ref) {
  return GetProjectsUseCase(ref.read(projectRepositoryProvider));
});

final forkProjectUseCaseProvider = Provider<ForkProjectUseCase>((ref) {
  return ForkProjectUseCase(ref.read(projectRepositoryProvider));
});

// Material module providers
final materialRemoteDataSourceProvider = Provider((ref) {
  return MaterialRemoteDataSourceImpl(
    supabaseClient: ref.read(supabaseClientProvider),
  );
});

final materialRepositoryProvider = Provider<MaterialRepository>((ref) {
  return MaterialRepositoryImpl(
    remoteDataSource: ref.read(materialRemoteDataSourceProvider),
    ref: ref,
  );
});

// Material services
final materialSearchServiceProvider = Provider<MaterialSearchService>((ref) {
  return MaterialSearchServiceImpl(materialRepository: ref.read(materialRepositoryProvider));
});

final materialRecommendationServiceProvider = Provider<MaterialRecommendationService>((ref) {
  return MaterialRecommendationServiceImpl(
    materialRepository: ref.read(materialRepositoryProvider),
    behaviorTracker: UserBehaviorTracker(),
    similarityEngine: SimilarityEngine(),
  );
});

final dataSyncServiceProvider = Provider<DataSyncService>((ref) {
  return DataSyncServiceImpl(
    materialDataSource: ref.read(materialRemoteDataSourceProvider),
    bomDataSource: ref.read(bomRemoteDataSourceProvider),
    materialRepository: ref.read(materialRepositoryProvider),
    supabaseClient: supabase.Supabase.instance.client,
  );
});

// ============ Project Fork Services ============
final forkPermissionServiceProvider = Provider<ForkPermissionService>((ref) {
  return ForkPermissionServiceImpl(
    projectRepository: ref.read(projectRepositoryProvider),
  );
});

final projectForkServiceProvider = Provider<ProjectForkService>((ref) {
  return ProjectForkServiceImpl(
    projectRepository: ref.read(projectRepositoryProvider),
    bomRepository: ref.read(bomRepositoryProvider),
    permissionService: ref.read(forkPermissionServiceProvider),
  );
});

final projectStatsServiceProvider = Provider<ProjectStatsService>((ref) {
  return ProjectStatsServiceImpl(
    ref.read(projectRepositoryProvider),
    ref.read(bomRepositoryProvider),
  );
});

// BOM module providers
final bomRemoteDataSourceProvider = Provider((ref) {
  return BomRemoteDataSourceImpl(
    supabaseClient: ref.read(supabaseClientProvider),
  );
});

final bomRepositoryProvider = Provider<BomRepository>((ref) {
  return BomRepositoryImpl(
    remoteDataSource: ref.read(bomRemoteDataSourceProvider),
    ref: ref,
  );
});

// BOM use cases
final getBomItemsUseCaseProvider = Provider((ref) {
  return GetBomItemsUseCase(ref.read(bomRepositoryProvider));
});

final createBomItemUseCaseProvider = Provider((ref) {
  return CreateBomItemUseCase(ref.read(bomRepositoryProvider));
});

final addMaterialToBomUseCaseProvider = Provider((ref) {
  return AddMaterialToBomUseCase(ref.read(bomRepositoryProvider));
});


// BOM services
final bomStatisticsServiceProvider = Provider<BomStatisticsService>((ref) {
  return BomStatisticsServiceImpl();
});

// BOM controller
final bomControllerProvider = Provider<BomController>((ref) {
  return BomController(ref);
});
// Log Repository and DataSource Providers
final logRemoteDataSourceProvider = Provider<LogRemoteDataSource>((ref) {
  return LogRemoteDataSourceImpl(
    supabaseClient: supabase.Supabase.instance.client,
  );
});

final logRepositoryProvider = Provider<LogRepository>((ref) {
  return LogRepositoryImpl(
    remoteDataSource: ref.read(logRemoteDataSourceProvider),
  );
});

final timelineRemoteDataSourceProvider = Provider<TimelineRemoteDataSource>((ref) {
  return TimelineRemoteDataSourceImpl(
    supabaseClient: supabase.Supabase.instance.client,
  );
});

final timelineRepositoryProvider = Provider<TimelineRepository>((ref) {
  return TimelineRepositoryImpl(
    remoteDataSource: ref.read(timelineRemoteDataSourceProvider),
  );
});

// Log UseCase Providers
final createLogEntryUseCaseProvider = Provider<CreateLogEntryUseCase>((ref) {
  return CreateLogEntryUseCase(ref.read(logRepositoryProvider));
});

final updateLogEntryUseCaseProvider = Provider<UpdateLogEntryUseCase>((ref) {
  return UpdateLogEntryUseCase(ref.read(logRepositoryProvider));
});

final deleteLogEntryUseCaseProvider = Provider<DeleteLogEntryUseCase>((ref) {
  return DeleteLogEntryUseCase(ref.read(logRepositoryProvider));
});

final getLogEntryUseCaseProvider = Provider<GetLogEntryUseCase>((ref) {
  return GetLogEntryUseCase(ref.read(logRepositoryProvider));
});

final getProjectLogsUseCaseProvider = Provider<GetProjectLogsUseCase>((ref) {
  return GetProjectLogsUseCase(ref.read(logRepositoryProvider));
});

final getSystemLogsUseCaseProvider = Provider<GetSystemLogsUseCase>((ref) {
  return GetSystemLogsUseCase(ref.read(logRepositoryProvider));
});

final searchLogsUseCaseProvider = Provider<SearchLogsUseCase>((ref) {
  return SearchLogsUseCase(ref.read(logRepositoryProvider));
});

final getProjectTimelineUseCaseProvider = Provider<GetProjectTimelineUseCase>((ref) {
  return GetProjectTimelineUseCase(ref.read(timelineRepositoryProvider));
});

final getProjectMilestonesUseCaseProvider = Provider<GetProjectMilestonesUseCase>((ref) {
  return GetProjectMilestonesUseCase(ref.read(timelineRepositoryProvider));
});

final addMilestoneUseCaseProvider = Provider<AddMilestoneUseCase>((ref) {
  return AddMilestoneUseCase(ref.read(timelineRepositoryProvider));
});