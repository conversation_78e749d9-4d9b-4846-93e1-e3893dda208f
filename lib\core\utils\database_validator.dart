import 'package:flutter/foundation.dart';
import '../api/supabase_config.dart';

/// 数据库连接验证工具
class DatabaseValidator {
  /// 验证数据库配置和连接
  static Future<ValidationResult> validateConnection() async {
    final results = <String, bool>{};
    final errors = <String>[];
    
    try {
      // 1. 检查初始化状态
      results['initialization'] = SupabaseConfig.isInitialized;
      if (!SupabaseConfig.isInitialized) {
        errors.add('Supabase未初始化');
      }
      
      // 2. 检查配置
      final hasUrl = SupabaseConfig.supabaseUrl.isNotEmpty;
      final hasKey = SupabaseConfig.supabaseAnonKey.isNotEmpty;
      results['configuration'] = hasUrl && hasKey;
      
      if (!hasUrl) errors.add('SUPABASE_URL未配置');
      if (!hasKey) errors.add('SUPABASE_ANON_KEY未配置');
      
      // 3. 检查URL格式
      if (hasUrl) {
        final urlValid = SupabaseConfig.supabaseUrl.startsWith('https://') && 
                        SupabaseConfig.supabaseUrl.contains('.supabase.co');
        results['url_format'] = urlValid;
        if (!urlValid) errors.add('Supabase URL格式不正确');
      }
      
      // 4. 检查连接
      if (results['initialization'] == true && results['configuration'] == true) {
        try {
          final connected = await SupabaseConfig.checkConnection();
          results['connection'] = connected;
          if (!connected) errors.add('无法连接到Supabase服务器');
        } catch (e) {
          results['connection'] = false;
          errors.add('连接测试失败: $e');
        }
      } else {
        results['connection'] = false;
        errors.add('跳过连接测试（配置不完整）');
      }
      
      // 5. 检查认证系统
      try {
        final auth = SupabaseConfig.client.auth;
        results['auth_system'] = auth != null;
      } catch (e) {
        results['auth_system'] = false;
        errors.add('认证系统检查失败: $e');
      }
      
      final allPassed = results.values.every((result) => result == true);
      
      return ValidationResult(
        success: allPassed,
        results: results,
        errors: errors,
        summary: _generateSummary(results, errors),
      );
      
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('数据库验证过程中发生错误: $e');
        print('堆栈跟踪: $stackTrace');
      }
      
      return ValidationResult(
        success: false,
        results: results,
        errors: [...errors, '验证过程异常: $e'],
        summary: '验证过程中发生严重错误',
      );
    }
  }
  
  static String _generateSummary(Map<String, bool> results, List<String> errors) {
    final total = results.length;
    final passed = results.values.where((r) => r == true).length;
    
    if (passed == total) {
      return '✅ 所有检查通过 ($passed/$total)';
    } else if (passed > total / 2) {
      return '⚠️ 部分检查通过 ($passed/$total)';
    } else {
      return '❌ 多项检查失败 ($passed/$total)';
    }
  }
  
  /// 生成详细的验证报告
  static String generateReport(ValidationResult result) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== VanHub 数据库连接验证报告 ===');
    buffer.writeln('验证时间: ${DateTime.now()}');
    buffer.writeln('总体状态: ${result.summary}');
    buffer.writeln();
    
    buffer.writeln('--- 检查结果 ---');
    result.results.forEach((check, passed) {
      final status = passed ? '✅' : '❌';
      final name = _getCheckDisplayName(check);
      buffer.writeln('$status $name');
    });
    
    if (result.errors.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('--- 发现的问题 ---');
      for (int i = 0; i < result.errors.length; i++) {
        buffer.writeln('${i + 1}. ${result.errors[i]}');
      }
    }
    
    buffer.writeln();
    buffer.writeln('--- 建议 ---');
    if (result.success) {
      buffer.writeln('• 数据库连接正常，可以开始使用应用功能');
    } else {
      buffer.writeln('• 请检查.env.local文件中的Supabase配置');
      buffer.writeln('• 确保网络连接正常');
      buffer.writeln('• 验证Supabase项目状态');
    }
    
    buffer.writeln();
    buffer.writeln('=== 报告结束 ===');
    
    return buffer.toString();
  }
  
  static String _getCheckDisplayName(String check) {
    switch (check) {
      case 'initialization': return '初始化检查';
      case 'configuration': return '配置检查';
      case 'url_format': return 'URL格式检查';
      case 'connection': return '网络连接检查';
      case 'auth_system': return '认证系统检查';
      default: return check;
    }
  }
}

/// 验证结果
class ValidationResult {
  final bool success;
  final Map<String, bool> results;
  final List<String> errors;
  final String summary;
  
  const ValidationResult({
    required this.success,
    required this.results,
    required this.errors,
    required this.summary,
  });
  
  /// 获取通过的检查数量
  int get passedCount => results.values.where((r) => r == true).length;
  
  /// 获取总检查数量
  int get totalCount => results.length;
  
  /// 获取通过率
  double get passRate => totalCount > 0 ? passedCount / totalCount : 0.0;
  
  /// 是否健康（所有关键检查都通过）
  bool get isHealthy {
    return success && 
           results['initialization'] == true &&
           results['configuration'] == true &&
           results['connection'] == true;
  }
}