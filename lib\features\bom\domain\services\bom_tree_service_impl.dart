import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bom_item.dart';
import '../entities/tree_node.dart';
import 'bom_tree_service.dart';

/// BOM树形结构服务实现
/// 严格遵循Clean Architecture原则，实现BOM树形结构的核心业务逻辑
class BomTreeServiceImpl implements BomTreeService {
  @override
  Either<Failure, List<TreeNode>> buildTreeFromBomItems(
    List<BomItem> bomItems,
  ) {
    try {
      if (bomItems.isEmpty) {
        return const Right([]);
      }

      // 按材料分类分组BOM项目
      final Map<String, List<BomItem>> categoryGroups = {};
      for (final bomItem in bomItems) {
        final category = bomItem.effectiveMaterialCategory ?? '未分类';
        if (!categoryGroups.containsKey(category)) {
          categoryGroups[category] = [];
        }
        categoryGroups[category]!.add(bomItem);
      }

      // 构建树节点
      final List<TreeNode> rootNodes = [];
      int categoryIndex = 0;

      for (final entry in categoryGroups.entries) {
        final categoryName = entry.key;
        final categoryBomItems = entry.value;

        // 计算分类统计信息
        final totalValue = categoryBomItems.fold<double>(
          0.0,
          (sum, item) => sum + (item.unitPrice * item.quantity),
        );
        final completedCount = categoryBomItems
            .where((item) => item.status == BomItemStatus.installed)
            .length;

        // 创建分类节点
        final categoryNode = TreeNode(
          id: 'bom_category_$categoryIndex',
          label: categoryName,
          type: BomTreeNodeType.category,
          depth: 0,
          children: _buildBomItemNodes(categoryBomItems, categoryName, 1),
          isExpanded: _shouldExpandNode(categoryName, 0),
          materialCount: categoryBomItems.length,
          totalValue: totalValue,
          statusDistribution: {
            'completed': completedCount,
            'total': categoryBomItems.length,
          },
          metadata: {
            'totalValue': totalValue,
            'completedCount': completedCount,
            'completionRate': completedCount / categoryBomItems.length,
            'categoryType': 'bom',
          },
        );

        rootNodes.add(categoryNode);
        categoryIndex++;
      }

      // 排序根节点 - 默认按名称排序
      _sortNodes(rootNodes, BomTreeSortMode.byName);

      return Right(rootNodes);
    } catch (e) {
      return Left(ServerFailure(message: '构建BOM树形结构失败: $e'));
    }
  }

  @override
  Either<Failure, List<String>> searchInBomTree(
    List<TreeNode> tree,
    String query,
  ) {
    try {
      if (query.trim().isEmpty) {
        return const Right([]);
      }

      final List<String> results = [];
      final lowerQuery = query.toLowerCase();

      void searchInNode(TreeNode node) {
        // 搜索节点标签
        if (node.label.toLowerCase().contains(lowerQuery)) {
          results.add(node.id);
        }

        // 如果是BOM项目节点，搜索更多字段
        if (node.type == BomTreeNodeType.bomItem) {
          final metadata = node.metadata;
          final materialName = metadata['materialName']?.toString() ?? '';
          final brand = metadata['brand']?.toString() ?? '';
          final model = metadata['model']?.toString() ?? '';
          final status = metadata['status']?.toString() ?? '';

          if (materialName.toLowerCase().contains(lowerQuery) ||
              brand.toLowerCase().contains(lowerQuery) ||
              model.toLowerCase().contains(lowerQuery) ||
              status.toLowerCase().contains(lowerQuery)) {
            results.add(node.id);
          }
        }

        // 递归搜索子节点
        for (final child in node.children) {
          searchInNode(child);
        }
      }

      for (final node in tree) {
        searchInNode(node);
      }

      return Right(results);
    } catch (e) {
      return Left(ServerFailure(message: 'BOM树搜索失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> toggleBomNodeExpansion(
    List<TreeNode> tree,
    String nodeId,
  ) {
    try {
      final updatedTree = _toggleNodeExpansionRecursive(tree, nodeId);
      return Right(updatedTree);
    } catch (e) {
      return Left(ServerFailure(message: '切换BOM节点展开状态失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> expandPathToBomItem(
    List<TreeNode> tree,
    String bomItemId,
  ) {
    try {
      final updatedTree = _expandPathToNodeRecursive(tree, bomItemId);
      return Right(updatedTree);
    } catch (e) {
      return Left(ServerFailure(message: '展开到BOM项目路径失败: $e'));
    }
  }

  @override
  Either<Failure, Map<String, BomCategoryStats>> calculateBomCategoryStatistics(
    List<BomItem> bomItems,
  ) {
    try {
      final Map<String, BomCategoryStats> stats = {};
      final Map<String, List<BomItem>> categoryGroups = {};

      // 按分类分组
      for (final bomItem in bomItems) {
        final category = bomItem.effectiveMaterialCategory ?? '未分类';
        if (!categoryGroups.containsKey(category)) {
          categoryGroups[category] = [];
        }
        categoryGroups[category]!.add(bomItem);
      }

      // 计算每个分类的统计信息
      for (final entry in categoryGroups.entries) {
        final categoryName = entry.key;
        final items = entry.value;

        final totalValue = items.fold<double>(
          0.0,
          (sum, item) => sum + (item.unitPrice * item.quantity),
        );

        final statusDistribution = <BomItemStatus, int>{};
        for (final status in BomItemStatus.values) {
          statusDistribution[status] = items.where((item) => item.status == status).length;
        }

        final pendingCount = items.where((item) => item.status == BomItemStatus.pending).length;
        final completedCount = items.where((item) => item.status == BomItemStatus.installed).length;
        final completionRate = items.isNotEmpty ? completedCount / items.length : 0.0;

        stats[categoryName] = BomCategoryStats(
          categoryName: categoryName,
          totalItems: items.length,
          totalValue: totalValue,
          statusDistribution: statusDistribution,
          pendingCount: pendingCount,
          completedCount: completedCount,
          completionRate: completionRate,
        );
      }

      return Right(stats);
    } catch (e) {
      return Left(ServerFailure(message: '计算BOM分类统计失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> filterBomTreeByStatus(
    List<TreeNode> tree,
    List<BomItemStatus> statuses,
  ) {
    try {
      final filteredTree = _filterTreeByStatus(tree, statuses);
      return Right(filteredTree);
    } catch (e) {
      return Left(ServerFailure(message: '按状态过滤BOM树失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> filterBomTreeByPriceRange(
    List<TreeNode> tree,
    double minPrice,
    double maxPrice,
  ) {
    try {
      final filteredTree = _filterTreeByPriceRange(tree, minPrice, maxPrice);
      return Right(filteredTree);
    } catch (e) {
      return Left(ServerFailure(message: '按价格过滤BOM树失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> sortBomTree(
    List<TreeNode> tree,
    BomTreeSortMode sortMode,
  ) {
    try {
      final sortedTree = List<TreeNode>.from(tree);
      _sortNodes(sortedTree, sortMode);
      
      // 递归排序子节点
      for (int i = 0; i < sortedTree.length; i++) {
        if (sortedTree[i].children.isNotEmpty) {
          final sortedChildren = List<TreeNode>.from(sortedTree[i].children);
          _sortNodes(sortedChildren, sortMode);
          sortedTree[i] = sortedTree[i].copyWith(children: sortedChildren);
        }
      }
      
      return Right(sortedTree);
    } catch (e) {
      return Left(ServerFailure(message: 'BOM树排序失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> updateBomItemInTree(
    List<TreeNode> tree,
    BomItem updatedItem,
  ) {
    try {
      final updatedTree = _updateBomItemInTreeRecursive(tree, updatedItem);
      return Right(updatedTree);
    } catch (e) {
      return Left(ServerFailure(message: '更新BOM树项目失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> removeBomItemFromTree(
    List<TreeNode> tree,
    String bomItemId,
  ) {
    try {
      final updatedTree = _removeBomItemFromTreeRecursive(tree, bomItemId);
      return Right(updatedTree);
    } catch (e) {
      return Left(ServerFailure(message: '从BOM树移除项目失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> addBomItemToTree(
    List<TreeNode> tree,
    BomItem newItem,
  ) {
    try {
      final updatedTree = _addBomItemToTreeRecursive(tree, newItem);
      return Right(updatedTree);
    } catch (e) {
      return Left(ServerFailure(message: '添加BOM项目到树失败: $e'));
    }
  }

  @override
  Either<Failure, bool> validateBomTreeStructure(
    List<TreeNode> tree,
  ) {
    try {
      final isValid = _validateTreeStructureRecursive(tree);
      return Right(isValid);
    } catch (e) {
      return Left(ServerFailure(message: '验证BOM树结构失败: $e'));
    }
  }

  @override
  Either<Failure, List<TreeNode>> rebuildBomTreeIndex(
    List<TreeNode> tree,
  ) {
    try {
      final reindexedTree = _rebuildTreeIndexRecursive(tree);
      return Right(reindexedTree);
    } catch (e) {
      return Left(ServerFailure(message: '重建BOM树索引失败: $e'));
    }
  }

  // ==================== 私有辅助方法 ====================

  /// 构建BOM项目节点
  List<TreeNode> _buildBomItemNodes(
    List<BomItem> bomItems,
    String categoryName,
    int depth,
  ) {
    final List<TreeNode> nodes = [];

    for (int i = 0; i < bomItems.length; i++) {
      final bomItem = bomItems[i];
      final node = TreeNode(
        id: 'bom_item_${bomItem.id}',
        label: bomItem.materialName,
        type: BomTreeNodeType.bomItem,
        depth: depth,
        bomItem: bomItem,
        parentId: 'bom_category_${categoryName.hashCode}',
        materialCount: 1,
        totalValue: bomItem.totalCost,
        metadata: {
          'bomItemId': bomItem.id,
          'materialId': bomItem.materialId,
          'materialName': bomItem.materialName,
          'brand': bomItem.effectiveMaterialBrand,
          'model': bomItem.effectiveMaterialModel,
          'quantity': bomItem.quantity,
          'unitPrice': bomItem.unitPrice,
          'totalPrice': bomItem.totalCost,
          'status': bomItem.status.code,
          'statusDisplay': bomItem.status.displayName,
          'category': bomItem.effectiveMaterialCategory,
          'notes': bomItem.notes,
          'createdAt': bomItem.createdAt.toIso8601String(),
          'updatedAt': bomItem.updatedAt.toIso8601String(),
        },
      );
      nodes.add(node);
    }

    return nodes;
  }

  /// 判断节点是否应该展开
  bool _shouldExpandNode(String nodeLabel, int depth) {
    return depth < 2; // 默认展开前2级
  }

  /// 映射排序模式
  BomTreeSortMode _mapToSortMode(dynamic sortMode) {
    if (sortMode is BomTreeSortMode) return sortMode;
    return BomTreeSortMode.byName; // 默认按名称排序
  }

  /// 排序节点
  void _sortNodes(List<TreeNode> nodes, BomTreeSortMode sortMode) {
    nodes.sort((a, b) {
      switch (sortMode) {
        case BomTreeSortMode.byName:
          return a.label.compareTo(b.label);
        case BomTreeSortMode.byPrice:
          final aPrice = a.metadata['totalPrice'] as double? ?? 0.0;
          final bPrice = b.metadata['totalPrice'] as double? ?? 0.0;
          return bPrice.compareTo(aPrice); // 降序
        case BomTreeSortMode.byStatus:
          final aStatus = a.metadata['status'] as String? ?? '';
          final bStatus = b.metadata['status'] as String? ?? '';
          return aStatus.compareTo(bStatus);
        case BomTreeSortMode.byQuantity:
          final aQty = a.metadata['quantity'] as int? ?? 0;
          final bQty = b.metadata['quantity'] as int? ?? 0;
          return bQty.compareTo(aQty); // 降序
        case BomTreeSortMode.byCreatedDate:
          final aDate = a.metadata['createdAt'] as String? ?? '';
          final bDate = b.metadata['createdAt'] as String? ?? '';
          return bDate.compareTo(aDate); // 最新的在前
        case BomTreeSortMode.byUpdatedDate:
          final aDate = a.metadata['updatedAt'] as String? ?? '';
          final bDate = b.metadata['updatedAt'] as String? ?? '';
          return bDate.compareTo(aDate); // 最新的在前
        case BomTreeSortMode.byCategory:
          final aCategory = a.metadata['category'] as String? ?? '';
          final bCategory = b.metadata['category'] as String? ?? '';
          return aCategory.compareTo(bCategory);
      }
    });
  }

  /// 递归切换节点展开状态
  List<TreeNode> _toggleNodeExpansionRecursive(List<TreeNode> nodes, String nodeId) {
    return nodes.map((node) {
      if (node.id == nodeId) {
        return node.copyWith(isExpanded: !node.isExpanded);
      } else if (node.children.isNotEmpty) {
        return node.copyWith(
          children: _toggleNodeExpansionRecursive(node.children, nodeId),
        );
      }
      return node;
    }).toList();
  }

  /// 递归展开到指定节点的路径
  List<TreeNode> _expandPathToNodeRecursive(List<TreeNode> nodes, String targetNodeId) {
    return nodes.map((node) {
      if (node.id == targetNodeId) {
        return node;
      } else if (node.children.isNotEmpty) {
        final updatedChildren = _expandPathToNodeRecursive(node.children, targetNodeId);
        // 如果子节点中包含目标节点，则展开当前节点
        final containsTarget = _containsNodeRecursive(updatedChildren, targetNodeId);
        return node.copyWith(
          children: updatedChildren,
          isExpanded: containsTarget ? true : node.isExpanded,
        );
      }
      return node;
    }).toList();
  }

  /// 递归检查是否包含指定节点
  bool _containsNodeRecursive(List<TreeNode> nodes, String nodeId) {
    for (final node in nodes) {
      if (node.id == nodeId) return true;
      if (_containsNodeRecursive(node.children, nodeId)) return true;
    }
    return false;
  }

  /// 按状态过滤树
  List<TreeNode> _filterTreeByStatus(List<TreeNode> nodes, List<BomItemStatus> statuses) {
    final statusCodes = statuses.map((s) => s.code).toSet();

    return nodes.map((node) {
      if (node.type == BomTreeNodeType.category) {
        // 分类节点：过滤子节点
        final filteredChildren = _filterTreeByStatus(node.children, statuses);
        return node.copyWith(
          children: filteredChildren,
          materialCount: filteredChildren.length,
        );
      } else if (node.type == BomTreeNodeType.bomItem) {
        // 材料节点：检查状态
        final nodeStatus = node.metadata['status'] as String? ?? '';
        return statusCodes.contains(nodeStatus) ? node : null;
      }
      return node;
    }).where((node) => node != null).cast<TreeNode>().toList();
  }

  /// 按价格范围过滤树
  List<TreeNode> _filterTreeByPriceRange(List<TreeNode> nodes, double minPrice, double maxPrice) {
    return nodes.map((node) {
      if (node.type == BomTreeNodeType.category) {
        // 分类节点：过滤子节点
        final filteredChildren = _filterTreeByPriceRange(node.children, minPrice, maxPrice);
        return node.copyWith(
          children: filteredChildren,
          materialCount: filteredChildren.length,
        );
      } else if (node.type == BomTreeNodeType.bomItem) {
        // 材料节点：检查价格
        final nodePrice = node.metadata['totalPrice'] as double? ?? 0.0;
        return (nodePrice >= minPrice && nodePrice <= maxPrice) ? node : null;
      }
      return node;
    }).where((node) => node != null).cast<TreeNode>().toList();
  }

  /// 递归更新BOM项目
  List<TreeNode> _updateBomItemInTreeRecursive(List<TreeNode> nodes, BomItem updatedItem) {
    return nodes.map((node) {
      if (node.metadata['bomItemId'] == updatedItem.id) {
        // 更新节点数据
        return node.copyWith(
          label: updatedItem.materialName,
          metadata: {
            ...node.metadata ?? {},
            'materialName': updatedItem.materialName,
            'brand': updatedItem.effectiveMaterialBrand,
            'model': updatedItem.effectiveMaterialModel,
            'quantity': updatedItem.quantity,
            'unitPrice': updatedItem.unitPrice,
            'totalPrice': updatedItem.unitPrice * updatedItem.quantity,
            'status': updatedItem.status.code,
            'statusDisplay': updatedItem.status.displayName,
            'notes': updatedItem.notes,
            'updatedAt': updatedItem.updatedAt.toIso8601String(),
          },
        );
      } else if (node.children.isNotEmpty) {
        return node.copyWith(
          children: _updateBomItemInTreeRecursive(node.children, updatedItem),
        );
      }
      return node;
    }).toList();
  }

  /// 递归移除BOM项目
  List<TreeNode> _removeBomItemFromTreeRecursive(List<TreeNode> nodes, String bomItemId) {
    return nodes.map((node) {
      if (node.children.isNotEmpty) {
        final filteredChildren = _removeBomItemFromTreeRecursive(node.children, bomItemId)
            .where((child) => child.metadata['bomItemId'] != bomItemId)
            .toList();
        return node.copyWith(
          children: filteredChildren,
          materialCount: filteredChildren.length,
        );
      }
      return node;
    }).where((node) => node.metadata['bomItemId'] != bomItemId).toList();
  }

  /// 递归添加BOM项目
  List<TreeNode> _addBomItemToTreeRecursive(List<TreeNode> nodes, BomItem newItem) {
    final targetCategory = newItem.effectiveMaterialCategory ?? '未分类';

    return nodes.map((node) {
      if (node.type == BomTreeNodeType.category && node.label == targetCategory) {
        // 找到目标分类，添加新项目
        final newItemNode = TreeNode(
          id: 'bom_item_${newItem.id}',
          label: newItem.materialName,
          type: BomTreeNodeType.bomItem,
          materialCount: 1,
          isExpanded: false,
          depth: node.depth + 1,
          children: [],
          metadata: {
            'bomItemId': newItem.id,
            'materialId': newItem.materialId,
            'materialName': newItem.materialName,
            'brand': newItem.effectiveMaterialBrand,
            'model': newItem.effectiveMaterialModel,
            'quantity': newItem.quantity,
            'unitPrice': newItem.unitPrice,
            'totalPrice': newItem.unitPrice * newItem.quantity,
            'status': newItem.status.code,
            'statusDisplay': newItem.status.displayName,
            'category': targetCategory,
            'notes': newItem.notes,
            'createdAt': newItem.createdAt.toIso8601String(),
            'updatedAt': newItem.updatedAt.toIso8601String(),
          },
        );

        return node.copyWith(
          children: [...node.children, newItemNode],
          materialCount: node.children.length + 1,
        );
      }
      return node;
    }).toList();
  }

  /// 递归验证树结构
  bool _validateTreeStructureRecursive(List<TreeNode> nodes) {
    for (final node in nodes) {
      // 检查节点ID是否唯一
      if (node.id.isEmpty) return false;

      // 检查深度是否正确
      if (node.depth < 0) return false;

      // 递归验证子节点
      if (node.children.isNotEmpty) {
        if (!_validateTreeStructureRecursive(node.children)) return false;

        // 检查子节点深度是否正确
        for (final child in node.children) {
          if (child.depth != node.depth + 1) return false;
        }
      }
    }
    return true;
  }

  /// 递归重建树索引
  List<TreeNode> _rebuildTreeIndexRecursive(List<TreeNode> nodes) {
    return nodes.asMap().entries.map((entry) {
      final index = entry.key;
      final node = entry.value;

      final newId = node.type == BomTreeNodeType.category
          ? 'bom_category_$index'
          : node.id; // 保持BOM项目节点的原始ID

      return node.copyWith(
        id: newId,
        children: node.children.isNotEmpty
            ? _rebuildTreeIndexRecursive(node.children)
            : node.children,
      );
    }).toList();
  }
}
