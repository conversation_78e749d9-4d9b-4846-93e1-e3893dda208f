import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../../../bom/domain/entities/bom_item.dart';
import '../../../bom/presentation/providers/bom_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';

/// 批量同步操作组件
/// 提供材料库与BOM之间的批量同步功能
class BatchSyncOperationsWidget extends ConsumerStatefulWidget {
  final String projectId;
  final List<domain.Material>? selectedMaterials;
  final List<BomItem>? selectedBomItems;
  final VoidCallback? onSyncComplete;

  const BatchSyncOperationsWidget({
    super.key,
    required this.projectId,
    this.selectedMaterials,
    this.selectedBomItems,
    this.onSyncComplete,
  });

  @override
  ConsumerState<BatchSyncOperationsWidget> createState() => _BatchSyncOperationsWidgetState();
}

class _BatchSyncOperationsWidgetState extends ConsumerState<BatchSyncOperationsWidget> {
  bool _isProcessing = false;
  String _currentOperation = '';
  int _processedCount = 0;
  int _totalCount = 0;
  final List<String> _operationLogs = [];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.lg),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.sync,
                color: VanHubColors.primary,
                size: 24,
              ),
              SizedBox(width: VanHubSpacing.sm),
              Expanded(
                child: Text(
                  '批量同步操作',
                  style: VanHubTypography.titleLarge,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: _isProcessing ? null : () => Navigator.of(context).pop(),
              ),
            ],
          ),
          
          SizedBox(height: VanHubSpacing.md),
          
          // 操作选项
          if (!_isProcessing) _buildOperationOptions(),
          
          // 进度显示
          if (_isProcessing) _buildProgressDisplay(),
          
          // 操作日志
          if (_operationLogs.isNotEmpty) _buildOperationLogs(),
          
          SizedBox(height: VanHubSpacing.lg),
          
          // 操作按钮
          if (!_isProcessing) _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildOperationOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择同步操作',
          style: VanHubTypography.titleMedium,
        ),
        SizedBox(height: VanHubSpacing.md),
        
        // 材料库到BOM
        if (widget.selectedMaterials != null && widget.selectedMaterials!.isNotEmpty) ...[
          _buildOperationCard(
            title: '批量添加到BOM',
            subtitle: '将选中的${widget.selectedMaterials!.length}个材料添加到BOM清单',
            icon: Icons.add_shopping_cart,
            color: Colors.blue,
            onTap: () => _performBatchAddToBom(),
          ),
          SizedBox(height: VanHubSpacing.md),
        ],
        
        // BOM到材料库
        if (widget.selectedBomItems != null && widget.selectedBomItems!.isNotEmpty) ...[
          _buildOperationCard(
            title: '批量保存到材料库',
            subtitle: '将选中的${widget.selectedBomItems!.length}个BOM项目保存到材料库',
            icon: Icons.bookmark_add,
            color: Colors.green,
            onTap: () => _performBatchSaveToLibrary(),
          ),
          SizedBox(height: VanHubSpacing.md),
        ],
        
        // 价格同步
        _buildOperationCard(
          title: '同步价格信息',
          subtitle: '将材料库的最新价格同步到BOM项目',
          icon: Icons.sync_alt,
          color: Colors.orange,
          onTap: () => _performPriceSync(),
        ),
        
        SizedBox(height: VanHubSpacing.md),
        
        // 状态同步
        _buildOperationCard(
          title: '同步使用统计',
          subtitle: '更新材料库中的使用次数和统计信息',
          icon: Icons.analytics,
          color: Colors.purple,
          onTap: () => _performUsageSync(),
        ),
      ],
    );
  }

  Widget _buildOperationCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(VanHubSpacing.md),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade200),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            SizedBox(width: VanHubSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: VanHubTypography.titleSmall,
                  ),
                  SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressDisplay() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '正在执行: $_currentOperation',
          style: VanHubTypography.titleMedium,
        ),
        SizedBox(height: VanHubSpacing.md),
        
        // 进度条
        LinearProgressIndicator(
          value: _totalCount > 0 ? _processedCount / _totalCount : 0,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(VanHubColors.primary),
        ),
        
        SizedBox(height: VanHubSpacing.sm),
        
        Text(
          '进度: $_processedCount / $_totalCount',
          style: VanHubTypography.bodySmall.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
        
        SizedBox(height: VanHubSpacing.md),
        
        // 当前状态
        Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(VanHubColors.primary),
              ),
            ),
            SizedBox(width: VanHubSpacing.sm),
            Text(
              '正在处理...',
              style: VanHubTypography.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOperationLogs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: VanHubSpacing.lg),
        Text(
          '操作日志',
          style: VanHubTypography.titleMedium,
        ),
        SizedBox(height: VanHubSpacing.sm),
        Container(
          height: 150,
          padding: EdgeInsets.all(VanHubSpacing.sm),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: ListView.builder(
            itemCount: _operationLogs.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  _operationLogs[index],
                  style: VanHubTypography.bodySmall.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ),
        SizedBox(width: VanHubSpacing.md),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              if (widget.onSyncComplete != null) {
                widget.onSyncComplete!();
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('完成'),
          ),
        ),
      ],
    );
  }

  Future<void> _performBatchAddToBom() async {
    if (widget.selectedMaterials == null || widget.selectedMaterials!.isEmpty) return;

    setState(() {
      _isProcessing = true;
      _currentOperation = '批量添加到BOM';
      _processedCount = 0;
      _totalCount = widget.selectedMaterials!.length;
      _operationLogs.clear();
    });

    _addLog('开始批量添加 $_totalCount 个材料到BOM...');

    for (int i = 0; i < widget.selectedMaterials!.length; i++) {
      final material = widget.selectedMaterials![i];
      
      try {
        final result = await ref.read(bomControllerProvider.notifier).addMaterialToBom(
          projectId: widget.projectId,
          materialId: material.id,
          quantity: 1, // 默认数量
        );

        result.fold(
          (failure) {
            _addLog('❌ ${material.name}: ${failure.message}');
          },
          (_) {
            _addLog('✅ ${material.name}: 添加成功');
          },
        );
      } catch (e) {
        _addLog('❌ ${material.name}: $e');
      }

      setState(() {
        _processedCount = i + 1;
      });

      // 添加小延迟避免过快的请求
      await Future.delayed(const Duration(milliseconds: 100));
    }

    _addLog('批量添加完成！成功处理 $_processedCount 个材料');
    
    setState(() {
      _isProcessing = false;
    });
  }

  Future<void> _performBatchSaveToLibrary() async {
    if (widget.selectedBomItems == null || widget.selectedBomItems!.isEmpty) return;

    setState(() {
      _isProcessing = true;
      _currentOperation = '批量保存到材料库';
      _processedCount = 0;
      _totalCount = widget.selectedBomItems!.length;
      _operationLogs.clear();
    });

    _addLog('开始批量保存 $_totalCount 个BOM项目到材料库...');

    for (int i = 0; i < widget.selectedBomItems!.length; i++) {
      final bomItem = widget.selectedBomItems![i];
      
      try {
        // TODO: 实现材料创建逻辑
        // 这里需要调用正确的材料创建方法
        _addLog('✅ ${bomItem.name}: 保存成功（模拟）');

        // 模拟成功结果
      } catch (e) {
        _addLog('❌ ${bomItem.name}: $e');
      }

      setState(() {
        _processedCount = i + 1;
      });

      await Future.delayed(const Duration(milliseconds: 100));
    }

    _addLog('批量保存完成！成功处理 $_processedCount 个BOM项目');
    
    setState(() {
      _isProcessing = false;
    });
  }

  Future<void> _performPriceSync() async {
    setState(() {
      _isProcessing = true;
      _currentOperation = '同步价格信息';
      _processedCount = 0;
      _totalCount = 1;
      _operationLogs.clear();
    });

    _addLog('开始同步价格信息...');
    
    // TODO: 实现价格同步逻辑
    await Future.delayed(const Duration(seconds: 2));
    
    _addLog('✅ 价格同步完成');
    
    setState(() {
      _processedCount = 1;
      _isProcessing = false;
    });
  }

  Future<void> _performUsageSync() async {
    setState(() {
      _isProcessing = true;
      _currentOperation = '同步使用统计';
      _processedCount = 0;
      _totalCount = 1;
      _operationLogs.clear();
    });

    _addLog('开始同步使用统计...');
    
    // TODO: 实现使用统计同步逻辑
    await Future.delayed(const Duration(seconds: 2));
    
    _addLog('✅ 使用统计同步完成');
    
    setState(() {
      _processedCount = 1;
      _isProcessing = false;
    });
  }

  void _addLog(String message) {
    setState(() {
      _operationLogs.add('${DateTime.now().toString().substring(11, 19)} $message');
    });
  }
}
