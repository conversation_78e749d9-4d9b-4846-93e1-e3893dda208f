import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/entities/log_template.dart';

/// 模板选择器组件
class TemplateSelectorWidget extends ConsumerStatefulWidget {
  final Function(LogTemplate)? onTemplateSelected;
  final Function()? onCreateCustom;
  final LogTemplateCategory? filterCategory;
  final bool showCreateOption;
  final bool showSearchBar;

  const TemplateSelectorWidget({
    super.key,
    this.onTemplateSelected,
    this.onCreateCustom,
    this.filterCategory,
    this.showCreateOption = true,
    this.showSearchBar = true,
  });

  @override
  ConsumerState<TemplateSelectorWidget> createState() => _TemplateSelectorWidgetState();
}

class _TemplateSelectorWidgetState extends ConsumerState<TemplateSelectorWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  LogTemplateCategory? _selectedCategory;
  List<LogTemplate> _templates = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _selectedCategory = widget.filterCategory;
    _loadTemplates();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (widget.showSearchBar) _buildSearchBar(),
          _buildCategoryTabs(),
          Expanded(child: _buildTemplateGrid()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: VanHubBrandColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.description,
              color: VanHubBrandColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: VanHubSpacing.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '选择日志模板',
                  style: VanHubTypography.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '使用模板快速创建结构化的改装日志',
                  style: VanHubTypography.bodyMedium.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      child: TextField(
        decoration: InputDecoration(
          hintText: '搜索模板...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: VanHubBrandColors.primary),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          _filterTemplates();
        },
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: VanHubBrandColors.primary,
        unselectedLabelColor: Colors.grey.shade600,
        indicatorColor: VanHubBrandColors.primary,
        tabs: const [
          Tab(text: '全部'),
          Tab(text: '系统模板'),
          Tab(text: '我的模板'),
          Tab(text: '热门模板'),
        ],
        onTap: (index) {
          _loadTemplatesByTab(index);
        },
      ),
    );
  }

  Widget _buildTemplateGrid() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_templates.isEmpty) {
      return _buildEmptyState();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildTemplateList(_templates),
        _buildTemplateList(_templates.where((t) => t.type == LogTemplateType.system).toList()),
        _buildTemplateList(_templates.where((t) => t.type == LogTemplateType.custom).toList()),
        _buildTemplateList(_templates.where((t) => t.isPopular).toList()),
      ],
    );
  }

  Widget _buildTemplateList(List<LogTemplate> templates) {
    return ListView(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      children: [
        // 创建自定义模板选项
        if (widget.showCreateOption && _tabController.index != 1) ...[
          _buildCreateCustomCard(),
          const SizedBox(height: VanHubSpacing.md),
        ],
        
        // 模板网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: VanHubSpacing.sm,
            mainAxisSpacing: VanHubSpacing.sm,
            childAspectRatio: 0.8,
          ),
          itemCount: templates.length,
          itemBuilder: (context, index) {
            return _buildTemplateCard(templates[index], index);
          },
        ),
      ],
    );
  }

  Widget _buildCreateCustomCard() {
    return InkWell(
      onTap: widget.onCreateCustom,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(VanHubSpacing.md),
        decoration: BoxDecoration(
          border: Border.all(
            color: VanHubBrandColors.primary,
            width: 2,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(12),
          color: VanHubBrandColors.primary.withValues(alpha: 0.05),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: VanHubBrandColors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: VanHubSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '创建自定义模板',
                    style: VanHubTypography.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: VanHubBrandColors.primary,
                    ),
                  ),
                  Text(
                    '根据你的需求创建专属模板',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: VanHubBrandColors.primary,
              size: 16,
            ),
          ],
        ),
      ),
    ).animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0);
  }

  Widget _buildTemplateCard(LogTemplate template, int index) {
    return InkWell(
      onTap: () => widget.onTemplateSelected?.call(template),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 模板头部
            Container(
              height: 80,
              decoration: BoxDecoration(
                color: Color(template.categoryColor).withValues(alpha: 0.1),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      _getIconData(template.categoryIcon),
                      color: Color(template.categoryColor),
                      size: 32,
                    ),
                  ),
                  
                  // 标签
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (template.isFeatured)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '推荐',
                              style: VanHubTypography.bodySmall.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        if (template.isNew) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '新',
                              style: VanHubTypography.bodySmall.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // 模板信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(VanHubSpacing.sm),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      template.name,
                      style: VanHubTypography.titleSmall.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: VanHubSpacing.xs),
                    Text(
                      template.description,
                      style: VanHubTypography.bodySmall.copyWith(
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    
                    // 统计信息
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 14,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          template.rating.toStringAsFixed(1),
                          style: VanHubTypography.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          template.usageText,
                          style: VanHubTypography.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ).animate(delay: (index * 100).ms)
        .fadeIn(duration: 300.ms)
        .scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: VanHubSpacing.md),
          Text(
            '暂无模板',
            style: VanHubTypography.titleMedium.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Text(
            '试试搜索其他关键词或创建自定义模板',
            style: VanHubTypography.bodyMedium.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.showCreateOption) ...[
            const SizedBox(height: VanHubSpacing.lg),
            ElevatedButton.icon(
              onPressed: widget.onCreateCustom,
              icon: const Icon(Icons.add),
              label: const Text('创建自定义模板'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubBrandColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'electrical_services':
        return Icons.electrical_services;
      case 'chair':
        return Icons.chair;
      case 'directions_car':
        return Icons.directions_car;
      case 'build':
        return Icons.build;
      case 'plumbing':
        return Icons.plumbing;
      case 'inventory_2':
        return Icons.inventory_2;
      case 'airline_seat_recline_extra':
        return Icons.airline_seat_recline_extra;
      case 'security':
        return Icons.security;
      case 'description':
        return Icons.description;
      default:
        return Icons.description;
    }
  }

  void _loadTemplates() async {
    setState(() {
      _isLoading = true;
    });

    // TODO: 从实际的Provider加载数据
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _templates = LogTemplate.getSystemTemplates();
      _isLoading = false;
    });
  }

  void _loadTemplatesByTab(int tabIndex) {
    // TODO: 根据标签页加载不同的模板
    _filterTemplates();
  }

  void _filterTemplates() {
    // TODO: 实现模板筛选逻辑
  }
}
