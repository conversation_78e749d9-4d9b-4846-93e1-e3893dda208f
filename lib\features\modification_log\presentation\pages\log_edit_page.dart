import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:uuid/uuid.dart';
import 'dart:io';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/entities/enums.dart';
import '../../di/providers.dart';
import '../widgets/rich_text_editor_widget.dart';
import '../widgets/bom_item_selector_dialog.dart';
import '../widgets/milestone_selector_dialog.dart';

/// 日志编辑页面
class LogEditPage extends ConsumerStatefulWidget {
  final LogEntry? logEntry; // null表示创建新日志
  final String projectId;

  const LogEditPage({
    super.key,
    this.logEntry,
    required this.projectId,
  });

  @override
  ConsumerState<LogEditPage> createState() => _LogEditPageState();
}

class _LogEditPageState extends ConsumerState<LogEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _summaryController = TextEditingController();
  
  String _content = '';
  String _title = '';
  final List<File> _selectedFiles = [];
  List<String> _selectedBomItems = [];
  List<String> _selectedMilestones = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();

    // 监听表单字段变化
    _titleController.addListener(() {
      _title = _titleController.text;
    });
  }

  void _initializeForm() {
    if (widget.logEntry != null) {
      _titleController.text = widget.logEntry!.title;
      _summaryController.text = widget.logEntry!.summary ?? '';
      _content = widget.logEntry!.content;
      // TODO: 加载已关联的BOM项目和里程碑
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _summaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.logEntry == null ? '创建日志' : '编辑日志'),
        backgroundColor: Colors.deepOrange,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveLog,
            child: const Text(
              '保存',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // 标题输入
                    _buildTitleField(),
                    const SizedBox(height: 16),
                    
                    // 摘要输入
                    _buildSummaryField(),
                    const SizedBox(height: 16),
                    
                    // 内容编辑器
                    _buildContentEditor(),
                    const SizedBox(height: 16),
                    
                    // 媒体文件
                    _buildMediaSection(),
                    const SizedBox(height: 16),
                    
                    // BOM关联
                    _buildBomSection(),
                    const SizedBox(height: 16),
                    
                    // 里程碑关联
                    _buildMilestoneSection(),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: '日志标题 *',
        hintText: '为这次改装记录起个标题',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.title),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '请输入日志标题';
        }
        return null;
      },
      maxLength: 100,
    );
  }

  Widget _buildSummaryField() {
    return TextFormField(
      controller: _summaryController,
      decoration: const InputDecoration(
        labelText: '简要描述',
        hintText: '简单描述这次改装的主要内容',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.description),
      ),
      maxLines: 2,
      maxLength: 200,
    );
  }

  Widget _buildContentEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '详细内容 *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        RichTextEditorWidget(
          initialText: _content,
          onChanged: (text) => _content = text,
          hintText: '详细记录这次改装的过程、遇到的问题、解决方案等...',
          maxLines: 20,
        ),
      ],
    );
  }

  Widget _buildMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '媒体文件',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextButton.icon(
              onPressed: _pickFiles,
              icon: const Icon(Icons.add_photo_alternate),
              label: const Text('添加文件'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_selectedFiles.isNotEmpty) ...[
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedFiles.length,
              itemBuilder: (context, index) {
                final file = _selectedFiles[index];
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _isImageFile(file.path)
                            ? Image.file(
                                file,
                                width: 120,
                                height: 120,
                                fit: BoxFit.cover,
                              )
                            : SizedBox(
                                width: 120,
                                height: 120,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      _getFileIcon(file.path),
                                      size: 32,
                                      color: Colors.grey.shade600,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _getFileName(file.path),
                                      style: const TextStyle(fontSize: 10),
                                      textAlign: TextAlign.center,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => _removeFile(index),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ] else ...[
          Container(
            height: 80,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, style: BorderStyle.solid),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.photo_library, color: Colors.grey.shade400),
                  const SizedBox(height: 4),
                  Text(
                    '点击上方按钮添加图片或文件',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildBomSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '关联BOM项目',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextButton.icon(
              onPressed: _selectBomItems,
              icon: const Icon(Icons.add_shopping_cart),
              label: const Text('选择物料'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_selectedBomItems.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _selectedBomItems.map((item) {
              return Chip(
                label: Text(item),
                onDeleted: () {
                  setState(() {
                    _selectedBomItems.remove(item);
                  });
                },
              );
            }).toList(),
          ),
        ] else ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                '暂未关联任何BOM项目',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMilestoneSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '关联里程碑',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            TextButton.icon(
              onPressed: _selectMilestones,
              icon: const Icon(Icons.flag),
              label: const Text('选择里程碑'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_selectedMilestones.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _selectedMilestones.map((milestone) {
              return Chip(
                label: Text(milestone),
                onDeleted: () {
                  setState(() {
                    _selectedMilestones.remove(milestone);
                  });
                },
              );
            }).toList(),
          ),
        ] else ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                '暂未关联任何里程碑',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _pickFiles() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any,
      );

      if (result != null) {
        setState(() {
          _selectedFiles.addAll(
            result.files.map((file) => File(file.path!)).toList(),
          );
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择文件失败: $e')),
      );
    }
  }

  void _removeFile(int index) {
    setState(() {
      _selectedFiles.removeAt(index);
    });
  }

  void _selectBomItems() async {
    final selectedItems = await showBomItemSelectorDialog(
      context: context,
      projectId: widget.projectId,
      selectedItemIds: _selectedBomItems,
    );

    if (selectedItems != null) {
      setState(() {
        _selectedBomItems = selectedItems;
      });
    }
  }

  void _selectMilestones() async {
    final selectedMilestones = await showMilestoneSelectorDialog(
      context: context,
      projectId: widget.projectId,
      selectedMilestoneIds: _selectedMilestones,
    );

    if (selectedMilestones != null) {
      setState(() {
        _selectedMilestones = selectedMilestones;
      });
    }
  }

  Future<void> _saveLog() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_content.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入详细内容')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 获取当前用户ID
      final currentUserId = ref.read(currentUserIdProvider);
      if (currentUserId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('保存失败: 用户未登录，请先登录')),
        );
        return;
      }

      // 1. 创建日志实体
      LogEntry logEntry;
      if (widget.logEntry == null) {
        // 创建新日志
        logEntry = LogEntry(
          id: const Uuid().v4(),
          projectId: widget.projectId,
          systemId: 'default_system', // 可以后续改为用户选择
          title: _title,
          content: _content,
          logDate: DateTime.now(),
          authorId: currentUserId,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          status: LogStatus.draft,
          difficulty: DifficultyLevel.medium,
          timeSpentMinutes: 0,
          mediaIds: [], // 媒体文件ID列表
          relatedBomItemIds: _selectedBomItems,
          totalCost: 0.0,
          metadata: {},
        );
      } else {
        // 更新现有日志
        logEntry = widget.logEntry!.copyWith(
          title: _title,
          content: _content,
          updatedAt: DateTime.now(),
          relatedBomItemIds: _selectedBomItems,
        );
      }

      // 2. 使用智能联动服务保存日志
      final smartLinkageService = ref.read(smartLinkageServiceProvider);
      final result = await smartLinkageService.saveLogWithSmartLinkage(
        logEntry: logEntry,
        relatedBomItemIds: _selectedBomItems.isNotEmpty ? _selectedBomItems : null,
        relatedMilestoneIds: _selectedMilestones.isNotEmpty ? _selectedMilestones : null,
        autoUpdateTaskStatus: true,
        autoUpdateBomStatus: true,
      );

      result.fold(
        (failure) {
          throw Exception(failure.message);
        },
        (savedLog) {
          logEntry = savedLog;
        },
      );

      // 3. 上传媒体文件 (如果有)
      // TODO: 实现媒体文件上传逻辑

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('保存成功')),
      );

      Navigator.of(context).pop(true); // 返回true表示保存成功
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  IconData _getFileIcon(String path) {
    final extension = path.toLowerCase().split('.').last;
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _getFileName(String path) {
    return path.split('/').last;
  }
}
