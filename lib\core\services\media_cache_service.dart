import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../errors/failures.dart';

/// 缓存配置
class MediaCacheConfig {
  final int maxCacheSizeBytes;
  final int maxCacheAgeHours;
  final int maxThumbnailSizeBytes;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final bool enableThumbnailCache;
  final bool enablePreloading;

  const MediaCacheConfig({
    this.maxCacheSizeBytes = 500 * 1024 * 1024, // 500MB
    this.maxCacheAgeHours = 24 * 7, // 7天
    this.maxThumbnailSizeBytes = 50 * 1024 * 1024, // 50MB
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.enableThumbnailCache = true,
    this.enablePreloading = true,
  });
}

/// 缓存项信息
class CacheItem {
  final String key;
  final String filePath;
  final int fileSize;
  final DateTime createdAt;
  final DateTime lastAccessedAt;
  final String mimeType;
  final Map<String, dynamic> metadata;

  const CacheItem({
    required this.key,
    required this.filePath,
    required this.fileSize,
    required this.createdAt,
    required this.lastAccessedAt,
    required this.mimeType,
    required this.metadata,
  });

  CacheItem copyWith({
    DateTime? lastAccessedAt,
    Map<String, dynamic>? metadata,
  }) {
    return CacheItem(
      key: key,
      filePath: filePath,
      fileSize: fileSize,
      createdAt: createdAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      mimeType: mimeType,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'filePath': filePath,
      'fileSize': fileSize,
      'createdAt': createdAt.toIso8601String(),
      'lastAccessedAt': lastAccessedAt.toIso8601String(),
      'mimeType': mimeType,
      'metadata': metadata,
    };
  }

  factory CacheItem.fromJson(Map<String, dynamic> json) {
    return CacheItem(
      key: json['key'],
      filePath: json['filePath'],
      fileSize: json['fileSize'],
      createdAt: DateTime.parse(json['createdAt']),
      lastAccessedAt: DateTime.parse(json['lastAccessedAt']),
      mimeType: json['mimeType'],
      metadata: json['metadata'] ?? {},
    );
  }
}

/// 缓存统计信息
class CacheStats {
  final int totalItems;
  final int totalSizeBytes;
  final int thumbnailItems;
  final int thumbnailSizeBytes;
  final double hitRate;
  final DateTime lastCleanup;

  const CacheStats({
    required this.totalItems,
    required this.totalSizeBytes,
    required this.thumbnailItems,
    required this.thumbnailSizeBytes,
    required this.hitRate,
    required this.lastCleanup,
  });

  String get totalSizeMB => (totalSizeBytes / (1024 * 1024)).toStringAsFixed(1);
  String get thumbnailSizeMB => (thumbnailSizeBytes / (1024 * 1024)).toStringAsFixed(1);

  Map<String, dynamic> toJson() {
    return {
      'totalItems': totalItems,
      'totalSizeBytes': totalSizeBytes,
      'totalSizeMB': totalSizeMB,
      'thumbnailItems': thumbnailItems,
      'thumbnailSizeBytes': thumbnailSizeBytes,
      'thumbnailSizeMB': thumbnailSizeMB,
      'hitRate': hitRate,
      'lastCleanup': lastCleanup.toIso8601String(),
    };
  }
}

/// 媒体缓存服务
class MediaCacheService {
  final MediaCacheConfig _config;
  final Map<String, Uint8List> _memoryCache = {};
  final Map<String, CacheItem> _cacheIndex = {};
  
  late Directory _cacheDirectory;
  late Directory _thumbnailDirectory;
  late File _indexFile;
  
  int _cacheHits = 0;
  int _cacheMisses = 0;
  DateTime _lastCleanup = DateTime.now();

  MediaCacheService({MediaCacheConfig? config})
      : _config = config ?? const MediaCacheConfig();

  /// 初始化缓存服务
  Future<Either<Failure, void>> initialize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/media_cache');
      _thumbnailDirectory = Directory('${appDir.path}/media_cache/thumbnails');
      _indexFile = File('${appDir.path}/media_cache/index.json');

      // 创建缓存目录
      await _cacheDirectory.create(recursive: true);
      await _thumbnailDirectory.create(recursive: true);

      // 加载缓存索引
      await _loadCacheIndex();

      // 执行清理
      await _cleanupExpiredItems();

      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '初始化媒体缓存失败: $e'));
    }
  }

  /// 缓存文件
  Future<Either<Failure, String>> cacheFile({
    required String url,
    required Uint8List data,
    required String mimeType,
    Map<String, dynamic>? metadata,
    bool isThumbnail = false,
  }) async {
    try {
      final key = _generateCacheKey(url);
      final fileName = '$key.${_getFileExtension(mimeType)}';
      final targetDir = isThumbnail ? _thumbnailDirectory : _cacheDirectory;
      final filePath = '${targetDir.path}/$fileName';
      
      // 写入文件
      final file = File(filePath);
      await file.writeAsBytes(data);

      // 更新内存缓存
      if (_config.enableMemoryCache && data.length < 1024 * 1024) { // 1MB以下
        _memoryCache[key] = data;
      }

      // 更新缓存索引
      final cacheItem = CacheItem(
        key: key,
        filePath: filePath,
        fileSize: data.length,
        createdAt: DateTime.now(),
        lastAccessedAt: DateTime.now(),
        mimeType: mimeType,
        metadata: metadata ?? {},
      );

      _cacheIndex[key] = cacheItem;
      await _saveCacheIndex();

      return Right(filePath);
    } catch (e) {
      return Left(UnknownFailure(message: '缓存文件失败: $e'));
    }
  }

  /// 获取缓存文件
  Future<Either<Failure, Uint8List>> getCachedFile(String url) async {
    try {
      final key = _generateCacheKey(url);

      // 检查内存缓存
      if (_config.enableMemoryCache && _memoryCache.containsKey(key)) {
        _cacheHits++;
        return Right(_memoryCache[key]!);
      }

      // 检查磁盘缓存
      if (_config.enableDiskCache && _cacheIndex.containsKey(key)) {
        final cacheItem = _cacheIndex[key]!;
        final file = File(cacheItem.filePath);

        if (await file.exists()) {
          final data = await file.readAsBytes();
          
          // 更新访问时间
          _cacheIndex[key] = cacheItem.copyWith(lastAccessedAt: DateTime.now());
          await _saveCacheIndex();

          // 更新内存缓存
          if (_config.enableMemoryCache && data.length < 1024 * 1024) {
            _memoryCache[key] = data;
          }

          _cacheHits++;
          return Right(data);
        } else {
          // 文件不存在，从索引中移除
          _cacheIndex.remove(key);
          await _saveCacheIndex();
        }
      }

      _cacheMisses++;
      return Left(NotFoundFailure(message: '缓存文件不存在'));
    } catch (e) {
      return Left(UnknownFailure(message: '获取缓存文件失败: $e'));
    }
  }

  /// 检查文件是否已缓存
  bool isCached(String url) {
    final key = _generateCacheKey(url);
    
    if (_memoryCache.containsKey(key)) {
      return true;
    }
    
    if (_cacheIndex.containsKey(key)) {
      final cacheItem = _cacheIndex[key]!;
      return File(cacheItem.filePath).existsSync();
    }
    
    return false;
  }

  /// 预加载文件
  Future<Either<Failure, void>> preloadFiles(List<String> urls) async {
    if (!_config.enablePreloading) {
      return const Right(null);
    }

    try {
      final futures = <Future>[];

      for (final url in urls) {
        if (!isCached(url)) {
          // 异步预加载，不阻塞主流程
          futures.add(_preloadSingleFile(url));
        }
      }

      // 等待所有预加载完成，但不抛出异常
      await Future.wait(futures, eagerError: false);

      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '预加载文件失败: $e'));
    }
  }

  /// 预加载单个文件
  Future<void> _preloadSingleFile(String url) async {
    try {
      // TODO: 实现实际的文件下载
      // 这里应该从网络下载文件并缓存
      debugPrint('预加载文件: $url');

      // 模拟网络下载延迟
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      debugPrint('预加载文件失败: $url, 错误: $e');
    }
  }

  /// 清理过期项目
  Future<Either<Failure, int>> cleanupExpiredItems() async {
    try {
      final now = DateTime.now();
      final expiredKeys = <String>[];
      
      for (final entry in _cacheIndex.entries) {
        final item = entry.value;
        final age = now.difference(item.lastAccessedAt);
        
        if (age.inHours > _config.maxCacheAgeHours) {
          expiredKeys.add(entry.key);
        }
      }

      // 删除过期文件
      for (final key in expiredKeys) {
        await _removeCacheItem(key);
      }

      _lastCleanup = now;
      await _saveCacheIndex();

      return Right(expiredKeys.length);
    } catch (e) {
      return Left(UnknownFailure(message: '清理过期项目失败: $e'));
    }
  }

  /// 清理缓存以释放空间
  Future<Either<Failure, int>> cleanupToFreeSpace(int targetSizeBytes) async {
    try {
      final currentSize = _getTotalCacheSize();
      if (currentSize <= targetSizeBytes) {
        return const Right(0);
      }

      // 按最后访问时间排序，删除最旧的项目
      final sortedItems = _cacheIndex.entries.toList()
        ..sort((a, b) => a.value.lastAccessedAt.compareTo(b.value.lastAccessedAt));

      int removedCount = 0;
      int freedSize = 0;

      for (final entry in sortedItems) {
        if (currentSize - freedSize <= targetSizeBytes) {
          break;
        }

        await _removeCacheItem(entry.key);
        freedSize += entry.value.fileSize;
        removedCount++;
      }

      await _saveCacheIndex();
      return Right(removedCount);
    } catch (e) {
      return Left(UnknownFailure(message: '清理缓存空间失败: $e'));
    }
  }

  /// 清空所有缓存
  Future<Either<Failure, void>> clearAllCache() async {
    try {
      // 清空内存缓存
      _memoryCache.clear();

      // 删除所有缓存文件
      if (await _cacheDirectory.exists()) {
        await _cacheDirectory.delete(recursive: true);
        await _cacheDirectory.create(recursive: true);
      }

      if (await _thumbnailDirectory.exists()) {
        await _thumbnailDirectory.delete(recursive: true);
        await _thumbnailDirectory.create(recursive: true);
      }

      // 清空索引
      _cacheIndex.clear();
      await _saveCacheIndex();

      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '清空缓存失败: $e'));
    }
  }

  /// 获取缓存统计信息
  CacheStats getCacheStats() {
    int totalSize = 0;
    int thumbnailSize = 0;
    int thumbnailCount = 0;

    for (final item in _cacheIndex.values) {
      totalSize += item.fileSize;
      if (item.filePath.contains('thumbnails')) {
        thumbnailSize += item.fileSize;
        thumbnailCount++;
      }
    }

    final totalRequests = _cacheHits + _cacheMisses;
    final hitRate = totalRequests > 0 ? _cacheHits / totalRequests : 0.0;

    return CacheStats(
      totalItems: _cacheIndex.length,
      totalSizeBytes: totalSize,
      thumbnailItems: thumbnailCount,
      thumbnailSizeBytes: thumbnailSize,
      hitRate: hitRate,
      lastCleanup: _lastCleanup,
    );
  }

  /// 生成缓存键
  String _generateCacheKey(String url) {
    final bytes = utf8.encode(url);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 获取文件扩展名
  String _getFileExtension(String mimeType) {
    switch (mimeType) {
      case 'image/jpeg':
        return 'jpg';
      case 'image/png':
        return 'png';
      case 'image/gif':
        return 'gif';
      case 'image/webp':
        return 'webp';
      case 'video/mp4':
        return 'mp4';
      case 'video/mov':
        return 'mov';
      case 'video/avi':
        return 'avi';
      default:
        return 'bin';
    }
  }

  /// 获取总缓存大小
  int _getTotalCacheSize() {
    return _cacheIndex.values.fold(0, (sum, item) => sum + item.fileSize);
  }

  /// 移除缓存项
  Future<void> _removeCacheItem(String key) async {
    final item = _cacheIndex[key];
    if (item != null) {
      // 从内存缓存中移除
      _memoryCache.remove(key);
      
      // 删除文件
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }
      
      // 从索引中移除
      _cacheIndex.remove(key);
    }
  }

  /// 加载缓存索引
  Future<void> _loadCacheIndex() async {
    try {
      if (await _indexFile.exists()) {
        final content = await _indexFile.readAsString();
        final data = jsonDecode(content) as Map<String, dynamic>;
        
        for (final entry in data.entries) {
          _cacheIndex[entry.key] = CacheItem.fromJson(entry.value);
        }
      }
    } catch (e) {
      debugPrint('加载缓存索引失败: $e');
    }
  }

  /// 保存缓存索引
  Future<void> _saveCacheIndex() async {
    try {
      final data = <String, dynamic>{};
      for (final entry in _cacheIndex.entries) {
        data[entry.key] = entry.value.toJson();
      }
      
      final content = jsonEncode(data);
      await _indexFile.writeAsString(content);
    } catch (e) {
      debugPrint('保存缓存索引失败: $e');
    }
  }

  /// 自动清理
  Future<void> _cleanupExpiredItems() async {
    final now = DateTime.now();
    
    // 如果距离上次清理超过1小时，执行清理
    if (now.difference(_lastCleanup).inHours >= 1) {
      await cleanupExpiredItems();
    }

    // 如果缓存大小超过限制，清理空间
    final currentSize = _getTotalCacheSize();
    if (currentSize > _config.maxCacheSizeBytes) {
      await cleanupToFreeSpace(_config.maxCacheSizeBytes);
    }
  }
}
