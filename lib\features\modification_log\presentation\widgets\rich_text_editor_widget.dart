import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 富文本编辑器组件
/// 支持Markdown语法和基本的文本格式化
class RichTextEditorWidget extends ConsumerStatefulWidget {
  final String? initialText;
  final Function(String)? onChanged;
  final String? hintText;
  final int? maxLines;
  final bool enabled;

  const RichTextEditorWidget({
    super.key,
    this.initialText,
    this.onChanged,
    this.hintText,
    this.maxLines,
    this.enabled = true,
  });

  @override
  ConsumerState<RichTextEditorWidget> createState() => _RichTextEditorWidgetState();
}

class _RichTextEditorWidgetState extends ConsumerState<RichTextEditorWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isPreviewMode = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText ?? '');
    _focusNode = FocusNode();
    
    _controller.addListener(() {
      widget.onChanged?.call(_controller.text);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 工具栏
        _buildToolbar(),
        const SizedBox(height: 8),
        
        // 编辑器/预览区域
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // 模式切换标签
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    _buildModeTab('编辑', !_isPreviewMode),
                    _buildModeTab('预览', _isPreviewMode),
                  ],
                ),
              ),
              
              // 内容区域
              Container(
                constraints: BoxConstraints(
                  minHeight: 200,
                  maxHeight: widget.maxLines != null ? widget.maxLines! * 24.0 : 400,
                ),
                child: _isPreviewMode ? _buildPreview() : _buildEditor(),
              ),
            ],
          ),
        ),
        
        // 帮助提示
        const SizedBox(height: 8),
        _buildHelpText(),
      ],
    );
  }

  Widget _buildToolbar() {
    if (!widget.enabled) return const SizedBox.shrink();
    
    return Wrap(
      spacing: 8,
      children: [
        _buildToolbarButton(
          icon: Icons.format_bold,
          tooltip: '粗体 (Ctrl+B)',
          onPressed: () => _insertMarkdown('**', '**'),
        ),
        _buildToolbarButton(
          icon: Icons.format_italic,
          tooltip: '斜体 (Ctrl+I)',
          onPressed: () => _insertMarkdown('*', '*'),
        ),
        _buildToolbarButton(
          icon: Icons.format_strikethrough,
          tooltip: '删除线',
          onPressed: () => _insertMarkdown('~~', '~~'),
        ),
        _buildToolbarButton(
          icon: Icons.code,
          tooltip: '代码',
          onPressed: () => _insertMarkdown('`', '`'),
        ),
        _buildToolbarButton(
          icon: Icons.format_list_bulleted,
          tooltip: '无序列表',
          onPressed: () => _insertMarkdown('- ', ''),
        ),
        _buildToolbarButton(
          icon: Icons.format_list_numbered,
          tooltip: '有序列表',
          onPressed: () => _insertMarkdown('1. ', ''),
        ),
        _buildToolbarButton(
          icon: Icons.link,
          tooltip: '链接',
          onPressed: () => _insertMarkdown('[链接文本](', ')'),
        ),
        _buildToolbarButton(
          icon: Icons.image,
          tooltip: '图片',
          onPressed: () => _insertMarkdown('![图片描述](', ')'),
        ),
      ],
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        padding: const EdgeInsets.all(4),
      ),
    );
  }

  Widget _buildModeTab(String title, bool isActive) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isPreviewMode = title == '预览';
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? Colors.white : Colors.transparent,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(8),
            topRight: Radius.circular(8),
          ),
          border: isActive ? Border.all(color: Colors.grey.shade300) : null,
        ),
        child: Text(
          title,
          style: TextStyle(
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            color: isActive ? Colors.black : Colors.grey.shade600,
          ),
        ),
      ),
    );
  }

  Widget _buildEditor() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top,
        decoration: InputDecoration(
          hintText: widget.hintText ?? '开始编写你的改装日志...\n\n支持Markdown语法：\n**粗体** *斜体* `代码`\n- 列表项\n[链接](url)',
          border: InputBorder.none,
          hintStyle: TextStyle(color: Colors.grey.shade500),
        ),
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 14,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildPreview() {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: SingleChildScrollView(
        child: _buildMarkdownPreview(_controller.text),
      ),
    );
  }

  Widget _buildMarkdownPreview(String text) {
    // 简单的Markdown渲染
    final lines = text.split('\n');
    final widgets = <Widget>[];
    
    for (final line in lines) {
      widgets.add(_renderMarkdownLine(line));
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _renderMarkdownLine(String line) {
    if (line.isEmpty) {
      return const SizedBox(height: 16);
    }
    
    // 标题
    if (line.startsWith('# ')) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Text(
          line.substring(2),
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
      );
    }
    
    if (line.startsWith('## ')) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: Text(
          line.substring(3),
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
      );
    }
    
    // 列表
    if (line.startsWith('- ') || line.startsWith('* ')) {
      return Padding(
        padding: const EdgeInsets.only(left: 16, bottom: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('• '),
            Expanded(child: Text(line.substring(2))),
          ],
        ),
      );
    }
    
    // 普通文本（支持简单的内联格式）
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: _renderInlineMarkdown(line),
    );
  }

  Widget _renderInlineMarkdown(String text) {
    // 简单的内联Markdown渲染
    final spans = <TextSpan>[];
    final regex = RegExp(r'\*\*(.*?)\*\*|\*(.*?)\*|`(.*?)`');
    int lastEnd = 0;
    
    for (final match in regex.allMatches(text)) {
      // 添加匹配前的普通文本
      if (match.start > lastEnd) {
        spans.add(TextSpan(text: text.substring(lastEnd, match.start)));
      }
      
      // 添加格式化文本
      if (match.group(1) != null) {
        // 粗体
        spans.add(TextSpan(
          text: match.group(1),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ));
      } else if (match.group(2) != null) {
        // 斜体
        spans.add(TextSpan(
          text: match.group(2),
          style: const TextStyle(fontStyle: FontStyle.italic),
        ));
      } else if (match.group(3) != null) {
        // 代码
        spans.add(TextSpan(
          text: match.group(3),
          style: TextStyle(
            fontFamily: 'monospace',
            backgroundColor: Colors.grey.shade200,
          ),
        ));
      }
      
      lastEnd = match.end;
    }
    
    // 添加剩余的普通文本
    if (lastEnd < text.length) {
      spans.add(TextSpan(text: text.substring(lastEnd)));
    }
    
    return RichText(
      text: TextSpan(
        children: spans,
        style: const TextStyle(color: Colors.black, fontSize: 14),
      ),
    );
  }

  Widget _buildHelpText() {
    return Text(
      '支持Markdown语法：**粗体** *斜体* `代码` [链接](url) ![图片](url)',
      style: TextStyle(
        fontSize: 12,
        color: Colors.grey.shade600,
      ),
    );
  }

  void _insertMarkdown(String before, String after) {
    final selection = _controller.selection;
    final text = _controller.text;
    
    if (selection.isValid) {
      final selectedText = selection.textInside(text);
      final newText = before + selectedText + after;
      
      _controller.text = text.replaceRange(selection.start, selection.end, newText);
      
      // 设置新的光标位置
      final newCursorPos = selection.start + before.length + selectedText.length;
      _controller.selection = TextSelection.collapsed(offset: newCursorPos);
    } else {
      // 如果没有选中文本，在光标位置插入
      final cursorPos = selection.baseOffset;
      final newText = before + after;
      
      _controller.text = text.replaceRange(cursorPos, cursorPos, newText);
      
      // 将光标放在标记之间
      _controller.selection = TextSelection.collapsed(offset: cursorPos + before.length);
    }
    
    _focusNode.requestFocus();
  }
}
