import '../../domain/entities/product_specification.dart';

/// 规格数据初始化器
/// 
/// 提供示例规格数据用于演示和测试
class SpecificationDataInitializer {
  /// 获取所有示例规格数据
  static List<ProductSpecification> getAllSampleSpecifications() {
    return [
      ...getElectricalSpecifications(),
      ...getPlumbingSpecifications(),
      ...getStorageSpecifications(),
      ...getBeddingSpecifications(),
      ...getKitchenSpecifications(),
      ...getBathroomSpecifications(),
      ...getExteriorSpecifications(),
      ...getChassisSpecifications(),
    ];
  }

  /// 电气设备规格
  static List<ProductSpecification> getElectricalSpecifications() {
    return [
      // 磷酸铁锂电池
      ProductSpecification(
        id: 'spec_battery_lifepo4_100ah',
        materialId: 'material_battery_001',
        category: 'ELECTRICAL',
        basicSpec: BasicSpecification(
          productName: '磷酸铁锂电池',
          brand: 'CATL',
          model: 'LFP-100Ah',
          manufacturer: '宁德时代',
          countryOfOrigin: '中国',
          warrantyMonths: 60,
          description: '高性能磷酸铁锂电池，适用于房车储能系统，具有长寿命、高安全性特点',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 12.8,
            ratedCurrent: 100.0,
            ratedPower: 1280.0,
            capacity: 100.0,
            capacityUnit: 'Ah',
            efficiency: 95.0,
            protectionRating: 'IP65',
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 330.0,
            width: 173.0,
            height: 220.0,
            unit: 'mm',
          ),
          weight: 13.5,
          color: '黑色',
          material: '磷酸铁锂',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 6000,
          lifespanUnit: '次',
          cycleCount: 6000,
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        certifications: [
          CertificationInfo(
            name: 'CE认证',
            authority: '欧盟',
            certificateNumber: 'CE-2023-001',
            expiryDate: DateTime(2025, 12, 31),
          ),
          CertificationInfo(
            name: 'UL认证',
            authority: '美国UL',
            certificateNumber: 'UL-2023-002',
            expiryDate: DateTime(2025, 12, 31),
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 太阳能板
      ProductSpecification(
        id: 'spec_solar_panel_300w',
        materialId: 'material_solar_001',
        category: 'ELECTRICAL',
        basicSpec: BasicSpecification(
          productName: '单晶硅太阳能板',
          brand: '隆基绿能',
          model: 'LR4-72HPH-300M',
          manufacturer: '隆基绿能科技股份有限公司',
          countryOfOrigin: '中国',
          warrantyMonths: 300,
          description: '高效单晶硅太阳能电池板，转换效率高，适用于房车屋顶安装',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 24.0,
            ratedCurrent: 12.5,
            ratedPower: 300.0,
            efficiency: 20.5,
            protectionRating: 'IP67',
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 1650.0,
            width: 991.0,
            height: 35.0,
            unit: 'mm',
          ),
          weight: 18.5,
          color: '深蓝色',
          material: '单晶硅',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 25,
          lifespanUnit: '年',
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 逆变器
      ProductSpecification(
        id: 'spec_inverter_2000w',
        materialId: 'material_inverter_001',
        category: 'ELECTRICAL',
        basicSpec: BasicSpecification(
          productName: '纯正弦波逆变器',
          brand: 'AIMS Power',
          model: 'PWRI200012120S',
          manufacturer: 'AIMS Power',
          countryOfOrigin: '美国',
          warrantyMonths: 24,
          description: '2000W纯正弦波逆变器，适用于房车电力系统，提供稳定的交流电输出',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 12.0,
            ratedCurrent: 166.7,
            ratedPower: 2000.0,
            efficiency: 90.0,
            protectionRating: 'IP54',
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 350.0,
            width: 200.0,
            height: 100.0,
            unit: 'mm',
          ),
          weight: 4.2,
          color: '银色',
          material: '铝合金',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 50000,
          lifespanUnit: '小时',
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 水路设备规格
  static List<ProductSpecification> getPlumbingSpecifications() {
    return [
      // 隔膜水泵
      ProductSpecification(
        id: 'spec_water_pump_12v',
        materialId: 'material_pump_001',
        category: 'PLUMBING',
        basicSpec: BasicSpecification(
          productName: '隔膜水泵',
          brand: 'Seaflo',
          model: 'SFDP1-012-035-21',
          manufacturer: '厦门海流',
          countryOfOrigin: '中国',
          warrantyMonths: 24,
          description: '12V直流隔膜水泵，适用于房车供水系统，自吸能力强，运行稳定',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 12.0,
            ratedCurrent: 3.5,
            ratedPower: 42.0,
            protectionRating: 'IP65',
          ),
          fluid: FluidSpecs(
            flowRate: 13.0,
            head: 17.0,
            pipeDiameter: 12.0,
            connectionType: '快插接头',
          ),
          mechanical: MechanicalSpecs(
            workingPressure: 0.8,
            maxPressure: 1.0,
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 140.0,
            width: 90.0,
            height: 110.0,
            unit: 'mm',
          ),
          weight: 1.2,
          color: '黑色',
          material: '工程塑料',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 5000,
          lifespanUnit: '小时',
          reliabilityGrade: 'B级',
          performanceGrade: '良好',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 净水器
      ProductSpecification(
        id: 'spec_water_filter_ro',
        materialId: 'material_filter_001',
        category: 'PLUMBING',
        basicSpec: BasicSpecification(
          productName: 'RO反渗透净水器',
          brand: '3M',
          model: 'RO-75G',
          manufacturer: '3M公司',
          countryOfOrigin: '美国',
          warrantyMonths: 36,
          description: '反渗透净水器，有效去除水中杂质、细菌和病毒，提供纯净饮用水',
        ),
        technicalParams: TechnicalParameters(
          fluid: FluidSpecs(
            flowRate: 0.2,
            pipeDiameter: 6.0,
            connectionType: '快插接头',
          ),
          mechanical: MechanicalSpecs(
            workingPressure: 0.3,
            maxPressure: 0.6,
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 380.0,
            width: 180.0,
            height: 420.0,
            unit: 'mm',
          ),
          weight: 8.5,
          color: '白色',
          material: 'ABS塑料',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 24,
          lifespanUnit: '月',
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 储物系统规格
  static List<ProductSpecification> getStorageSpecifications() {
    return [
      // 储物柜
      ProductSpecification(
        id: 'spec_storage_cabinet',
        materialId: 'material_cabinet_001',
        category: 'STORAGE',
        basicSpec: BasicSpecification(
          productName: '多层储物柜',
          brand: 'IKEA',
          model: 'IVAR-80',
          manufacturer: '宜家',
          countryOfOrigin: '瑞典',
          warrantyMonths: 120,
          description: '实木多层储物柜，可调节层板，适用于房车内部储物',
        ),
        technicalParams: TechnicalParameters(
          mechanical: MechanicalSpecs(
            maxLoad: 50.0,
            strengthGrade: 'C24',
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 800.0,
            width: 300.0,
            height: 1800.0,
            unit: 'mm',
          ),
          weight: 25.0,
          color: '原木色',
          material: '松木',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 10,
          lifespanUnit: '年',
          reliabilityGrade: 'B级',
          performanceGrade: '良好',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 床铺系统规格
  static List<ProductSpecification> getBeddingSpecifications() {
    return [
      // 床垫
      ProductSpecification(
        id: 'spec_mattress_memory_foam',
        materialId: 'material_mattress_001',
        category: 'BEDDING',
        basicSpec: BasicSpecification(
          productName: '记忆棉床垫',
          brand: 'Tempur',
          model: 'Original-19',
          manufacturer: 'Tempur-Pedic',
          countryOfOrigin: '丹麦',
          warrantyMonths: 120,
          description: '记忆棉床垫，提供优秀的支撑和舒适性，适用于房车床铺',
        ),
        technicalParams: const TechnicalParameters(),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 1900.0,
            width: 1350.0,
            height: 190.0,
            unit: 'mm',
          ),
          weight: 35.0,
          color: '白色',
          material: '记忆棉',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 10,
          lifespanUnit: '年',
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 厨房系统规格
  static List<ProductSpecification> getKitchenSpecifications() {
    return [
      // 车载冰箱
      ProductSpecification(
        id: 'spec_fridge_compressor',
        materialId: 'material_fridge_001',
        category: 'KITCHEN',
        basicSpec: BasicSpecification(
          productName: '压缩机车载冰箱',
          brand: 'Dometic',
          model: 'CFX-65W',
          manufacturer: 'Dometic Group',
          countryOfOrigin: '德国',
          warrantyMonths: 36,
          description: '65L压缩机车载冰箱，制冷效果好，能耗低，适用于房车厨房',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 12.0,
            ratedCurrent: 5.0,
            ratedPower: 60.0,
            protectionRating: 'IP54',
          ),
          thermal: ThermalSpecs(
            workingTempRange: TemperatureRange(
              min: -22.0,
              max: 10.0,
              unit: '°C',
            ),
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 705.0,
            width: 375.0,
            height: 583.0,
            unit: 'mm',
          ),
          weight: 21.0,
          color: '灰色',
          material: 'ABS塑料',
          volume: 65.0,
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 8,
          lifespanUnit: '年',
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 卫浴系统规格
  static List<ProductSpecification> getBathroomSpecifications() {
    return [
      // 盒式马桶
      ProductSpecification(
        id: 'spec_toilet_cassette',
        materialId: 'material_toilet_001',
        category: 'BATHROOM',
        basicSpec: BasicSpecification(
          productName: '盒式马桶',
          brand: 'Thetford',
          model: 'C-200CW',
          manufacturer: 'Thetford',
          countryOfOrigin: '荷兰',
          warrantyMonths: 24,
          description: '盒式马桶，便于清洁和维护，适用于房车卫生间',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 12.0,
            ratedCurrent: 1.5,
            ratedPower: 18.0,
          ),
          fluid: FluidSpecs(
            flowRate: 1.0,
            connectionType: '标准接头',
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 450.0,
            width: 380.0,
            height: 450.0,
            unit: 'mm',
          ),
          weight: 12.0,
          color: '白色',
          material: 'ABS塑料',
          volume: 21.0,
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 5,
          lifespanUnit: '年',
          reliabilityGrade: 'B级',
          performanceGrade: '良好',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 外观系统规格
  static List<ProductSpecification> getExteriorSpecifications() {
    return [
      // 遮阳篷
      ProductSpecification(
        id: 'spec_awning_retractable',
        materialId: 'material_awning_001',
        category: 'EXTERIOR',
        basicSpec: BasicSpecification(
          productName: '可收缩遮阳篷',
          brand: 'Fiamma',
          model: 'F45S-300',
          manufacturer: 'Fiamma',
          countryOfOrigin: '意大利',
          warrantyMonths: 24,
          description: '可收缩遮阳篷，提供户外遮阳，操作简便',
        ),
        technicalParams: TechnicalParameters(
          mechanical: MechanicalSpecs(
            maxLoad: 30.0,
            workingPressure: 0.5,
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 3000.0,
            width: 2500.0,
            height: 200.0,
            unit: 'mm',
          ),
          weight: 18.0,
          color: '蓝白条纹',
          material: '丙烯酸纤维',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 5,
          lifespanUnit: '年',
          reliabilityGrade: 'B级',
          performanceGrade: '良好',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 底盘系统规格
  static List<ProductSpecification> getChassisSpecifications() {
    return [
      // 全地形轮胎
      ProductSpecification(
        id: 'spec_tire_all_terrain',
        materialId: 'material_tire_001',
        category: 'CHASSIS',
        basicSpec: BasicSpecification(
          productName: '全地形轮胎',
          brand: 'BFGoodrich',
          model: 'All-Terrain T/A KO2',
          manufacturer: 'Michelin',
          countryOfOrigin: '美国',
          warrantyMonths: 60,
          description: '全地形轮胎，适用于各种路况，耐磨性好',
        ),
        technicalParams: TechnicalParameters(
          mechanical: MechanicalSpecs(
            maxLoad: 1250.0,
            workingPressure: 2.4,
            maxPressure: 3.5,
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 265.0,
            width: 265.0,
            height: 70.0,
            unit: 'mm',
          ),
          weight: 25.0,
          color: '黑色',
          material: '橡胶',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 80000,
          lifespanUnit: 'km',
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// 根据分类获取示例规格
  static List<ProductSpecification> getSpecificationsByCategory(String category) {
    switch (category.toUpperCase()) {
      case 'ELECTRICAL':
        return getElectricalSpecifications();
      case 'PLUMBING':
        return getPlumbingSpecifications();
      case 'STORAGE':
        return getStorageSpecifications();
      case 'BEDDING':
        return getBeddingSpecifications();
      case 'KITCHEN':
        return getKitchenSpecifications();
      case 'BATHROOM':
        return getBathroomSpecifications();
      case 'EXTERIOR':
        return getExteriorSpecifications();
      case 'CHASSIS':
        return getChassisSpecifications();
      default:
        return [];
    }
  }

  /// 获取分类统计
  static Map<String, int> getCategoryStats() {
    return {
      'ELECTRICAL': getElectricalSpecifications().length,
      'PLUMBING': getPlumbingSpecifications().length,
      'STORAGE': getStorageSpecifications().length,
      'BEDDING': getBeddingSpecifications().length,
      'KITCHEN': getKitchenSpecifications().length,
      'BATHROOM': getBathroomSpecifications().length,
      'EXTERIOR': getExteriorSpecifications().length,
      'CHASSIS': getChassisSpecifications().length,
    };
  }
}
