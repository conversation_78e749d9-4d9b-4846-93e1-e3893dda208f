import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../widgets/material_card_unified_widget.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';

/// 演示材料页面
/// 
/// 展示带有规格信息的演示材料
class DemoMaterialsPage extends ConsumerWidget {
  const DemoMaterialsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('规格演示材料'),
        backgroundColor: VanHubBrandColors.primary,
        foregroundColor: VanHubBrandColors.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildMaterialsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.science,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '规格系统演示',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '以下材料展示了新的规格系统功能。每个材料卡片都会显示关键的技术参数，点击可查看详细规格信息。',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '这些是演示数据，展示了8大房车改装分类的专业规格参数',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaterialsList(BuildContext context) {
    final demoMaterials = _createDemoMaterials();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '演示材料列表',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...demoMaterials.map((material) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: MaterialCardUnifiedWidget(
            material: material,
            onTap: () => _showMaterialDetail(context, material),
          ),
        )),
      ],
    );
  }

  List<domain.Material> _createDemoMaterials() {
    return [
      // 电气设备
      domain.Material(
        id: 'demo_battery_1',
        userId: 'demo_user',
        name: '磷酸铁锂电池 100Ah',
        description: '高性能磷酸铁锂电池，适用于房车储能系统',
        category: '电气设备',
        price: 2800.0,
        brand: 'CATL',
        model: 'LFP-100Ah',
        specifications: null, // 使用新的规格系统
        specificationId: 'spec_battery_lifepo4_100ah',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      
      domain.Material(
        id: 'demo_solar_1',
        userId: 'demo_user',
        name: '单晶硅太阳能板 300W',
        description: '高效单晶硅太阳能电池板，转换效率高',
        category: '电气设备',
        price: 1200.0,
        brand: '隆基绿能',
        model: 'LR4-72HPH-300M',
        specifications: null,
        specificationId: 'spec_solar_panel_300w',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      domain.Material(
        id: 'demo_inverter_1',
        userId: 'demo_user',
        name: '纯正弦波逆变器 2000W',
        description: '2000W纯正弦波逆变器，提供稳定的交流电输出',
        category: '电气设备',
        price: 800.0,
        brand: 'AIMS Power',
        model: 'PWRI200012120S',
        specifications: null,
        specificationId: 'spec_inverter_2000w',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 水路设备
      domain.Material(
        id: 'demo_pump_1',
        userId: 'demo_user',
        name: '隔膜水泵 12V',
        description: '12V直流隔膜水泵，适用于房车供水系统',
        category: '水路设备',
        price: 350.0,
        brand: 'Seaflo',
        model: 'SFDP1-012-035-21',
        specifications: null,
        specificationId: 'spec_water_pump_12v',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      domain.Material(
        id: 'demo_filter_1',
        userId: 'demo_user',
        name: 'RO反渗透净水器',
        description: '反渗透净水器，有效去除水中杂质、细菌和病毒',
        category: '水路设备',
        price: 1500.0,
        brand: '3M',
        model: 'RO-75G',
        specifications: null,
        specificationId: 'spec_water_filter_ro',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 厨房设备
      domain.Material(
        id: 'demo_fridge_1',
        userId: 'demo_user',
        name: '压缩机车载冰箱 65L',
        description: '65L压缩机车载冰箱，制冷效果好，能耗低',
        category: '厨房设备',
        price: 3200.0,
        brand: 'Dometic',
        model: 'CFX-65W',
        specifications: null,
        specificationId: 'spec_fridge_compressor',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 卫浴设备
      domain.Material(
        id: 'demo_toilet_1',
        userId: 'demo_user',
        name: '盒式马桶',
        description: '盒式马桶，便于清洁和维护，适用于房车卫生间',
        category: '卫浴设备',
        price: 1800.0,
        brand: 'Thetford',
        model: 'C-200CW',
        specifications: null,
        specificationId: 'spec_toilet_cassette',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 外观设备
      domain.Material(
        id: 'demo_awning_1',
        userId: 'demo_user',
        name: '可收缩遮阳篷 3m',
        description: '可收缩遮阳篷，提供户外遮阳，操作简便',
        category: '外观设备',
        price: 2500.0,
        brand: 'Fiamma',
        model: 'F45S-300',
        specifications: null,
        specificationId: 'spec_awning_retractable',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 底盘设备
      domain.Material(
        id: 'demo_tire_1',
        userId: 'demo_user',
        name: '全地形轮胎 265/70R16',
        description: '全地形轮胎，适用于各种路况，耐磨性好',
        category: '底盘设备',
        price: 800.0,
        brand: 'BFGoodrich',
        model: 'All-Terrain T/A KO2',
        specifications: null,
        specificationId: 'spec_tire_all_terrain',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 储物设备
      domain.Material(
        id: 'demo_cabinet_1',
        userId: 'demo_user',
        name: '多层储物柜',
        description: '实木多层储物柜，可调节层板，适用于房车内部储物',
        category: '储物设备',
        price: 600.0,
        brand: 'IKEA',
        model: 'IVAR-80',
        specifications: null,
        specificationId: 'spec_storage_cabinet',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // 床铺设备
      domain.Material(
        id: 'demo_mattress_1',
        userId: 'demo_user',
        name: '记忆棉床垫',
        description: '记忆棉床垫，提供优秀的支撑和舒适性',
        category: '床铺设备',
        price: 2200.0,
        brand: 'Tempur',
        model: 'Original-19',
        specifications: null,
        specificationId: 'spec_mattress_memory_foam',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  void _showMaterialDetail(BuildContext context, domain.Material material) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(material.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('品牌: ${material.brand ?? '未知'}'),
            Text('型号: ${material.model ?? '未知'}'),
            Text('价格: ¥${material.price.toStringAsFixed(2)}'),
            const SizedBox(height: 8),
            Text(material.description),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '此材料已关联详细规格信息',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/test_specification');
            },
            child: const Text('查看规格详情'),
          ),
        ],
      ),
    );
  }
}
