import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

/// Server-related failures
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });

  factory ServerFailure.fromException(Exception exception) {
    return ServerFailure(
      message: exception.toString(),
      code: 500,
    );
  }

  factory ServerFailure.connectionTimeout() {
    return const ServerFailure(
      message: 'Connection timeout',
      code: 408,
    );
  }

  factory ServerFailure.noInternetConnection() {
    return const ServerFailure(
      message: 'No internet connection',
      code: 503,
    );
  }
}

/// Cache-related failures
class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
  });

  factory CacheFailure.fromException(Exception exception) {
    return CacheFailure(
      message: exception.toString(),
      code: 500,
    );
  }

  factory CacheFailure.notFound() {
    return const CacheFailure(
      message: 'Data not found in cache',
      code: 404,
    );
  }

  factory CacheFailure.expired() {
    return const CacheFailure(
      message: 'Cached data has expired',
      code: 410,
    );
  }
}

/// Database-related failures
class DatabaseFailure extends Failure {
  const DatabaseFailure({
    required super.message,
    super.code,
  });

  factory DatabaseFailure.fromException(Exception exception) {
    return DatabaseFailure(
      message: exception.toString(),
      code: 500,
    );
  }

  factory DatabaseFailure.connectionFailed() {
    return const DatabaseFailure(
      message: 'Database connection failed',
      code: 503,
    );
  }

  factory DatabaseFailure.queryFailed() {
    return const DatabaseFailure(
      message: 'Database query failed',
      code: 500,
    );
  }
}

/// Authentication-related failures
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
  });

  factory AuthFailure.invalidCredentials() {
    return const AuthFailure(
      message: 'Invalid credentials',
      code: 401,
    );
  }

  factory AuthFailure.userNotFound() {
    return const AuthFailure(
      message: 'User not found',
      code: 404,
    );
  }

  factory AuthFailure.tokenExpired() {
    return const AuthFailure(
      message: 'Authentication token expired',
      code: 401,
    );
  }

  factory AuthFailure.accessDenied() {
    return const AuthFailure(
      message: 'Access denied',
      code: 403,
    );
  }
}

/// Validation-related failures
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
  });

  factory ValidationFailure.invalidInput(String field) {
    return ValidationFailure(
      message: 'Invalid input for field: $field',
      code: 400,
    );
  }

  factory ValidationFailure.requiredField(String field) {
    return ValidationFailure(
      message: 'Required field missing: $field',
      code: 400,
    );
  }

  factory ValidationFailure.formatError(String field) {
    return ValidationFailure(
      message: 'Invalid format for field: $field',
      code: 400,
    );
  }
}

/// File operation failures
class FileFailure extends Failure {
  const FileFailure({
    required super.message,
    super.code,
  });

  factory FileFailure.notFound(String path) {
    return FileFailure(
      message: 'File not found: $path',
      code: 404,
    );
  }

  factory FileFailure.accessDenied(String path) {
    return FileFailure(
      message: 'Access denied to file: $path',
      code: 403,
    );
  }

  factory FileFailure.corruptedFile(String path) {
    return FileFailure(
      message: 'File is corrupted: $path',
      code: 422,
    );
  }
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
  });

  factory NetworkFailure.noConnection() {
    return const NetworkFailure(
      message: 'No network connection available',
      code: 503,
    );
  }

  factory NetworkFailure.timeout() {
    return const NetworkFailure(
      message: 'Network request timeout',
      code: 408,
    );
  }

  factory NetworkFailure.badRequest() {
    return const NetworkFailure(
      message: 'Bad network request',
      code: 400,
    );
  }
}

/// Not found failures
class NotFoundFailure extends Failure {
  const NotFoundFailure({
    required super.message,
    int? code,
  }) : super(code: code ?? 404);

  factory NotFoundFailure.resource(String resource) {
    return NotFoundFailure(
      message: 'Resource not found: $resource',
    );
  }
}

/// Unknown or unexpected failures
class UnknownFailure extends Failure {
  const UnknownFailure({
    required super.message,
    super.code,
  });

  factory UnknownFailure.fromException(Exception exception) {
    return UnknownFailure(
      message: 'Unknown error: ${exception.toString()}',
      code: 500,
    );
  }
}
