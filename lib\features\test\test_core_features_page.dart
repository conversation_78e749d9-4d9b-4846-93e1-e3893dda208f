import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/design_system/vanhub_design_system.dart';
import '../../core/theme/vanhub_text_styles.dart';
import 'database_connection_test_page.dart';
import '../project/domain/entities/project.dart';
import '../project/domain/entities/project_visibility.dart';
import '../project/domain/entities/fork_settings.dart';
import '../bom/presentation/providers/bom_provider.dart';
import '../bom/domain/entities/bom_statistics.dart';

/// 测试核心功能页面
class TestCoreFeaturesPage extends ConsumerStatefulWidget {
  const TestCoreFeaturesPage({super.key});

  @override
  ConsumerState<TestCoreFeaturesPage> createState() => _TestCoreFeaturesPageState();
}

class _TestCoreFeaturesPageState extends ConsumerState<TestCoreFeaturesPage> {
  final String _testProjectId = 'test-project-123';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('核心功能测试'),
        backgroundColor: VanHubColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(VanHubSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // BOM功能测试区域
            _buildSectionCard(
              title: 'BOM管理功能测试',
              icon: Icons.list_alt,
              color: VanHubColors.primary,
              children: [
                _buildTestButton(
                  title: '测试BOM统计计算',
                  subtitle: '验证BOM统计服务是否正常工作',
                  onPressed: _testBomStatistics,
                ),
                _buildTestButton(
                  title: '创建测试BOM项目',
                  subtitle: '创建一个测试用的BOM项目',
                  onPressed: _testCreateBomItem,
                ),
                _buildTestButton(
                  title: '获取项目BOM列表',
                  subtitle: '获取指定项目的所有BOM项目',
                  onPressed: _testGetBomItems,
                ),
              ],
            ),
            
            const SizedBox(height: VanHubSpacing.xl),
            
            // 项目复刻功能测试区域
            _buildSectionCard(
              title: '项目复刻功能测试',
              icon: Icons.fork_right,
              color: VanHubColors.secondary,
              children: [
                _buildTestButton(
                  title: '显示复刻对话框',
                  subtitle: '测试复刻对话框UI组件',
                  onPressed: _testForkDialog,
                ),
                _buildTestButton(
                  title: '测试复刻权限检查',
                  subtitle: '验证复刻权限验证逻辑',
                  onPressed: _testForkPermission,
                ),
                _buildTestButton(
                  title: '模拟项目复刻流程',
                  subtitle: '完整的项目复刻流程测试',
                  onPressed: _testForkProcess,
                ),
              ],
            ),
            
            const SizedBox(height: VanHubSpacing.xl),
            const SizedBox(height: VanHubSpacing.xl),
            
            // 数据库连接测试区域
            _buildSectionCard(
              title: '数据库连接测试',
              icon: Icons.storage,
              color: VanHubColors.success,
              children: [
                _buildTestButton(
                  title: '数据库连接测试',
                  subtitle: '检查Supabase数据库连接状态',
                  onPressed: _openDatabaseTest,
                ),
                _buildTestButton(
                  title: '快速连接检查',
                  subtitle: '执行快速的数据库连接验证',
                  onPressed: _quickConnectionTest,
                ),
              ],
            ),
            
            const SizedBox(height: VanHubSpacing.xl),
            
            // 智能联动功能测试区域
            _buildSectionCard(
              title: '智能联动功能测试',
              icon: Icons.link,
              color: VanHubColors.info,
              children: [
                _buildTestButton(
                  title: '材料库到BOM联动',
                  subtitle: '测试从材料库添加到BOM的功能',
                  onPressed: _testMaterialToBom,
                ),
                _buildTestButton(
                  title: 'BOM到材料库联动',
                  subtitle: '测试将BOM项目保存到材料库',
                  onPressed: _testBomToMaterial,
                ),
                _buildTestButton(
                  title: '智能推荐测试',
                  subtitle: '测试材料智能推荐算法',
                  onPressed: _testSmartRecommendation,
                ),
              ],
            ),
            
            const SizedBox(height: VanHubSpacing.xl),
            
            // 测试结果显示区域
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(VanHubSpacing.lg),
              decoration: BoxDecoration(
                color: VanHubColors.surface,
                borderRadius: BorderRadius.circular(VanHubSpacing.radiusLarge),
                border: Border.all(color: VanHubColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.terminal,
                        color: VanHubColors.textSecondary,
                        size: 20,
                      ),
                      const SizedBox(width: VanHubSpacing.sm),
                      Text(
                        '测试结果输出',
                        style: VanHubTextStyles.subtitle1.copyWith(
                          color: VanHubColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: VanHubSpacing.md),
                  Container(
                    width: double.infinity,
                    height: 200,
                    padding: const EdgeInsets.all(VanHubSpacing.md),
                    decoration: BoxDecoration(
                      color: Colors.grey[900],
                      borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
                    ),
                    child: SingleChildScrollView(
                      child: Text(
                        _testOutput,
                        style: const TextStyle(
                          color: Colors.green,
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: VanHubSpacing.md),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _clearTestOutput,
                        icon: const Icon(Icons.clear, size: 16),
                        label: const Text('清空输出'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: VanHubColors.warning,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: VanHubSpacing.sm),
                      OutlinedButton.icon(
                        onPressed: _runAllTests,
                        icon: const Icon(Icons.play_arrow, size: 16),
                        label: const Text('运行所有测试'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(VanHubSpacing.lg),
      decoration: BoxDecoration(
        color: VanHubColors.surface,
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusLarge),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(VanHubSpacing.sm),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: VanHubSpacing.md),
              Expanded(
                child: Text(
                  title,
                  style: VanHubTextStyles.headline3.copyWith(
                    color: VanHubColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: VanHubSpacing.lg),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTestButton({
    required String title,
    required String subtitle,
    required VoidCallback onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: VanHubSpacing.md),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
        child: Container(
          padding: const EdgeInsets.all(VanHubSpacing.md),
          decoration: BoxDecoration(
            border: Border.all(color: VanHubColors.border),
            borderRadius: BorderRadius.circular(VanHubSpacing.radiusMedium),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: VanHubTextStyles.body1.copyWith(
                        fontWeight: FontWeight.w500,
                        color: VanHubColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: VanHubSpacing.xs),
                    Text(
                      subtitle,
                      style: VanHubTextStyles.caption.copyWith(
                        color: VanHubColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.play_arrow,
                color: VanHubColors.primary,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _testOutput = '准备运行测试...\n';

  void _addTestOutput(String message) {
    setState(() {
      _testOutput += '${DateTime.now().toString().substring(11, 19)} - $message\n';
    });
  }

  void _clearTestOutput() {
    setState(() {
      _testOutput = '输出已清空\n';
    });
  }

  // BOM功能测试方法
  Future<void> _testBomStatistics() async {
    _addTestOutput('开始测试BOM统计计算...');
    try {
      final statistics = await ref.read(projectBomStatisticsProvider(_testProjectId).future);
      _addTestOutput('✅ BOM统计计算成功');
      _addTestOutput('   总项目数: ${statistics.totalItems}');
      _addTestOutput('   完成项目数: ${statistics.completedItems}');
      _addTestOutput('   总预算: ¥${statistics.totalBudget.toStringAsFixed(2)}');
      _addTestOutput('   已花费: ¥${statistics.spentAmount.toStringAsFixed(2)}');
      _addTestOutput('   完成度: ${((statistics.completedItems / statistics.totalItems) * 100).toStringAsFixed(1)}%');
    } catch (e) {
      _addTestOutput('❌ BOM统计计算失败: $e');
    }
  }

  Future<void> _testCreateBomItem() async {
    _addTestOutput('开始测试创建BOM项目...');
    try {
      // 使用新的createBomItem方法签名
      final result = await ref.read(bomControllerProvider.notifier).createBomItem(
        _testProjectId,
        '测试物料-${DateTime.now().millisecondsSinceEpoch}',
        '这是一个测试用的BOM项目',
        1,
        100.0,
        category: '测试分类',
      );
      result.fold(
        (failure) => _addTestOutput('❌ 创建BOM项目失败: ${failure.message}'),
        (bomItem) {
          _addTestOutput('✅ BOM项目创建成功');
          _addTestOutput('   项目ID: ${bomItem.id}');
          _addTestOutput('   项目名称: ${bomItem.materialName}');
          _addTestOutput('   单价: ¥${bomItem.unitPrice}');
        },
      );
    } catch (e) {
      _addTestOutput('❌ 创建BOM项目异常: $e');
    }
  }

  Future<void> _testGetBomItems() async {
    _addTestOutput('开始测试获取BOM列表...');
    try {
      final bomItems = await ref.read(projectBomItemsProvider(_testProjectId).future);
      _addTestOutput('✅ 获取BOM列表成功');
      _addTestOutput('   找到 ${bomItems.length} 个BOM项目');
      
      for (int i = 0; i < bomItems.length && i < 3; i++) {
        final item = bomItems[i];
        _addTestOutput('   ${i + 1}. ${item.materialName} - ¥${item.unitPrice} x ${item.quantity}');
      }
      
      if (bomItems.length > 3) {
        _addTestOutput('   ... 还有 ${bomItems.length - 3} 个项目');
      }
    } catch (e) {
      _addTestOutput('❌ 获取BOM列表失败: $e');
    }
  }

  // 项目复刻功能测试方法
  void _testForkDialog() {
    _addTestOutput('显示复刻对话框...');
    
    // 创建一个测试项目
    final testProject = Project(
      id: 'test-project-123',
      authorId: 'test-user-456',
      title: '测试改装项目',
      description: '这是一个用于测试复刻功能的项目',
      status: ProjectStatus.inProgress,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      authorName: '测试用户',
      vehicleBrand: '大通',
      vehicleModel: 'V90',
      vehicleYear: '2023',
      budget: 50000.0,
      spentAmount: 25000.0,
      progress: 60,
      visibility: ProjectVisibility.public,
      forkSettings: const ForkSettings(
        allowFork: true,
        copyBomItems: true,
        copySystems: true,
      ),
    );

    // TODO: 实现复刻对话框显示
    // showDialog(
    //   context: context,
    //   builder: (context) => ForkProjectDialogWidget(
    //     sourceProject: testProject,
    //     onForkSuccess: () {
    //       _addTestOutput('✅ 复刻对话框测试完成');
    //     },
    //   ),
    // );
    _addTestOutput('✅ 复刻对话框测试完成（模拟）');
  }

  Future<void> _testForkPermission() async {
    _addTestOutput('开始测试复刻权限检查...');
    // TODO: 实现复刻权限检查测试
    await Future.delayed(const Duration(seconds: 1));
    _addTestOutput('✅ 复刻权限检查测试完成（模拟）');
  }

  Future<void> _testForkProcess() async {
    _addTestOutput('开始测试项目复刻流程...');
    // TODO: 实现完整复刻流程测试
    await Future.delayed(const Duration(seconds: 2));
    _addTestOutput('✅ 项目复刻流程测试完成（模拟）');
  }

  // 智能联动功能测试方法
  Future<void> _testMaterialToBom() async {
    _addTestOutput('开始测试材料库到BOM联动...');
    try {
      await ref.read(bomControllerProvider.notifier).addMaterialToBom(
        projectId: _testProjectId,
        materialId: 'test-material-123',
        quantity: 2,
        customPrice: 150.0,
        notes: '从材料库添加的测试物料',
      );

      _addTestOutput('✅ 材料库到BOM联动成功');
      _addTestOutput('   添加物料: test-material-123');
      _addTestOutput('   数量: 2');
      _addTestOutput('   单价: ¥150.0');
    } catch (e) {
      _addTestOutput('❌ 材料库到BOM联动异常: $e');
    }
  }

  Future<void> _testBomToMaterial() async {
    _addTestOutput('开始测试BOM到材料库联动...');
    try {
      final result = await ref.read(bomControllerProvider.notifier).saveBomItemToMaterialLibrary('test-bom-item-123');
      
      result.fold(
        (failure) => _addTestOutput('❌ BOM到材料库联动失败: ${failure.message}'),
        (materialId) {
          _addTestOutput('✅ BOM到材料库联动成功');
          _addTestOutput('   新材料ID: $materialId');
        },
      );
    } catch (e) {
      _addTestOutput('❌ BOM到材料库联动异常: $e');
    }
  }

  Future<void> _testSmartRecommendation() async {
    _addTestOutput('开始测试智能推荐功能...');
    // TODO: 实现智能推荐测试
    await Future.delayed(const Duration(seconds: 1));
    _addTestOutput('✅ 智能推荐功能测试完成（模拟）');
  }

  // 数据库连接测试方法
  void _openDatabaseTest() {
    _addTestOutput('打开数据库连接测试页面...');
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DatabaseConnectionTestPage(),
      ),
    );
  }

  Future<void> _quickConnectionTest() async {
    _addTestOutput('开始快速数据库连接测试...');
    try {
      // 这里可以添加快速连接测试逻辑
      await Future.delayed(const Duration(seconds: 1));
      _addTestOutput('✅ 快速连接测试完成（模拟）');
    } catch (e) {
      _addTestOutput('❌ 快速连接测试失败: $e');
    }
  }

  Future<void> _runAllTests() async {
    _addTestOutput('🚀 开始运行所有测试...');
    
    await _quickConnectionTest();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testBomStatistics();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testCreateBomItem();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testGetBomItems();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testForkPermission();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testForkProcess();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testMaterialToBom();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testBomToMaterial();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await _testSmartRecommendation();
    
    _addTestOutput('🎉 所有测试运行完成！');
  }
}