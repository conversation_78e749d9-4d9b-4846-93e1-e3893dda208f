import 'package:flutter_test/flutter_test.dart';
import 'package:vanhub/features/material/data/services/specification_service_impl.dart';
import 'package:vanhub/features/material/domain/entities/product_specification.dart';
import 'package:vanhub/features/material/domain/services/specification_service.dart';
import 'package:vanhub/core/error/failures.dart';

void main() {
  group('SpecificationService', () {
    late SpecificationService service;

    setUp(() {
      service = SpecificationServiceImpl();
    });

    group('getSpecificationByMaterialId', () {
      test('should return specification when material has one', () async {
        // Arrange
        const materialId = 'material_battery_001';

        // Act
        final result = await service.getSpecificationByMaterialId(materialId);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specification) {
            expect(specification, isNotNull);
            expect(specification!.materialId, equals(materialId));
            expect(specification.category, equals('ELECTRICAL'));
            expect(specification.basicSpec.productName, equals('磷酸铁锂电池'));
          },
        );
      });

      test('should return null when material has no specification', () async {
        // Arrange
        const materialId = 'non_existent_material';

        // Act
        final result = await service.getSpecificationByMaterialId(materialId);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specification) => expect(specification, isNull),
        );
      });
    });

    group('getSpecificationById', () {
      test('should return specification when id exists', () async {
        // Arrange
        const specId = 'spec_battery_lifepo4_100ah';

        // Act
        final result = await service.getSpecificationById(specId);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specification) {
            expect(specification, isNotNull);
            expect(specification!.id, equals(specId));
            expect(specification.basicSpec.brand, equals('CATL'));
          },
        );
      });

      test('should return null when id does not exist', () async {
        // Arrange
        const specId = 'non_existent_spec';

        // Act
        final result = await service.getSpecificationById(specId);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specification) => expect(specification, isNull),
        );
      });
    });

    group('searchSpecifications', () {
      test('should return all specifications when no filters', () async {
        // Act
        final result = await service.searchSpecifications();

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications, isNotEmpty);
            expect(specifications.length, greaterThan(5)); // 我们有多个示例数据
          },
        );
      });

      test('should filter by category', () async {
        // Act
        final result = await service.searchSpecifications(category: 'ELECTRICAL');

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications, isNotEmpty);
            for (final spec in specifications) {
              expect(spec.category, equals('ELECTRICAL'));
            }
          },
        );
      });

      test('should respect limit parameter', () async {
        // Act
        final result = await service.searchSpecifications(limit: 2);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications.length, lessThanOrEqualTo(2));
          },
        );
      });

      test('should handle pagination with offset', () async {
        // Arrange
        const limit = 3;
        const offset = 2;

        // Act
        final result = await service.searchSpecifications(
          limit: limit,
          offset: offset,
        );

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications.length, lessThanOrEqualTo(limit));
          },
        );
      });
    });

    group('getSpecificationsByMaterialIds', () {
      test('should return specifications for existing material ids', () async {
        // Arrange
        const materialIds = ['material_battery_001', 'material_pump_001'];

        // Act
        final result = await service.getSpecificationsByMaterialIds(materialIds);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications.length, equals(2));
            expect(specifications.containsKey('material_battery_001'), true);
            expect(specifications.containsKey('material_pump_001'), true);
          },
        );
      });

      test('should return empty map for non-existent material ids', () async {
        // Arrange
        const materialIds = ['non_existent_1', 'non_existent_2'];

        // Act
        final result = await service.getSpecificationsByMaterialIds(materialIds);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (specifications) {
            expect(specifications.isEmpty, true);
          },
        );
      });
    });

    group('createSpecification', () {
      test('should create new specification successfully', () async {
        // Arrange
        final specification = ProductSpecification(
          id: 'test_spec_001',
          materialId: 'test_material_001',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '测试产品',
            brand: '测试品牌',
            model: 'TEST-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final result = await service.createSpecification(specification);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (createdSpec) {
            expect(createdSpec.id, equals(specification.id));
            expect(createdSpec.basicSpec.productName, equals('测试产品'));
          },
        );

        // Verify it can be retrieved
        final getResult = await service.getSpecificationById('test_spec_001');
        expect(getResult.isRight(), true);
        getResult.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (retrievedSpec) {
            expect(retrievedSpec, isNotNull);
            expect(retrievedSpec!.id, equals('test_spec_001'));
          },
        );
      });
    });

    group('updateSpecification', () {
      test('should update existing specification', () async {
        // Arrange - First create a specification
        final originalSpec = ProductSpecification(
          id: 'update_test_001',
          materialId: 'update_material_001',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '原始产品',
            brand: '原始品牌',
            model: 'ORIG-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await service.createSpecification(originalSpec);

        // Update the specification
        final updatedSpec = originalSpec.copyWith(
          basicSpec: originalSpec.basicSpec.copyWith(
            productName: '更新产品',
          ),
          updatedAt: DateTime.now(),
        );

        // Act
        final result = await service.updateSpecification(updatedSpec);

        // Assert
        expect(result.isRight(), true);
        result.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (spec) {
            expect(spec.basicSpec.productName, equals('更新产品'));
          },
        );
      });

      test('should fail when updating non-existent specification', () async {
        // Arrange
        final nonExistentSpec = ProductSpecification(
          id: 'non_existent_spec',
          materialId: 'test_material',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '不存在的产品',
            brand: '测试品牌',
            model: 'TEST-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final result = await service.updateSpecification(nonExistentSpec);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure, isA<NotFoundFailure>());
            expect(failure.message, contains('规格不存在'));
          },
          (spec) => fail('Expected Left but got Right'),
        );
      });
    });

    group('deleteSpecification', () {
      test('should delete existing specification', () async {
        // Arrange - First create a specification
        final spec = ProductSpecification(
          id: 'delete_test_001',
          materialId: 'delete_material_001',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '待删除产品',
            brand: '测试品牌',
            model: 'DEL-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await service.createSpecification(spec);

        // Act
        final result = await service.deleteSpecification('delete_test_001');

        // Assert
        expect(result.isRight(), true);

        // Verify it's deleted
        final getResult = await service.getSpecificationById('delete_test_001');
        expect(getResult.isRight(), true);
        getResult.fold(
          (failure) => fail('Expected Right but got Left: ${failure.message}'),
          (retrievedSpec) => expect(retrievedSpec, isNull),
        );
      });

      test('should fail when deleting non-existent specification', () async {
        // Act
        final result = await service.deleteSpecification('non_existent_spec');

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure, isA<NotFoundFailure>());
            expect(failure.message, contains('规格不存在'));
          },
          (_) => fail('Expected Left but got Right'),
        );
      });
    });

    group('validateSpecification', () {
      test('should pass validation for valid specification', () {
        // Arrange
        final validSpec = ProductSpecification(
          id: 'valid_spec_001',
          materialId: 'valid_material_001',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '有效产品',
            brand: '有效品牌',
            model: 'VALID-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 100.0,
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 1.0,
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final result = service.validateSpecification(validSpec);

        // Assert
        expect(result.isRight(), true);
      });

      test('should fail validation for invalid specification', () {
        // Arrange
        final invalidSpec = ProductSpecification(
          id: '', // 空ID
          materialId: 'test_material',
          category: 'ELECTRICAL',
          basicSpec: BasicSpecification(
            productName: '',
            brand: '',
            model: 'TEST-001',
          ),
          technicalParams: const TechnicalParameters(),
          physicalProps: PhysicalProperties(
            dimensions: Dimensions(
              length: 0.0, // 无效尺寸
              width: 100.0,
              height: 100.0,
              unit: 'mm',
            ),
            weight: 0.0, // 无效重量
          ),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final result = service.validateSpecification(invalidSpec);

        // Assert
        expect(result.isLeft(), true);
        result.fold(
          (failure) {
            expect(failure, isA<ValidationFailure>());
          },
          (_) => fail('Expected Left but got Right'),
        );
      });
    });
  });
}
