import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../domain/entities/dashboard_data.dart';
import '../providers/dashboard_provider.dart';

/// 成本分析图表组件
class CostAnalysisChart extends ConsumerWidget {
  final String projectId;

  const CostAnalysisChart({
    super.key,
    required this.projectId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final costAnalysisAsync = ref.watch(costAnalysisProvider(projectId));

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: costAnalysisAsync.when(
          data: (costAnalysis) => _buildCostAnalysisContent(context, costAnalysis),
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 8),
                Text('加载失败: $error'),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => ref.refresh(costAnalysisProvider(projectId)),
                  child: const Text('重试'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCostAnalysisContent(BuildContext context, CostAnalysis costAnalysis) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和总览
        Row(
          children: [
            const Icon(Icons.analytics, color: Colors.green, size: 28),
            const SizedBox(width: 12),
            const Text(
              '成本分析',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            _buildCostSummary(costAnalysis),
          ],
        ),
        const SizedBox(height: 24),

        // 图表区域
        SizedBox(
          height: 300,
          child: Row(
            children: [
              // 分类成本饼图
              Expanded(
                flex: 2,
                child: _buildCategoryPieChart(costAnalysis.categoryCosts),
              ),
              const SizedBox(width: 20),
              // 成本趋势线图
              Expanded(
                flex: 3,
                child: _buildCostTrendChart(costAnalysis.costTrends),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // 成本预警
        if (costAnalysis.costAlerts.isNotEmpty) ...[
          const Text(
            '成本预警',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...costAnalysis.costAlerts.map((alert) => _buildCostAlert(alert)),
        ],
      ],
    );
  }

  Widget _buildCostSummary(CostAnalysis costAnalysis) {
    final variance = costAnalysis.actualCost - costAnalysis.plannedCost;
    final variancePercentage = costAnalysis.plannedCost > 0 
        ? (variance / costAnalysis.plannedCost) * 100 
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '总成本: ¥${costAnalysis.totalCost.toStringAsFixed(0)}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              variance >= 0 ? Icons.trending_up : Icons.trending_down,
              size: 16,
              color: variance >= 0 ? Colors.red : Colors.green,
            ),
            const SizedBox(width: 4),
            Text(
              '${variance >= 0 ? '+' : ''}${variancePercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 12,
                color: variance >= 0 ? Colors.red : Colors.green,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryPieChart(List<CategoryCost> categoryCosts) {
    if (categoryCosts.isEmpty) {
      return const Center(
        child: Text('暂无成本数据'),
      );
    }

    return Column(
      children: [
        const Text(
          '分类成本分布',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: PieChart(
            PieChartData(
              sectionsSpace: 2,
              centerSpaceRadius: 40,
              sections: categoryCosts.map((category) {
                return PieChartSectionData(
                  value: category.amount,
                  color: Color(int.parse('0xFF${category.colorHex.substring(1)}')),
                  radius: 50,
                  title: '${(category.percentage * 100).toStringAsFixed(0)}%',
                  titleStyle: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // 图例
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: categoryCosts.map((category) => _buildLegendItem(
            category.categoryName,
            Color(int.parse('0xFF${category.colorHex.substring(1)}')),
            '¥${category.amount.toStringAsFixed(0)}',
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label: $value',
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  Widget _buildCostTrendChart(List<CostTrend> costTrends) {
    if (costTrends.isEmpty) {
      return const Center(
        child: Text('暂无趋势数据'),
      );
    }

    return Column(
      children: [
        const Text(
          '成本趋势',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: LineChart(
            LineChartData(
              gridData: FlGridData(
                show: true,
                drawVerticalLine: false,
                horizontalInterval: costTrends.last.cumulativeCost / 5,
                getDrawingHorizontalLine: (value) {
                  return FlLine(
                    color: Colors.grey.withOpacity(0.2),
                    strokeWidth: 1,
                  );
                },
              ),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 60,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        '¥${(value / 1000).toStringAsFixed(0)}k',
                        style: const TextStyle(fontSize: 10),
                      );
                    },
                  ),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30,
                    interval: costTrends.length / 5,
                    getTitlesWidget: (value, meta) {
                      final index = value.toInt();
                      if (index >= 0 && index < costTrends.length) {
                        final date = costTrends[index].date;
                        return Text(
                          '${date.month}/${date.day}',
                          style: const TextStyle(fontSize: 10),
                        );
                      }
                      return const Text('');
                    },
                  ),
                ),
                rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: false),
              lineBarsData: [
                // 实际成本线
                LineChartBarData(
                  spots: costTrends.asMap().entries.map((entry) {
                    return FlSpot(entry.key.toDouble(), entry.value.cumulativeCost);
                  }).toList(),
                  isCurved: true,
                  color: Colors.blue,
                  barWidth: 3,
                  dotData: const FlDotData(show: false),
                  belowBarData: BarAreaData(
                    show: true,
                    color: Colors.blue.withOpacity(0.1),
                  ),
                ),
                // 计划成本线
                LineChartBarData(
                  spots: costTrends.asMap().entries.map((entry) {
                    return FlSpot(entry.key.toDouble(), entry.value.plannedCost);
                  }).toList(),
                  isCurved: true,
                  color: Colors.grey,
                  barWidth: 2,
                  dotData: const FlDotData(show: false),
                  dashArray: [5, 5],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        // 图例
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildTrendLegend('实际成本', Colors.blue, false),
            const SizedBox(width: 20),
            _buildTrendLegend('计划成本', Colors.grey, true),
          ],
        ),
      ],
    );
  }

  Widget _buildTrendLegend(String label, Color color, bool isDashed) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 20,
          height: 2,
          decoration: BoxDecoration(
            color: color,
            border: isDashed ? Border.all(color: color, width: 1) : null,
          ),
          child: isDashed ? CustomPaint(
            painter: DashedLinePainter(color: color),
          ) : null,
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  Widget _buildCostAlert(CostAlert alert) {
    Color alertColor;
    IconData alertIcon;

    switch (alert.severity) {
      case AlertSeverity.low:
        alertColor = Colors.blue;
        alertIcon = Icons.info;
        break;
      case AlertSeverity.medium:
        alertColor = Colors.orange;
        alertIcon = Icons.warning;
        break;
      case AlertSeverity.high:
        alertColor = Colors.red;
        alertIcon = Icons.error;
        break;
      case AlertSeverity.critical:
        alertColor = Colors.red.shade800;
        alertIcon = Icons.dangerous;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: alertColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: alertColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(alertIcon, color: alertColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alert.title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: alertColor,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  alert.description,
                  style: TextStyle(
                    color: alertColor.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashWidth = 3.0;
    const dashSpace = 2.0;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
