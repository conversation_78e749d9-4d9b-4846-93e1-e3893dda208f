import 'package:freezed_annotation/freezed_annotation.dart';
import 'product_specification.dart';

part 'material.freezed.dart';
part 'material.g.dart';

@freezed
class Material with _$Material {
  const factory Material({
    required String id,
    required String userId,
    required String name,
    required String description,
    required String category,  // 改为String类型
    required double price,     // 价格改为必填
    required DateTime createdAt,
    required DateTime updatedAt,
    String? brand,
    String? model,
    String? specifications,
    double? minPrice,
    double? maxPrice,
    String? supplier,
    String? supplierUrl,
    String? imageUrl,
    DateTime? purchaseDate,    // 添加采购日期
    List<String>? tags,
    @Default(0) int usageCount,
    DateTime? lastUsedAt,
    Map<String, dynamic>? metadata,

    /// 关联的产品规格
    ProductSpecification? productSpecification,

    /// 规格ID（用于延迟加载）
    String? specificationId,
  }) = _Material;

  factory Material.fromJson(Map<String, dynamic> json) => _$MaterialFromJson(json);
}