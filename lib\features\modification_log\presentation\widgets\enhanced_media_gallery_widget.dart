import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';

/// 媒体类型
enum MediaType {
  image,
  video,
  document,
  unknown,
}

/// 媒体项
class MediaItem {
  final String id;
  final String url;
  final String? thumbnailUrl;
  final MediaType type;
  final String? title;
  final String? description;
  final int? fileSize;
  final DateTime? createdAt;

  const MediaItem({
    required this.id,
    required this.url,
    this.thumbnailUrl,
    required this.type,
    this.title,
    this.description,
    this.fileSize,
    this.createdAt,
  });

  /// 获取显示用的缩略图URL
  String get displayThumbnail => thumbnailUrl ?? url;

  /// 获取文件大小显示文本
  String get fileSizeText {
    if (fileSize == null) return '';
    
    if (fileSize! < 1024) {
      return '${fileSize}B';
    } else if (fileSize! < 1024 * 1024) {
      return '${(fileSize! / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize! / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// 获取媒体类型图标
  IconData get typeIcon {
    switch (type) {
      case MediaType.image:
        return Icons.image;
      case MediaType.video:
        return Icons.videocam;
      case MediaType.document:
        return Icons.description;
      case MediaType.unknown:
        return Icons.attachment;
    }
  }

  /// 获取媒体类型颜色
  Color get typeColor {
    switch (type) {
      case MediaType.image:
        return Colors.green;
      case MediaType.video:
        return Colors.red;
      case MediaType.document:
        return Colors.blue;
      case MediaType.unknown:
        return Colors.grey;
    }
  }
}

/// 增强的媒体画廊组件
class EnhancedMediaGalleryWidget extends ConsumerStatefulWidget {
  final List<String> mediaIds;
  final Function(String mediaId)? onMediaTap;
  final Function(String mediaId)? onMediaLongPress;
  final bool showDetails;
  final bool allowSelection;
  final int crossAxisCount;

  const EnhancedMediaGalleryWidget({
    super.key,
    required this.mediaIds,
    this.onMediaTap,
    this.onMediaLongPress,
    this.showDetails = true,
    this.allowSelection = false,
    this.crossAxisCount = 3,
  });

  @override
  ConsumerState<EnhancedMediaGalleryWidget> createState() => _EnhancedMediaGalleryWidgetState();
}

class _EnhancedMediaGalleryWidgetState extends ConsumerState<EnhancedMediaGalleryWidget> {
  final Set<String> _selectedMediaIds = {};
  bool _isSelectionMode = false;

  @override
  Widget build(BuildContext context) {
    if (widget.mediaIds.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        if (_isSelectionMode) _buildSelectionHeader(),
        _buildMediaGrid(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_library_outlined,
              size: 32,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: VanHubSpacing.xs),
            Text(
              '暂无媒体文件',
              style: VanHubTypography.bodyMedium.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      padding: const EdgeInsets.all(VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: VanHubBrandColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(
            '已选择 ${_selectedMediaIds.length} 项',
            style: VanHubTypography.titleSmall.copyWith(
              color: VanHubBrandColors.primary,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: _clearSelection,
            child: const Text('取消'),
          ),
          const SizedBox(width: VanHubSpacing.xs),
          ElevatedButton(
            onPressed: _selectedMediaIds.isNotEmpty ? _handleBatchAction : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubBrandColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('操作'),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          crossAxisSpacing: VanHubSpacing.sm,
          mainAxisSpacing: VanHubSpacing.sm,
          childAspectRatio: 1.0,
        ),
        itemCount: widget.mediaIds.length,
        itemBuilder: (context, index) {
          final mediaId = widget.mediaIds[index];
          return _buildMediaItem(mediaId, index);
        },
      ),
    );
  }

  Widget _buildMediaItem(String mediaId, int index) {
    final isSelected = _selectedMediaIds.contains(mediaId);
    final mediaItem = _getMediaItem(mediaId); // TODO: 从Provider获取实际数据

    return GestureDetector(
      onTap: () => _handleMediaTap(mediaId),
      onLongPress: widget.allowSelection ? () => _handleMediaLongPress(mediaId) : null,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: isSelected 
              ? Border.all(color: VanHubBrandColors.primary, width: 3)
              : Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              _buildMediaContent(mediaItem),
              
              // 选择状态覆盖层
              if (isSelected)
                Container(
                  color: VanHubBrandColors.primary.withValues(alpha: 0.3),
                  child: const Center(
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
              
              // 媒体类型标识
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    mediaItem.typeIcon,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
              
              // 详情信息
              if (widget.showDetails)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(VanHubSpacing.xs),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (mediaItem.title != null)
                          Text(
                            mediaItem.title!,
                            style: VanHubTypography.bodySmall.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        if (mediaItem.fileSizeText.isNotEmpty)
                          Text(
                            mediaItem.fileSizeText,
                            style: VanHubTypography.bodySmall.copyWith(
                              color: Colors.white70,
                              fontSize: 10,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    ).animate(delay: (index * 50).ms)
        .fadeIn(duration: 300.ms)
        .scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildMediaContent(MediaItem mediaItem) {
    switch (mediaItem.type) {
      case MediaType.image:
        return CachedNetworkImage(
          imageUrl: mediaItem.displayThumbnail,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: Icon(Icons.error, color: Colors.red),
            ),
          ),
        );
      
      case MediaType.video:
        return Stack(
          fit: StackFit.expand,
          children: [
            CachedNetworkImage(
              imageUrl: mediaItem.displayThumbnail,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey.shade200,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey.shade200,
                child: const Center(
                  child: Icon(Icons.videocam, size: 32),
                ),
              ),
            ),
            const Center(
              child: Icon(
                Icons.play_circle_fill,
                color: Colors.white,
                size: 32,
              ),
            ),
          ],
        );
      
      default:
        return Container(
          color: Colors.grey.shade200,
          child: Center(
            child: Icon(
              mediaItem.typeIcon,
              color: mediaItem.typeColor,
              size: 32,
            ),
          ),
        );
    }
  }

  void _handleMediaTap(String mediaId) {
    if (_isSelectionMode) {
      _toggleSelection(mediaId);
    } else {
      widget.onMediaTap?.call(mediaId);
    }
  }

  void _handleMediaLongPress(String mediaId) {
    if (!_isSelectionMode) {
      setState(() {
        _isSelectionMode = true;
      });
    }
    _toggleSelection(mediaId);
    widget.onMediaLongPress?.call(mediaId);
  }

  void _toggleSelection(String mediaId) {
    setState(() {
      if (_selectedMediaIds.contains(mediaId)) {
        _selectedMediaIds.remove(mediaId);
        if (_selectedMediaIds.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedMediaIds.add(mediaId);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedMediaIds.clear();
      _isSelectionMode = false;
    });
  }

  void _handleBatchAction() {
    // TODO: 实现批量操作
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(VanHubSpacing.md),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('下载'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现下载功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('分享'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现分享功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('删除', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现删除功能
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 获取媒体项数据（临时实现）
  MediaItem _getMediaItem(String mediaId) {
    // TODO: 从实际的Provider获取数据
    return MediaItem(
      id: mediaId,
      url: 'https://picsum.photos/300/300?random=$mediaId',
      type: MediaType.image,
      title: '媒体文件 $mediaId',
      fileSize: 1024 * 512, // 512KB
      createdAt: DateTime.now(),
    );
  }
}
