import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vanhub/features/material/domain/entities/product_specification.dart';
import 'package:vanhub/features/material/presentation/widgets/product_specification_widget.dart';

void main() {
  group('ProductSpecificationWidget', () {
    late ProductSpecification testSpecification;

    setUp(() {
      testSpecification = ProductSpecification(
        id: 'test_spec_001',
        materialId: 'test_material_001',
        category: 'ELECTRICAL',
        basicSpec: BasicSpecification(
          productName: '测试电池',
          brand: '测试品牌',
          model: 'TEST-100Ah',
          manufacturer: '测试制造商',
          countryOfOrigin: '中国',
          warrantyMonths: 24,
          description: '这是一个测试用的电池规格',
        ),
        technicalParams: TechnicalParameters(
          electrical: ElectricalSpecs(
            ratedVoltage: 12.0,
            ratedCurrent: 100.0,
            ratedPower: 1200.0,
            capacity: 100.0,
            capacityUnit: 'Ah',
            efficiency: 95.0,
            protectionRating: 'IP65',
          ),
        ),
        physicalProps: PhysicalProperties(
          dimensions: Dimensions(
            length: 300.0,
            width: 200.0,
            height: 150.0,
            unit: 'mm',
          ),
          weight: 12.5,
          color: '黑色',
          material: '磷酸铁锂',
        ),
        performanceMetrics: PerformanceMetrics(
          lifespan: 5000,
          lifespanUnit: '次',
          cycleCount: 5000,
          reliabilityGrade: 'A级',
          performanceGrade: '优秀',
        ),
        certifications: [
          CertificationInfo(
            name: 'CE认证',
            authority: '欧盟',
            certificateNumber: 'CE-2023-001',
            expiryDate: DateTime(2025, 12, 31),
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    Widget createTestWidget({
      required ProductSpecification specification,
      bool showAllFields = true,
      bool isCompactMode = false,
    }) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: ProductSpecificationWidget(
                specification: specification,
                showAllFields: showAllFields,
                isCompactMode: isCompactMode,
              ),
            ),
          ),
        ),
      );
    }

    group('基本显示测试', () {
      testWidgets('should display product name and brand', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('测试电池'), findsOneWidget);
        expect(find.text('测试品牌 - TEST-100Ah'), findsOneWidget);
      });

      testWidgets('should display category chip', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('电气设备'), findsOneWidget);
        expect(find.byType(Chip), findsAtLeastNWidgets(1));
      });

      testWidgets('should display category icon', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.byIcon(Icons.electrical_services), findsOneWidget);
      });
    });

    group('基础信息显示测试', () {
      testWidgets('should display basic specification info', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('基础信息'), findsOneWidget);
        expect(find.text('制造商'), findsOneWidget);
        expect(find.text('测试制造商'), findsOneWidget);
        expect(find.text('产地'), findsOneWidget);
        expect(find.text('中国'), findsOneWidget);
        expect(find.text('保修期'), findsOneWidget);
        expect(find.text('24个月'), findsOneWidget);
      });
    });

    group('技术参数显示测试', () {
      testWidgets('should display electrical specifications', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('技术参数'), findsOneWidget);
        expect(find.text('额定电压'), findsOneWidget);
        expect(find.text('12.0V'), findsOneWidget);
        expect(find.text('额定电流'), findsOneWidget);
        expect(find.text('100.0A'), findsOneWidget);
        expect(find.text('额定功率'), findsOneWidget);
        expect(find.text('1200.0W'), findsOneWidget);
        expect(find.text('容量'), findsOneWidget);
        expect(find.text('100.0Ah'), findsOneWidget);
        expect(find.text('效率'), findsOneWidget);
        expect(find.text('95.0%'), findsOneWidget);
        expect(find.text('防护等级'), findsOneWidget);
        expect(find.text('IP65'), findsOneWidget);
      });
    });

    group('物理属性显示测试', () {
      testWidgets('should display physical properties', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('物理属性'), findsOneWidget);
        expect(find.text('尺寸'), findsOneWidget);
        expect(find.text('300.0×200.0×150.0mm'), findsOneWidget);
        expect(find.text('重量'), findsOneWidget);
        expect(find.text('12.5kg'), findsOneWidget);
        expect(find.text('颜色'), findsOneWidget);
        expect(find.text('黑色'), findsOneWidget);
        expect(find.text('材质'), findsOneWidget);
        expect(find.text('磷酸铁锂'), findsOneWidget);
      });
    });

    group('性能指标显示测试', () {
      testWidgets('should display performance metrics', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('性能指标'), findsOneWidget);
        expect(find.text('使用寿命'), findsOneWidget);
        expect(find.text('5000次'), findsOneWidget);
        expect(find.text('循环次数'), findsOneWidget);
        expect(find.text('5000次'), findsAtLeastNWidgets(1));
        expect(find.text('可靠性等级'), findsOneWidget);
        expect(find.text('A级'), findsOneWidget);
        expect(find.text('性能等级'), findsOneWidget);
        expect(find.text('优秀'), findsOneWidget);
      });
    });

    group('认证信息显示测试', () {
      testWidgets('should display certification info', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.text('认证信息'), findsOneWidget);
        expect(find.text('CE认证'), findsOneWidget);
        expect(find.text('欧盟 - CE-2023-001'), findsOneWidget);
      });
    });

    group('显示模式测试', () {
      testWidgets('should hide optional fields when showAllFields is false', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(
          specification: testSpecification,
          showAllFields: false,
        ));

        // Assert
        expect(find.text('测试电池'), findsOneWidget); // 基本信息应该显示
        expect(find.text('技术参数'), findsNothing); // 技术参数应该隐藏
        expect(find.text('物理属性'), findsNothing); // 物理属性应该隐藏
        expect(find.text('性能指标'), findsNothing); // 性能指标应该隐藏
        expect(find.text('认证信息'), findsNothing); // 认证信息应该隐藏
      });

      testWidgets('should show all fields when showAllFields is true', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(
          specification: testSpecification,
          showAllFields: true,
        ));

        // Assert
        expect(find.text('基础信息'), findsOneWidget);
        expect(find.text('技术参数'), findsOneWidget);
        expect(find.text('物理属性'), findsOneWidget);
        expect(find.text('性能指标'), findsOneWidget);
        expect(find.text('认证信息'), findsOneWidget);
      });
    });

    group('不同分类测试', () {
      testWidgets('should display correct icon for PLUMBING category', (tester) async {
        // Arrange
        final plumbingSpec = testSpecification.copyWith(
          category: 'PLUMBING',
        );

        // Act
        await tester.pumpWidget(createTestWidget(specification: plumbingSpec));

        // Assert
        expect(find.byIcon(Icons.water_drop), findsOneWidget);
        expect(find.text('水路设备'), findsOneWidget);
      });

      testWidgets('should display correct icon for STORAGE category', (tester) async {
        // Arrange
        final storageSpec = testSpecification.copyWith(
          category: 'STORAGE',
        );

        // Act
        await tester.pumpWidget(createTestWidget(specification: storageSpec));

        // Assert
        expect(find.byIcon(Icons.inventory_2), findsOneWidget);
        expect(find.text('储物系统'), findsOneWidget);
      });
    });

    group('空数据处理测试', () {
      testWidgets('should handle specification without performance metrics', (tester) async {
        // Arrange
        final specWithoutMetrics = testSpecification.copyWith(
          performanceMetrics: null,
        );

        // Act
        await tester.pumpWidget(createTestWidget(specification: specWithoutMetrics));

        // Assert
        expect(find.text('基础信息'), findsOneWidget);
        expect(find.text('技术参数'), findsOneWidget);
        expect(find.text('物理属性'), findsOneWidget);
        expect(find.text('性能指标'), findsNothing); // 应该不显示
      });

      testWidgets('should handle specification without certifications', (tester) async {
        // Arrange
        final specWithoutCerts = testSpecification.copyWith(
          certifications: null,
        );

        // Act
        await tester.pumpWidget(createTestWidget(specification: specWithoutCerts));

        // Assert
        expect(find.text('基础信息'), findsOneWidget);
        expect(find.text('技术参数'), findsOneWidget);
        expect(find.text('物理属性'), findsOneWidget);
        expect(find.text('认证信息'), findsNothing); // 应该不显示
      });

      testWidgets('should handle specification with empty certifications', (tester) async {
        // Arrange
        final specWithEmptyCerts = testSpecification.copyWith(
          certifications: [],
        );

        // Act
        await tester.pumpWidget(createTestWidget(specification: specWithEmptyCerts));

        // Assert
        expect(find.text('认证信息'), findsNothing); // 应该不显示
      });
    });

    group('UI组件测试', () {
      testWidgets('should display Card widget', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.byType(Card), findsOneWidget);
      });

      testWidgets('should display section icons', (tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget(specification: testSpecification));

        // Assert
        expect(find.byIcon(Icons.info_outline), findsOneWidget); // 基础信息图标
        expect(find.byIcon(Icons.engineering), findsOneWidget); // 技术参数图标
        expect(find.byIcon(Icons.straighten), findsOneWidget); // 物理属性图标
        expect(find.byIcon(Icons.speed), findsOneWidget); // 性能指标图标
        expect(find.byIcon(Icons.verified), findsOneWidget); // 认证信息图标
      });
    });
  });
}
