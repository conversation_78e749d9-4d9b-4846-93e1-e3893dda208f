import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/services/social_share_service.dart';
import '../../domain/entities/share_link.dart';
import '../../domain/entities/share_config.dart';

/// 增强的分享对话框
class EnhancedShareDialog extends ConsumerStatefulWidget {
  final String contentId;
  final String contentType;
  final String title;
  final String description;
  final String? thumbnailUrl;
  final Function(ShareLink)? onShareCreated;
  final Function(SocialPlatform, String)? onSocialShare;

  const EnhancedShareDialog({
    super.key,
    required this.contentId,
    required this.contentType,
    required this.title,
    required this.description,
    this.thumbnailUrl,
    this.onShareCreated,
    this.onSocialShare,
  });

  @override
  ConsumerState<EnhancedShareDialog> createState() => _EnhancedShareDialogState();
}

class _EnhancedShareDialogState extends ConsumerState<EnhancedShareDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  ShareConfig _shareConfig = const ShareConfig();
  ShareLink? _generatedLink;
  bool _isGenerating = false;
  bool _showAdvancedOptions = false;

  final List<SocialPlatform> _socialPlatforms = [
    SocialPlatform.wechat,
    SocialPlatform.wechatMoments,
    SocialPlatform.qq,
    SocialPlatform.qzone,
    SocialPlatform.weibo,
    SocialPlatform.copyLink,
    SocialPlatform.system,
    SocialPlatform.more,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _generateShareLink();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(VanHubSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: VanHubSpacing.md),
            _buildContentPreview(),
            const SizedBox(height: VanHubSpacing.md),
            _buildTabBar(),
            const SizedBox(height: VanHubSpacing.md),
            Expanded(
              child: _buildTabContent(),
            ),
            const SizedBox(height: VanHubSpacing.md),
            _buildActionButtons(),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).scale(begin: const Offset(0.9, 0.9));
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.share,
          color: VanHubBrandColors.primary,
          size: 28,
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '分享内容',
                style: VanHubTypography.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '选择分享方式和权限设置',
                style: VanHubTypography.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  /// 构建内容预览
  Widget _buildContentPreview() {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          if (widget.thumbnailUrl != null) ...[
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                widget.thumbnailUrl!,
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 60,
                  height: 60,
                  color: Colors.grey.shade300,
                  child: const Icon(Icons.image),
                ),
              ),
            ),
            const SizedBox(width: VanHubSpacing.md),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: VanHubTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: VanHubSpacing.xs),
                Text(
                  widget.description,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建标签栏
  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: VanHubBrandColors.primary,
      unselectedLabelColor: Colors.grey,
      indicatorColor: VanHubBrandColors.primary,
      tabs: const [
        Tab(text: '快速分享', icon: Icon(Icons.share)),
        Tab(text: '链接设置', icon: Icon(Icons.link)),
      ],
    );
  }

  /// 构建标签内容
  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildQuickShareTab(),
        _buildLinkSettingsTab(),
      ],
    );
  }

  /// 构建快速分享标签
  Widget _buildQuickShareTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择分享平台',
            style: VanHubTypography.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: VanHubSpacing.md),
          _buildSocialPlatformGrid(),
          
          const SizedBox(height: VanHubSpacing.lg),
          
          if (_generatedLink != null) ...[
            Text(
              '分享链接',
              style: VanHubTypography.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: VanHubSpacing.sm),
            _buildShareLinkCard(),
          ],
        ],
      ),
    );
  }

  /// 构建链接设置标签
  Widget _buildLinkSettingsTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPermissionSettings(),
          const SizedBox(height: VanHubSpacing.lg),
          _buildAdvancedSettings(),
          const SizedBox(height: VanHubSpacing.lg),
          if (_generatedLink != null) _buildLinkPreview(),
        ],
      ),
    );
  }

  /// 构建社交平台网格
  Widget _buildSocialPlatformGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: VanHubSpacing.md,
        mainAxisSpacing: VanHubSpacing.md,
        childAspectRatio: 1.0,
      ),
      itemCount: _socialPlatforms.length,
      itemBuilder: (context, index) {
        final platform = _socialPlatforms[index];
        return _buildSocialPlatformItem(platform);
      },
    );
  }

  /// 构建社交平台项
  Widget _buildSocialPlatformItem(SocialPlatform platform) {
    return InkWell(
      onTap: () => _handleSocialShare(platform),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Color(platform.brandColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getPlatformIcon(platform),
                color: Color(platform.brandColor),
                size: 20,
              ),
            ),
            const SizedBox(height: VanHubSpacing.xs),
            Text(
              platform.displayName,
              style: VanHubTypography.bodySmall.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    ).animate(delay: (100 * _socialPlatforms.indexOf(platform)).ms)
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.2, end: 0);
  }

  /// 构建分享链接卡片
  Widget _buildShareLinkCard() {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: VanHubBrandColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: VanHubBrandColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.link,
                color: VanHubBrandColors.primary,
                size: 20,
              ),
              const SizedBox(width: VanHubSpacing.xs),
              Text(
                '分享链接',
                style: VanHubTypography.titleSmall.copyWith(
                  color: VanHubBrandColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                _generatedLink!.permissionLevelText,
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Container(
            padding: const EdgeInsets.all(VanHubSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _generatedLink!.shareUrl,
                    style: VanHubTypography.bodySmall.copyWith(
                      fontFamily: 'monospace',
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: VanHubSpacing.sm),
                InkWell(
                  onTap: _copyLinkToClipboard,
                  child: Container(
                    padding: const EdgeInsets.all(VanHubSpacing.xs),
                    decoration: BoxDecoration(
                      color: VanHubBrandColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.copy,
                      color: VanHubBrandColors.primary,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Row(
            children: [
              Icon(
                Icons.visibility,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: VanHubSpacing.xs),
              Text(
                '${_generatedLink!.viewCount} 次查看',
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: VanHubSpacing.md),
              Icon(
                Icons.share,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: VanHubSpacing.xs),
              Text(
                '${_generatedLink!.shareCount} 次分享',
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Text(
                _generatedLink!.remainingTimeText,
                style: VanHubTypography.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建权限设置
  Widget _buildPermissionSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '访问权限',
          style: VanHubTypography.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: VanHubSpacing.sm),
        
        RadioListTile<SharePermissionLevel>(
          title: const Text('公开'),
          subtitle: const Text('任何人都可以访问和搜索到'),
          value: SharePermissionLevel.public,
          groupValue: _shareConfig.permissionLevel,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(permissionLevel: value);
              });
              _regenerateShareLink();
            }
          },
        ),
        
        RadioListTile<SharePermissionLevel>(
          title: const Text('不公开'),
          subtitle: const Text('只有拥有链接的人才能访问'),
          value: SharePermissionLevel.unlisted,
          groupValue: _shareConfig.permissionLevel,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(permissionLevel: value);
              });
              _regenerateShareLink();
            }
          },
        ),
        
        RadioListTile<SharePermissionLevel>(
          title: const Text('私有'),
          subtitle: const Text('需要权限验证才能访问'),
          value: SharePermissionLevel.private,
          groupValue: _shareConfig.permissionLevel,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(permissionLevel: value);
              });
              _regenerateShareLink();
            }
          },
        ),
      ],
    );
  }

  /// 构建高级设置
  Widget _buildAdvancedSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '高级设置',
              style: VanHubTypography.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () {
                setState(() {
                  _showAdvancedOptions = !_showAdvancedOptions;
                });
              },
              child: Text(_showAdvancedOptions ? '收起' : '展开'),
            ),
          ],
        ),
        
        if (_showAdvancedOptions) ...[
          const SizedBox(height: VanHubSpacing.sm),
          
          SwitchListTile(
            title: const Text('允许评论'),
            subtitle: const Text('访问者可以对分享内容进行评论'),
            value: _shareConfig.allowComments,
            onChanged: (value) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(allowComments: value);
              });
            },
          ),
          
          SwitchListTile(
            title: const Text('允许点赞'),
            subtitle: const Text('访问者可以对分享内容进行点赞'),
            value: _shareConfig.allowLikes,
            onChanged: (value) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(allowLikes: value);
              });
            },
          ),
          
          SwitchListTile(
            title: const Text('统计访问量'),
            subtitle: const Text('记录分享链接的访问统计信息'),
            value: _shareConfig.trackViews,
            onChanged: (value) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(trackViews: value);
              });
            },
          ),
          
          SwitchListTile(
            title: const Text('访问通知'),
            subtitle: const Text('有人访问时发送通知'),
            value: _shareConfig.notifyOnAccess,
            onChanged: (value) {
              setState(() {
                _shareConfig = _shareConfig.copyWith(notifyOnAccess: value);
              });
            },
          ),
        ],
      ],
    );
  }

  /// 构建链接预览
  Widget _buildLinkPreview() {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '链接预览',
            style: VanHubTypography.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Text(
            '权限级别: ${_generatedLink!.permissionLevelText}',
            style: VanHubTypography.bodySmall,
          ),
          Text(
            '功能设置: ${_shareConfig.featureDescriptions.join(', ')}',
            style: VanHubTypography.bodySmall,
          ),
          Text(
            '有效期: ${_shareConfig.expirationDescription}',
            style: VanHubTypography.bodySmall,
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: ElevatedButton(
            onPressed: _isGenerating ? null : _handleConfirmShare,
            style: ElevatedButton.styleFrom(
              backgroundColor: VanHubBrandColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isGenerating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('确认分享'),
          ),
        ),
      ],
    );
  }

  /// 生成分享链接
  Future<void> _generateShareLink() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      // TODO: 调用分享服务生成链接
      await Future.delayed(const Duration(seconds: 1)); // 模拟网络请求
      
      final shareLink = ShareLink(
        id: 'share_${DateTime.now().millisecondsSinceEpoch}',
        contentId: widget.contentId,
        contentType: ShareContentType.project, // TODO: 根据实际类型设置
        userId: 'current_user_id', // TODO: 获取当前用户ID
        shareUrl: 'https://vanhub.app/share/abc123',
        permissionLevel: _shareConfig.permissionLevel,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        title: widget.title,
        description: widget.description,
        thumbnailUrl: widget.thumbnailUrl,
      );

      setState(() {
        _generatedLink = shareLink;
        _isGenerating = false;
      });

      widget.onShareCreated?.call(shareLink);
    } catch (e) {
      setState(() {
        _isGenerating = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('生成分享链接失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 重新生成分享链接
  Future<void> _regenerateShareLink() async {
    if (_generatedLink != null) {
      await _generateShareLink();
    }
  }

  /// 处理社交分享
  void _handleSocialShare(SocialPlatform platform) {
    if (_generatedLink == null) return;

    final shareText = platform.getShareTextTemplate(
      widget.title,
      widget.description,
      _generatedLink!.shareUrl,
    );

    widget.onSocialShare?.call(platform, shareText);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已分享到${platform.displayName}'),
        backgroundColor: Color(platform.brandColor),
      ),
    );
  }

  /// 复制链接到剪贴板
  void _copyLinkToClipboard() {
    if (_generatedLink == null) return;

    // TODO: 实现复制到剪贴板
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('链接已复制到剪贴板'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// 处理确认分享
  void _handleConfirmShare() {
    Navigator.of(context).pop(_generatedLink);
  }

  /// 获取平台图标
  IconData _getPlatformIcon(SocialPlatform platform) {
    switch (platform) {
      case SocialPlatform.wechat:
      case SocialPlatform.wechatMoments:
        return Icons.chat;
      case SocialPlatform.qq:
      case SocialPlatform.qzone:
        return Icons.forum;
      case SocialPlatform.weibo:
        return Icons.public;
      case SocialPlatform.douyin:
        return Icons.video_library;
      case SocialPlatform.xiaohongshu:
        return Icons.book;
      case SocialPlatform.system:
        return Icons.share;
      case SocialPlatform.copyLink:
        return Icons.copy;
      case SocialPlatform.more:
        return Icons.more_horiz;
    }
  }
}
