import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/components/vanhub_chart.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../providers/chart_data_provider.dart';

/// 图表交互类型
enum ChartInteractionType {
  tap,
  longPress,
  hover,
  zoom,
  pan,
}

/// 图表交互事件
class ChartInteractionEvent {
  final ChartInteractionType type;
  final AnalyticsChartDataPoint? dataPoint;
  final Offset? position;
  final double? scale;

  const ChartInteractionEvent({
    required this.type,
    this.dataPoint,
    this.position,
    this.scale,
  });
}

/// 交互式图表组件
class InteractiveChartWidget extends ConsumerStatefulWidget {
  final String projectId;
  final String title;
  final RealTimeChartType chartType;
  final double? height;
  final bool enableZoom;
  final bool enablePan;
  final bool enableTooltip;
  final bool enableLegend;
  final Function(ChartInteractionEvent)? onInteraction;

  const InteractiveChartWidget({
    super.key,
    required this.projectId,
    required this.title,
    required this.chartType,
    this.height = 300,
    this.enableZoom = true,
    this.enablePan = true,
    this.enableTooltip = true,
    this.enableLegend = true,
    this.onInteraction,
  });

  @override
  ConsumerState<InteractiveChartWidget> createState() => _InteractiveChartWidgetState();
}

class _InteractiveChartWidgetState extends ConsumerState<InteractiveChartWidget>
    with TickerProviderStateMixin {
  late AnimationController _tooltipController;
  late AnimationController _zoomController;
  
  AnalyticsChartDataPoint? _hoveredPoint;
  Offset? _tooltipPosition;
  double _currentZoom = 1.0;
  Offset _panOffset = Offset.zero;
  bool _showTooltip = false;
  String _selectedFilter = '全部';

  final List<String> _filterOptions = ['全部', '本月', '本季度', '本年'];

  @override
  void initState() {
    super.initState();
    _tooltipController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _zoomController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tooltipController.dispose();
    _zoomController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildFilterBar(),
          SizedBox(
            height: widget.height,
            child: Stack(
              children: [
                _buildInteractiveChart(),
                if (_showTooltip && _tooltipPosition != null)
                  _buildTooltip(),
              ],
            ),
          ),
          if (widget.enableLegend) _buildLegend(),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1, end: 0);
  }

  /// 构建头部
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            _getChartIcon(),
            color: VanHubBrandColors.primary,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.enableZoom)
          IconButton(
            icon: const Icon(Icons.zoom_in),
            onPressed: _zoomIn,
            tooltip: '放大',
          ),
        if (widget.enableZoom)
          IconButton(
            icon: const Icon(Icons.zoom_out),
            onPressed: _zoomOut,
            tooltip: '缩小',
          ),
        IconButton(
          icon: const Icon(Icons.fullscreen),
          onPressed: _showFullscreen,
          tooltip: '全屏查看',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('导出数据'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('分享图表'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('图表设置'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建筛选栏
  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          const Text(
            '时间范围:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _filterOptions.map((filter) {
                  final isSelected = filter == _selectedFilter;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(filter),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedFilter = filter;
                        });
                        _applyFilter(filter);
                      },
                      selectedColor: VanHubBrandColors.primary.withValues(alpha: 0.2),
                      checkmarkColor: VanHubBrandColors.primary,
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建交互式图表
  Widget _buildInteractiveChart() {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onLongPressStart: _handleLongPressStart,
      onPanUpdate: widget.enablePan ? _handlePanUpdate : null,
      onScaleUpdate: widget.enableZoom ? _handleScaleUpdate : null,
      child: MouseRegion(
        onHover: _handleHover,
        onExit: _handleHoverExit,
        child: Transform.scale(
          scale: _currentZoom,
          child: Transform.translate(
            offset: _panOffset,
            child: _buildChart(),
          ),
        ),
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    switch (widget.chartType) {
      case RealTimeChartType.costTrend:
        return _buildCostTrendChart();
      case RealTimeChartType.progressStats:
        return _buildProgressChart();
      case RealTimeChartType.materialStats:
        return _buildMaterialChart();
    }
  }

  /// 构建成本趋势图表
  Widget _buildCostTrendChart() {
    final costTrendAsync = ref.watch(realTimeCostTrendProvider(widget.projectId));
    
    return costTrendAsync.when(
      data: (series) => VanHubChart.line(
        series: series.map((s) => ChartDataSeries(
          name: s.name,
          color: Color(int.parse(s.color.substring(1), radix: 16) + 0xFF000000),
          data: s.data.map((point) => ChartDataPoint(
            x: point.x,
            y: point.y,
          )).toList(),
        )).toList(),
        enableAnimation: true,
        onPointTap: _handlePointTap,
        onPointHover: _handlePointHover,
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('加载失败: $error')),
    );
  }

  /// 构建进度图表
  Widget _buildProgressChart() {
    final progressAsync = ref.watch(realTimeProgressStatsProvider(widget.projectId));
    
    return progressAsync.when(
      data: (stats) {
        final taskBreakdown = stats['task_breakdown'] as Map<String, dynamic>;
        return VanHubChart.pie(
          series: [
            ChartDataSeries(
              name: '任务分布',
              color: VanHubBrandColors.primary,
              data: [
                ChartDataPoint(x: 0, y: taskBreakdown['completed'].toDouble()),
                ChartDataPoint(x: 1, y: taskBreakdown['in_progress'].toDouble()),
                ChartDataPoint(x: 2, y: taskBreakdown['pending'].toDouble()),
              ],
            ),
          ],
          enableAnimation: true,
          // onPointTap: _handlePointTap, // VanHubChart.pie不支持此参数
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('加载失败: $error')),
    );
  }

  /// 构建材料图表
  Widget _buildMaterialChart() {
    final materialStatsAsync = ref.watch(realTimeMaterialStatsProvider(widget.projectId));
    
    return materialStatsAsync.when(
      data: (series) => VanHubChart.bar(
        series: series.map((s) => ChartDataSeries(
          name: s.name,
          color: Color(int.parse(s.color.substring(1), radix: 16) + 0xFF000000),
          data: s.data.map((point) => ChartDataPoint(
            x: point.x,
            y: point.y,
          )).toList(),
        )).toList(),
        enableAnimation: true,
        // onPointTap: _handlePointTap, // VanHubChart.bar不支持此参数
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(child: Text('加载失败: $error')),
    );
  }

  /// 构建工具提示
  Widget _buildTooltip() {
    if (_hoveredPoint == null || _tooltipPosition == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: _tooltipPosition!.dx,
      top: _tooltipPosition!.dy,
      child: FadeTransition(
        opacity: _tooltipController,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black87,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _hoveredPoint!.label ?? '数据点',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '值: ${_hoveredPoint!.y.toStringAsFixed(2)}',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建图例
  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Row(
        children: [
          // TODO: 实现图例
          Text('图例区域'),
        ],
      ),
    );
  }

  // 交互处理方法
  void _handleTapDown(TapDownDetails details) {
    _notifyInteraction(ChartInteractionEvent(
      type: ChartInteractionType.tap,
      position: details.localPosition,
    ));
  }

  void _handleLongPressStart(LongPressStartDetails details) {
    _notifyInteraction(ChartInteractionEvent(
      type: ChartInteractionType.longPress,
      position: details.localPosition,
    ));
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      _panOffset += details.delta;
    });
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      _currentZoom = (_currentZoom * details.scale).clamp(0.5, 3.0);
    });
  }

  void _handleHover(PointerHoverEvent event) {
    // TODO: 实现悬停检测
  }

  void _handleHoverExit(PointerExitEvent event) {
    if (_showTooltip) {
      setState(() {
        _showTooltip = false;
      });
      _tooltipController.reverse();
    }
  }

  void _handlePointTap(ChartDataPoint point) {
    // 转换为AnalyticsChartDataPoint
    final analyticsPoint = AnalyticsChartDataPoint(
      x: point.x,
      y: point.y,
      label: point.label,
      timestamp: DateTime.now(),
    );

    _notifyInteraction(ChartInteractionEvent(
      type: ChartInteractionType.tap,
      dataPoint: analyticsPoint,
    ));
  }

  void _handlePointHover(ChartDataPoint point) {
    // 转换为AnalyticsChartDataPoint
    final analyticsPoint = AnalyticsChartDataPoint(
      x: point.x,
      y: point.y,
      label: point.label,
      timestamp: DateTime.now(),
    );

    setState(() {
      _hoveredPoint = analyticsPoint;
      _showTooltip = true;
    });
    _tooltipController.forward();
  }

  void _zoomIn() {
    setState(() {
      _currentZoom = (_currentZoom * 1.2).clamp(0.5, 3.0);
    });
    _zoomController.forward().then((_) => _zoomController.reset());
  }

  void _zoomOut() {
    setState(() {
      _currentZoom = (_currentZoom / 1.2).clamp(0.5, 3.0);
    });
    _zoomController.forward().then((_) => _zoomController.reset());
  }

  void _showFullscreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: Text(widget.title)),
          body: InteractiveChartWidget(
            projectId: widget.projectId,
            title: widget.title,
            chartType: widget.chartType,
            height: MediaQuery.of(context).size.height - 100,
          ),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportData();
        break;
      case 'share':
        _shareChart();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _applyFilter(String filter) {
    // TODO: 实现筛选逻辑
  }

  void _exportData() {
    // TODO: 实现数据导出
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('数据导出功能开发中...')),
    );
  }

  void _shareChart() {
    // TODO: 实现图表分享
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('图表分享功能开发中...')),
    );
  }

  void _showSettings() {
    // TODO: 实现设置对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('图表设置功能开发中...')),
    );
  }

  void _notifyInteraction(ChartInteractionEvent event) {
    widget.onInteraction?.call(event);
  }

  IconData _getChartIcon() {
    switch (widget.chartType) {
      case RealTimeChartType.costTrend:
        return Icons.trending_up;
      case RealTimeChartType.progressStats:
        return Icons.pie_chart;
      case RealTimeChartType.materialStats:
        return Icons.bar_chart;
    }
  }
}
