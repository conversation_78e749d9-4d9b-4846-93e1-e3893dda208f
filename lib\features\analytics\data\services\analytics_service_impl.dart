import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/analytics_service.dart';
import '../../domain/entities/analytics_data.dart';
import '../../domain/entities/cost_trend.dart';
import '../../domain/entities/progress_stats.dart';
import '../../../project/domain/repositories/project_repository.dart';
import '../../../material/domain/repositories/material_repository.dart';

/// 数据分析服务实现
class AnalyticsServiceImpl implements AnalyticsService {
  final ProjectRepository projectRepository;
  final MaterialRepository materialRepository;

  const AnalyticsServiceImpl({
    required this.projectRepository,
    required this.materialRepository,
  });

  @override
  Future<Either<Failure, AnalyticsData>> getProjectAnalytics(String projectId) async {
    try {
      // 获取项目基础信息
      final projectResult = await projectRepository.getProject(projectId);
      
      return await projectResult.fold(
        (failure) async => Left(failure),
        (project) async {
          // 获取项目材料
          final materialsResult = await materialRepository.getProjectMaterials(projectId);
          
          return await materialsResult.fold(
            (failure) async => Left(failure),
            (materials) async {
              // 计算统计数据
              final totalBudget = project.budget ?? 0.0;
              final actualCost = materials.fold<double>(0, (sum, material) => sum + material.price);
              final remainingBudget = totalBudget - actualCost;
              
              // 分类成本统计
              final categoryCosts = <String, double>{};
              for (final material in materials) {
                categoryCosts[material.category] = 
                    (categoryCosts[material.category] ?? 0) + material.price;
              }
              
              // 材料数量统计
              final materialCounts = <String, int>{};
              for (final material in materials) {
                materialCounts[material.category] = 
                    (materialCounts[material.category] ?? 0) + 1;
              }
              
              // 生成月度趋势数据（模拟）
              final monthlyTrends = _generateMonthlyTrends(actualCost, materials.length);
              
              // 模拟任务数据
              final totalTasks = 20;
              final completedTasks = (totalTasks * 0.6).round();
              final completionPercentage = (completedTasks / totalTasks) * 100;
              
              final analyticsData = AnalyticsData(
                id: 'analytics_$projectId',
                projectId: projectId,
                totalBudget: totalBudget,
                actualCost: actualCost,
                remainingBudget: remainingBudget,
                totalMaterials: materials.length,
                completedTasks: completedTasks,
                totalTasks: totalTasks,
                completionPercentage: completionPercentage,
                lastUpdated: DateTime.now(),
                categoryCosts: categoryCosts,
                materialCounts: materialCounts,
                monthlyTrends: monthlyTrends,
              );
              
              return Right(analyticsData);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取项目分析数据失败: $e'));
    }
  }

  @override
  Future<Either<Failure, AnalyticsData>> getUserAnalytics(String userId) async {
    try {
      // 获取用户所有项目
      final projectsResult = await projectRepository.getUserProjects(userId);
      
      return await projectsResult.fold(
        (failure) async => Left(failure),
        (projects) async {
          // 获取用户所有材料
          final materialsResult = await materialRepository.getUserMaterials(userId);
          
          return await materialsResult.fold(
            (failure) async => Left(failure),
            (materials) async {
              // 计算总体统计
              final totalBudget = projects.fold<double>(0, (sum, project) => sum + (project.budget ?? 0));
              final actualCost = materials.fold<double>(0, (sum, material) => sum + material.price);
              final remainingBudget = totalBudget - actualCost;
              
              // 分类成本统计
              final categoryCosts = <String, double>{};
              for (final material in materials) {
                categoryCosts[material.category] = 
                    (categoryCosts[material.category] ?? 0) + material.price;
              }
              
              // 材料数量统计
              final materialCounts = <String, int>{};
              for (final material in materials) {
                materialCounts[material.category] = 
                    (materialCounts[material.category] ?? 0) + 1;
              }
              
              // 生成月度趋势数据
              final monthlyTrends = _generateMonthlyTrends(actualCost, materials.length);
              
              // 模拟任务数据
              final totalTasks = projects.length * 15;
              final completedTasks = (totalTasks * 0.7).round();
              final completionPercentage = (completedTasks / totalTasks) * 100;
              
              final analyticsData = AnalyticsData(
                id: 'user_analytics_$userId',
                projectId: 'user_$userId',
                totalBudget: totalBudget,
                actualCost: actualCost,
                remainingBudget: remainingBudget,
                totalMaterials: materials.length,
                completedTasks: completedTasks,
                totalTasks: totalTasks,
                completionPercentage: completionPercentage,
                lastUpdated: DateTime.now(),
                categoryCosts: categoryCosts,
                materialCounts: materialCounts,
                monthlyTrends: monthlyTrends,
              );
              
              return Right(analyticsData);
            },
          );
        },
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户分析数据失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<CostTrend>>> getCostTrends(
    String projectId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // 获取项目材料
      final materialsResult = await materialRepository.getProjectMaterials(projectId);
      
      return materialsResult.fold(
        (failure) => Left(failure),
        (materials) {
          // 生成成本趋势数据
          final trends = <CostTrend>[];
          final now = DateTime.now();
          final start = startDate ?? now.subtract(const Duration(days: 180));
          final end = endDate ?? now;
          
          // 按月生成趋势数据
          for (int i = 0; i < 6; i++) {
            final date = DateTime(start.year, start.month + i, 1);
            if (date.isAfter(end)) break;
            
            final monthlyBudget = 5000.0 + (i * 1000);
            final monthlyActual = monthlyBudget * (0.8 + (i * 0.05));
            
            trends.add(CostTrend(
              date: date,
              budgetAmount: monthlyBudget,
              actualAmount: monthlyActual,
              category: '总体',
              description: '${date.year}年${date.month}月成本',
            ));
          }
          
          return Right(trends);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '获取成本趋势失败: $e'));
    }
  }

  @override
  Future<Either<Failure, ProgressStats>> getProgressStats(String projectId) async {
    try {
      // 模拟进度统计数据
      final taskProgress = [
        TaskProgress(
          taskId: '1',
          taskName: '电路系统安装',
          status: 'completed',
          progress: 100.0,
          startDate: DateTime.now().subtract(const Duration(days: 30)),
          endDate: DateTime.now().subtract(const Duration(days: 20)),
          category: '电气系统',
        ),
        TaskProgress(
          taskId: '2',
          taskName: '水路系统安装',
          status: 'in_progress',
          progress: 75.0,
          startDate: DateTime.now().subtract(const Duration(days: 15)),
          category: '水路系统',
        ),
        TaskProgress(
          taskId: '3',
          taskName: '储物系统安装',
          status: 'pending',
          progress: 0.0,
          startDate: DateTime.now().add(const Duration(days: 5)),
          category: '储物系统',
        ),
      ];
      
      final stats = ProgressStats(
        projectId: projectId,
        totalTasks: taskProgress.length,
        completedTasks: taskProgress.where((t) => t.status == 'completed').length,
        inProgressTasks: taskProgress.where((t) => t.status == 'in_progress').length,
        pendingTasks: taskProgress.where((t) => t.status == 'pending').length,
        overallProgress: taskProgress.fold<double>(0, (sum, t) => sum + t.progress) / taskProgress.length,
        lastUpdated: DateTime.now(),
        taskProgress: taskProgress,
      );
      
      return Right(stats);
    } catch (e) {
      return Left(UnknownFailure(message: '获取进度统计失败: $e'));
    }
  }

  /// 生成月度趋势数据
  List<MonthlyData> _generateMonthlyTrends(double totalCost, int materialCount) {
    final trends = <MonthlyData>[];
    final now = DateTime.now();
    
    for (int i = 5; i >= 0; i--) {
      final date = DateTime(now.year, now.month - i, 1);
      final monthlyExpense = totalCost / 6 * (1 + (i * 0.1));
      final monthlyTasks = (materialCount / 6).round() + i;
      final monthlyMaterials = (materialCount / 6).round();
      
      trends.add(MonthlyData(
        month: date.month,
        year: date.year,
        expenses: monthlyExpense,
        completedTasks: monthlyTasks,
        newMaterials: monthlyMaterials,
      ));
    }
    
    return trends;
  }

  // 其他方法的简化实现
  @override
  Future<Either<Failure, Map<String, double>>> getCategoryCostDistribution(String projectId) async {
    final analyticsResult = await getProjectAnalytics(projectId);
    return analyticsResult.fold(
      (failure) => Left(failure),
      (analytics) => Right(analytics.categoryCosts),
    );
  }

  @override
  Future<Either<Failure, Map<String, double>>> getMonthlyExpenses(String projectId, {int months = 12}) async {
    final analyticsResult = await getProjectAnalytics(projectId);
    return analyticsResult.fold(
      (failure) => Left(failure),
      (analytics) {
        final expenses = <String, double>{};
        for (final trend in analytics.monthlyTrends) {
          expenses['${trend.year}-${trend.month.toString().padLeft(2, '0')}'] = trend.expenses;
        }
        return Right(expenses);
      },
    );
  }

  @override
  Future<Either<Failure, Map<String, int>>> getMaterialUsageStats(String projectId) async {
    final analyticsResult = await getProjectAnalytics(projectId);
    return analyticsResult.fold(
      (failure) => Left(failure),
      (analytics) => Right(analytics.materialCounts),
    );
  }

  @override
  Future<Either<Failure, Map<String, double>>> getBudgetVsActual(String projectId) async {
    final analyticsResult = await getProjectAnalytics(projectId);
    return analyticsResult.fold(
      (failure) => Left(failure),
      (analytics) => Right({
        'budget': analytics.totalBudget,
        'actual': analytics.actualCost,
        'remaining': analytics.remainingBudget,
      }),
    );
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>> getCompletionTrends(String projectId) async {
    try {
      final trends = <Map<String, dynamic>>[];
      final now = DateTime.now();
      
      for (int i = 5; i >= 0; i--) {
        final date = DateTime(now.year, now.month - i, 1);
        final completion = (i + 1) * 15.0; // 递增的完成度
        
        trends.add({
          'date': date.toIso8601String(),
          'completion': completion,
          'tasks_completed': (i + 1) * 3,
        });
      }
      
      return Right(trends);
    } catch (e) {
      return Left(UnknownFailure(message: '获取完成度趋势失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>> getPopularMaterialsAnalysis({
    String? category,
    int limit = 10,
  }) async {
    try {
      // 模拟热门材料分析数据
      final popularMaterials = [
        {'name': '锂电池', 'usage_count': 45, 'category': '电气设备', 'avg_price': 2500.0},
        {'name': '太阳能板', 'usage_count': 38, 'category': '电气设备', 'avg_price': 1800.0},
        {'name': '水泵', 'usage_count': 32, 'category': '水路系统', 'avg_price': 800.0},
        {'name': '逆变器', 'usage_count': 28, 'category': '电气设备', 'avg_price': 1200.0},
        {'name': '储水箱', 'usage_count': 25, 'category': '水路系统', 'avg_price': 600.0},
      ];
      
      var filteredMaterials = popularMaterials;
      if (category != null && category.isNotEmpty && category != '全部') {
        filteredMaterials = popularMaterials
            .where((material) => material['category'] == category)
            .toList();
      }
      
      return Right(filteredMaterials.take(limit).toList());
    } catch (e) {
      return Left(UnknownFailure(message: '获取热门材料分析失败: $e'));
    }
  }
}
