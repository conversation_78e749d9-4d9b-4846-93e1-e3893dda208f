import 'package:freezed_annotation/freezed_annotation.dart';

part 'share_access_log.freezed.dart';
part 'share_access_log.g.dart';

/// 访问结果类型
enum AccessResult {
  success,
  denied,
  expired,
  limitReached,
  blocked,
  error,
}

/// 分享访问日志实体
@freezed
class ShareAccessLog with _$ShareAccessLog {
  const factory ShareAccessLog({
    required String id,
    required String shareId,
    String? userId,
    required String ipAddress,
    required DateTime accessedAt,
    required AccessResult result,
    String? userAgent,
    String? referrer,
    String? country,
    String? city,
    String? device,
    String? platform,
    String? browser,
    String? failureReason,
    @Default({}) Map<String, dynamic> metadata,
  }) = _ShareAccessLog;

  factory ShareAccessLog.fromJson(Map<String, dynamic> json) => 
      _$ShareAccessLogFromJson(json);
}

/// 分享访问日志扩展方法
extension ShareAccessLogX on ShareAccessLog {
  /// 是否成功访问
  bool get isSuccessful => result == AccessResult.success;

  /// 获取访问结果文本
  String get resultText {
    switch (result) {
      case AccessResult.success:
        return '成功';
      case AccessResult.denied:
        return '拒绝访问';
      case AccessResult.expired:
        return '已过期';
      case AccessResult.limitReached:
        return '达到访问上限';
      case AccessResult.blocked:
        return '被阻止';
      case AccessResult.error:
        return '错误';
    }
  }

  /// 获取访问结果颜色
  int get resultColor {
    switch (result) {
      case AccessResult.success:
        return 0xFF4CAF50; // 绿色
      case AccessResult.denied:
        return 0xFFF44336; // 红色
      case AccessResult.expired:
        return 0xFFFF9800; // 橙色
      case AccessResult.limitReached:
        return 0xFFFF5722; // 深橙色
      case AccessResult.blocked:
        return 0xFF9C27B0; // 紫色
      case AccessResult.error:
        return 0xFF607D8B; // 蓝灰色
    }
  }

  /// 获取用户显示名称
  String get userDisplayName {
    if (userId != null) {
      return userId!;
    } else {
      return '匿名用户';
    }
  }

  /// 获取设备信息
  String get deviceInfo {
    final parts = <String>[];
    
    if (device != null) parts.add(device!);
    if (platform != null) parts.add(platform!);
    if (browser != null) parts.add(browser!);
    
    return parts.isEmpty ? '未知设备' : parts.join(' / ');
  }

  /// 获取地理位置信息
  String get locationInfo {
    final parts = <String>[];
    
    if (city != null) parts.add(city!);
    if (country != null) parts.add(country!);
    
    return parts.isEmpty ? '未知位置' : parts.join(', ');
  }

  /// 获取访问时间格式化文本
  String get accessTimeText {
    final now = DateTime.now();
    final difference = now.difference(accessedAt);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${accessedAt.year}-${accessedAt.month.toString().padLeft(2, '0')}-${accessedAt.day.toString().padLeft(2, '0')}';
    }
  }

  /// 是否为可疑访问
  bool get isSuspicious {
    // 检查是否为失败访问
    if (!isSuccessful) return true;
    
    // 检查User-Agent是否异常
    if (userAgent != null) {
      final ua = userAgent!.toLowerCase();
      if (ua.contains('bot') || ua.contains('crawler') || ua.contains('spider')) {
        return true;
      }
    }
    
    // 检查是否为高频访问
    // 这里可以添加更多的可疑行为检测逻辑
    
    return false;
  }

  /// 获取风险等级
  String get riskLevel {
    if (!isSuccessful) {
      switch (result) {
        case AccessResult.blocked:
          return '高风险';
        case AccessResult.denied:
          return '中风险';
        default:
          return '低风险';
      }
    }
    
    if (isSuspicious) {
      return '中风险';
    }
    
    return '正常';
  }

  /// 获取风险等级颜色
  int get riskLevelColor {
    switch (riskLevel) {
      case '高风险':
        return 0xFFF44336; // 红色
      case '中风险':
        return 0xFFFF9800; // 橙色
      case '低风险':
        return 0xFFFFEB3B; // 黄色
      default:
        return 0xFF4CAF50; // 绿色
    }
  }

  /// 创建成功访问日志
  factory ShareAccessLog.success({
    required String shareId,
    String? userId,
    required String ipAddress,
    String? userAgent,
    String? referrer,
    Map<String, dynamic>? metadata,
  }) {
    return ShareAccessLog(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      shareId: shareId,
      userId: userId,
      ipAddress: ipAddress,
      accessedAt: DateTime.now(),
      result: AccessResult.success,
      userAgent: userAgent,
      referrer: referrer,
      metadata: metadata ?? {},
    );
  }

  /// 创建失败访问日志
  factory ShareAccessLog.failure({
    required String shareId,
    String? userId,
    required String ipAddress,
    required AccessResult result,
    String? failureReason,
    String? userAgent,
    String? referrer,
    Map<String, dynamic>? metadata,
  }) {
    return ShareAccessLog(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      shareId: shareId,
      userId: userId,
      ipAddress: ipAddress,
      accessedAt: DateTime.now(),
      result: result,
      failureReason: failureReason,
      userAgent: userAgent,
      referrer: referrer,
      metadata: metadata ?? {},
    );
  }

  /// 解析User-Agent
  Map<String, String?> parseUserAgent() {
    if (userAgent == null) {
      return {
        'device': null,
        'platform': null,
        'browser': null,
      };
    }

    final ua = userAgent!;
    String? device;
    String? platform;
    String? browser;

    // 检测平台
    if (ua.contains('Windows')) {
      platform = 'Windows';
    } else if (ua.contains('Mac OS')) {
      platform = 'macOS';
    } else if (ua.contains('Linux')) {
      platform = 'Linux';
    } else if (ua.contains('Android')) {
      platform = 'Android';
    } else if (ua.contains('iOS') || ua.contains('iPhone') || ua.contains('iPad')) {
      platform = 'iOS';
    }

    // 检测设备
    if (ua.contains('Mobile')) {
      device = '手机';
    } else if (ua.contains('Tablet') || ua.contains('iPad')) {
      device = '平板';
    } else {
      device = '桌面';
    }

    // 检测浏览器
    if (ua.contains('Chrome')) {
      browser = 'Chrome';
    } else if (ua.contains('Firefox')) {
      browser = 'Firefox';
    } else if (ua.contains('Safari')) {
      browser = 'Safari';
    } else if (ua.contains('Edge')) {
      browser = 'Edge';
    }

    return {
      'device': device,
      'platform': platform,
      'browser': browser,
    };
  }

  /// 更新设备信息
  ShareAccessLog updateDeviceInfo() {
    final deviceInfo = parseUserAgent();
    return copyWith(
      device: deviceInfo['device'],
      platform: deviceInfo['platform'],
      browser: deviceInfo['browser'],
    );
  }
}
