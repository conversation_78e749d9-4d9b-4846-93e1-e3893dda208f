import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/error/ui_failure.dart';
import '../../domain/entities/bom_tree_state.dart';
import '../../domain/entities/tree_node.dart';
import '../../domain/entities/bom_item.dart';
import 'bom_tree_node_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_display_widget.dart';
import '../providers/bom_tree_provider.dart';

/// BOM树形结构Widget
/// 严格遵循Clean Architecture原则，展示BOM的树形结构
class BomTreeWidget extends ConsumerWidget {
  final String projectId;
  final VoidCallback? onRefresh;
  final Function(String)? onNodeSelected;
  final Function(String)? onBomItemEdit;
  final Function(String)? onBomItemDelete;

  const BomTreeWidget({
    super.key,
    required this.projectId,
    this.onRefresh,
    this.onNodeSelected,
    this.onBomItemEdit,
    this.onBomItemDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final bomTreeState = ref.watch(bomTreeProvider);

    // 初始化BOM树
    ref.listen<BomTreeState>(bomTreeProvider, (previous, next) {
      if (previous?.tree.isEmpty == true && next.tree.isEmpty && !next.isLoading) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ref.read(bomTreeProvider.notifier).initializeBomTree(projectId);
        });
      }
    });

    return Column(
      children: [
        // 工具栏
        _buildToolbar(context, ref, theme, bomTreeState),
        
        // 统计信息
        if (bomTreeState.showStatistics)
          _buildStatisticsPanel(context, theme, bomTreeState),
        
        // 树形结构内容
        Expanded(
          child: _buildTreeContent(context, ref, theme, bomTreeState),
        ),
      ],
    );
  }

  /// 构建工具栏
  Widget _buildToolbar(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    BomTreeState bomTreeState,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          // 标题
          Icon(
            Icons.account_tree,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'BOM树形结构',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const Spacer(),
          
          // 统计信息切换
          IconButton(
            onPressed: () {
              ref.read(bomTreeProvider.notifier).toggleStatistics();
            },
            icon: Icon(
              bomTreeState.showStatistics 
                  ? Icons.visibility_off 
                  : Icons.visibility,
            ),
            tooltip: bomTreeState.showStatistics ? '隐藏统计' : '显示统计',
          ),
          
          // 拖拽功能切换
          IconButton(
            onPressed: () {
              ref.read(bomTreeProvider.notifier).toggleDragEnabled();
            },
            icon: Icon(
              bomTreeState.isDragEnabled 
                  ? Icons.drag_indicator 
                  : Icons.drag_handle_outlined,
              color: bomTreeState.isDragEnabled 
                  ? theme.colorScheme.primary 
                  : null,
            ),
            tooltip: bomTreeState.isDragEnabled ? '禁用拖拽' : '启用拖拽',
          ),
          
          // 刷新按钮
          IconButton(
            onPressed: bomTreeState.isLoading 
                ? null 
                : () {
                    ref.read(bomTreeProvider.notifier).initializeBomTree(projectId);
                    onRefresh?.call();
                  },
            icon: bomTreeState.isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  )
                : const Icon(Icons.refresh),
            tooltip: '刷新',
          ),
        ],
      ),
    );
  }

  /// 构建统计信息面板
  Widget _buildStatisticsPanel(
    BuildContext context,
    ThemeData theme,
    BomTreeState bomTreeState,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 总体统计
          Row(
            children: [
              _buildStatCard(
                theme,
                '总项目',
                '${bomTreeState.totalBomItemCount}',
                Icons.inventory_2,
                theme.colorScheme.primary,
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                theme,
                '总价值',
                '¥${bomTreeState.totalValue.toStringAsFixed(2)}',
                Icons.attach_money,
                Colors.green,
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                theme,
                '完成率',
                '${(bomTreeState.completionRate * 100).toStringAsFixed(1)}%',
                Icons.check_circle,
                Colors.blue,
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                theme,
                '分类数',
                '${bomTreeState.categoryStats.length}',
                Icons.category,
                Colors.orange,
              ),
            ],
          ),
          
          if (bomTreeState.categoryStats.isNotEmpty) ...[
            const SizedBox(height: 16),
            // 状态分布
            _buildStatusDistribution(theme, bomTreeState),
          ],
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(
    ThemeData theme,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    value,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态分布
  Widget _buildStatusDistribution(ThemeData theme, BomTreeState bomTreeState) {
    final statusDistribution = bomTreeState.statusDistribution;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '状态分布',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: statusDistribution.entries.map((entry) {
            final status = entry.key;
            final count = entry.value;
            
            if (count == 0) return const SizedBox.shrink();
            
            return Chip(
              avatar: CircleAvatar(
                backgroundColor: Color(int.parse('0xFF${status.color.substring(1)}')),
                radius: 8,
              ),
              label: Text('${status.displayName}: $count'),
              backgroundColor: theme.colorScheme.surface,
              side: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建树形结构内容
  Widget _buildTreeContent(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    BomTreeState bomTreeState,
  ) {
    if (bomTreeState.isLoading) {
      return const LoadingWidget(message: '加载BOM树形结构...');
    }

    if (bomTreeState.hasError) {
      return ErrorDisplayWidget(
        failure: UIFailure.server(message: bomTreeState.errorMessage!),
      );
    }

    if (bomTreeState.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.account_tree_outlined,
        title: '暂无BOM数据',
        subtitle: '该项目还没有添加任何BOM项目\n点击添加按钮开始构建您的BOM清单',
        actionText: '添加BOM项目',
        onAction: () {
          // TODO: 导航到添加BOM项目页面
        },
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bomTreeState.tree.length,
      itemBuilder: (context, index) {
        final node = bomTreeState.tree[index];
        return _buildTreeNode(context, ref, theme, bomTreeState, node);
      },
    );
  }

  /// 构建树节点
  Widget _buildTreeNode(
    BuildContext context,
    WidgetRef ref,
    ThemeData theme,
    BomTreeState bomTreeState,
    TreeNode node,
  ) {
    // 使用BOM专用的TreeNodeWidget
    return BomTreeNodeWidget(
      node: node,
      isSelected: node.id == bomTreeState.selectedNodeId,
      isHighlighted: bomTreeState.searchResults.contains(node.id),
      isDragEnabled: bomTreeState.isDragEnabled,
      isDragging: node.id == bomTreeState.draggingNodeId,
      isDropTarget: node.id == bomTreeState.dropTargetNodeId,
      onTap: () {
        ref.read(bomTreeProvider.notifier).selectNode(node.id);
        onNodeSelected?.call(node.id);
      },
      onExpansionChanged: (expanded) {
        ref.read(bomTreeProvider.notifier).toggleNodeExpansion(node.id);
      },
      onEdit: node.type == BomTreeNodeType.bomItem && node.bomItem != null
          ? () => onBomItemEdit?.call(node.bomItem!.id)
          : null,
      onDelete: node.type == BomTreeNodeType.bomItem && node.bomItem != null
          ? () => onBomItemDelete?.call(node.bomItem!.id)
          : null,
      onDragStart: bomTreeState.isDragEnabled
          ? () => ref.read(bomTreeProvider.notifier).startDragging(node.id)
          : null,
      onDragEnd: bomTreeState.isDragEnabled
          ? () => ref.read(bomTreeProvider.notifier).endDragging()
          : null,
      onDragEnter: bomTreeState.isDragEnabled
          ? () => ref.read(bomTreeProvider.notifier).setDropTarget(node.id)
          : null,
      onDragLeave: bomTreeState.isDragEnabled
          ? () => ref.read(bomTreeProvider.notifier).setDropTarget(null)
          : null,
    );
  }


}
