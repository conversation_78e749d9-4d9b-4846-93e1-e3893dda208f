import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/entities/bom_item.dart';

part 'bom_calculation_provider.g.dart';

/// BOM计算服务
/// 将计算逻辑从Widget中移动到Provider，遵循Clean Architecture原则
@riverpod
class BomCalculationService extends _$BomCalculationService {
  @override
  void build() {
    // 无状态服务
  }
  
  /// 计算总价
  /// [quantity] 数量
  /// [unitPrice] 单价
  /// 返回格式化的总价字符串
  String calculateTotalPrice(int quantity, double unitPrice) {
    final totalPrice = quantity * unitPrice;
    return '¥${totalPrice.toStringAsFixed(2)}';
  }
  
  /// 计算总价（数值）
  /// [quantity] 数量
  /// [unitPrice] 单价
  /// 返回总价数值
  double calculateTotalPriceValue(int quantity, double unitPrice) {
    return quantity * unitPrice;
  }
  
  /// 解析数量输入
  /// [input] 用户输入的字符串
  /// 返回解析后的数量，失败时返回0
  int parseQuantity(String input) {
    return int.tryParse(input.trim()) ?? 0;
  }
  
  /// 解析价格输入
  /// [input] 用户输入的字符串
  /// 返回解析后的价格，失败时返回0.0
  double parsePrice(String input) {
    return double.tryParse(input.trim()) ?? 0.0;
  }
  
  /// 验证数量输入
  /// [input] 用户输入的字符串
  /// 返回验证结果和错误信息
  ValidationResult validateQuantity(String input) {
    if (input.trim().isEmpty) {
      return const ValidationResult(isValid: false, errorMessage: '数量不能为空');
    }
    
    final quantity = int.tryParse(input.trim());
    if (quantity == null) {
      return const ValidationResult(isValid: false, errorMessage: '请输入有效的数量');
    }
    
    if (quantity <= 0) {
      return const ValidationResult(isValid: false, errorMessage: '数量必须大于0');
    }
    
    if (quantity > 99999) {
      return const ValidationResult(isValid: false, errorMessage: '数量不能超过99999');
    }
    
    return const ValidationResult(isValid: true);
  }
  
  /// 验证价格输入
  /// [input] 用户输入的字符串
  /// 返回验证结果和错误信息
  ValidationResult validatePrice(String input) {
    if (input.trim().isEmpty) {
      return const ValidationResult(isValid: false, errorMessage: '价格不能为空');
    }
    
    final price = double.tryParse(input.trim());
    if (price == null) {
      return const ValidationResult(isValid: false, errorMessage: '请输入有效的价格');
    }
    
    if (price < 0) {
      return const ValidationResult(isValid: false, errorMessage: '价格不能为负数');
    }
    
    if (price > 999999.99) {
      return const ValidationResult(isValid: false, errorMessage: '价格不能超过999999.99');
    }
    
    return const ValidationResult(isValid: true);
  }
  
  /// 计算BOM项目列表的总成本
  /// [bomItems] BOM项目列表
  /// 返回总成本
  double calculateTotalCost(List<BomItem> bomItems) {
    return bomItems.fold<double>(0, (sum, item) {
      final price = item.actualPrice ?? item.estimatedPrice ?? 0.0;
      return sum + (price * item.quantity);
    });
  }
  
  /// 计算BOM项目列表的预估成本
  /// [bomItems] BOM项目列表
  /// 返回预估成本
  double calculateEstimatedCost(List<BomItem> bomItems) {
    return bomItems.fold<double>(0, (sum, item) {
      final price = item.estimatedPrice ?? 0.0;
      return sum + (price * item.quantity);
    });
  }
  
  /// 计算BOM项目列表的实际成本
  /// [bomItems] BOM项目列表
  /// 返回实际成本
  double calculateActualCost(List<BomItem> bomItems) {
    return bomItems.fold<double>(0, (sum, item) {
      final price = item.actualPrice ?? 0.0;
      return sum + (price * item.quantity);
    });
  }
  
  /// 计算成本差异
  /// [estimatedCost] 预估成本
  /// [actualCost] 实际成本
  /// 返回成本差异（正数表示超支，负数表示节省）
  double calculateCostDifference(double estimatedCost, double actualCost) {
    return actualCost - estimatedCost;
  }
  
  /// 计算成本差异百分比
  /// [estimatedCost] 预估成本
  /// [actualCost] 实际成本
  /// 返回成本差异百分比
  double calculateCostDifferencePercentage(double estimatedCost, double actualCost) {
    if (estimatedCost == 0) return 0.0;
    return ((actualCost - estimatedCost) / estimatedCost) * 100;
  }
  
  /// 格式化货币
  /// [amount] 金额
  /// [showSymbol] 是否显示货币符号
  /// 返回格式化的货币字符串
  String formatCurrency(double amount, {bool showSymbol = true}) {
    final symbol = showSymbol ? '¥' : '';
    return '$symbol${amount.toStringAsFixed(2)}';
  }
  
  /// 格式化百分比
  /// [percentage] 百分比
  /// [decimalPlaces] 小数位数
  /// 返回格式化的百分比字符串
  String formatPercentage(double percentage, {int decimalPlaces = 1}) {
    return '${percentage.toStringAsFixed(decimalPlaces)}%';
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  
  const ValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}

/// BOM状态显示名称Provider
/// 将状态转换逻辑从Widget中移动到Provider
@riverpod
class BomStatusDisplayService extends _$BomStatusDisplayService {
  @override
  void build() {
    // 无状态服务
  }
  
  /// 获取状态显示名称
  /// [status] BOM项目状态
  /// 返回本地化的状态显示名称
  String getStatusDisplayName(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return '待采购';
      case BomItemStatus.ordered:
        return '已下单';
      case BomItemStatus.received:
        return '已收货';
      case BomItemStatus.installed:
        return '已安装';
      case BomItemStatus.cancelled:
        return '已取消';
    }
  }
  
  /// 获取状态颜色
  /// [status] BOM项目状态
  /// 返回状态对应的颜色代码
  String getStatusColor(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return '#FFA726'; // 橙色
      case BomItemStatus.ordered:
        return '#42A5F5'; // 蓝色
      case BomItemStatus.received:
        return '#66BB6A'; // 绿色
      case BomItemStatus.installed:
        return '#4CAF50'; // 深绿色
      case BomItemStatus.cancelled:
        return '#EF5350'; // 红色
    }
  }
  
  /// 获取状态图标
  /// [status] BOM项目状态
  /// 返回状态对应的图标名称
  String getStatusIcon(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return 'shopping_cart';
      case BomItemStatus.ordered:
        return 'local_shipping';
      case BomItemStatus.received:
        return 'inventory';
      case BomItemStatus.installed:
        return 'check_circle';
      case BomItemStatus.cancelled:
        return 'cancel';
    }
  }
  
  /// 获取状态描述
  /// [status] BOM项目状态
  /// 返回状态的详细描述
  String getStatusDescription(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return '等待采购，尚未下单';
      case BomItemStatus.ordered:
        return '已下单，等待收货';
      case BomItemStatus.received:
        return '已收货，等待安装';
      case BomItemStatus.installed:
        return '已安装完成';
      case BomItemStatus.cancelled:
        return '已取消，不再需要';
    }
  }
  
  /// 获取下一个状态
  /// [currentStatus] 当前状态
  /// 返回下一个可能的状态，如果没有则返回null
  BomItemStatus? getNextStatus(BomItemStatus currentStatus) {
    switch (currentStatus) {
      case BomItemStatus.pending:
        return BomItemStatus.ordered;
      case BomItemStatus.ordered:
        return BomItemStatus.received;
      case BomItemStatus.received:
        return BomItemStatus.installed;
      case BomItemStatus.installed:
        return null; // 已完成，无下一状态
      case BomItemStatus.cancelled:
        return null; // 已取消，无下一状态
    }
  }
  
  /// 获取可选状态列表
  /// [currentStatus] 当前状态
  /// 返回可以切换到的状态列表
  List<BomItemStatus> getAvailableStatuses(BomItemStatus currentStatus) {
    switch (currentStatus) {
      case BomItemStatus.pending:
        return [BomItemStatus.ordered, BomItemStatus.cancelled];
      case BomItemStatus.ordered:
        return [BomItemStatus.received, BomItemStatus.cancelled];
      case BomItemStatus.received:
        return [BomItemStatus.installed, BomItemStatus.cancelled];
      case BomItemStatus.installed:
        return []; // 已完成，不能切换
      case BomItemStatus.cancelled:
        return [BomItemStatus.pending]; // 可以重新激活
    }
  }
}
