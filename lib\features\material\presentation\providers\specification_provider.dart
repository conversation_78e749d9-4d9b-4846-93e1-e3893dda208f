import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fpdart/fpdart.dart';
import '../../domain/entities/product_specification.dart';
import '../../domain/services/specification_service.dart';
import '../../data/services/specification_service_impl.dart';
import '../../../../core/error/failures.dart';

/// 规格服务Provider
final specificationServiceProvider = Provider<SpecificationService>((ref) {
  return SpecificationServiceImpl();
});

/// 根据材料ID获取规格的Provider
final specificationByMaterialIdProvider = FutureProvider.family<ProductSpecification?, String>((ref, materialId) async {
  final service = ref.read(specificationServiceProvider);
  final result = await service.getSpecificationByMaterialId(materialId);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (specification) => specification,
  );
});

/// 批量获取规格的Provider
final specificationsByMaterialIdsProvider = FutureProvider.family<Map<String, ProductSpecification>, List<String>>((ref, materialIds) async {
  final service = ref.read(specificationServiceProvider);
  final result = await service.getSpecificationsByMaterialIds(materialIds);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (specifications) => specifications,
  );
});

/// 规格模板Provider
final specificationTemplateProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, category) async {
  final service = ref.read(specificationServiceProvider);
  final result = await service.getSpecificationTemplate(category);
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (template) => template,
  );
});

/// 规格搜索Provider
final specificationSearchProvider = FutureProvider.family<List<ProductSpecification>, SpecificationSearchParams>((ref, params) async {
  final service = ref.read(specificationServiceProvider);
  final result = await service.searchSpecifications(
    category: params.category,
    filters: params.filters,
    limit: params.limit,
    offset: params.offset,
  );
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (specifications) => specifications,
  );
});

/// 规格管理状态Provider
final specificationManagerProvider = StateNotifierProvider<SpecificationManager, SpecificationManagerState>((ref) {
  final service = ref.read(specificationServiceProvider);
  return SpecificationManager(service);
});

/// 搜索参数
class SpecificationSearchParams {
  final String? category;
  final Map<String, dynamic>? filters;
  final int limit;
  final int offset;

  const SpecificationSearchParams({
    this.category,
    this.filters,
    this.limit = 20,
    this.offset = 0,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpecificationSearchParams &&
          runtimeType == other.runtimeType &&
          category == other.category &&
          limit == other.limit &&
          offset == other.offset;

  @override
  int get hashCode => category.hashCode ^ limit.hashCode ^ offset.hashCode;
}

/// 规格管理状态
class SpecificationManagerState {
  final bool isLoading;
  final String? error;
  final ProductSpecification? currentSpecification;
  final Map<String, ProductSpecification> cache;

  const SpecificationManagerState({
    this.isLoading = false,
    this.error,
    this.currentSpecification,
    this.cache = const {},
  });

  SpecificationManagerState copyWith({
    bool? isLoading,
    String? error,
    ProductSpecification? currentSpecification,
    Map<String, ProductSpecification>? cache,
  }) {
    return SpecificationManagerState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentSpecification: currentSpecification ?? this.currentSpecification,
      cache: cache ?? this.cache,
    );
  }
}

/// 规格管理器
class SpecificationManager extends StateNotifier<SpecificationManagerState> {
  final SpecificationService _service;

  SpecificationManager(this._service) : super(const SpecificationManagerState());

  /// 创建规格
  Future<Either<Failure, ProductSpecification>> createSpecification(ProductSpecification specification) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _service.createSpecification(specification);
      
      return result.fold(
        (failure) {
          state = state.copyWith(isLoading: false, error: failure.message);
          return Left(failure);
        },
        (specification) {
          final newCache = Map<String, ProductSpecification>.from(state.cache);
          newCache[specification.id] = specification;
          
          state = state.copyWith(
            isLoading: false,
            currentSpecification: specification,
            cache: newCache,
          );
          return Right(specification);
        },
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// 更新规格
  Future<Either<Failure, ProductSpecification>> updateSpecification(ProductSpecification specification) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _service.updateSpecification(specification);
      
      return result.fold(
        (failure) {
          state = state.copyWith(isLoading: false, error: failure.message);
          return Left(failure);
        },
        (specification) {
          final newCache = Map<String, ProductSpecification>.from(state.cache);
          newCache[specification.id] = specification;
          
          state = state.copyWith(
            isLoading: false,
            currentSpecification: specification,
            cache: newCache,
          );
          return Right(specification);
        },
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// 删除规格
  Future<Either<Failure, void>> deleteSpecification(String specificationId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _service.deleteSpecification(specificationId);
      
      return result.fold(
        (failure) {
          state = state.copyWith(isLoading: false, error: failure.message);
          return Left(failure);
        },
        (_) {
          final newCache = Map<String, ProductSpecification>.from(state.cache);
          newCache.remove(specificationId);
          
          ProductSpecification? newCurrent = state.currentSpecification;
          if (newCurrent?.id == specificationId) {
            newCurrent = null;
          }
          
          state = state.copyWith(
            isLoading: false,
            currentSpecification: newCurrent,
            cache: newCache,
          );
          return const Right(null);
        },
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// 从模板创建规格
  Future<Either<Failure, ProductSpecification>> createFromTemplate(
    String materialId,
    String category,
    Map<String, dynamic> templateValues,
  ) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _service.createFromTemplate(materialId, category, templateValues);
      
      return result.fold(
        (failure) {
          state = state.copyWith(isLoading: false, error: failure.message);
          return Left(failure);
        },
        (specification) {
          final newCache = Map<String, ProductSpecification>.from(state.cache);
          newCache[specification.id] = specification;
          
          state = state.copyWith(
            isLoading: false,
            currentSpecification: specification,
            cache: newCache,
          );
          return Right(specification);
        },
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  /// 加载规格
  Future<void> loadSpecification(String specificationId) async {
    // 先检查缓存
    if (state.cache.containsKey(specificationId)) {
      state = state.copyWith(currentSpecification: state.cache[specificationId]);
      return;
    }

    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _service.getSpecificationById(specificationId);
      
      result.fold(
        (failure) {
          state = state.copyWith(isLoading: false, error: failure.message);
        },
        (specification) {
          if (specification != null) {
            final newCache = Map<String, ProductSpecification>.from(state.cache);
            newCache[specification.id] = specification;
            
            state = state.copyWith(
              isLoading: false,
              currentSpecification: specification,
              cache: newCache,
            );
          } else {
            state = state.copyWith(isLoading: false);
          }
        },
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 清除当前规格
  void clearCurrentSpecification() {
    state = state.copyWith(currentSpecification: null);
  }
}
