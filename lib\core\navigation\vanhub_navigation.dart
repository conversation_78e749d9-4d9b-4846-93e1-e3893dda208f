import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// VanHub导航系统
/// 提供Material 3风格的导航组件和页面切换动画
class VanHubNavigation {
  /// 创建底部导航栏
  static Widget createBottomNavBar({
    required List<VanHubNavigationDestination> destinations,
    required int currentIndex,
    required ValueChanged<int> onDestinationSelected,
    Color? backgroundColor,
    Color? indicatorColor,
    double elevation = 3.0,
    double? height,
  }) {
    return NavigationBar(
      destinations: destinations.map((destination) {
        return NavigationDestination(
          icon: destination.icon,
          selectedIcon: destination.selectedIcon ?? destination.icon,
          label: destination.label,
          tooltip: destination.tooltip,
        );
      }).toList(),
      selectedIndex: currentIndex,
      onDestinationSelected: onDestinationSelected,
      backgroundColor: backgroundColor,
      indicatorColor: indicatorColor,
      elevation: elevation,
      height: height ?? 80,
    );
  }

  /// 创建侧边导航栏
  static Widget createNavigationRail({
    required List<VanHubNavigationDestination> destinations,
    required int currentIndex,
    required ValueChanged<int> onDestinationSelected,
    Widget? header,
    Widget? trailing,
    bool extended = false,
    double width = 72.0,
    Color? backgroundColor,
    Color? indicatorColor,
  }) {
    return NavigationRail(
      destinations: destinations.map((destination) {
        return NavigationRailDestination(
          icon: destination.icon,
          selectedIcon: destination.selectedIcon ?? destination.icon,
          label: Text(destination.label),
        );
      }).toList(),
      selectedIndex: currentIndex,
      onDestinationSelected: onDestinationSelected,
      leading: header,
      trailing: trailing,
      extended: extended,
      minWidth: width,
      backgroundColor: backgroundColor,
      indicatorColor: indicatorColor,
    );
  }

  /// 创建抽屉导航
  static Widget createNavigationDrawer({
    required List<VanHubNavigationDestination> destinations,
    required int currentIndex,
    required ValueChanged<int> onDestinationSelected,
    Widget? header,
    Widget? footer,
    Color? backgroundColor,
  }) {
    return NavigationDrawer(
      backgroundColor: backgroundColor,
      selectedIndex: currentIndex,
      onDestinationSelected: onDestinationSelected,
      children: [
        if (header != null) header,
        ...destinations.asMap().entries.map((entry) {
          final index = entry.key;
          final destination = entry.value;
          return NavigationDrawerDestination(
            icon: destination.icon,
            selectedIcon: destination.selectedIcon ?? destination.icon,
            label: Text(destination.label),
            enabled: destination.enabled,
          );
        }),
        if (footer != null) footer,
      ],
    );
  }

  /// 创建面包屑导航
  static Widget createBreadcrumbs({
    required List<VanHubBreadcrumbItem> items,
    required ValueChanged<int> onItemTapped,
    TextStyle? textStyle,
    TextStyle? activeTextStyle,
    Color? dividerColor,
    Widget divider = const Text(' / '),
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isLast = index == items.length - 1;
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: isLast ? null : () => onItemTapped(index),
              child: Text(
                item.label,
                style: isLast ? activeTextStyle : textStyle,
              ),
            ),
            if (!isLast) divider,
          ],
        );
      }).toList(),
    );
  }

  /// 创建标签页导航
  static Widget createTabBar({
    required List<VanHubTabItem> tabs,
    required TabController controller,
    bool isScrollable = false,
    Color? indicatorColor,
    Color? labelColor,
    Color? unselectedLabelColor,
    TextStyle? labelStyle,
    TextStyle? unselectedLabelStyle,
    double indicatorWeight = 2.0,
    TabBarIndicatorSize indicatorSize = TabBarIndicatorSize.label,
  }) {
    return TabBar(
      tabs: tabs.map((tab) {
        return Tab(
          text: tab.label,
          icon: tab.icon,
          iconMargin: tab.iconMargin ?? const EdgeInsets.only(bottom: 4.0),
        );
      }).toList(),
      controller: controller,
      isScrollable: isScrollable,
      indicatorColor: indicatorColor,
      labelColor: labelColor,
      unselectedLabelColor: unselectedLabelColor,
      labelStyle: labelStyle,
      unselectedLabelStyle: unselectedLabelStyle,
      indicatorWeight: indicatorWeight,
      indicatorSize: indicatorSize,
    );
  }

  /// 创建页面切换动画
  static Widget createPageTransition({
    required Widget child,
    required Animation<double> animation,
    VanHubPageTransitionType type = VanHubPageTransitionType.fade,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    switch (type) {
      case VanHubPageTransitionType.fade:
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      case VanHubPageTransitionType.scale:
        return ScaleTransition(
          scale: animation,
          child: child,
        );
      case VanHubPageTransitionType.slide:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(animation),
          child: child,
        );
      case VanHubPageTransitionType.slideUp:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(animation),
          child: child,
        );
      case VanHubPageTransitionType.slideDown:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, -1.0),
            end: Offset.zero,
          ).animate(animation),
          child: child,
        );
      case VanHubPageTransitionType.rotation:
        return RotationTransition(
          turns: animation,
          child: child,
        );
      case VanHubPageTransitionType.size:
        return SizeTransition(
          sizeFactor: animation,
          child: child,
        );
      case VanHubPageTransitionType.fadeThrough:
        return FadeTransition(
          opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: animation,
              curve: const Interval(0.5, 1.0),
            ),
          ),
          child: child,
        );
    }
  }

  /// 创建Hero动画
  static Widget createHeroTransition({
    required String tag,
    required Widget child,
    VanHubHeroFlightShuttleBuilder? flightShuttleBuilder,
    VanHubHeroPlaceholderBuilder? placeholderBuilder,
  }) {
    return Hero(
      tag: tag,
      flightShuttleBuilder: flightShuttleBuilder != null
          ? (flightContext, animation, direction, fromContext, toContext) {
              return flightShuttleBuilder(
                flightContext,
                animation,
                direction == HeroFlightDirection.push,
                fromContext.widget,
                toContext.widget,
              );
            }
          : null,
      placeholderBuilder: placeholderBuilder != null
          ? (context, heroSize, child) {
              return placeholderBuilder(context, heroSize, child);
            }
          : null,
      child: child,
    );
  }

  /// 创建返回按钮
  static Widget createBackButton({
    required BuildContext context,
    VoidCallback? onPressed,
    Color? color,
    String? tooltip,
  }) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: onPressed ?? () => Navigator.of(context).pop(),
      color: color,
      tooltip: tooltip ?? '返回',
    );
  }

  /// 创建路由配置
  static GoRouter createRouter({
    required List<VanHubRoute> routes,
    String initialLocation = '/',
    VanHubRouteErrorBuilder? errorBuilder,
    VanHubRouteRedirectHandler? redirect,
  }) {
    return GoRouter(
      initialLocation: initialLocation,
      routes: routes.map((route) {
        return GoRoute(
          path: route.path,
          name: route.name,
          builder: (context, state) => route.builder(context, state),
          routes: route.children?.map((child) {
            return GoRoute(
              path: child.path,
              name: child.name,
              builder: (context, state) => child.builder(context, state),
            );
          }).toList() ?? [],
        );
      }).toList(),
      errorBuilder: errorBuilder != null
          ? (context, state) => errorBuilder(context, state)
          : null,
      redirect: redirect != null
          ? (context, state) => redirect(context, state)
          : null,
    );
  }
}

/// 导航目的地
class VanHubNavigationDestination {
  final String label;
  final Widget icon;
  final Widget? selectedIcon;
  final String? tooltip;
  final bool enabled;

  const VanHubNavigationDestination({
    required this.label,
    required this.icon,
    this.selectedIcon,
    this.tooltip,
    this.enabled = true,
  });
}

/// 面包屑项
class VanHubBreadcrumbItem {
  final String label;
  final String? route;
  final Map<String, dynamic>? params;

  const VanHubBreadcrumbItem({
    required this.label,
    this.route,
    this.params,
  });
}

/// 标签页项
class VanHubTabItem {
  final String label;
  final Widget? icon;
  final EdgeInsetsGeometry? iconMargin;

  const VanHubTabItem({
    required this.label,
    this.icon,
    this.iconMargin,
  });
}

/// 页面切换动画类型
enum VanHubPageTransitionType {
  fade,
  scale,
  slide,
  slideUp,
  slideDown,
  rotation,
  size,
  fadeThrough,
}

/// Hero飞行构建器
typedef VanHubHeroFlightShuttleBuilder = Widget Function(
  BuildContext context,
  Animation<double> animation,
  bool isPush,
  Widget fromHero,
  Widget toHero,
);

/// Hero占位构建器
typedef VanHubHeroPlaceholderBuilder = Widget Function(
  BuildContext context,
  Size heroSize,
  Widget child,
);

/// 路由错误构建器
typedef VanHubRouteErrorBuilder = Widget Function(
  BuildContext context,
  GoRouterState state,
);

/// 路由重定向处理器
typedef VanHubRouteRedirectHandler = String? Function(
  BuildContext context,
  GoRouterState state,
);

/// 路由构建器
typedef VanHubRouteBuilder = Widget Function(
  BuildContext context,
  GoRouterState state,
);

/// 路由定义
class VanHubRoute {
  final String path;
  final String? name;
  final VanHubRouteBuilder builder;
  final List<VanHubRoute>? children;

  const VanHubRoute({
    required this.path,
    this.name,
    required this.builder,
    this.children,
  });
}

/// 响应式导航控制器
class VanHubResponsiveNavigationController extends StatefulWidget {
  final Widget mobileNavigationBar;
  final Widget tabletNavigationRail;
  final Widget desktopNavigationDrawer;
  final Widget child;
  final double tabletBreakpoint;
  final double desktopBreakpoint;

  const VanHubResponsiveNavigationController({
    super.key,
    required this.mobileNavigationBar,
    required this.tabletNavigationRail,
    required this.desktopNavigationDrawer,
    required this.child,
    this.tabletBreakpoint = 600,
    this.desktopBreakpoint = 1200,
  });

  @override
  State<VanHubResponsiveNavigationController> createState() =>
      _VanHubResponsiveNavigationControllerState();
}

class _VanHubResponsiveNavigationControllerState
    extends State<VanHubResponsiveNavigationController> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        if (width < widget.tabletBreakpoint) {
          // 移动端布局
          return Scaffold(
            body: widget.child,
            bottomNavigationBar: widget.mobileNavigationBar,
          );
        } else if (width < widget.desktopBreakpoint) {
          // 平板布局
          return Scaffold(
            body: Row(
              children: [
                widget.tabletNavigationRail,
                Expanded(child: widget.child),
              ],
            ),
          );
        } else {
          // 桌面布局
          return Scaffold(
            body: Row(
              children: [
                widget.desktopNavigationDrawer,
                Expanded(child: widget.child),
              ],
            ),
          );
        }
      },
    );
  }
}