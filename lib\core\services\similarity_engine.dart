import 'dart:math' as math;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../features/material/domain/entities/material.dart';

// part 'similarity_engine.g.dart'; // 暂时禁用代码生成

/// 相似度计算引擎
///
/// 提供多种相似度计算算法，用于智能推荐系统
class SimilarityEngine {
  SimilarityEngine() {
    // 初始化相似度引擎
  }

  /// 计算两个向量的余弦相似度
  /// 
  /// 返回值范围：-1.0 到 1.0，1.0表示完全相同
  double calculateCosineSimilarity(List<double> vectorA, List<double> vectorB) {
    if (vectorA.length != vectorB.length) {
      throw ArgumentError('向量长度必须相同');
    }

    if (vectorA.isEmpty) return 0.0;

    double dotProduct = 0.0;
    double normA = 0.0;
    double normB = 0.0;

    for (int i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    if (normA == 0.0 || normB == 0.0) return 0.0;

    return dotProduct / (math.sqrt(normA) * math.sqrt(normB));
  }

  /// 计算两个集合的Jaccard相似度
  /// 
  /// 返回值范围：0.0 到 1.0，1.0表示完全相同
  double calculateJaccardSimilarity(Set<String> setA, Set<String> setB) {
    if (setA.isEmpty && setB.isEmpty) return 1.0;
    if (setA.isEmpty || setB.isEmpty) return 0.0;

    final intersection = setA.intersection(setB);
    final union = setA.union(setB);

    return intersection.length / union.length;
  }

  /// 计算两个字符串的文本相似度
  /// 
  /// 结合多种算法：编辑距离、n-gram、词汇重叠
  double calculateTextSimilarity(String textA, String textB) {
    if (textA.isEmpty && textB.isEmpty) return 1.0;
    if (textA.isEmpty || textB.isEmpty) return 0.0;

    final textALower = textA.toLowerCase();
    final textBLower = textB.toLowerCase();

    // 1. 编辑距离相似度
    final editSimilarity = _calculateEditDistanceSimilarity(textALower, textBLower);

    // 2. N-gram相似度
    final ngramSimilarity = _calculateNGramSimilarity(textALower, textBLower, 2);

    // 3. 词汇重叠相似度
    final wordSimilarity = _calculateWordOverlapSimilarity(textALower, textBLower);

    // 加权平均
    return (editSimilarity * 0.3 + ngramSimilarity * 0.4 + wordSimilarity * 0.3);
  }

  /// 计算两个材料的综合相似度
  /// 
  /// 考虑多个维度：名称、分类、品牌、价格、标签等
  double calculateMaterialSimilarity(Material materialA, Material materialB) {
    if (materialA.id == materialB.id) return 1.0;

    double totalScore = 0.0;
    double totalWeight = 0.0;

    // 1. 名称相似度（权重：30%）
    final nameWeight = 0.3;
    final nameSimilarity = calculateTextSimilarity(materialA.name, materialB.name);
    totalScore += nameSimilarity * nameWeight;
    totalWeight += nameWeight;

    // 2. 分类相似度（权重：25%）
    final categoryWeight = 0.25;
    final categorySimilarity = materialA.category == materialB.category ? 1.0 : 0.0;
    totalScore += categorySimilarity * categoryWeight;
    totalWeight += categoryWeight;

    // 3. 品牌相似度（权重：15%）
    if (materialA.brand != null && materialB.brand != null) {
      final brandWeight = 0.15;
      final brandSimilarity = materialA.brand == materialB.brand ? 1.0 : 0.0;
      totalScore += brandSimilarity * brandWeight;
      totalWeight += brandWeight;
    }

    // 4. 价格相似度（权重：10%）
    final priceWeight = 0.1;
    final priceSimilarity = _calculatePriceSimilarity(materialA.price, materialB.price);
    totalScore += priceSimilarity * priceWeight;
    totalWeight += priceWeight;

    // 5. 标签相似度（权重：15%）
    if (materialA.tags != null && materialB.tags != null) {
      final tagWeight = 0.15;
      final tagSimilarity = calculateJaccardSimilarity(
        materialA.tags!.toSet(),
        materialB.tags!.toSet(),
      );
      totalScore += tagSimilarity * tagWeight;
      totalWeight += tagWeight;
    }

    // 6. 描述相似度（权重：5%）
    final descWeight = 0.05;
    final descSimilarity = calculateTextSimilarity(
      materialA.description,
      materialB.description,
    );
    totalScore += descSimilarity * descWeight;
    totalWeight += descWeight;

    return totalWeight > 0 ? totalScore / totalWeight : 0.0;
  }

  /// 为材料生成特征向量
  /// 
  /// 用于基于向量的相似度计算和机器学习
  List<double> generateMaterialFeatureVector(Material material) {
    final features = <double>[];

    // 1. 分类特征（one-hot编码）
    final categories = [
      '电气设备', '水路系统', '储物系统', '床铺系统', 
      '厨房系统', '卫浴系统', '外观系统', '底盘系统'
    ];
    for (final category in categories) {
      features.add(material.category == category ? 1.0 : 0.0);
    }

    // 2. 价格特征（归一化）
    features.add(_normalizePriceFeature(material.price));

    // 3. 使用次数特征（归一化）
    features.add(_normalizeUsageFeature(material.usageCount));

    // 4. 品牌特征（哈希编码）
    features.add(_hashStringToFeature(material.brand ?? ''));

    // 5. 标签特征（TF-IDF风格）
    final tagFeatures = _generateTagFeatures(material.tags ?? []);
    features.addAll(tagFeatures);

    // 6. 文本特征（简化的TF-IDF）
    final textFeatures = _generateTextFeatures(material.name + ' ' + material.description);
    features.addAll(textFeatures);

    return features;
  }

  /// 计算编辑距离相似度
  double _calculateEditDistanceSimilarity(String s1, String s2) {
    final distance = _calculateLevenshteinDistance(s1, s2);
    final maxLength = math.max(s1.length, s2.length);
    return maxLength > 0 ? 1.0 - (distance / maxLength) : 1.0;
  }

  /// 计算Levenshtein编辑距离
  int _calculateLevenshteinDistance(String s1, String s2) {
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;

    final matrix = List.generate(
      s1.length + 1,
      (i) => List.filled(s2.length + 1, 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,      // 删除
          matrix[i][j - 1] + 1,      // 插入
          matrix[i - 1][j - 1] + cost, // 替换
        ].reduce(math.min);
      }
    }

    return matrix[s1.length][s2.length];
  }

  /// 计算N-gram相似度
  double _calculateNGramSimilarity(String s1, String s2, int n) {
    final ngrams1 = _generateNGrams(s1, n).toSet();
    final ngrams2 = _generateNGrams(s2, n).toSet();
    return calculateJaccardSimilarity(ngrams1, ngrams2);
  }

  /// 生成N-gram
  List<String> _generateNGrams(String text, int n) {
    if (text.length < n) return [text];
    
    final ngrams = <String>[];
    for (int i = 0; i <= text.length - n; i++) {
      ngrams.add(text.substring(i, i + n));
    }
    return ngrams;
  }

  /// 计算词汇重叠相似度
  double _calculateWordOverlapSimilarity(String s1, String s2) {
    final words1 = s1.split(RegExp(r'\s+')).where((w) => w.isNotEmpty).toSet();
    final words2 = s2.split(RegExp(r'\s+')).where((w) => w.isNotEmpty).toSet();
    return calculateJaccardSimilarity(words1, words2);
  }

  /// 计算价格相似度
  double _calculatePriceSimilarity(double price1, double price2) {
    if (price1 == 0 && price2 == 0) return 1.0;
    if (price1 == 0 || price2 == 0) return 0.0;

    final ratio = math.min(price1, price2) / math.max(price1, price2);
    return ratio;
  }

  /// 归一化价格特征
  double _normalizePriceFeature(double price) {
    // 使用对数归一化，处理价格的大范围变化
    return price > 0 ? math.log(price + 1) / math.log(10000 + 1) : 0.0;
  }

  /// 归一化使用次数特征
  double _normalizeUsageFeature(int usageCount) {
    // 使用平方根归一化，减少极值影响
    return math.sqrt(usageCount) / math.sqrt(100);
  }

  /// 字符串哈希特征
  double _hashStringToFeature(String str) {
    if (str.isEmpty) return 0.0;
    final hash = str.hashCode.abs();
    return (hash % 1000) / 1000.0;
  }

  /// 生成标签特征
  List<double> _generateTagFeatures(List<String> tags) {
    // 简化的标签特征，实际应用中可以使用更复杂的编码
    final commonTags = ['防水', '耐用', '轻量', '节能', '智能', '便携'];
    return commonTags.map((tag) => tags.contains(tag) ? 1.0 : 0.0).toList();
  }

  /// 生成文本特征
  List<double> _generateTextFeatures(String text) {
    // 简化的文本特征，实际应用中可以使用TF-IDF或词嵌入
    final keywords = ['电池', '太阳能', '水泵', '储物', '床垫', '厨具', '卫浴', '轮胎'];
    final textLower = text.toLowerCase();
    return keywords.map((keyword) => textLower.contains(keyword) ? 1.0 : 0.0).toList();
  }
}
