import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import '../errors/failures.dart';

/// 图片处理配置
class ImageProcessingConfig {
  final int maxWidth;
  final int maxHeight;
  final int quality;
  final img.Interpolation resizeMethod;
  final bool maintainAspectRatio;
  final bool autoRotate;
  final img.Format outputFormat;
  final bool removeExifData;

  const ImageProcessingConfig({
    this.maxWidth = 1920,
    this.maxHeight = 1080,
    this.quality = 85,
    this.resizeMethod = img.Interpolation.linear,
    this.maintainAspectRatio = true,
    this.autoRotate = true,
    this.outputFormat = img.Format.jpeg,
    this.removeExifData = true,
  });

  /// 高质量配置
  static const ImageProcessingConfig highQuality = ImageProcessingConfig(
    maxWidth: 2560,
    maxHeight: 1440,
    quality: 95,
    resizeMethod: img.Interpolation.cubic,
  );

  /// 中等质量配置
  static const ImageProcessingConfig mediumQuality = ImageProcessingConfig(
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 85,
  );

  /// 低质量配置（缩略图）
  static const ImageProcessingConfig thumbnail = ImageProcessingConfig(
    maxWidth: 400,
    maxHeight: 400,
    quality: 70,
    resizeMethod: img.Interpolation.linear,
  );

  /// Web优化配置
  static const ImageProcessingConfig webOptimized = ImageProcessingConfig(
    maxWidth: 1200,
    maxHeight: 800,
    quality: 80,
    outputFormat: img.Format.webp,
  );
}

/// 图片处理结果
class ImageProcessingResult {
  final File processedFile;
  final int originalSize;
  final int processedSize;
  final int originalWidth;
  final int originalHeight;
  final int processedWidth;
  final int processedHeight;
  final double compressionRatio;
  final String outputFormat;
  final Duration processingTime;

  const ImageProcessingResult({
    required this.processedFile,
    required this.originalSize,
    required this.processedSize,
    required this.originalWidth,
    required this.originalHeight,
    required this.processedWidth,
    required this.processedHeight,
    required this.compressionRatio,
    required this.outputFormat,
    required this.processingTime,
  });

  /// 是否有效压缩（文件变小了）
  bool get isEffectiveCompression => processedSize < originalSize;

  /// 压缩百分比
  double get compressionPercentage => (1 - (processedSize / originalSize)) * 100;

  Map<String, dynamic> toJson() {
    return {
      'originalSize': originalSize,
      'processedSize': processedSize,
      'originalWidth': originalWidth,
      'originalHeight': originalHeight,
      'processedWidth': processedWidth,
      'processedHeight': processedHeight,
      'compressionRatio': compressionRatio,
      'compressionPercentage': compressionPercentage,
      'outputFormat': outputFormat,
      'processingTimeMs': processingTime.inMilliseconds,
      'isEffectiveCompression': isEffectiveCompression,
    };
  }
}

/// 图片处理服务
class ImageProcessingService {
  /// 处理图片
  Future<Either<Failure, ImageProcessingResult>> processImage({
    required File inputFile,
    ImageProcessingConfig? config,
    String? outputPath,
  }) async {
    final startTime = DateTime.now();
    
    try {
      final processingConfig = config ?? const ImageProcessingConfig();
      
      // 读取原始图片
      final originalBytes = await inputFile.readAsBytes();
      final originalImage = img.decodeImage(originalBytes);
      
      if (originalImage == null) {
        return Left(ValidationFailure(message: '无法解码图片文件'));
      }

      final originalSize = originalBytes.length;
      final originalWidth = originalImage.width;
      final originalHeight = originalImage.height;

      // 处理图片
      img.Image processedImage = originalImage;

      // 自动旋转（基于EXIF数据）
      if (processingConfig.autoRotate) {
        processedImage = img.bakeOrientation(processedImage);
      }

      // 调整大小
      if (_needsResize(processedImage, processingConfig)) {
        final newSize = _calculateNewSize(
          processedImage.width,
          processedImage.height,
          processingConfig,
        );
        
        processedImage = img.copyResize(
          processedImage,
          width: newSize.width,
          height: newSize.height,
          interpolation: processingConfig.resizeMethod,
        );
      }

      // 移除EXIF数据
      if (processingConfig.removeExifData) {
        processedImage = img.Image.from(processedImage);
      }

      // 编码输出
      Uint8List processedBytes;
      String outputFormatName;
      
      switch (processingConfig.outputFormat) {
        case img.Format.jpeg:
          processedBytes = img.encodeJpg(processedImage, quality: processingConfig.quality);
          outputFormatName = 'jpeg';
          break;
        case img.Format.png:
          processedBytes = img.encodePng(processedImage);
          outputFormatName = 'png';
          break;
        case img.Format.webp:
          processedBytes = img.encodeWebP(processedImage, quality: processingConfig.quality);
          outputFormatName = 'webp';
          break;
        default:
          processedBytes = img.encodeJpg(processedImage, quality: processingConfig.quality);
          outputFormatName = 'jpeg';
      }

      // 保存处理后的文件
      final outputFile = await _saveProcessedImage(
        inputFile,
        processedBytes,
        outputPath,
        outputFormatName,
      );

      final endTime = DateTime.now();
      final processingTime = endTime.difference(startTime);

      // 构建结果
      final result = ImageProcessingResult(
        processedFile: outputFile,
        originalSize: originalSize,
        processedSize: processedBytes.length,
        originalWidth: originalWidth,
        originalHeight: originalHeight,
        processedWidth: processedImage.width,
        processedHeight: processedImage.height,
        compressionRatio: processedBytes.length / originalSize,
        outputFormat: outputFormatName,
        processingTime: processingTime,
      );

      return Right(result);
    } catch (e) {
      return Left(UnknownFailure(message: '图片处理失败: $e'));
    }
  }

  /// 批量处理图片
  Future<Either<Failure, List<ImageProcessingResult>>> processMultipleImages({
    required List<File> inputFiles,
    ImageProcessingConfig? config,
    String? outputDirectory,
    Function(int current, int total)? onProgress,
  }) async {
    try {
      final results = <ImageProcessingResult>[];
      
      for (int i = 0; i < inputFiles.length; i++) {
        onProgress?.call(i, inputFiles.length);
        
        final result = await processImage(
          inputFile: inputFiles[i],
          config: config,
          outputPath: outputDirectory,
        );

        final processResult = result.fold(
          (failure) => throw Exception('处理图片 ${inputFiles[i].path} 失败: ${failure.message}'),
          (success) => success,
        );

        results.add(processResult);
      }

      onProgress?.call(inputFiles.length, inputFiles.length);
      return Right(results);
    } catch (e) {
      return Left(UnknownFailure(message: '批量图片处理失败: $e'));
    }
  }

  /// 生成缩略图
  Future<Either<Failure, File>> generateThumbnail({
    required File inputFile,
    int size = 200,
    String? outputPath,
  }) async {
    final config = ImageProcessingConfig(
      maxWidth: size,
      maxHeight: size,
      quality: 70,
      maintainAspectRatio: true,
      outputFormat: img.Format.jpeg,
    );

    final result = await processImage(
      inputFile: inputFile,
      config: config,
      outputPath: outputPath,
    );

    return result.fold(
      (failure) => Left(failure),
      (success) => Right(success.processedFile),
    );
  }

  /// 转换图片格式
  Future<Either<Failure, File>> convertFormat({
    required File inputFile,
    required img.Format targetFormat,
    int quality = 85,
    String? outputPath,
  }) async {
    final config = ImageProcessingConfig(
      maxWidth: 4096, // 保持原始尺寸
      maxHeight: 4096,
      quality: quality,
      outputFormat: targetFormat,
      maintainAspectRatio: true,
    );

    final result = await processImage(
      inputFile: inputFile,
      config: config,
      outputPath: outputPath,
    );

    return result.fold(
      (failure) => Left(failure),
      (success) => Right(success.processedFile),
    );
  }

  /// 获取图片信息
  Future<Either<Failure, Map<String, dynamic>>> getImageInfo(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        return Left(ValidationFailure(message: '无法解码图片文件'));
      }

      final info = {
        'width': image.width,
        'height': image.height,
        'fileSize': bytes.length,
        'format': _detectImageFormat(bytes),
        'hasAlpha': image.hasAlpha,
        'aspectRatio': image.width / image.height,
        'megapixels': (image.width * image.height / 1000000).toStringAsFixed(1),
      };

      return Right(info);
    } catch (e) {
      return Left(UnknownFailure(message: '获取图片信息失败: $e'));
    }
  }

  /// 检查是否需要调整大小
  bool _needsResize(img.Image image, ImageProcessingConfig config) {
    return image.width > config.maxWidth || image.height > config.maxHeight;
  }

  /// 计算新尺寸
  ({int width, int height}) _calculateNewSize(
    int originalWidth,
    int originalHeight,
    ImageProcessingConfig config,
  ) {
    if (!config.maintainAspectRatio) {
      return (width: config.maxWidth, height: config.maxHeight);
    }

    final aspectRatio = originalWidth / originalHeight;
    
    int newWidth = config.maxWidth;
    int newHeight = (newWidth / aspectRatio).round();
    
    if (newHeight > config.maxHeight) {
      newHeight = config.maxHeight;
      newWidth = (newHeight * aspectRatio).round();
    }

    return (width: newWidth, height: newHeight);
  }

  /// 保存处理后的图片
  Future<File> _saveProcessedImage(
    File originalFile,
    Uint8List processedBytes,
    String? outputPath,
    String outputFormat,
  ) async {
    String outputFilePath;
    
    if (outputPath != null) {
      // 使用指定的输出路径
      if (outputPath.endsWith('/') || Directory(outputPath).existsSync()) {
        // 输出路径是目录
        final originalName = path.basenameWithoutExtension(originalFile.path);
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        outputFilePath = '$outputPath/${originalName}_processed_$timestamp.$outputFormat';
      } else {
        // 输出路径是完整文件路径
        outputFilePath = outputPath;
      }
    } else {
      // 在原文件旁边创建处理后的文件
      final originalDir = path.dirname(originalFile.path);
      final originalName = path.basenameWithoutExtension(originalFile.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      outputFilePath = '$originalDir/${originalName}_processed_$timestamp.$outputFormat';
    }

    final outputFile = File(outputFilePath);
    await outputFile.writeAsBytes(processedBytes);
    
    return outputFile;
  }

  /// 检测图片格式
  String _detectImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return 'unknown';
    
    // JPEG
    if (bytes[0] == 0xFF && bytes[1] == 0xD8) {
      return 'jpeg';
    }
    
    // PNG
    if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
      return 'png';
    }
    
    // GIF
    if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46) {
      return 'gif';
    }
    
    // WebP
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
      return 'webp';
    }
    
    return 'unknown';
  }
}
