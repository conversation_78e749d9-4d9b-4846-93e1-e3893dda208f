import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/entities/notification.dart' as domain;
import '../../domain/entities/notification_category.dart';

/// 增强的通知列表组件
class EnhancedNotificationList extends ConsumerStatefulWidget {
  final List<domain.Notification> notifications;
  final List<NotificationCategory> categories;
  final Function(domain.Notification)? onNotificationTap;
  final Function(domain.Notification)? onNotificationRead;
  final Function(List<domain.Notification>)? onBatchRead;
  final Function(List<domain.Notification>)? onBatchDelete;
  final Function(List<domain.Notification>)? onBatchArchive;
  final bool enableBatchOperations;
  final bool enableSwipeActions;
  final bool enableRealTimeUpdates;
  final bool showCategories;
  final String? emptyMessage;

  const EnhancedNotificationList({
    super.key,
    required this.notifications,
    this.categories = const [],
    this.onNotificationTap,
    this.onNotificationRead,
    this.onBatchRead,
    this.onBatchDelete,
    this.onBatchArchive,
    this.enableBatchOperations = true,
    this.enableSwipeActions = true,
    this.enableRealTimeUpdates = true,
    this.showCategories = true,
    this.emptyMessage,
  });

  @override
  ConsumerState<EnhancedNotificationList> createState() => _EnhancedNotificationListState();
}

class _EnhancedNotificationListState extends ConsumerState<EnhancedNotificationList>
    with TickerProviderStateMixin {
  final Set<String> _selectedNotifications = {};
  bool _isSelectionMode = false;
  late AnimationController _selectionController;
  NotificationCategory? _selectedCategory;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredNotifications = _getFilteredNotifications();

    return Column(
      children: [
        if (widget.showCategories) _buildCategoryFilter(),
        _buildSearchBar(),
        if (_isSelectionMode) _buildBatchActionBar(),
        Expanded(
          child: filteredNotifications.isEmpty
              ? _buildEmptyState()
              : _buildNotificationList(filteredNotifications),
        ),
      ],
    );
  }

  /// 构建分类筛选器
  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: VanHubSpacing.sm),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
        itemCount: widget.categories.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildCategoryChip(
              category: null,
              label: '全部',
              isSelected: _selectedCategory == null,
            );
          }
          
          final category = widget.categories[index - 1];
          return _buildCategoryChip(
            category: category,
            label: category.name,
            isSelected: _selectedCategory?.id == category.id,
          );
        },
      ),
    );
  }

  /// 构建分类筛选芯片
  Widget _buildCategoryChip({
    NotificationCategory? category,
    required String label,
    required bool isSelected,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: VanHubSpacing.sm),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedCategory = selected ? category : null;
          });
        },
        selectedColor: category != null 
            ? Color(category.categoryColor).withValues(alpha: 0.2)
            : VanHubBrandColors.primary.withValues(alpha: 0.2),
        checkmarkColor: category != null 
            ? Color(category.categoryColor)
            : VanHubBrandColors.primary,
        avatar: category != null 
            ? Icon(
                _getIconData(category.categoryIcon),
                size: 16,
                color: isSelected ? Color(category.categoryColor) : Colors.grey,
              )
            : null,
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      child: TextField(
        decoration: InputDecoration(
          hintText: '搜索通知...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: VanHubBrandColors.primary),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  /// 构建批量操作栏
  Widget _buildBatchActionBar() {
    return AnimatedBuilder(
      animation: _selectionController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _selectionController.value)),
          child: Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
            decoration: BoxDecoration(
              color: VanHubBrandColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(color: VanHubBrandColors.primary.withValues(alpha: 0.3)),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '已选择 ${_selectedNotifications.length} 项',
                  style: VanHubTypography.titleSmall.copyWith(
                    color: VanHubBrandColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _handleBatchRead,
                  icon: const Icon(Icons.mark_email_read),
                  tooltip: '标记为已读',
                ),
                IconButton(
                  onPressed: _handleBatchArchive,
                  icon: const Icon(Icons.archive),
                  tooltip: '归档',
                ),
                IconButton(
                  onPressed: _handleBatchDelete,
                  icon: const Icon(Icons.delete),
                  tooltip: '删除',
                ),
                IconButton(
                  onPressed: _exitSelectionMode,
                  icon: const Icon(Icons.close),
                  tooltip: '取消选择',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建通知列表
  Widget _buildNotificationList(List<domain.Notification> notifications) {
    return ListView.builder(
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationItem(notification, index);
      },
    );
  }

  /// 构建通知项
  Widget _buildNotificationItem(domain.Notification notification, int index) {
    final isSelected = _selectedNotifications.contains(notification.id);
    final category = _getCategoryForNotification(notification);

    Widget notificationCard = Card(
      margin: const EdgeInsets.symmetric(
        horizontal: VanHubSpacing.md,
        vertical: VanHubSpacing.xs,
      ),
      elevation: notification.isUnread ? 2 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected 
            ? BorderSide(color: VanHubBrandColors.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => _handleNotificationTap(notification),
        onLongPress: widget.enableBatchOperations 
            ? () => _handleNotificationLongPress(notification)
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(VanHubSpacing.md),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: notification.isUnread 
                ? Colors.blue.shade50.withValues(alpha: 0.3)
                : null,
          ),
          child: Row(
            children: [
              if (_isSelectionMode) ...[
                Checkbox(
                  value: isSelected,
                  onChanged: (value) => _toggleNotificationSelection(notification),
                  activeColor: VanHubBrandColors.primary,
                ),
                const SizedBox(width: VanHubSpacing.sm),
              ],
              _buildNotificationIcon(notification, category),
              const SizedBox(width: VanHubSpacing.md),
              Expanded(
                child: _buildNotificationContent(notification),
              ),
              const SizedBox(width: VanHubSpacing.sm),
              _buildNotificationMeta(notification),
            ],
          ),
        ),
      ),
    );

    // 添加滑动操作
    if (widget.enableSwipeActions && !_isSelectionMode) {
      notificationCard = Dismissible(
        key: Key(notification.id),
        background: _buildSwipeBackground(Colors.green, Icons.mark_email_read, '标记已读'),
        secondaryBackground: _buildSwipeBackground(Colors.red, Icons.delete, '删除'),
        onDismissed: (direction) {
          if (direction == DismissDirection.startToEnd) {
            _handleNotificationRead(notification);
          } else {
            _handleNotificationDelete(notification);
          }
        },
        child: notificationCard,
      );
    }

    return notificationCard
        .animate(delay: (index * 50).ms)
        .fadeIn(duration: 300.ms)
        .slideX(begin: 0.2, end: 0);
  }

  /// 构建通知图标
  Widget _buildNotificationIcon(domain.Notification notification, NotificationCategory? category) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Color(notification.priorityColor).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(
            _getIconData(notification.typeIcon),
            color: Color(notification.priorityColor),
            size: 24,
          ),
          if (notification.isUnread)
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建通知内容
  Widget _buildNotificationContent(domain.Notification notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: VanHubTypography.titleSmall.copyWith(
                  fontWeight: notification.isUnread ? FontWeight.w600 : FontWeight.normal,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (notification.priority == domain.NotificationPriority.urgent)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: VanHubSpacing.xs,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '紧急',
                  style: VanHubTypography.bodySmall.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: VanHubSpacing.xs),
        Text(
          notification.message,
          style: VanHubTypography.bodyMedium.copyWith(
            color: Colors.grey[600],
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (notification.subtitle != null) ...[
          const SizedBox(height: VanHubSpacing.xs),
          Text(
            notification.subtitle!,
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey[500],
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 构建通知元信息
  Widget _buildNotificationMeta(domain.Notification notification) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          notification.timeDisplayText,
          style: VanHubTypography.bodySmall.copyWith(
            color: Colors.grey[500],
          ),
        ),
        const SizedBox(height: VanHubSpacing.xs),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: VanHubSpacing.xs,
            vertical: 2,
          ),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            notification.typeDisplayName,
            style: VanHubTypography.bodySmall.copyWith(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建滑动背景
  Widget _buildSwipeBackground(Color color, IconData icon, String text) {
    return Container(
      color: color,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.lg),
      child: Row(
        children: [
          Icon(icon, color: Colors.white),
          const SizedBox(width: VanHubSpacing.sm),
          Text(
            text,
            style: VanHubTypography.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: VanHubSpacing.md),
          Text(
            widget.emptyMessage ?? '暂无通知',
            style: VanHubTypography.titleMedium.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: VanHubSpacing.sm),
          Text(
            '当有新通知时，会在这里显示',
            style: VanHubTypography.bodyMedium.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取筛选后的通知列表
  List<domain.Notification> _getFilteredNotifications() {
    var filtered = widget.notifications.where((notification) {
      // 分类筛选
      if (_selectedCategory != null) {
        if (!_selectedCategory!.matchesNotification(notification)) {
          return false;
        }
      }

      // 搜索筛选
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final title = notification.title.toLowerCase();
        final message = notification.message.toLowerCase();
        if (!title.contains(query) && !message.contains(query)) {
          return false;
        }
      }

      return notification.isVisible;
    }).toList();

    // 按时间排序，未读在前
    filtered.sort((a, b) {
      if (a.isUnread && !b.isUnread) return -1;
      if (!a.isUnread && b.isUnread) return 1;
      return b.createdAt.compareTo(a.createdAt);
    });

    return filtered;
  }

  /// 获取通知对应的分类
  NotificationCategory? _getCategoryForNotification(domain.Notification notification) {
    return widget.categories.firstWhere(
      (category) => category.matchesNotification(notification),
      orElse: () => widget.categories.first,
    );
  }

  /// 处理通知点击
  void _handleNotificationTap(domain.Notification notification) {
    if (_isSelectionMode) {
      _toggleNotificationSelection(notification);
    } else {
      widget.onNotificationTap?.call(notification);
      if (notification.isUnread) {
        widget.onNotificationRead?.call(notification);
      }
    }
  }

  /// 处理通知长按
  void _handleNotificationLongPress(domain.Notification notification) {
    if (!_isSelectionMode) {
      _enterSelectionMode();
    }
    _toggleNotificationSelection(notification);
  }

  /// 切换通知选择状态
  void _toggleNotificationSelection(domain.Notification notification) {
    setState(() {
      if (_selectedNotifications.contains(notification.id)) {
        _selectedNotifications.remove(notification.id);
        if (_selectedNotifications.isEmpty) {
          _exitSelectionMode();
        }
      } else {
        _selectedNotifications.add(notification.id);
      }
    });
  }

  /// 进入选择模式
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
    });
    _selectionController.forward();
  }

  /// 退出选择模式
  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedNotifications.clear();
    });
    _selectionController.reverse();
  }

  /// 处理批量已读
  void _handleBatchRead() {
    final selectedNotifications = widget.notifications
        .where((n) => _selectedNotifications.contains(n.id))
        .toList();
    widget.onBatchRead?.call(selectedNotifications);
    _exitSelectionMode();
  }

  /// 处理批量归档
  void _handleBatchArchive() {
    final selectedNotifications = widget.notifications
        .where((n) => _selectedNotifications.contains(n.id))
        .toList();
    widget.onBatchArchive?.call(selectedNotifications);
    _exitSelectionMode();
  }

  /// 处理批量删除
  void _handleBatchDelete() {
    final selectedNotifications = widget.notifications
        .where((n) => _selectedNotifications.contains(n.id))
        .toList();
    widget.onBatchDelete?.call(selectedNotifications);
    _exitSelectionMode();
  }

  /// 处理通知已读
  void _handleNotificationRead(domain.Notification notification) {
    widget.onNotificationRead?.call(notification);
  }

  /// 处理通知删除
  void _handleNotificationDelete(domain.Notification notification) {
    // TODO: 实现删除逻辑
  }

  /// 获取图标数据
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'system_update':
        return Icons.system_update;
      case 'folder':
        return Icons.folder;
      case 'list_alt':
        return Icons.list_alt;
      case 'timeline':
        return Icons.timeline;
      case 'inventory':
        return Icons.inventory;
      case 'share':
        return Icons.share;
      case 'comment':
        return Icons.comment;
      case 'alarm':
        return Icons.alarm;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      case 'people':
        return Icons.people;
      case 'notifications':
        return Icons.notifications;
      default:
        return Icons.notifications;
    }
  }
}
