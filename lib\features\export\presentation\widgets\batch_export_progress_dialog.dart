import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/services/batch_export_service.dart';

/// 批量导出进度对话框
class BatchExportProgressDialog extends ConsumerStatefulWidget {
  final String batchId;
  final BatchExportService batchExportService;
  final Function(BatchExportResult)? onCompleted;
  final Function()? onCancelled;

  const BatchExportProgressDialog({
    super.key,
    required this.batchId,
    required this.batchExportService,
    this.onCompleted,
    this.onCancelled,
  });

  @override
  ConsumerState<BatchExportProgressDialog> createState() => _BatchExportProgressDialogState();
}

class _BatchExportProgressDialogState extends ConsumerState<BatchExportProgressDialog>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  BatchExportProgress? _progress;
  BatchExportResult? _result;
  bool _isPaused = false;
  bool _isCancelled = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);
    
    _startProgressMonitoring();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  /// 开始进度监控
  void _startProgressMonitoring() {
    _monitorProgress();
  }

  /// 监控进度
  Future<void> _monitorProgress() async {
    while (!_isCancelled) {
      final progressResult = await widget.batchExportService.getBatchProgress(widget.batchId);
      
      progressResult.fold(
        (failure) {
          // 处理错误
          if (mounted) {
            setState(() {
              _progress = null;
            });
          }
          break;
        },
        (progress) {
          if (mounted) {
            setState(() {
              _progress = progress;
            });
            
            // 检查是否完成
            if (progress.isCompleted) {
              _checkForResult();
              break;
            }
          }
        },
      );
      
      // 等待一段时间再检查
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  /// 检查结果
  Future<void> _checkForResult() async {
    final resultResult = await widget.batchExportService.getBatchResult(widget.batchId);
    
    resultResult.fold(
      (failure) {
        // 处理错误
      },
      (result) {
        if (mounted) {
          setState(() {
            _result = result;
          });
          widget.onCompleted?.call(result);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(VanHubSpacing.lg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: VanHubSpacing.lg),
            if (_result != null) _buildResultSection() else _buildProgressSection(),
            const SizedBox(height: VanHubSpacing.lg),
            _buildActionButtons(),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms).scale(begin: const Offset(0.9, 0.9));
  }

  /// 构建头部
  Widget _buildHeader() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Transform.scale(
              scale: _result != null ? 1.0 : 0.8 + (_pulseController.value * 0.2),
              child: Icon(
                _result != null 
                    ? (_result!.isSuccessful ? Icons.check_circle : Icons.error)
                    : Icons.cloud_download,
                color: _result != null 
                    ? (_result!.isSuccessful ? Colors.green : Colors.red)
                    : VanHubBrandColors.primary,
                size: 32,
              ),
            );
          },
        ),
        const SizedBox(width: VanHubSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _result != null ? '导出完成' : '批量导出进行中',
                style: VanHubTypography.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                _result != null 
                    ? (_result!.isSuccessful ? '所有文件导出成功' : '部分文件导出失败')
                    : '正在导出您选择的文件...',
                style: VanHubTypography.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建进度部分
  Widget _buildProgressSection() {
    if (_progress == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      children: [
        // 总体进度
        _buildProgressCard(
          title: '总体进度',
          current: _progress!.completedItems,
          total: _progress!.totalItems,
          percentage: _progress!.percentage,
          color: VanHubBrandColors.primary,
        ),
        
        const SizedBox(height: VanHubSpacing.md),
        
        // 当前项目
        if (_progress!.currentItem != null) ...[
          Container(
            padding: const EdgeInsets.all(VanHubSpacing.md),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: VanHubSpacing.sm),
                Expanded(
                  child: Text(
                    '正在导出: ${_progress!.currentItem}',
                    style: VanHubTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: VanHubSpacing.md),
        ],
        
        // 统计信息
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: '已完成',
                value: _progress!.completedItems.toString(),
                color: Colors.green,
                icon: Icons.check_circle,
              ),
            ),
            const SizedBox(width: VanHubSpacing.sm),
            Expanded(
              child: _buildStatCard(
                title: '失败',
                value: _progress!.failedItems.toString(),
                color: Colors.red,
                icon: Icons.error,
              ),
            ),
            const SizedBox(width: VanHubSpacing.sm),
            Expanded(
              child: _buildStatCard(
                title: '剩余',
                value: (_progress!.totalItems - _progress!.completedItems - _progress!.failedItems).toString(),
                color: Colors.orange,
                icon: Icons.pending,
              ),
            ),
          ],
        ),
        
        // 时间信息
        if (_progress!.estimatedTimeRemaining != null) ...[
          const SizedBox(height: VanHubSpacing.md),
          Container(
            padding: const EdgeInsets.all(VanHubSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '已用时间: ${_formatDuration(_progress!.elapsedTime)}',
                  style: VanHubTypography.bodySmall,
                ),
                Text(
                  '预计剩余: ${_formatDuration(_progress!.estimatedTimeRemaining!)}',
                  style: VanHubTypography.bodySmall,
                ),
              ],
            ),
          ),
        ],
        
        // 错误信息
        if (_progress!.errors.isNotEmpty) ...[
          const SizedBox(height: VanHubSpacing.md),
          Container(
            padding: const EdgeInsets.all(VanHubSpacing.md),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red.shade600, size: 20),
                    const SizedBox(width: VanHubSpacing.xs),
                    Text(
                      '错误信息',
                      style: VanHubTypography.titleSmall.copyWith(
                        color: Colors.red.shade800,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: VanHubSpacing.sm),
                ...(_progress!.errors.take(3).map((error) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• $error',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.red.shade700,
                    ),
                  ),
                ))),
                if (_progress!.errors.length > 3)
                  Text(
                    '... 还有 ${_progress!.errors.length - 3} 个错误',
                    style: VanHubTypography.bodySmall.copyWith(
                      color: Colors.red.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建结果部分
  Widget _buildResultSection() {
    if (_result == null) return const SizedBox.shrink();

    return Column(
      children: [
        // 结果摘要
        Container(
          padding: const EdgeInsets.all(VanHubSpacing.md),
          decoration: BoxDecoration(
            color: _result!.isSuccessful ? Colors.green.shade50 : Colors.orange.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _result!.isSuccessful ? Colors.green.shade200 : Colors.orange.shade200,
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildResultStat('成功', _result!.successCount.toString(), Colors.green),
                  _buildResultStat('失败', _result!.failureCount.toString(), Colors.red),
                  _buildResultStat('总计', _result!.totalCount.toString(), Colors.blue),
                ],
              ),
              const SizedBox(height: VanHubSpacing.md),
              Text(
                '总用时: ${_formatDuration(_result!.totalTime)}',
                style: VanHubTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        
        // 压缩包信息
        if (_result!.archiveFile != null) ...[
          const SizedBox(height: VanHubSpacing.md),
          Container(
            padding: const EdgeInsets.all(VanHubSpacing.md),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.archive, color: Colors.blue.shade600),
                const SizedBox(width: VanHubSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '压缩包已创建',
                        style: VanHubTypography.titleSmall.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        _result!.archiveFile!.path.split('/').last,
                        style: VanHubTypography.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: 打开文件位置
                  },
                  icon: const Icon(Icons.folder_open),
                  tooltip: '打开文件位置',
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建进度卡片
  Widget _buildProgressCard({
    required String title,
    required int current,
    required int total,
    required double percentage,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: VanHubTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '$current / $total',
                style: VanHubTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: VanHubSpacing.sm),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          const SizedBox(height: VanHubSpacing.xs),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(VanHubSpacing.sm),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: VanHubSpacing.xs),
          Text(
            value,
            style: VanHubTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建结果统计
  Widget _buildResultStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: VanHubTypography.headlineMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: VanHubTypography.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    if (_result != null) {
      return Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ),
          const SizedBox(width: VanHubSpacing.sm),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                // TODO: 打开导出文件夹
              },
              icon: const Icon(Icons.folder_open),
              label: const Text('打开文件夹'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubBrandColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _handleCancel,
            icon: const Icon(Icons.close),
            label: const Text('取消'),
          ),
        ),
        const SizedBox(width: VanHubSpacing.sm),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _handlePauseResume,
            icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
            label: Text(_isPaused ? '继续' : '暂停'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isPaused ? Colors.green : Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// 处理取消
  Future<void> _handleCancel() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认取消'),
        content: const Text('确定要取消批量导出吗？已导出的文件将保留。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('继续导出'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确认取消'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await widget.batchExportService.cancelBatchExport(widget.batchId);
      setState(() {
        _isCancelled = true;
      });
      widget.onCancelled?.call();
      Navigator.of(context).pop();
    }
  }

  /// 处理暂停/恢复
  Future<void> _handlePauseResume() async {
    if (_isPaused) {
      await widget.batchExportService.resumeBatchExport(widget.batchId);
    } else {
      await widget.batchExportService.pauseBatchExport(widget.batchId);
    }
    
    setState(() {
      _isPaused = !_isPaused;
    });
  }

  /// 格式化持续时间
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '$hours时$minutes分$seconds秒';
    } else if (minutes > 0) {
      return '$minutes分$seconds秒';
    } else {
      return '$seconds秒';
    }
  }
}
