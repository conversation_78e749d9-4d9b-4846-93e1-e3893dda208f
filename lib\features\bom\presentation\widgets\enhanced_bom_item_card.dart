import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../domain/entities/bom_item.dart';

/// 增强的BOM物料卡片 - 显示使用情况和关联日志
class EnhancedBomItemCard extends ConsumerWidget {
  final BomItem item;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onStatusChange;

  const EnhancedBomItemCard({
    super.key,
    required this.item,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态行
              _buildHeaderRow(context),
              
              const SizedBox(height: 8),
              
              // 物料详细信息
              _buildItemDetails(context),
              
              const SizedBox(height: 12),
              
              // 成本和数量信息
              _buildCostAndQuantityRow(context),
              
              const SizedBox(height: 12),
              
              // 使用情况和关联日志
              _buildUsageSection(context, ref),
              
              const SizedBox(height: 12),
              
              // 底部操作行
              _buildActionRow(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建标题和状态行
  Widget _buildHeaderRow(BuildContext context) {
    return Row(
      children: [
        // 分类图标
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getCategoryColor(item.category),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCategoryIcon(item.category),
            color: Colors.white,
            size: 20,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 物料名称和描述
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.materialName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (item.description.isNotEmpty) ...[
                const SizedBox(height: 2),
                Text(
                  item.description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
        
        // 状态标签
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(item.status),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatusText(item.status),
            style: const TextStyle(
              fontSize: 10,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        // 操作菜单
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                onEdit?.call();
                break;
              case 'delete':
                onDelete?.call();
                break;
              case 'status':
                onStatusChange?.call();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'status',
              child: Row(
                children: [
                  Icon(Icons.update, size: 16),
                  SizedBox(width: 8),
                  Text('更新状态'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('删除', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          child: const Icon(Icons.more_vert, size: 20),
        ),
      ],
    );
  }

  /// 构建物料详细信息
  Widget _buildItemDetails(BuildContext context) {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: [
        if (item.brand != null) ...[
          _buildInfoChip(
            icon: Icons.business,
            label: '品牌',
            value: item.brand!,
            color: Colors.blue,
          ),
        ],
        if (item.model != null) ...[
          _buildInfoChip(
            icon: Icons.model_training,
            label: '型号',
            value: item.model!,
            color: Colors.green,
          ),
        ],
        if (item.supplier != null) ...[
          _buildInfoChip(
            icon: Icons.store,
            label: '供应商',
            value: item.supplier!,
            color: Colors.orange,
          ),
        ],
      ],
    );
  }

  /// 构建信息芯片
  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            '$label: $value',
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建成本和数量行
  Widget _buildCostAndQuantityRow(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 数量信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '数量',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${item.quantity}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // 单价信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '单价',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '¥${item.unitPrice.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // 总价信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '总价',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '¥${item.totalCost.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建使用情况区域
  Widget _buildUsageSection(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                size: 16,
                color: Colors.blue.shade700,
              ),
              const SizedBox(width: 4),
              Text(
                '使用情况',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 时间线信息
          _buildTimelineInfo(context),
          
          const SizedBox(height: 8),
          
          // 关联日志信息
          _buildRelatedLogsInfo(context, ref),
        ],
      ),
    );
  }

  /// 构建时间线信息
  Widget _buildTimelineInfo(BuildContext context) {
    return Row(
      children: [
        // 计划日期
        if (item.plannedDate != null) ...[
          _buildDateInfo(
            label: '计划',
            date: item.plannedDate!,
            icon: Icons.schedule,
            color: Colors.blue,
          ),
          const SizedBox(width: 12),
        ],
        
        // 采购日期
        if (item.purchasedDate != null) ...[
          _buildDateInfo(
            label: '采购',
            date: item.purchasedDate!,
            icon: Icons.shopping_cart,
            color: Colors.orange,
          ),
          const SizedBox(width: 12),
        ],
        
        // 使用日期
        if (item.usedDate != null) ...[
          _buildDateInfo(
            label: '使用',
            date: item.usedDate!,
            icon: Icons.build,
            color: Colors.green,
          ),
        ],
      ],
    );
  }

  /// 构建日期信息
  Widget _buildDateInfo({
    required String label,
    required DateTime date,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: color),
          const SizedBox(width: 2),
          Text(
            '$label: ${date.month}/${date.day}',
            style: TextStyle(
              fontSize: 9,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建关联日志信息
  Widget _buildRelatedLogsInfo(BuildContext context, WidgetRef ref) {
    // 这里应该根据物料ID查询关联的改装日志
    // 为了简化，先显示占位信息
    return Row(
      children: [
        Icon(
          Icons.edit_note,
          size: 12,
          color: Colors.blue.shade600,
        ),
        const SizedBox(width: 4),
        Text(
          '在 2 个改装日志中使用',
          style: TextStyle(
            fontSize: 10,
            color: Colors.blue.shade600,
          ),
        ),
        const Spacer(),
        TextButton(
          onPressed: () {
            // TODO: 显示关联的改装日志
          },
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            '查看详情',
            style: TextStyle(
              fontSize: 10,
              color: Colors.blue.shade700,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建操作行
  Widget _buildActionRow(BuildContext context) {
    return Row(
      children: [
        // 逾期警告
        if (item.isOverdue) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.warning,
                  size: 12,
                  color: Colors.red.shade700,
                ),
                const SizedBox(width: 4),
                Text(
                  '已逾期',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
        ],
        
        const Spacer(),
        
        // 快速状态切换按钮
        _buildQuickStatusButton(context),
      ],
    );
  }

  /// 构建快速状态切换按钮
  Widget _buildQuickStatusButton(BuildContext context) {
    final nextStatus = _getNextStatus(item.status);
    if (nextStatus == null) return const SizedBox.shrink();

    return ElevatedButton.icon(
      onPressed: onStatusChange,
      icon: Icon(_getStatusIcon(nextStatus), size: 14),
      label: Text(_getStatusText(nextStatus)),
      style: ElevatedButton.styleFrom(
        backgroundColor: _getStatusColor(nextStatus),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        textStyle: const TextStyle(fontSize: 11),
      ),
    );
  }

  // 辅助方法
  Color _getCategoryColor(String? category) {
    if (category == null) return Colors.grey;
    
    final colors = {
      '电气': Colors.yellow.shade700,
      '水路': Colors.blue,
      '燃气': Colors.red,
      '家具': Colors.brown,
      '装饰': Colors.purple,
      '工具': Colors.grey,
      '其他': Colors.teal,
    };
    
    return colors[category] ?? Colors.grey;
  }

  IconData _getCategoryIcon(String? category) {
    if (category == null) return Icons.category;
    
    final icons = {
      '电气': Icons.electrical_services,
      '水路': Icons.water_drop,
      '燃气': Icons.local_fire_department,
      '家具': Icons.chair,
      '装饰': Icons.palette,
      '工具': Icons.build,
      '其他': Icons.category,
    };
    
    return icons[category] ?? Icons.category;
  }

  Color _getStatusColor(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return Colors.grey;
      case BomItemStatus.ordered:
        return Colors.orange;
      case BomItemStatus.received:
        return Colors.blue;
      case BomItemStatus.installed:
        return Colors.green;
      case BomItemStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return Icons.schedule;
      case BomItemStatus.ordered:
        return Icons.shopping_cart;
      case BomItemStatus.received:
        return Icons.build;
      case BomItemStatus.installed:
        return Icons.check_circle;
      case BomItemStatus.cancelled:
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(BomItemStatus status) {
    switch (status) {
      case BomItemStatus.pending:
        return '待采购';
      case BomItemStatus.ordered:
        return '已下单';
      case BomItemStatus.received:
        return '已收货';
      case BomItemStatus.installed:
        return '已安装';
      case BomItemStatus.cancelled:
        return '已取消';
      default:
        return '未知';
    }
  }

  BomItemStatus? _getNextStatus(BomItemStatus currentStatus) {
    switch (currentStatus) {
      case BomItemStatus.pending:
        return BomItemStatus.ordered;
      case BomItemStatus.ordered:
        return BomItemStatus.received;
      case BomItemStatus.received:
        return BomItemStatus.installed;
      case BomItemStatus.installed:
        return null; // 已完成，无下一状态
      case BomItemStatus.cancelled:
        return null; // 已取消，无下一状态
      default:
        return null;
    }
  }
}
