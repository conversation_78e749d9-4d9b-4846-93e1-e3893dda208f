import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/material.dart' as domain;
import '../../domain/entities/search_criteria.dart';
import '../providers/material_search_provider.dart';
import '../../../../core/design_system/foundation/colors/colors.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';

/// 材料库智能搜索组件
/// 专门用于材料库与BOM的智能联动，提供流畅的搜索和添加体验
class MaterialSmartSearchWidget extends ConsumerStatefulWidget {
  final String? projectId;
  final Function(domain.Material)? onMaterialSelected;
  final Function(domain.Material)? onAddToBom;
  final bool showAddToBomButton;
  final String? initialQuery;
  final String? hintText;

  const MaterialSmartSearchWidget({
    super.key,
    this.projectId,
    this.onMaterialSelected,
    this.onAddToBom,
    this.showAddToBomButton = true,
    this.initialQuery,
    this.hintText,
  });

  @override
  ConsumerState<MaterialSmartSearchWidget> createState() => _MaterialSmartSearchWidgetState();
}

class _MaterialSmartSearchWidgetState extends ConsumerState<MaterialSmartSearchWidget> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();
  Timer? _debounceTimer;
  bool _isSearching = false;
  bool _showResults = false;
  String _selectedCategory = '全部';

  final List<String> _categories = [
    '全部', '电气设备', '水路系统', '储物系统', '床铺系统', 
    '厨房系统', '卫浴系统', '外观系统', '底盘系统', '安全设备', '其他'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
    }
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus && _searchController.text.isNotEmpty) {
      setState(() {
        _showResults = true;
      });
      _performSearch();
    }
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    
    if (query.trim().isEmpty) {
      setState(() {
        _showResults = false;
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _showResults = true;
    });

    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch();
    });
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    final criteria = SearchCriteria(
      query: query,
      category: _selectedCategory == '全部' ? null : _selectedCategory,
      limit: 20,
      offset: 0,
    );

    ref.read(materialSearchStateProvider.notifier).search(criteria).then((_) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchHeader(),
        if (_showResults) _buildSearchResults(),
      ],
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: EdgeInsets.all(VanHubSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 搜索栏
          TextField(
            controller: _searchController,
            focusNode: _focusNode,
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: widget.hintText ?? '搜索材料名称、品牌、型号...',
              prefixIcon: Icon(
                Icons.search,
                color: VanHubColors.primary,
              ),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_isSearching)
                    Container(
                      width: 20,
                      height: 20,
                      margin: EdgeInsets.only(right: VanHubSpacing.xs),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(VanHubColors.primary),
                      ),
                    ),
                  if (_searchController.text.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _showResults = false;
                          _isSearching = false;
                        });
                      },
                    ),
                ],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: VanHubColors.primary),
              ),
            ),
          ),
          
          SizedBox(height: VanHubSpacing.sm),
          
          // 分类筛选
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return Container(
                  margin: EdgeInsets.only(right: VanHubSpacing.xs),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = selected ? category : '全部';
                      });
                      if (_searchController.text.isNotEmpty) {
                        _performSearch();
                      }
                    },
                    selectedColor: VanHubColors.primary.withValues(alpha: 0.2),
                    checkmarkColor: VanHubColors.primary,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    final searchState = ref.watch(materialSearchStateProvider);
    
    return Container(
      margin: EdgeInsets.only(top: VanHubSpacing.sm),
      constraints: const BoxConstraints(maxHeight: 400),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: searchState.when(
        data: (materials) {
          if (materials.isEmpty) {
            return _buildEmptyResults();
          }

          return ListView.separated(
            shrinkWrap: true,
            itemCount: materials.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Colors.grey.shade200,
            ),
            itemBuilder: (context, index) {
              final material = materials[index];
              return _buildMaterialResultItem(material);
            },
          );
        },
        loading: () => SizedBox(
          height: 100,
          child: const LoadingWidget(message: '搜索中...'),
        ),
        error: (error, stack) => SizedBox(
          height: 100,
          child: ErrorDisplayWidget(
            message: '搜索失败: ${error.toString()}',
            onRetry: _performSearch,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyResults() {
    return SizedBox(
      height: 100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 32,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: VanHubSpacing.xs),
            Text(
              '暂无搜索结果',
              style: VanHubTypography.bodyMedium.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            Text(
              '尝试调整搜索关键词或筛选条件',
              style: VanHubTypography.bodySmall.copyWith(
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaterialResultItem(domain.Material material) {
    return ListTile(
      contentPadding: EdgeInsets.all(VanHubSpacing.md),
      leading: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: VanHubColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.inventory_2,
          color: VanHubColors.primary,
        ),
      ),
      title: Text(
        material.name,
        style: VanHubTypography.titleSmall,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (material.brand != null) ...[
            Text(
              '品牌: ${material.brand}',
              style: VanHubTypography.bodySmall,
            ),
            SizedBox(height: 2),
          ],
          ...[
          Text(
            '分类: ${material.category}',
            style: VanHubTypography.bodySmall.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          SizedBox(height: 2),
        ],
          ...[
          Text(
            '价格: ¥${material.price.toStringAsFixed(2)}',
            style: VanHubTypography.bodySmall.copyWith(
              color: VanHubColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
        ],
      ),
      trailing: widget.showAddToBomButton && widget.projectId != null
          ? ElevatedButton.icon(
              onPressed: () => _showAddToBomDialog(material),
              icon: const Icon(Icons.add_shopping_cart, size: 16),
              label: const Text('添加'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: const Size(80, 32),
              ),
            )
          : const Icon(Icons.chevron_right),
      onTap: () {
        if (widget.onMaterialSelected != null) {
          widget.onMaterialSelected!(material);
        }
        setState(() {
          _showResults = false;
        });
        _focusNode.unfocus();
      },
    );
  }

  void _showAddToBomDialog(domain.Material material) {
    if (widget.projectId == null) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('添加 ${material.name} 到BOM'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('确认添加'),
              ),
            ],
          ),
        ),
      ),
    ).then((result) {
      if (result != null && widget.onAddToBom != null) {
        widget.onAddToBom!(material);
      }
    });
  }
}
