import 'package:fpdart/fpdart.dart';
import 'dart:io';
import '../../../../core/errors/failures.dart';
import '../entities/project.dart';
import '../../data/models/project_export_config.dart';

/// 项目导出服务接口
abstract class ProjectExportService {
  /// 导出项目完整报告为PDF
  Future<Either<Failure, File>> exportProjectReport({
    required String projectId,
    String? filePath,
    ProjectExportConfig? config,
  });

  /// 导出项目数据为JSON
  Future<Either<Failure, File>> exportProjectData({
    required String projectId,
    String? filePath,
    bool includeMedia = false,
  });

  /// 导出项目概览为PDF
  Future<Either<Failure, File>> exportProjectOverview({
    required String projectId,
    String? filePath,
  });

  /// 导出项目进度报告
  Future<Either<Failure, File>> exportProgressReport({
    required String projectId,
    String? filePath,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 导出成本分析报告
  Future<Either<Failure, File>> exportCostAnalysisReport({
    required String projectId,
    String? filePath,
  });

  /// 获取支持的导出格式
  List<String> getSupportedFormats();

  /// 获取默认导出路径
  Future<String> getDefaultExportPath();

  /// 预览导出内容
  Future<Either<Failure, Map<String, dynamic>>> previewExportContent({
    required String projectId,
    required String exportType,
  });
}
