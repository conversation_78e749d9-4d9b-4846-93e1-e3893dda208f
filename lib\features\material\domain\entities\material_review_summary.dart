import 'package:freezed_annotation/freezed_annotation.dart';

part 'material_review_summary.freezed.dart';
part 'material_review_summary.g.dart';

/// 材料评价等级枚举
enum MaterialReviewRating {
  /// 1星 - 很差
  terrible,
  /// 2星 - 较差
  poor,
  /// 3星 - 一般
  average,
  /// 4星 - 良好
  good,
  /// 5星 - 优秀
  excellent,
}

/// 评价维度评分
@freezed
class ReviewAspects with _$ReviewAspects {
  const factory ReviewAspects({
    /// 质量评分
    @Default(0.0) double quality,
    /// 性价比评分
    @Default(0.0) double value,
    /// 耐用性评分
    @Default(0.0) double durability,
    /// 安装难度评分
    @Default(0.0) double installation,
  }) = _ReviewAspects;

  factory ReviewAspects.fromJson(Map<String, dynamic> json) =>
      _$ReviewAspectsFromJson(json);
}

/// Material review summary entity for aggregated review data
@freezed
class MaterialReviewSummary with _$MaterialReviewSummary {
  const factory MaterialReviewSummary({
    required String materialId,
    required int totalReviews,
    required double averageRating,
    required Map<MaterialReviewRating, int> ratingDistribution,
    required int recommendationCount,
    required int totalRecommendations,
    required DateTime lastUpdated,
    @Default([]) List<String> topPros,
    @Default([]) List<String> topCons,
    @Default(0) int verifiedPurchaseCount,
    @Default(0) int reviewsWithImages,
    @Default(0) int detailedReviewsCount,
    ReviewAspects? averageAspectRatings,
    Map<String, dynamic>? metadata,
    // Additional properties for widget compatibility
    @Default(0.0) double qualityAverage,
    @Default(0.0) double valueAverage,
    @Default(0.0) double durabilityAverage,
    @Default(0.0) double installationAverage,
    @Default(0.0) double recommendationScore,
  }) = _MaterialReviewSummary;

  factory MaterialReviewSummary.fromJson(Map<String, dynamic> json) => 
      _$MaterialReviewSummaryFromJson(json);
}

/// Material review summary business logic extensions
extension MaterialReviewSummaryX on MaterialReviewSummary {
  /// Get overall rating percentage (0-100)
  double get overallRatingPercentage => (averageRating / 5.0) * 100;

  /// Get recommendation percentage
  double get recommendationPercentage {
    return totalRecommendations > 0 
        ? (recommendationCount / totalRecommendations) * 100 
        : 0.0;
  }

  /// Check if material is highly rated
  bool get isHighlyRated => averageRating >= 4.0 && totalReviews >= 5;

  /// Check if material is well reviewed
  bool get isWellReviewed => totalReviews >= 10;

  /// Get rating quality description
  String get ratingQualityDescription {
    if (totalReviews == 0) return 'No reviews yet';
    if (totalReviews < 5) return 'Limited reviews';
    if (totalReviews < 20) return 'Some reviews';
    if (totalReviews < 50) return 'Many reviews';
    return 'Extensive reviews';
  }

  /// Get most common rating
  MaterialReviewRating get mostCommonRating {
    var maxCount = 0;
    var mostCommon = MaterialReviewRating.average;
    
    for (final entry in ratingDistribution.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        mostCommon = entry.key;
      }
    }
    
    return mostCommon;
  }

  /// Get rating distribution percentages
  Map<MaterialReviewRating, double> get ratingDistributionPercentages {
    final percentages = <MaterialReviewRating, double>{};
    
    for (final entry in ratingDistribution.entries) {
      percentages[entry.key] = totalReviews > 0 
          ? (entry.value / totalReviews) * 100 
          : 0.0;
    }
    
    return percentages;
  }

  /// Get positive review percentage (4-5 stars)
  double get positiveReviewPercentage {
    final positiveCount = (ratingDistribution[MaterialReviewRating.excellent] ?? 0) +
                         (ratingDistribution[MaterialReviewRating.good] ?? 0);
    return totalReviews > 0 ? (positiveCount / totalReviews) * 100 : 0.0;
  }

  /// Get negative review percentage (1-2 stars)
  double get negativeReviewPercentage {
    final negativeCount = (ratingDistribution[MaterialReviewRating.terrible] ?? 0) +
                         (ratingDistribution[MaterialReviewRating.poor] ?? 0);
    return totalReviews > 0 ? (negativeCount / totalReviews) * 100 : 0.0;
  }

  /// Get verified purchase percentage
  double get verifiedPurchasePercentage {
    return totalReviews > 0 ? (verifiedPurchaseCount / totalReviews) * 100 : 0.0;
  }

  /// Get reviews with images percentage
  double get reviewsWithImagesPercentage {
    return totalReviews > 0 ? (reviewsWithImages / totalReviews) * 100 : 0.0;
  }

  /// Get detailed reviews percentage
  double get detailedReviewsPercentage {
    return totalReviews > 0 ? (detailedReviewsCount / totalReviews) * 100 : 0.0;
  }

  /// Get trust score based on various factors
  double get trustScore {
    double score = 0.0;
    
    // Base score from number of reviews
    if (totalReviews >= 50) {
      score += 30;
    } else if (totalReviews >= 20) score += 20;
    else if (totalReviews >= 10) score += 15;
    else if (totalReviews >= 5) score += 10;
    
    // Verified purchase bonus
    score += verifiedPurchasePercentage * 0.3;
    
    // Detailed reviews bonus
    score += detailedReviewsPercentage * 0.2;
    
    // Reviews with images bonus
    score += reviewsWithImagesPercentage * 0.1;
    
    // Rating consistency bonus (less spread = more consistent)
    final ratingSpread = _calculateRatingSpread();
    score += (1.0 - ratingSpread) * 10;
    
    return score.clamp(0, 100);
  }

  /// Calculate rating spread (0-1, where 0 is very consistent)
  double _calculateRatingSpread() {
    if (totalReviews == 0) return 0.0;
    
    final percentages = ratingDistributionPercentages;
    final maxPercentage = percentages.values.reduce((a, b) => a > b ? a : b);
    
    return 1.0 - (maxPercentage / 100.0);
  }

  /// Get summary description
  String get summaryDescription {
    if (totalReviews == 0) {
      return 'No reviews available for this material yet.';
    }
    
    final ratingText = averageRating >= 4.0 ? 'highly rated' :
                      averageRating >= 3.0 ? 'well rated' :
                      averageRating >= 2.0 ? 'moderately rated' : 'poorly rated';
    
    final recommendText = recommendationPercentage >= 80 ? 'highly recommended' :
                         recommendationPercentage >= 60 ? 'recommended' :
                         recommendationPercentage >= 40 ? 'somewhat recommended' : 'not recommended';
    
    return 'This material is $ratingText with ${averageRating.toStringAsFixed(1)} stars '
           'from $totalReviews reviews and is $recommendText by users.';
  }

  /// Check if summary needs update
  bool get needsUpdate {
    final now = DateTime.now();
    final daysSinceUpdate = now.difference(lastUpdated).inDays;
    return daysSinceUpdate > 7; // Update weekly
  }
}
