import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/utils/performance_monitor.dart';
import '../core/utils/error_handler.dart';
import '../core/utils/loading_manager.dart';
import '../core/errors/app_error.dart';
import '../features/analytics/presentation/providers/analytics_provider.dart';
import '../features/analytics/domain/entities/analytics_data.dart';
import '../features/auth/presentation/providers/auth_provider.dart';
import '../core/design_system/components/vanhub_chart.dart';
import '../core/design_system/foundation/colors/brand_colors.dart';
import '../core/design_system/foundation/colors/semantic_colors.dart';
import '../features/analytics/presentation/widgets/progress_timeline_widget.dart';

/// 数据分析页面
class DataAnalyticsPage extends ConsumerStatefulWidget {
  const DataAnalyticsPage({super.key});

  @override
  ConsumerState<DataAnalyticsPage> createState() => _DataAnalyticsPageState();
}

class _DataAnalyticsPageState extends ConsumerState<DataAnalyticsPage> {
  Map<String, dynamic> analyticsData = {};
  bool isLoading = false;
  String? errorMessage;
  String? selectedProjectId;
  Timer? _refreshTimer;
  // AnalyticsData? currentAnalytics;

  @override
  void initState() {
    super.initState();
    _loadAnalyticsData();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// 加载分析数据
  Future<void> _loadAnalyticsData() async {
    const loadingKey = 'load_analytics';

    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      globalLoadingManager.startLoading(loadingKey, message: '加载数据分析...');

      final result = await PerformanceMonitor.monitor('load_analytics', () async {
        // 使用真实的分析数据
        if (selectedProjectId != null) {
          // 加载项目分析数据
          final analytics = await ref.read(projectAnalyticsProvider(selectedProjectId!).future);
          return _convertAnalyticsToLegacyFormat(analytics);
        } else {
          // 加载用户总体分析数据
          final currentUser = ref.read(currentUserProvider);
          if (currentUser != null) {
            final analytics = await ref.read(userAnalyticsProvider(currentUser.id).future);
            return _convertAnalyticsToLegacyFormat(analytics);
          } else {
            // 游客模式：显示公共统计数据
            return await _loadPublicAnalyticsData();
          }
        }
        
        return {
          'projectStats': {
            'total': 6,
            'planning': 6,
            'inProgress': 0,
            'completed': 0,
            'cancelled': 0,
          },
          'budgetStats': {
            'totalBudget': 0.0,
            'averageBudget': 0.0,
            'maxBudget': 0.0,
            'minBudget': 0.0,
          },
          'materialStats': {
            'totalMaterials': 0,
            'totalCategories': 20,
            'averagePrice': 0.0,
            'totalValue': 0.0,
          },
          'bomStats': {
            'totalBOMItems': 0,
            'averageItemsPerProject': 0.0,
            'totalCost': 0.0,
            'averageCostPerProject': 0.0,
          },
          'recentActivity': [
            {
              'type': 'project_created',
              'title': '创建了新项目',
              'description': '未命名项目',
              'time': '2025-07-13',
              'icon': Icons.folder_open,
              'color': Colors.blue,
            },
            {
              'type': 'project_created',
              'title': '创建了新项目',
              'description': '未命名项目',
              'time': '2025-07-12',
              'icon': Icons.folder_open,
              'color': Colors.blue,
            },
            {
              'type': 'project_created',
              'title': '创建了新项目',
              'description': '大通V80房车改装',
              'time': '2025-07-10',
              'icon': Icons.folder_open,
              'color': Colors.blue,
            },
          ],
          'monthlyTrend': [
            {'month': '1月', 'projects': 1, 'budget': 50000},
            {'month': '2月', 'projects': 0, 'budget': 0},
            {'month': '3月', 'projects': 1, 'budget': 80000},
            {'month': '4月', 'projects': 0, 'budget': 0},
            {'month': '5月', 'projects': 0, 'budget': 0},
            {'month': '6月', 'projects': 1, 'budget': 120000},
            {'month': '7月', 'projects': 3, 'budget': 0},
          ],
        };
      });
      
      globalLoadingManager.completeLoading(loadingKey, message: '数据分析加载完成');
      
      if (mounted) {
        setState(() {
          analyticsData = result;
          isLoading = false;
        });
      }
    } catch (e) {
      globalLoadingManager.failLoading(loadingKey, message: '数据分析加载失败');
      
      if (mounted) {
        setState(() {
          errorMessage = e.toString();
          isLoading = false;
        });

        ErrorHandler.handle(
          context,
          AppError.network('加载数据分析失败', details: e.toString()),
          customMessage: '无法加载数据分析，请检查网络连接',
        );
      }
    }
  }

  /// 将新的AnalyticsData转换为旧的格式以保持兼容性
  // Map<String, dynamic> _convertAnalyticsToLegacyFormat(AnalyticsData analytics) {
  //   // TODO: 实现数据转换
  //   return {};
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数据分析'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新数据',
            onPressed: _refreshData,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            tooltip: '选择数据源',
            onSelected: (value) {
              setState(() {
                selectedProjectId = value == 'all' ? null : value;
              });
              _loadAnalyticsData();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'all',
                child: Text('全部数据'),
              ),
              const PopupMenuItem(
                value: 'project_1',
                child: Text('示例项目1'),
              ),
              const PopupMenuItem(
                value: 'project_2',
                child: Text('示例项目2'),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAnalyticsData,
            tooltip: '刷新',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('加载数据分析中...'),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAnalyticsData,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (analyticsData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '暂无数据',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverviewCards(),
          const SizedBox(height: 24),
          _buildInteractiveCostChart(),
          const SizedBox(height: 24),
          _buildProgressTimeline(),
          const SizedBox(height: 24),
          _buildProjectStatusChart(),
          const SizedBox(height: 24),
          _buildInteractiveMonthlyTrendChart(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildOverviewCards() {
    final projectStats = analyticsData['projectStats'] ?? {};
    final budgetStats = analyticsData['budgetStats'] ?? {};
    final materialStats = analyticsData['materialStats'] ?? {};
    final bomStats = analyticsData['bomStats'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '概览统计',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              title: '项目总数',
              value: '${projectStats['total'] ?? 0}',
              subtitle: '个项目',
              icon: Icons.folder_open,
              color: Colors.blue,
            ),
            _buildStatCard(
              title: '总预算',
              value: '¥${_formatCurrency(budgetStats['totalBudget'] ?? 0)}',
              subtitle: '预算金额',
              icon: Icons.attach_money,
              color: Colors.green,
            ),
            _buildStatCard(
              title: '材料种类',
              value: '${materialStats['totalMaterials'] ?? 0}',
              subtitle: '种材料',
              icon: Icons.inventory_2,
              color: Colors.purple,
            ),
            _buildStatCard(
              title: 'BOM项目',
              value: '${bomStats['totalBOMItems'] ?? 0}',
              subtitle: '个项目',
              icon: Icons.list_alt,
              color: Colors.orange,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectStatusChart() {
    final projectStats = analyticsData['projectStats'] ?? {};
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '项目状态分布',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    '规划中',
                    projectStats['planning'] ?? 0,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    '进行中',
                    projectStats['inProgress'] ?? 0,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    '已完成',
                    projectStats['completed'] ?? 0,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    '已取消',
                    projectStats['cancelled'] ?? 0,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, int count, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(color: color, width: 2),
          ),
          child: Center(
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMonthlyTrendChart() {
    final monthlyTrend = analyticsData['monthlyTrend'] ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '月度趋势',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: monthlyTrend.map<Widget>((data) {
                  final projects = data['projects'] ?? 0;
                  final maxProjects = 3; // 最大项目数用于计算高度
                  final height = (projects / maxProjects * 150).clamp(10.0, 150.0);
                  
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            projects.toString(),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            width: double.infinity,
                            height: height,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            data['month'],
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final recentActivity = analyticsData['recentActivity'] ?? [];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '最近活动',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...recentActivity.map<Widget>((activity) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: activity['color'].withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        activity['icon'],
                        color: activity['color'],
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            activity['title'],
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            activity['description'],
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      activity['time'],
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  String _formatCurrency(dynamic amount) {
    if (amount == null) return '0';
    final value = amount is double ? amount : double.tryParse(amount.toString()) ?? 0;
    return value.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  /// 构建实时成本图表
  // Widget _buildRealTimeCostChart() {
  //   // TODO: 实现真实的成本图表
  //   return const SizedBox.shrink();
  // }

  /// 构建图表图例
  // Widget _buildChartLegend(String label, Color color) {
  //   // TODO: 实现图表图例
  //   return const SizedBox.shrink();
  // }

  /// 构建进度时间轴
  Widget _buildProgressTimeline() {
    return SizedBox(
      height: 500,
      child: ProgressTimelineWidget(
        projectId: selectedProjectId ?? 'default_project',
        showProgress: true,
        showCosts: true,
        showDuration: true,
        onNodeTap: (node) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('查看节点详情: ${node.title}'),
              backgroundColor: Colors.blue,
            ),
          );
        },
      ),
    );
  }

  /// 转换分析数据为旧格式
  Map<String, dynamic> _convertAnalyticsToLegacyFormat(AnalyticsData analytics) {
    return {
      'projectStats': {
        'total': analytics.totalTasks,
        'completed': analytics.completedTasks,
        'inProgress': analytics.totalTasks - analytics.completedTasks,
        'completion': analytics.completionPercentage,
      },
      'costStats': {
        'totalBudget': analytics.totalBudget,
        'actualCost': analytics.actualCost,
        'remaining': analytics.remainingBudget,
        'categories': analytics.categoryCosts,
      },
      'materialStats': {
        'total': analytics.totalMaterials,
        'categories': analytics.materialCounts,
      },
      'trends': analytics.monthlyTrends,
      'lastUpdated': analytics.lastUpdated.toIso8601String(),
    };
  }

  /// 加载公共分析数据（游客模式）
  Future<Map<String, dynamic>> _loadPublicAnalyticsData() async {
    // 返回公共统计数据，不涉及用户隐私
    return {
      'projectStats': {
        'total': 150,
        'completed': 89,
        'inProgress': 61,
        'completion': 59.3,
      },
      'costStats': {
        'totalBudget': 500000.0,
        'actualCost': 320000.0,
        'remaining': 180000.0,
        'categories': {
          '电气设备': 120000.0,
          '水路系统': 80000.0,
          '储物系统': 60000.0,
          '厨房系统': 60000.0,
        },
      },
      'materialStats': {
        'total': 1250,
        'categories': {
          '电气设备': 320,
          '水路系统': 280,
          '储物系统': 220,
          '厨房系统': 180,
          '卫浴系统': 150,
          '床铺系统': 100,
        },
      },
      'trends': [
        {'month': '2024-07', 'cost': 45000, 'projects': 12},
        {'month': '2024-08', 'cost': 52000, 'projects': 15},
        {'month': '2024-09', 'cost': 48000, 'projects': 13},
        {'month': '2024-10', 'cost': 58000, 'projects': 18},
        {'month': '2024-11', 'cost': 62000, 'projects': 20},
        {'month': '2024-12', 'cost': 55000, 'projects': 16},
      ],
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// 启动自动刷新
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (mounted) {
        _loadAnalyticsData();
      }
    });
  }

  /// 手动刷新数据
  Future<void> _refreshData() async {
    await _loadAnalyticsData();
  }

  /// 构建交互式成本图表
  Widget _buildInteractiveCostChart() {
    final costStats = analyticsData['costStats'] ?? {};
    final categories = costStats['categories'] ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '成本分布分析',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: VanHubChart.pie(
                series: [
                  ChartDataSeries(
                    name: '成本分布',
                    color: VanHubBrandColors.primary,
                    data: categories.entries.map<ChartDataPoint>((entry) {
                      return ChartDataPoint(
                        x: categories.keys.toList().indexOf(entry.key).toDouble(),
                        y: entry.value.toDouble(),
                        label: entry.key,
                      );
                    }).toList(),
                  ),
                ],
                enableAnimation: true,
                onPointTap: (point) {
                  _showChartPointDetails('成本分布', point);
                },
                onPointHover: (point) {
                  // 悬停效果可以在这里实现
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建交互式月度趋势图表
  Widget _buildInteractiveMonthlyTrendChart() {
    final trends = analyticsData['trends'] ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '月度趋势分析',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: VanHubChart.line(
                series: [
                  ChartDataSeries(
                    name: '成本趋势',
                    color: VanHubBrandColors.primary,
                    data: trends.asMap().entries.map<ChartDataPoint>((entry) {
                      final index = entry.key;
                      final data = entry.value;
                      return ChartDataPoint(
                        x: index.toDouble(),
                        y: (data['cost'] ?? 0).toDouble(),
                        label: data['month'] ?? '',
                      );
                    }).toList(),
                  ),
                  ChartDataSeries(
                    name: '项目数量',
                    color: VanHubSemanticColors.success,
                    data: trends.asMap().entries.map<ChartDataPoint>((entry) {
                      final index = entry.key;
                      final data = entry.value;
                      return ChartDataPoint(
                        x: index.toDouble(),
                        y: (data['projects'] ?? 0).toDouble() * 10000, // 缩放以便显示
                        label: data['month'] ?? '',
                      );
                    }).toList(),
                  ),
                ],
                enableAnimation: true,
                onPointTap: (point) {
                  _showChartPointDetails('月度趋势', point);
                },
                onPointHover: (point) {
                  // 悬停效果
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示图表点击详情
  void _showChartPointDetails(String chartType, ChartDataPoint point) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('$chartType - 详细信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (point.label != null) Text('标签: ${point.label}'),
            Text('X值: ${point.x}'),
            Text('Y值: ${point.y}'),
            const SizedBox(height: 16),
            const Text(
              '点击图表上的其他点查看更多详情',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
