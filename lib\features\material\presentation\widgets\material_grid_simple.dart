import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../domain/entities/material.dart' as MaterialEntity;

/// 简化的材料网格组件
class MaterialGridSimple extends ConsumerWidget {
  final List<MaterialEntity.Material> materials;
  final bool isLoading;
  final String searchQuery;
  final Function(MaterialEntity.Material)? onMaterialTap;
  final Function(MaterialEntity.Material)? onAddToBOM;

  const MaterialGridSimple({
    super.key,
    required this.materials,
    this.isLoading = false,
    this.searchQuery = '',
    this.onMaterialTap,
    this.onAddToBOM,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isLoading) {
      return _buildLoadingState();
    }

    if (materials.isEmpty) {
      return _buildEmptyState();
    }

    return _buildMaterialGrid();
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: VanHubDesignSystem.brandPrimary,
          ),
          SizedBox(height: VanHubDesignSystem.spacing4),
          Text(
            '正在加载材料库...',
            style: TextStyle(
              fontSize: VanHubDesignSystem.fontSizeBase,
              color: VanHubDesignSystem.neutralGray600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(VanHubDesignSystem.spacing6),
            decoration: BoxDecoration(
              color: VanHubDesignSystem.neutralGray100,
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radius2xl),
            ),
            child: Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: VanHubDesignSystem.neutralGray400,
            ),
          ),
          SizedBox(height: VanHubDesignSystem.spacing6),
          Text(
            searchQuery.isNotEmpty ? '未找到相关材料' : '材料库为空',
            style: TextStyle(
              fontSize: VanHubDesignSystem.fontSize2xl,
              fontWeight: VanHubDesignSystem.fontWeightSemiBold,
              color: VanHubDesignSystem.neutralGray700,
            ),
          ),
          SizedBox(height: VanHubDesignSystem.spacing3),
          Text(
            searchQuery.isNotEmpty 
                ? '尝试调整搜索条件或浏览其他分类'
                : '开始添加您的第一个材料',
            style: TextStyle(
              fontSize: VanHubDesignSystem.fontSizeBase,
              color: VanHubDesignSystem.neutralGray500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMaterialGrid() {
    return GridView.builder(
      padding: EdgeInsets.all(VanHubDesignSystem.spacing4),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(),
        childAspectRatio: 0.8,
        crossAxisSpacing: VanHubDesignSystem.spacing4,
        mainAxisSpacing: VanHubDesignSystem.spacing4,
      ),
      itemCount: materials.length,
      itemBuilder: (context, index) {
        return _buildMaterialCard(materials[index]);
      },
    );
  }

  int _getCrossAxisCount() {
    // 简单的响应式逻辑
    return 2; // 默认2列，可以根据屏幕宽度调整
  }

  Widget _buildMaterialCard(MaterialEntity.Material material) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
      ),
      child: InkWell(
        onTap: () => onMaterialTap?.call(material),
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
        child: Padding(
          padding: EdgeInsets.all(VanHubDesignSystem.spacing3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 材料图片/图标
              _buildMaterialImage(material),
              
              SizedBox(height: VanHubDesignSystem.spacing2),
              
              // 材料名称
              Text(
                material.name,
                style: TextStyle(
                  fontSize: VanHubDesignSystem.fontSizeBase,
                  fontWeight: VanHubDesignSystem.fontWeightMedium,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              SizedBox(height: VanHubDesignSystem.spacing1),
              
              // 分类
              Text(
                material.category,
                style: TextStyle(
                  fontSize: VanHubDesignSystem.fontSizeSm,
                  color: VanHubDesignSystem.neutralGray600,
                ),
              ),
              
              const Spacer(),
              
              // 操作按钮
              if (onAddToBOM != null)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => onAddToBOM?.call(material),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: VanHubDesignSystem.brandPrimary,
                      foregroundColor: VanHubDesignSystem.neutralWhite,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
                      ),
                    ),
                    child: Text(
                      '添加到BOM',
                      style: TextStyle(
                        fontSize: VanHubDesignSystem.fontSizeSm,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMaterialImage(MaterialEntity.Material material) {
    return Container(
      height: 80,
      width: double.infinity,
      decoration: BoxDecoration(
        color: VanHubDesignSystem.neutralGray100,
        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
      ),
      child: material.imageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
              child: Image.network(
                material.imageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultIcon();
                },
              ),
            )
          : _buildDefaultIcon(),
    );
  }

  Widget _buildDefaultIcon() {
    return Icon(
      Icons.inventory_2_outlined,
      size: 32,
      color: VanHubDesignSystem.neutralGray400,
    );
  }
}