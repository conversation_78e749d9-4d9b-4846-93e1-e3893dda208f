import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../domain/entities/category_spec_template.dart';

/// 规格字段输入/展示组件
/// 
/// 根据字段类型动态渲染不同的输入控件
class SpecificationFieldWidget extends StatefulWidget {
  final SpecField field;
  final dynamic value;
  final ValueChanged<dynamic>? onChanged;
  final bool isReadOnly;
  final bool showValidationErrors;

  const SpecificationFieldWidget({
    Key? key,
    required this.field,
    this.value,
    this.onChanged,
    this.isReadOnly = false,
    this.showValidationErrors = true,
  }) : super(key: key);

  @override
  State<SpecificationFieldWidget> createState() => _SpecificationFieldWidgetState();
}

class _SpecificationFieldWidgetState extends State<SpecificationFieldWidget> {
  late TextEditingController _textController;
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(
      text: _getDisplayValue(widget.value),
    );
  }

  @override
  void didUpdateWidget(SpecificationFieldWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _textController.text = _getDisplayValue(widget.value);
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(),
        const SizedBox(height: 8),
        _buildInputWidget(),
        if (_validationError != null && widget.showValidationErrors) ...[
          const SizedBox(height: 4),
          Text(
            _validationError!,
            style: TextStyle(
              color: Theme.of(context).colorScheme.error,
              fontSize: 12,
            ),
          ),
        ],
        if (widget.field.description != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.field.description!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建字段标签
  Widget _buildLabel() {
    return Row(
      children: [
        Text(
          widget.field.label,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        if (widget.field.required) ...[
          const SizedBox(width: 4),
          Text(
            '*',
            style: TextStyle(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
        if (widget.field.unit != null) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              widget.field.unit!,
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建输入控件
  Widget _buildInputWidget() {
    switch (widget.field.type) {
      case SpecFieldType.text:
        return _buildTextInput();
      case SpecFieldType.number:
      case SpecFieldType.decimal:
        return _buildNumberInput();
      case SpecFieldType.boolean:
        return _buildBooleanInput();
      case SpecFieldType.enumSingle:
        return _buildSingleSelectInput();
      case SpecFieldType.enumMultiple:
        return _buildMultiSelectInput();
      case SpecFieldType.date:
        return _buildDateInput();
      case SpecFieldType.rangeNumeric:
        return _buildNumericRangeInput();
      case SpecFieldType.dimensions:
        return _buildDimensionsInput();
      case SpecFieldType.voltage:
      case SpecFieldType.power:
      case SpecFieldType.capacity:
      case SpecFieldType.temperature:
      case SpecFieldType.pressure:
      case SpecFieldType.flowRate:
      case SpecFieldType.weight:
        return _buildSpecializedNumberInput();
      case SpecFieldType.color:
        return _buildColorInput();
      default:
        return _buildTextInput();
    }
  }

  /// 构建文本输入
  Widget _buildTextInput() {
    return TextFormField(
      controller: _textController,
      readOnly: widget.isReadOnly,
      maxLength: widget.field.stringLengthLimit?.maxLength,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: widget.field.defaultValue?.toString(),
        suffixText: widget.field.unit,
      ),
      onChanged: (value) {
        _validateAndNotify(value);
      },
    );
  }

  /// 构建数字输入
  Widget _buildNumberInput() {
    return TextFormField(
      controller: _textController,
      readOnly: widget.isReadOnly,
      keyboardType: widget.field.type == SpecFieldType.decimal
          ? const TextInputType.numberWithOptions(decimal: true)
          : TextInputType.number,
      inputFormatters: [
        if (widget.field.type == SpecFieldType.number)
          FilteringTextInputFormatter.digitsOnly
        else
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: widget.field.defaultValue?.toString(),
        suffixText: widget.field.unit,
      ),
      onChanged: (value) {
        final numValue = widget.field.type == SpecFieldType.decimal
            ? double.tryParse(value)
            : int.tryParse(value);
        _validateAndNotify(numValue);
      },
    );
  }

  /// 构建专业数字输入（带单位验证）
  Widget _buildSpecializedNumberInput() {
    return TextFormField(
      controller: _textController,
      readOnly: widget.isReadOnly,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        hintText: widget.field.defaultValue?.toString(),
        suffixText: widget.field.unit,
        prefixIcon: Icon(_getFieldTypeIcon()),
      ),
      onChanged: (value) {
        final numValue = double.tryParse(value);
        _validateAndNotify(numValue);
      },
    );
  }

  /// 构建布尔值输入
  Widget _buildBooleanInput() {
    return SwitchListTile(
      title: Text(widget.field.label),
      value: widget.value == true,
      onChanged: widget.isReadOnly ? null : (value) {
        _validateAndNotify(value);
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  /// 构建单选输入
  Widget _buildSingleSelectInput() {
    return DropdownButtonFormField<String>(
      value: widget.value?.toString(),
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
      ),
      items: widget.field.options?.map((option) => DropdownMenuItem(
        value: option.value,
        child: Text(option.label),
      )).toList(),
      onChanged: widget.isReadOnly ? null : (value) {
        _validateAndNotify(value);
      },
    );
  }

  /// 构建多选输入
  Widget _buildMultiSelectInput() {
    final selectedValues = widget.value is List 
        ? List<String>.from(widget.value) 
        : <String>[];

    return Column(
      children: widget.field.options?.map((option) => CheckboxListTile(
        title: Text(option.label),
        value: selectedValues.contains(option.value),
        onChanged: widget.isReadOnly ? null : (checked) {
          final newValues = List<String>.from(selectedValues);
          if (checked == true) {
            newValues.add(option.value);
          } else {
            newValues.remove(option.value);
          }
          _validateAndNotify(newValues);
        },
        contentPadding: EdgeInsets.zero,
      )).toList() ?? [],
    );
  }

  /// 构建日期输入
  Widget _buildDateInput() {
    return TextFormField(
      controller: _textController,
      readOnly: true,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        suffixIcon: Icon(Icons.calendar_today),
      ),
      onTap: widget.isReadOnly ? null : () async {
        final date = await showDatePicker(
          context: context,
          initialDate: widget.value is DateTime 
              ? widget.value 
              : DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime(2100),
        );
        if (date != null) {
          _validateAndNotify(date);
        }
      },
    );
  }

  /// 构建数值范围输入
  Widget _buildNumericRangeInput() {
    // 简化实现，实际应该有两个输入框
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: '最小值',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
        ),
        const SizedBox(width: 8),
        const Text('~'),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: '最大值',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
        ),
      ],
    );
  }

  /// 构建尺寸输入
  Widget _buildDimensionsInput() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: '长',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
        ),
        const SizedBox(width: 8),
        const Text('×'),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: '宽',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
        ),
        const SizedBox(width: 8),
        const Text('×'),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: '高',
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
        ),
      ],
    );
  }

  /// 构建颜色输入
  Widget _buildColorInput() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            controller: _textController,
            readOnly: widget.isReadOnly,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '输入颜色名称或代码',
            ),
            onChanged: (value) {
              _validateAndNotify(value);
            },
          ),
        ),
        const SizedBox(width: 8),
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _parseColor(widget.value?.toString()) ?? Colors.grey[300],
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  /// 获取字段类型图标
  IconData _getFieldTypeIcon() {
    switch (widget.field.type) {
      case SpecFieldType.voltage:
        return Icons.electrical_services;
      case SpecFieldType.power:
        return Icons.power;
      case SpecFieldType.capacity:
        return Icons.battery_full;
      case SpecFieldType.temperature:
        return Icons.thermostat;
      case SpecFieldType.pressure:
        return Icons.compress;
      case SpecFieldType.flowRate:
        return Icons.water_drop;
      case SpecFieldType.weight:
        return Icons.scale;
      default:
        return Icons.straighten;
    }
  }

  /// 获取显示值
  String _getDisplayValue(dynamic value) {
    if (value == null) return '';
    if (value is DateTime) {
      return '${value.year}-${value.month.toString().padLeft(2, '0')}-${value.day.toString().padLeft(2, '0')}';
    }
    return value.toString();
  }

  /// 解析颜色
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    
    // 简单的颜色解析
    switch (colorString.toLowerCase()) {
      case '红色':
      case 'red':
        return Colors.red;
      case '蓝色':
      case 'blue':
        return Colors.blue;
      case '绿色':
      case 'green':
        return Colors.green;
      case '黄色':
      case 'yellow':
        return Colors.yellow;
      case '黑色':
      case 'black':
        return Colors.black;
      case '白色':
      case 'white':
        return Colors.white;
      default:
        return null;
    }
  }

  /// 验证并通知值变化
  void _validateAndNotify(dynamic value) {
    setState(() {
      _validationError = _validateValue(value);
    });
    
    if (_validationError == null && widget.onChanged != null) {
      widget.onChanged!(value);
    }
  }

  /// 验证值
  String? _validateValue(dynamic value) {
    // 必填验证
    if (widget.field.required && (value == null || value.toString().isEmpty)) {
      return '${widget.field.label}为必填项';
    }

    // 数值范围验证
    if (widget.field.numericRange != null && value is num) {
      final range = widget.field.numericRange!;
      if (range.min != null && value < range.min!) {
        return '${widget.field.label}不能小于${range.min}';
      }
      if (range.max != null && value > range.max!) {
        return '${widget.field.label}不能大于${range.max}';
      }
    }

    // 字符串长度验证
    if (widget.field.stringLengthLimit != null && value is String) {
      final limit = widget.field.stringLengthLimit!;
      if (limit.minLength != null && value.length < limit.minLength!) {
        return '${widget.field.label}长度不能少于${limit.minLength}个字符';
      }
      if (limit.maxLength != null && value.length > limit.maxLength!) {
        return '${widget.field.label}长度不能超过${limit.maxLength}个字符';
      }
    }

    return null;
  }
}
