/// VanHub Search Page 2.0
/// 
/// 现代化搜索页面，使用新设计系统
/// 
/// 特性：
/// - 智能搜索建议
/// - 多维度筛选
/// - 搜索历史记录
/// - 实时搜索结果
/// - 语音搜索支持
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/components/vanhub_input_v2.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../../core/design_system/foundation/animations/animation_tokens.dart';
import '../../../../core/services/voice_search_service.dart';


/// 搜索类型枚举
enum SearchType {
  all,        // 全部
  projects,   // 项目
  materials,  // 物料
  users,      // 用户
}

/// 搜索结果类型
enum SearchResultType {
  project,
  material,
  user,
}

/// 搜索结果数据模型
class SearchResult {
  final String id;
  final String title;
  final String subtitle;
  final String? imageUrl;
  final SearchResultType type;
  final Map<String, dynamic> data;

  const SearchResult({
    required this.id,
    required this.title,
    required this.subtitle,
    this.imageUrl,
    required this.type,
    required this.data,
  });
}

/// VanHub搜索页面 2.0
class SearchPageV2 extends ConsumerStatefulWidget {
  const SearchPageV2({super.key});

  @override
  ConsumerState<SearchPageV2> createState() => _SearchPageV2State();
}

class _SearchPageV2State extends ConsumerState<SearchPageV2>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  
  String _searchQuery = '';
  SearchType _selectedType = SearchType.all;
  bool _isSearching = false;
  List<SearchResult> _searchResults = [];
  List<String> _searchHistory = [];
  List<String> _searchSuggestions = [];

  // 语音搜索相关
  final VoiceSearchService _voiceSearchService = VoiceSearchService();
  final VoiceSearchState _voiceSearchState = VoiceSearchState.uninitialized;
  bool _isVoiceSearchSupported = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _fadeController = AnimationController(
      duration: VanHubAnimationDurations.normal,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: VanHubAnimationDurations.slow,
      vsync: this,
    );
    
    _loadSearchHistory();
    _initializeVoiceSearch();
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _voiceSearchService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
      appBar: _buildSearchAppBar(),
      body: _buildSearchContent(),
    );
  }

  /// 构建搜索AppBar
  PreferredSizeWidget _buildSearchAppBar() {
    return AppBar(
      backgroundColor: VanHubBrandColors.primary,
      foregroundColor: VanHubBrandColors.onPrimary,
      elevation: 0,
      title: VanHubInputV2(
        controller: _searchController,
        hint: '搜索项目、物料、用户...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_searchQuery.isNotEmpty)
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: _clearSearch,
              ),
            IconButton(
              icon: Icon(
                _voiceSearchState.isListening ? Icons.mic : Icons.mic_none,
                color: _voiceSearchState.isListening
                    ? Colors.red
                    : (_isVoiceSearchSupported ? null : Colors.grey),
              ),
              onPressed: _isVoiceSearchSupported ? _startVoiceSearch : null,
              tooltip: _voiceSearchState.description,
            ),
          ],
        ),
        onChanged: _onSearchChanged,
        onSubmitted: _onSearchSubmitted,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
        ),
      ],
    );
  }

  /// 构建搜索内容
  Widget _buildSearchContent() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeController,
          child: Column(
            children: [
              _buildSearchTypeFilter(),
              Expanded(
                child: _searchQuery.isEmpty
                    ? _buildSearchHome()
                    : _buildSearchResults(),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建搜索类型筛选
  Widget _buildSearchTypeFilter() {
    return Container(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.md),
      decoration: BoxDecoration(
        color: VanHubSemanticColors.getBackgroundColor(context),
        border: Border(
          bottom: BorderSide(
            color: VanHubSemanticColors.getBorderColor(context),
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: SearchType.values.map((type) {
            final isSelected = type == _selectedType;
            return Container(
              margin: EdgeInsets.only(right: VanHubResponsiveSpacing.sm),
              child: FilterChip(
                label: Text(_getSearchTypeText(type)),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedType = type;
                  });
                  if (_searchQuery.isNotEmpty) {
                    _performSearch();
                  }
                },
                backgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                selectedColor: VanHubBrandColors.primaryContainer,
                labelStyle: TextStyle(
                  color: isSelected 
                      ? VanHubBrandColors.onPrimaryContainer
                      : VanHubSemanticColors.getTextColor(context),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建搜索首页
  Widget _buildSearchHome() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索建议
          if (_searchSuggestions.isNotEmpty) ...[
            Text(
              '搜索建议',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.md),
            _buildSuggestionChips(),
            SizedBox(height: VanHubResponsiveSpacing.xl),
          ],
          
          // 搜索历史
          if (_searchHistory.isNotEmpty) ...[
            Row(
              children: [
                Text(
                  '搜索历史',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearSearchHistory,
                  child: const Text('清除'),
                ),
              ],
            ),
            SizedBox(height: VanHubResponsiveSpacing.md),
            _buildSearchHistory(),
            SizedBox(height: VanHubResponsiveSpacing.xl),
          ],
          
          // 热门搜索
          Text(
            '热门搜索',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: VanHubResponsiveSpacing.md),
          _buildHotSearches(),
        ],
      ),
    );
  }

  /// 构建搜索建议芯片
  Widget _buildSuggestionChips() {
    return Wrap(
      spacing: VanHubResponsiveSpacing.sm,
      runSpacing: VanHubResponsiveSpacing.sm,
      children: _searchSuggestions.map((suggestion) {
        return ActionChip(
          label: Text(suggestion),
          onPressed: () => _selectSuggestion(suggestion),
          backgroundColor: VanHubBrandColors.primaryContainer.withOpacity(0.3),
          labelStyle: TextStyle(
            color: VanHubBrandColors.onPrimaryContainer,
          ),
        );
      }).toList(),
    );
  }

  /// 构建搜索历史
  Widget _buildSearchHistory() {
    return Column(
      children: _searchHistory.map((query) {
        return ListTile(
          leading: const Icon(Icons.history),
          title: Text(query),
          trailing: IconButton(
            icon: const Icon(Icons.close, size: 18),
            onPressed: () => _removeFromHistory(query),
          ),
          onTap: () => _selectFromHistory(query),
        );
      }).toList(),
    );
  }

  /// 构建热门搜索
  Widget _buildHotSearches() {
    final hotSearches = [
      '电力系统', '太阳能板', '逆变器', '锂电池',
      '水路系统', '净水器', '水泵', '储水箱',
      '内饰改装', '床铺设计', '储物柜', '厨房改装',
    ];

    return Wrap(
      spacing: VanHubResponsiveSpacing.sm,
      runSpacing: VanHubResponsiveSpacing.sm,
      children: hotSearches.asMap().entries.map((entry) {
        final index = entry.key;
        final search = entry.value;
        return ActionChip(
          label: Text(search),
          onPressed: () => _selectHotSearch(search),
          backgroundColor: VanHubSemanticColors.getBackgroundColor(context, level: 2),
        );
      }).toList(),
    );
  }

  /// 构建搜索结果
  Widget _buildSearchResults() {
    if (_isSearching) {
      return _buildLoadingState();
    }

    if (_searchResults.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 结果统计
          Text(
            '找到 ${_searchResults.length} 个结果',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
          SizedBox(height: 24.0), // 临时使用固定值
          
          // 结果列表
          Column(
            children: _searchResults.asMap().entries.map((entry) {
              final index = entry.key;
              final result = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: _buildSearchResultCard(result, index),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建搜索结果卡片
  Widget _buildSearchResultCard(SearchResult result, int index) {
    return VanHubCardV2.interactive(
      size: VanHubCardSize.md,
      onTap: () => _openSearchResult(result),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 结果图片
          if (result.imageUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Image.network(
                  result.imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                      child: Icon(
                        _getResultTypeIcon(result.type),
                        color: VanHubSemanticColors.getTextColor(context, secondary: true),
                      ),
                    );
                  },
                ),
              ),
            )
          else
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: VanHubSemanticColors.getBackgroundColor(context, level: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Icon(
                  _getResultTypeIcon(result.type),
                  size: 40,
                  color: VanHubSemanticColors.getTextColor(context, secondary: true),
                ),
              ),
            ),
          
          SizedBox(height: VanHubResponsiveSpacing.sm),
          
          // 结果标题
          Text(
            result.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.xs),
          
          // 结果副标题
          Text(
            result.subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.sm),
          
          // 结果类型标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getResultTypeColor(result.type).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getResultTypeColor(result.type).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Text(
              _getResultTypeText(result.type),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: _getResultTypeColor(result.type),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: VanHubBrandColors.primary,
          ),
          SizedBox(height: VanHubResponsiveSpacing.lg),
          Text(
            '搜索中...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '未找到相关结果',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              '尝试使用其他关键词或筛选条件',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 辅助方法
  String _getSearchTypeText(SearchType type) {
    switch (type) {
      case SearchType.all:
        return '全部';
      case SearchType.projects:
        return '项目';
      case SearchType.materials:
        return '物料';
      case SearchType.users:
        return '用户';
    }
  }

  IconData _getResultTypeIcon(SearchResultType type) {
    switch (type) {
      case SearchResultType.project:
        return Icons.folder;
      case SearchResultType.material:
        return Icons.inventory_2;
      case SearchResultType.user:
        return Icons.person;
    }
  }

  Color _getResultTypeColor(SearchResultType type) {
    switch (type) {
      case SearchResultType.project:
        return VanHubBrandColors.primary;
      case SearchResultType.material:
        return VanHubSemanticColors.success;
      case SearchResultType.user:
        return VanHubSemanticColors.info;
    }
  }

  String _getResultTypeText(SearchResultType type) {
    switch (type) {
      case SearchResultType.project:
        return '项目';
      case SearchResultType.material:
        return '物料';
      case SearchResultType.user:
        return '用户';
    }
  }

  // 事件处理方法
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    
    if (query.isNotEmpty) {
      _updateSearchSuggestions(query);
    }
  }

  void _onSearchSubmitted(String query) {
    if (query.isNotEmpty) {
      _addToHistory(query);
      _performSearch();
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
      _searchResults.clear();
    });
  }

  /// 初始化语音搜索
  void _initializeVoiceSearch() async {
    try {
      _isVoiceSearchSupported = await _voiceSearchService.isSupported();
      if (_isVoiceSearchSupported) {
        await _voiceSearchService.initialize();
      }
      setState(() {});
    } catch (e) {
      debugPrint('语音搜索初始化失败: $e');
      _isVoiceSearchSupported = false;
    }
  }

  /// 显示语音搜索不支持对话框
  void _showVoiceSearchNotSupportedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('语音搜索不可用'),
        content: const Text('您的设备不支持语音搜索功能，或者语音搜索权限未开启。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示语音搜索错误对话框
  void _showVoiceSearchErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('语音搜索失败'),
        content: const Text('启动语音搜索时发生错误，请检查麦克风权限或稍后重试。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _startVoiceSearch() async {
    if (!_isVoiceSearchSupported) {
      _showVoiceSearchNotSupportedDialog();
      return;
    }

    if (_voiceSearchState.isListening) {
      await _voiceSearchService.stopListening();
      return;
    }

    final success = await _voiceSearchService.startListening();
    if (!success) {
      _showVoiceSearchErrorDialog();
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AdvancedFilterDialog(
        currentType: _selectedType,
        onFiltersApplied: (filters) {
          setState(() {
            _selectedType = filters['searchType'] ?? _selectedType;
            // 应用其他筛选条件
          });
          _performSearch();
        },
      ),
    );
  }

  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _onSearchSubmitted(suggestion);
  }

  void _selectFromHistory(String query) {
    _searchController.text = query;
    _onSearchSubmitted(query);
  }

  void _selectHotSearch(String search) {
    _searchController.text = search;
    _onSearchSubmitted(search);
  }

  void _removeFromHistory(String query) {
    setState(() {
      _searchHistory.remove(query);
    });
    // TODO: 保存到本地存储
  }

  void _clearSearchHistory() {
    setState(() {
      _searchHistory.clear();
    });
    // TODO: 清除本地存储
  }

  void _openSearchResult(SearchResult result) {
    // TODO: 根据结果类型导航到相应页面
  }

  void _loadSearchHistory() {
    // TODO: 从本地存储加载搜索历史
    setState(() {
      _searchHistory = ['电力系统改装', '太阳能板选择', '水路系统设计'];
      _searchSuggestions = ['电力', '水路', '内饰', '储物'];
    });
  }

  void _addToHistory(String query) {
    if (!_searchHistory.contains(query)) {
      setState(() {
        _searchHistory.insert(0, query);
        if (_searchHistory.length > 10) {
          _searchHistory.removeLast();
        }
      });
      // TODO: 保存到本地存储
    }
  }

  void _updateSearchSuggestions(String query) {
    // TODO: 根据输入更新搜索建议
  }

  void _performSearch() {
    setState(() {
      _isSearching = true;
    });

    // 模拟搜索延迟
    Future.delayed(const Duration(milliseconds: 1000), () {
      setState(() {
        _isSearching = false;
        _searchResults = _generateMockResults();
      });
    });
  }

  List<SearchResult> _generateMockResults() {
    // TODO: 实现真实的搜索逻辑
    return [
      const SearchResult(
        id: '1',
        title: '电力系统改装项目',
        subtitle: '完整的房车电力系统改装方案',
        type: SearchResultType.project,
        data: {},
      ),
      const SearchResult(
        id: '2',
        title: '太阳能板 - 单晶硅300W',
        subtitle: '高效单晶硅太阳能板，适合房车使用',
        type: SearchResultType.material,
        data: {},
      ),
      const SearchResult(
        id: '3',
        title: '张三的改装工坊',
        subtitle: '专业房车改装师，5年经验',
        type: SearchResultType.user,
        data: {},
      ),
    ];
  }
}

/// 高级筛选对话框
class AdvancedFilterDialog extends StatefulWidget {
  final SearchType currentType;
  final Function(Map<String, dynamic>) onFiltersApplied;

  const AdvancedFilterDialog({
    super.key,
    required this.currentType,
    required this.onFiltersApplied,
  });

  @override
  State<AdvancedFilterDialog> createState() => _AdvancedFilterDialogState();
}

class _AdvancedFilterDialogState extends State<AdvancedFilterDialog> {
  late SearchType _selectedType;
  String _selectedCategory = '全部';
  double? _minPrice;
  double? _maxPrice;
  String _selectedBrand = '全部';
  bool _onlyWithImages = false;
  bool _onlyInStock = false;
  String _sortBy = 'relevance';
  bool _ascending = false;

  final List<String> _categories = [
    '全部', '电气设备', '水路系统', '储物系统', '床铺系统',
    '厨房系统', '卫浴系统', '外观系统', '底盘系统', '安全设备', '其他'
  ];

  final List<String> _brands = [
    '全部', 'Victron', 'CATL', 'Seaflo', 'Dometic', 'Truma',
    'Webasto', 'Fiamma', 'Thule', 'Yakima', 'Goal Zero'
  ];

  final Map<String, String> _sortOptions = {
    'relevance': '相关性',
    'name': '名称',
    'price': '价格',
    'popularity': '热门度',
    'created_at': '创建时间',
  };

  @override
  void initState() {
    super.initState();
    _selectedType = widget.currentType;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  color: VanHubBrandColors.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  '高级筛选',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // 筛选内容
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSearchTypeSection(),
                    const SizedBox(height: 24),
                    _buildCategorySection(),
                    const SizedBox(height: 24),
                    _buildPriceSection(),
                    const SizedBox(height: 24),
                    _buildBrandSection(),
                    const SizedBox(height: 24),
                    _buildOptionsSection(),
                    const SizedBox(height: 24),
                    _buildSortSection(),
                  ],
                ),
              ),
            ),

            // 操作按钮
            const Divider(),
            Row(
              children: [
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text('重置'),
                ),
                const Spacer(),
                OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _applyFilters,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: VanHubBrandColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('应用筛选'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '搜索类型',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          children: SearchType.values.map((type) {
            final isSelected = type == _selectedType;
            return FilterChip(
              label: Text(_getSearchTypeText(type)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedType = type;
                });
              },
              selectedColor: VanHubBrandColors.primary.withValues(alpha: 0.2),
              checkmarkColor: VanHubBrandColors.primary,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分类',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _categories.map((category) {
            final isSelected = category == _selectedCategory;
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = selected ? category : '全部';
                });
              },
              selectedColor: VanHubBrandColors.primary.withValues(alpha: 0.2),
              checkmarkColor: VanHubBrandColors.primary,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '价格区间',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: '最低价格',
                  prefixText: '¥',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _minPrice = double.tryParse(value);
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: '最高价格',
                  prefixText: '¥',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _maxPrice = double.tryParse(value);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBrandSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '品牌',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<String>(
          value: _selectedBrand,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: _brands.map((brand) {
            return DropdownMenuItem<String>(
              value: brand,
              child: Text(brand),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedBrand = value ?? '全部';
            });
          },
        ),
      ],
    );
  }

  Widget _buildOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '其他选项',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('仅显示有图片的项目'),
          value: _onlyWithImages,
          onChanged: (value) {
            setState(() {
              _onlyWithImages = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('仅显示有库存的材料'),
          value: _onlyInStock,
          onChanged: (value) {
            setState(() {
              _onlyInStock = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildSortSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '排序方式',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _sortBy,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: _sortOptions.entries.map((entry) {
                  return DropdownMenuItem<String>(
                    value: entry.key,
                    child: Text(entry.value),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _sortBy = value ?? 'relevance';
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: IconButton(
                icon: Icon(
                  _ascending ? Icons.arrow_upward : Icons.arrow_downward,
                  color: VanHubBrandColors.primary,
                ),
                onPressed: () {
                  setState(() {
                    _ascending = !_ascending;
                  });
                },
                tooltip: _ascending ? '升序' : '降序',
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getSearchTypeText(SearchType type) {
    switch (type) {
      case SearchType.all:
        return '全部';
      case SearchType.projects:
        return '项目';
      case SearchType.materials:
        return '材料';
      case SearchType.users:
        return '用户';
    }
  }

  void _resetFilters() {
    setState(() {
      _selectedType = SearchType.all;
      _selectedCategory = '全部';
      _minPrice = null;
      _maxPrice = null;
      _selectedBrand = '全部';
      _onlyWithImages = false;
      _onlyInStock = false;
      _sortBy = 'relevance';
      _ascending = false;
    });
  }

  void _applyFilters() {
    final filters = <String, dynamic>{
      'searchType': _selectedType,
      'category': _selectedCategory == '全部' ? null : _selectedCategory,
      'minPrice': _minPrice,
      'maxPrice': _maxPrice,
      'brand': _selectedBrand == '全部' ? null : _selectedBrand,
      'onlyWithImages': _onlyWithImages,
      'onlyInStock': _onlyInStock,
      'sortBy': _sortBy,
      'ascending': _ascending,
    };

    widget.onFiltersApplied(filters);
    Navigator.of(context).pop();
  }

}
