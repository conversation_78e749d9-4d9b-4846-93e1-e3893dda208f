import 'package:flutter/material.dart';

/// VanHub项目色彩系统
/// 包含主色、辅助色、语义色等所有颜色常量
class VanHubColors {
  VanHubColors._();

  // ============================================================================
  // 主色系 (Primary Colors)
  // ============================================================================
  
  /// 主色 - 品牌主色调
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryContainer = Color(0XFFE3F2FD);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onPrimaryContainer = Color(0xFF0D47A1);

  // ============================================================================
  // 辅助色系 (Secondary Colors)
  // ============================================================================
  
  /// 辅助色 - 品牌辅助色调
  static const Color secondary = Color(0xFF424242);
  static const Color secondaryContainer = Color(0xFFF5F5F5);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSecondaryContainer = Color(0xFF212121);

  // ============================================================================
  // 第三色系 (Tertiary Colors)
  // ============================================================================
  
  /// 第三色 - 强调色
  static const Color tertiary = Color(0xFF00ACC1);
  static const Color tertiaryContainer = Color(0xFFE0F7FA);
  static const Color onTertiary = Color(0xFFFFFFFF);
  static const Color onTertiaryContainer = Color(0xFF006064);

  // ============================================================================
  // 语义色系 (Semantic Colors)
  // ============================================================================
  
  /// 错误色
  static const Color error = Color(0xFFD32F2F);
  static const Color errorContainer = Color(0xFFFFEBEE);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color onErrorContainer = Color(0xFFB71C1C);

  /// 警告色
  static const Color warning = Color(0xFFF57C00);
  static const Color warningContainer = Color(0xFFFFF3E0);
  static const Color onWarning = Color(0xFFFFFFFF);
  static const Color onWarningContainer = Color(0xFFE65100);

  /// 成功色
  static const Color success = Color(0xFF388E3C);
  static const Color successContainer = Color(0xFFE8F5E8);
  static const Color onSuccess = Color(0xFFFFFFFF);
  static const Color onSuccessContainer = Color(0xFF1B5E20);

  /// 信息色
  static const Color info = Color(0xFF1976D2);
  static const Color infoContainer = Color(0xFFE3F2FD);
  static const Color onInfo = Color(0xFFFFFFFF);
  static const Color onInfoContainer = Color(0xFF0D47A1);

  // ============================================================================
  // 透明度变体 (Opacity Variants)
  // ============================================================================

  /// 错误色透明度变体
  static Color get errorWithOpacity => error.withValues(alpha: 0.1);

  /// 警告色透明度变体
  static Color get warningWithOpacity => warning.withValues(alpha: 0.1);

  /// 成功色透明度变体
  static Color get successWithOpacity => success.withValues(alpha: 0.1);

  /// 信息色透明度变体
  static Color get infoWithOpacity => info.withValues(alpha: 0.1);

  // ============================================================================
  // 表面色系 (Surface Colors)
  // ============================================================================
  
  /// 背景色
  static const Color background = Color(0xFFFAFAFA);
  static const Color onBackground = Color(0xFF212121);

  /// 表面色
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceVariant = Color(0xFF757575);

  /// 表面色调
  static const Color surfaceTint = primary;
  static const Color inverseSurface = Color(0xFF303030);
  static const Color onInverseSurface = Color(0xFFFFFFFF);
  static const Color inversePrimary = Color(0xFF90CAF9);

  // ============================================================================
  // 轮廓色系 (Outline Colors)
  // ============================================================================

  /// 轮廓色
  static const Color outline = Color(0xFFBDBDBD);
  static const Color outlineVariant = Color(0xFFE0E0E0);

  // ============================================================================
  // 阴影和覆盖色 (Shadow & Overlay Colors)
  // ============================================================================

  /// 阴影色
  static const Color shadow = Color(0x1F000000);
  static const Color scrim = Color(0x66000000);

  // ============================================================================
  // 品牌色系 (Brand Colors) - 兼容性属性
  // ============================================================================

  /// 品牌主色 - 兼容性属性
  static const Color brandPrimary = primary;

  /// 品牌辅助色 - 兼容性属性
  static const Color brandSecondary = secondary;

  /// 品牌强调色 - 兼容性属性
  static const Color brandAccent = tertiary;

  // ============================================================================
  // 中性色系扩展 (Extended Neutral Colors)
  // ============================================================================

  /// 中性灰色系列 - 兼容性属性
  static const Color neutralGray100 = Color(0xFFF5F5F5);
  static const Color neutralGray200 = Color(0xFFEEEEEE);
  static const Color neutralGray300 = Color(0xFFE0E0E0);
  static const Color neutralGray400 = Color(0xFFBDBDBD);
  static const Color neutralGray500 = Color(0xFF9E9E9E);
  static const Color neutralGray600 = Color(0xFF757575);
  static const Color neutralGray700 = Color(0xFF616161);
  static const Color neutralGray800 = Color(0xFF424242);
  static const Color neutralGray900 = Color(0xFF212121);

  // ============================================================================
  // 语义色系扩展 (Extended Semantic Colors)
  // ============================================================================

  /// 语义信息色 - 兼容性属性
  static const Color semanticInfo = info;

  /// 语义成功色 - 兼容性属性
  static const Color semanticSuccess = success;

  /// 语义成功色上的文字色 - 兼容性属性
  static const Color onSemanticSuccess = onSuccess;

  /// 覆盖色
  static const Color overlay = Color(0x0A000000);
  static const Color overlayLight = Color(0x05000000);
  static const Color overlayDark = Color(0x14000000);

  // ============================================================================
  // 状态色系 (State Colors)
  // ============================================================================
  
  /// 禁用状态
  static const Color disabled = Color(0xFFBDBDBD);
  static const Color onDisabled = Color(0xFF9E9E9E);

  /// 焦点状态
  static const Color focus = Color(0x1F1976D2);
  static const Color hover = Color(0x0A1976D2);
  static const Color pressed = Color(0x141976D2);

  /// 选中状态
  static const Color selected = Color(0xFFE3F2FD);
  static const Color onSelected = Color(0xFF1976D2);

  // ============================================================================
  // 特殊用途色 (Special Purpose Colors)
  // ============================================================================
  
  /// 分割线
  static const Color divider = Color(0xFFE0E0E0);
  static const Color dividerLight = Color(0xFFF5F5F5);

  /// 文本色
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textDisabled = Color(0xFFBDBDBD);
  static const Color textHint = Color(0xFF9E9E9E);

  /// 图标色
  static const Color iconPrimary = Color(0xFF424242);
  static const Color iconSecondary = Color(0xFF757575);
  static const Color iconDisabled = Color(0xFFBDBDBD);

  // ============================================================================
  // 渐变色 (Gradient Colors)
  // ============================================================================
  
  /// 主色渐变
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF1976D2), Color(0xFF1565C0)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 辅助色渐变
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [Color(0xFF424242), Color(0xFF616161)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 成功色渐变
  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF388E3C), Color(0xFF2E7D32)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 错误色渐变
  static const LinearGradient errorGradient = LinearGradient(
    colors: [Color(0xFFD32F2F), Color(0xFFC62828)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ============================================================================
  // 颜色工具方法 (Color Utility Methods)
  // ============================================================================
  
  /// 获取颜色的透明度变体
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  /// 获取颜色的明度变体
  static Color lighten(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 获取颜色的暗度变体
  static Color darken(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// 获取对比色
  static Color getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  // ============================================================================
  // 主题色彩映射 (Theme Color Mapping)
  // ============================================================================
  
  /// 获取Material 3 ColorScheme
  static ColorScheme get lightColorScheme => const ColorScheme.light(
    primary: primary,
    onPrimary: onPrimary,
    primaryContainer: primaryContainer,
    onPrimaryContainer: onPrimaryContainer,
    secondary: secondary,
    onSecondary: onSecondary,
    secondaryContainer: secondaryContainer,
    onSecondaryContainer: onSecondaryContainer,
    tertiary: tertiary,
    onTertiary: onTertiary,
    tertiaryContainer: tertiaryContainer,
    onTertiaryContainer: onTertiaryContainer,
    error: error,
    onError: onError,
    errorContainer: errorContainer,
    onErrorContainer: onErrorContainer,
    surface: surface,
    onSurface: onSurface,
    surfaceContainerHighest: surfaceVariant,
    onSurfaceVariant: onSurfaceVariant,
    outline: outline,
    outlineVariant: outlineVariant,
    shadow: shadow,
    scrim: scrim,
    inverseSurface: inverseSurface,
    onInverseSurface: onInverseSurface,
    inversePrimary: inversePrimary,
    surfaceTint: surfaceTint,
  );

  /// 获取暗色主题ColorScheme
  static ColorScheme get darkColorScheme => const ColorScheme.dark(
    primary: Color(0xFF90CAF9),
    onPrimary: Color(0xFF0D47A1),
    primaryContainer: Color(0xFF1565C0),
    onPrimaryContainer: Color(0xFFE3F2FD),
    secondary: Color(0xFFBDBDBD),
    onSecondary: Color(0xFF212121),
    secondaryContainer: Color(0xFF616161),
    onSecondaryContainer: Color(0xFFF5F5F5),
    tertiary: Color(0xFF4DD0E1),
    onTertiary: Color(0xFF006064),
    tertiaryContainer: Color(0xFF00838F),
    onTertiaryContainer: Color(0xFFE0F7FA),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFC62828),
    onErrorContainer: Color(0xFFFFEBEE),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFFFFFFF),
    surfaceContainerHighest: Color(0xFF2C2C2C),
    onSurfaceVariant: Color(0xFFBDBDBD),
    outline: Color(0xFF757575),
    outlineVariant: Color(0xFF424242),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFFFFFFF),
    onInverseSurface: Color(0xFF121212),
    inversePrimary: Color(0xFF1976D2),
    surfaceTint: Color(0xFF90CAF9),
  );

  // ============================================================================
  // 兼容性颜色 (Compatibility Colors)
  // ============================================================================

  /// 边框色 - 兼容性属性
  static const Color border = outline;
}
