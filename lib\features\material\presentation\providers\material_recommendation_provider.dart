import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/providers/auth_state_provider.dart';
import '../../domain/entities/material_recommendation.dart';
import '../../../../core/di/injection_container.dart';

part 'material_recommendation_provider.g.dart';

/// 项目推荐材料提供者
@riverpod
Future<List<MaterialRecommendation>> projectRecommendations(
  ProjectRecommendationsRef ref,
  String projectId, {
  int limit = 10,
}) async {
  final result = await ref.read(materialRecommendationServiceProvider).recommendForProject(
    projectId,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (recommendations) => recommendations,
  );
}

/// 系统推荐材料提供者
@riverpod
Future<List<MaterialRecommendation>> systemRecommendations(
  SystemRecommendationsRef ref,
  String projectId,
  String systemType, {
  int limit = 10,
}) async {
  final result = await ref.read(materialRecommendationServiceProvider).recommendForSystem(
    projectId,
    systemType,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (recommendations) => recommendations,
  );
}

/// 相似材料推荐提供者
@riverpod
Future<List<MaterialRecommendation>> similarMaterialRecommendations(
  SimilarMaterialRecommendationsRef ref,
  String materialId, {
  int limit = 10,
}) async {
  final result = await ref.read(materialRecommendationServiceProvider).recommendSimilarMaterials(
    materialId,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (recommendations) => recommendations,
  );
}

/// 配套材料推荐提供者
@riverpod
Future<List<MaterialRecommendation>> complementaryMaterialRecommendations(
  ComplementaryMaterialRecommendationsRef ref,
  String materialId, {
  int limit = 10,
}) async {
  final result = await ref.read(materialRecommendationServiceProvider).recommendComplementaryMaterials(
    materialId,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (recommendations) => recommendations,
  );
}

/// 热门材料推荐提供者
@riverpod
Future<List<MaterialRecommendation>> popularMaterialRecommendations(
  PopularMaterialRecommendationsRef ref, {
  String? category,
  int limit = 10,
}) async {
  final userId = ref.read(currentUserIdProvider);
  if (userId == null) return [];
  
  final result = await ref.read(materialRecommendationServiceProvider).recommendPopularMaterials(
    userId,
    category: category,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (recommendations) => recommendations,
  );
}

/// 性价比材料推荐提供者
@riverpod
Future<List<MaterialRecommendation>> valueForMoneyRecommendations(
  ValueForMoneyRecommendationsRef ref, {
  String? category,
  int limit = 10,
}) async {
  final userId = ref.read(currentUserIdProvider);
  if (userId == null) return [];

  final result = await ref.read(materialRecommendationServiceProvider).recommendValueForMoneyMaterials(
    userId,
    category: category,
    limit: limit,
  );
  
  return result.fold(
    (failure) => [],
    (recommendations) => recommendations,
  );
}

/// 材料推荐状态管理
@riverpod
class RecommendationState extends _$RecommendationState {
  @override
  Future<List<MaterialRecommendation>> build() async {
    // 初始状态返回空列表
    return [];
  }

  /// 加载项目推荐
  Future<Either<Failure, List<MaterialRecommendation>>> loadProjectRecommendations(
    String projectId, {
    int limit = 10,
  }) async {
    state = const AsyncLoading();
    
    final result = await ref.read(materialRecommendationServiceProvider).recommendForProject(
      projectId,
      limit: limit,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (recommendations) {
        state = AsyncData(recommendations);
      },
    );
    
    return result;
  }

  /// 加载系统推荐
  Future<Either<Failure, List<MaterialRecommendation>>> loadSystemRecommendations(
    String projectId,
    String systemType, {
    int limit = 10,
  }) async {
    state = const AsyncLoading();
    
    final result = await ref.read(materialRecommendationServiceProvider).recommendForSystem(
      projectId,
      systemType,
      limit: limit,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (recommendations) {
        state = AsyncData(recommendations);
      },
    );
    
    return result;
  }

  /// 加载相似材料推荐
  Future<Either<Failure, List<MaterialRecommendation>>> loadSimilarMaterialRecommendations(
    String materialId, {
    int limit = 10,
  }) async {
    state = const AsyncLoading();
    
    final result = await ref.read(materialRecommendationServiceProvider).recommendSimilarMaterials(
      materialId,
      limit: limit,
    );
    
    result.fold(
      (failure) {
        state = AsyncError(failure, StackTrace.current);
      },
      (recommendations) {
        state = AsyncData(recommendations);
      },
    );
    
    return result;
  }

  /// 清空推荐结果
  void clearRecommendations() {
    state = const AsyncData([]);
  }
}