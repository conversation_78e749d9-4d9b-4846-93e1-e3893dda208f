/// VanHub Project Detail Page 2.0
/// 
/// 现代化项目详情页面，使用新设计系统
/// 
/// 特性：
/// - 沉浸式项目展示
/// - 智能进度跟踪
/// - 实时协作状态
/// - 3D数据可视化
/// - 响应式多栏布局
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/components/vanhub_card_v2.dart';
import '../../../../core/design_system/components/vanhub_button_v2.dart';
import '../../../../core/design_system/components/vanhub_chart.dart';
import '../../../../core/design_system/components/vanhub_progress_indicator.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/colors/semantic_colors.dart';
import '../../../../core/design_system/foundation/spacing/responsive_spacing.dart';
import '../../../../core/design_system/foundation/animations/animation_tokens.dart';
import '../../domain/entities/project.dart';
import '../providers/project_provider.dart';
import '../../../bom/presentation/pages/bom_management_page_v2.dart';

/// 项目详情标签页枚举
enum ProjectDetailTab {
  overview,   // 概览
  bom,        // BOM管理
  timeline,   // 时间轴
  team,       // 团队协作
  analytics,  // 数据分析
}

/// VanHub项目详情页面 2.0
class ProjectDetailPageV2 extends ConsumerStatefulWidget {
  final String projectId;

  const ProjectDetailPageV2({
    super.key,
    required this.projectId,
  });

  @override
  ConsumerState<ProjectDetailPageV2> createState() => _ProjectDetailPageV2State();
}

class _ProjectDetailPageV2State extends ConsumerState<ProjectDetailPageV2>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _heroController;
  late AnimationController _statsController;
  
  ProjectDetailTab _currentTab = ProjectDetailTab.overview;
  bool _isHeroExpanded = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _heroController = AnimationController(
      duration: VanHubAnimationDurations.slow,
      vsync: this,
    );
    _statsController = AnimationController(
      duration: VanHubAnimationDurations.normal,
      vsync: this,
    );
    
    _tabController.addListener(() {
      setState(() {
        _currentTab = ProjectDetailTab.values[_tabController.index];
      });
    });
    
    // 启动动画
    _heroController.forward();
    _statsController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _heroController.dispose();
    _statsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final projectAsync = ref.watch(projectDetailProvider(widget.projectId));
    
    return Scaffold(
      backgroundColor: VanHubSemanticColors.getBackgroundColor(context),
      body: projectAsync.when(
        data: (project) => _buildProjectDetail(project),
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  /// 构建项目详情内容
  Widget _buildProjectDetail(Project project) {
    return CustomScrollView(
      slivers: [
        _buildHeroSection(project),
        _buildTabBar(),
        _buildTabContent(project),
      ],
    );
  }

  /// 构建英雄区域
  Widget _buildHeroSection(Project project) {
    return SliverAppBar(
      expandedHeight: _isHeroExpanded ? 400 : 300,
      floating: false,
      pinned: true,
      backgroundColor: VanHubBrandColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        background: AnimatedBuilder(
          animation: _heroController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: VanHubBrandColors.getEmotionalGradient(EmotionalState.confident),
              ),
              child: Stack(
                children: [
                  // 背景装饰
                  Positioned(
                    right: -100,
                    top: 50,
                    child: Transform.rotate(
                      angle: _heroController.value * 0.1,
                      child: Icon(
                        Icons.engineering,
                        size: 200,
                        color: VanHubBrandColors.onPrimary.withOpacity(0.1),
                      ),
                    ),
                  ),
                  
                  // 项目信息
                  Positioned(
                    left: VanHubResponsiveSpacing.lg,
                    bottom: VanHubResponsiveSpacing.xl,
                    right: VanHubResponsiveSpacing.lg,
                    child: _buildProjectInfo(project),
                  ),
                ],
              ),
            );
          },
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(_isHeroExpanded ? Icons.expand_less : Icons.expand_more),
          onPressed: () {
            setState(() {
              _isHeroExpanded = !_isHeroExpanded;
            });
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _editProject(project);
                break;
              case 'share':
                _shareProject(project);
                break;
              case 'export':
                _exportProject(project);
                break;
              case 'delete':
                _deleteProject(project);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('编辑项目'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, size: 20),
                  SizedBox(width: 8),
                  Text('分享项目'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 20),
                  SizedBox(width: 8),
                  Text('导出数据'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('删除项目', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建项目信息
  Widget _buildProjectInfo(Project project) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 项目名称
        Text(
          project.title,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: VanHubBrandColors.onPrimary,
          ),
        ),
        
        SizedBox(height: VanHubResponsiveSpacing.sm),
        
        // 项目描述
        if (project.description.isNotEmpty)
          Text(
            project.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: VanHubBrandColors.onPrimary.withOpacity(0.9),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        
        SizedBox(height: VanHubResponsiveSpacing.lg),
        
        // 项目统计
        _buildProjectStats(project),
      ],
    );
  }

  /// 构建项目统计
  Widget _buildProjectStats(Project project) {
    return AnimatedBuilder(
      animation: _statsController,
      builder: (context, child) {
        return Row(
          children: [
            _buildStatChip(
              '进度',
              '${(project.progress * 100).toInt()}%',
              VanHubSemanticColors.success,
              Icons.trending_up,
            ),
            SizedBox(width: VanHubResponsiveSpacing.md),
            _buildStatChip(
              '预算',
              '¥${project.budget.toStringAsFixed(0)}',
              VanHubSemanticColors.warning,
              Icons.account_balance_wallet,
            ),
            SizedBox(width: VanHubResponsiveSpacing.md),
            _buildStatChip(
              '状态',
              project.status.displayName,
              VanHubSemanticColors.info,
              Icons.info,
            ),
          ],
        );
      },
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(String label, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: VanHubResponsiveSpacing.md,
        vertical: VanHubResponsiveSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          SizedBox(width: VanHubResponsiveSpacing.xs),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: VanHubBrandColors.onPrimary.withOpacity(0.8),
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: VanHubBrandColors.onPrimary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建标签栏
  Widget _buildTabBar() {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _TabBarDelegate(
        TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: VanHubBrandColors.primary,
          labelColor: VanHubBrandColors.primary,
          unselectedLabelColor: VanHubSemanticColors.getTextColor(context, secondary: true),
          tabs: const [
            Tab(text: '概览', icon: Icon(Icons.dashboard, size: 20)),
            Tab(text: 'BOM', icon: Icon(Icons.inventory_2, size: 20)),
            Tab(text: '时间轴', icon: Icon(Icons.timeline, size: 20)),
            Tab(text: '团队', icon: Icon(Icons.group, size: 20)),
            Tab(text: '分析', icon: Icon(Icons.analytics, size: 20)),
          ],
        ),
      ),
    );
  }

  /// 构建标签内容
  Widget _buildTabContent(Project project) {
    return SliverFillRemaining(
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(project),
          _buildBomTab(project),
          _buildTimelineTab(project),
          _buildTeamTab(project),
          _buildAnalyticsTab(project),
        ],
      ),
    );
  }

  /// 构建概览标签
  Widget _buildOverviewTab(Project project) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 项目进度卡片
          VanHubCardV2(
            variant: VanHubCardVariant.filled,
            size: VanHubCardSize.lg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      color: VanHubBrandColors.primary,
                    ),
                    SizedBox(width: VanHubResponsiveSpacing.sm),
                    Text(
                      '项目进度',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: VanHubResponsiveSpacing.lg),
                VanHubProgressIndicator.circular(
                  progress: project.progress / 100.0,
                  size: VanHubProgressSize.lg,
                  showPercentage: true,
                ),
                SizedBox(height: VanHubResponsiveSpacing.md),
                Text(
                  '当前进度：${project.progress}%',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.lg),
          
          // 快速操作
          Text(
            '快速操作',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: VanHubResponsiveSpacing.md),
          Wrap(
            spacing: VanHubResponsiveSpacing.md,
            runSpacing: VanHubResponsiveSpacing.md,
            children: [
              VanHubButtonV2(
                text: '添加物料',
                variant: VanHubButtonVariant.outline,
                leadingIcon: Icons.add_box,
                onPressed: () => _addMaterial(project),
              ),
              VanHubButtonV2(
                text: '记录日志',
                variant: VanHubButtonVariant.outline,
                leadingIcon: Icons.edit_note,
                onPressed: () => _addLog(project),
              ),
              VanHubButtonV2(
                text: '邀请成员',
                variant: VanHubButtonVariant.outline,
                leadingIcon: Icons.person_add,
                onPressed: () => _inviteMember(project),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建BOM标签
  Widget _buildBomTab(Project project) {
    return BomManagementPageV2(projectId: project.id);
  }

  /// 构建时间轴标签
  Widget _buildTimelineTab(Project project) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.timeline,
            size: 80,
            color: VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
          SizedBox(height: VanHubResponsiveSpacing.lg),
          Text(
            '时间轴功能',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          Text(
            '开发中...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建团队标签
  Widget _buildTeamTab(Project project) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group,
            size: 80,
            color: VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
          SizedBox(height: VanHubResponsiveSpacing.lg),
          Text(
            '团队协作',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          Text(
            '开发中...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分析标签
  Widget _buildAnalyticsTab(Project project) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(VanHubResponsiveSpacing.lg),
      child: Column(
        children: [
          // 成本分析图表
          VanHubCardV2.outlined(
            size: VanHubCardSize.lg,
            child: VanHubChart.line(
              title: '成本趋势',
              series: [
                ChartDataSeries(
                  name: '预算',
                  color: VanHubBrandColors.primary,
                  data: [
                    ChartDataPoint(x: 1, y: 5000),
                    ChartDataPoint(x: 2, y: 8000),
                    ChartDataPoint(x: 3, y: 12000),
                    ChartDataPoint(x: 4, y: 15000),
                    ChartDataPoint(x: 5, y: 18000),
                  ],
                ),
                ChartDataSeries(
                  name: '实际',
                  color: VanHubSemanticColors.success,
                  data: [
                    ChartDataPoint(x: 1, y: 4500),
                    ChartDataPoint(x: 2, y: 7200),
                    ChartDataPoint(x: 3, y: 11500),
                    ChartDataPoint(x: 4, y: 14200),
                    ChartDataPoint(x: 5, y: 16800),
                  ],
                ),
              ],
            ),
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.lg),
          
          // 进度分析图表
          VanHubCardV2.outlined(
            size: VanHubCardSize.lg,
            child: VanHubChart.bar(
              title: '月度进度',
              series: [
                ChartDataSeries(
                  name: '完成任务',
                  color: VanHubSemanticColors.success,
                  data: [
                    ChartDataPoint(x: 1, y: 12),
                    ChartDataPoint(x: 2, y: 18),
                    ChartDataPoint(x: 3, y: 25),
                    ChartDataPoint(x: 4, y: 22),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: VanHubBrandColors.primary,
          ),
          SizedBox(height: VanHubResponsiveSpacing.lg),
          Text(
            '加载项目详情中...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(Object error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(VanHubResponsiveSpacing.xxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: VanHubSemanticColors.error,
            ),
            SizedBox(height: VanHubResponsiveSpacing.lg),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: VanHubSemanticColors.error,
              ),
            ),
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: VanHubSemanticColors.getTextColor(context, secondary: true),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: VanHubResponsiveSpacing.xl),
            VanHubButtonV2(
              text: '重试',
              variant: VanHubButtonVariant.outline,
              leadingIcon: Icons.refresh,
              onPressed: () {
                ref.invalidate(projectDetailProvider(widget.projectId));
              },
            ),
          ],
        ),
      ),
    );
  }

  // 操作方法
  void _editProject(Project project) {
    // TODO: 实现编辑项目功能
  }

  void _shareProject(Project project) {
    // TODO: 实现分享项目功能
  }

  void _exportProject(Project project) {
    // TODO: 实现导出项目功能
  }

  void _deleteProject(Project project) {
    // TODO: 实现删除项目功能
  }

  void _addMaterial(Project project) {
    // TODO: 实现添加物料功能
  }

  void _addLog(Project project) {
    // TODO: 实现添加日志功能
  }

  void _inviteMember(Project project) {
    // TODO: 实现邀请成员功能
  }
}

/// 标签栏委托
class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: VanHubSemanticColors.getBackgroundColor(context),
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
