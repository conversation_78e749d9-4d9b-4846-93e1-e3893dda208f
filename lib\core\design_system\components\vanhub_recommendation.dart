/// VanHub Recommendation Component 2.0
/// 
/// AI智能推荐组件，支持个性化推荐和学习算法
/// 
/// 特性：
/// - AI驱动的个性化推荐
/// - 多种推荐类型：材料、项目、工具、教程
/// - 智能学习用户偏好
/// - 情感化推荐界面
/// - 实时推荐更新
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../foundation/colors/brand_colors.dart';
import '../foundation/colors/semantic_colors.dart';
import '../foundation/spacing/responsive_spacing.dart';
import '../foundation/animations/animation_tokens.dart';
import '../utils/responsive_utils.dart';
import 'vanhub_card_v2.dart';
import 'vanhub_button_v2.dart';

/// 推荐类型枚举
enum RecommendationType {
  material,    // 材料推荐
  project,     // 项目推荐
  tool,        // 工具推荐
  tutorial,    // 教程推荐
  supplier,    // 供应商推荐
  technique,   // 技术推荐
}

/// 推荐置信度枚举
enum RecommendationConfidence {
  low,      // 低置信度 (< 60%)
  medium,   // 中等置信度 (60-80%)
  high,     // 高置信度 (80-95%)
  veryHigh, // 极高置信度 (> 95%)
}

/// 推荐原因枚举
enum RecommendationReason {
  userHistory,        // 基于用户历史
  similarUsers,       // 基于相似用户
  projectSimilarity,  // 基于项目相似性
  trending,          // 基于趋势
  expertChoice,      // 专家推荐
  costEffective,     // 性价比推荐
  qualityFirst,      // 质量优先
  innovative,        // 创新推荐
}

/// 推荐数据模型
class RecommendationItem {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final RecommendationConfidence confidence;
  final List<RecommendationReason> reasons;
  final String? imageUrl;
  final double? price;
  final double? rating;
  final int? reviewCount;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;

  const RecommendationItem({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.confidence,
    required this.reasons,
    this.imageUrl,
    this.price,
    this.rating,
    this.reviewCount,
    this.metadata,
    required this.createdAt,
  });

  /// 获取置信度颜色
  Color get confidenceColor {
    switch (confidence) {
      case RecommendationConfidence.low:
        return VanHubSemanticColors.textSecondary;
      case RecommendationConfidence.medium:
        return VanHubSemanticColors.warning;
      case RecommendationConfidence.high:
        return VanHubSemanticColors.success;
      case RecommendationConfidence.veryHigh:
        return VanHubBrandColors.primary;
    }
  }

  /// 获取置信度文本
  String get confidenceText {
    switch (confidence) {
      case RecommendationConfidence.low:
        return '可能适合';
      case RecommendationConfidence.medium:
        return '比较适合';
      case RecommendationConfidence.high:
        return '非常适合';
      case RecommendationConfidence.veryHigh:
        return '强烈推荐';
    }
  }

  /// 获取类型图标
  IconData get typeIcon {
    switch (type) {
      case RecommendationType.material:
        return Icons.inventory_2_outlined;
      case RecommendationType.project:
        return Icons.folder_outlined;
      case RecommendationType.tool:
        return Icons.build_outlined;
      case RecommendationType.tutorial:
        return Icons.school_outlined;
      case RecommendationType.supplier:
        return Icons.store_outlined;
      case RecommendationType.technique:
        return Icons.lightbulb_outlined;
    }
  }

  /// 获取类型文本
  String get typeText {
    switch (type) {
      case RecommendationType.material:
        return '材料';
      case RecommendationType.project:
        return '项目';
      case RecommendationType.tool:
        return '工具';
      case RecommendationType.tutorial:
        return '教程';
      case RecommendationType.supplier:
        return '供应商';
      case RecommendationType.technique:
        return '技术';
    }
  }
}

/// VanHub推荐组件
class VanHubRecommendation extends StatefulWidget {
  final List<RecommendationItem> recommendations;
  final String? title;
  final bool enablePersonalization;
  final bool showConfidence;
  final bool showReasons;
  final bool enableFeedback;
  final int maxItems;
  final Function(RecommendationItem)? onItemTap;
  final Function(RecommendationItem, bool)? onFeedback; // true=喜欢, false=不喜欢
  final Function(RecommendationItem)? onDismiss;
  final VoidCallback? onRefresh;
  
  // 布局配置
  final bool useGridLayout;
  final int gridColumns;
  
  // 动画配置
  final Duration animationDuration;
  final Duration staggerDelay;

  const VanHubRecommendation({
    super.key,
    required this.recommendations,
    this.title,
    this.enablePersonalization = true,
    this.showConfidence = true,
    this.showReasons = true,
    this.enableFeedback = true,
    this.maxItems = 10,
    this.onItemTap,
    this.onFeedback,
    this.onDismiss,
    this.onRefresh,
    this.useGridLayout = false,
    this.gridColumns = 2,
    this.animationDuration = VanHubAnimationDurations.normal,
    this.staggerDelay = const Duration(milliseconds: 100),
  });

  @override
  State<VanHubRecommendation> createState() => _VanHubRecommendationState();
}

class _VanHubRecommendationState extends State<VanHubRecommendation>
    with TickerProviderStateMixin {
  late List<RecommendationItem> _displayedItems;
  final Set<String> _dismissedItems = {};
  final Map<String, bool> _feedbackItems = {};

  @override
  void initState() {
    super.initState();
    _updateDisplayedItems();
  }

  @override
  void didUpdateWidget(VanHubRecommendation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.recommendations != oldWidget.recommendations) {
      _updateDisplayedItems();
    }
  }

  /// 更新显示的推荐项
  void _updateDisplayedItems() {
    _displayedItems = widget.recommendations
        .where((item) => !_dismissedItems.contains(item.id))
        .take(widget.maxItems)
        .toList();
  }

  /// 处理反馈
  void _handleFeedback(RecommendationItem item, bool isPositive) {
    setState(() {
      _feedbackItems[item.id] = isPositive;
    });
    
    widget.onFeedback?.call(item, isPositive);
    
    // 触觉反馈
    HapticFeedback.lightImpact();
  }

  /// 处理忽略
  void _handleDismiss(RecommendationItem item) {
    setState(() {
      _dismissedItems.add(item.id);
      _updateDisplayedItems();
    });
    
    widget.onDismiss?.call(item);
    
    // 触觉反馈
    HapticFeedback.mediumImpact();
  }

  @override
  Widget build(BuildContext context) {
    if (_displayedItems.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null) _buildHeader(),
        if (widget.useGridLayout) _buildGridLayout() else _buildListLayout(),
      ],
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(bottom: VanHubResponsiveSpacing.lg),
      child: Row(
        children: [
          Icon(
            Icons.auto_awesome,
            color: VanHubBrandColors.primary,
            size: 24,
          ),
          SizedBox(width: VanHubResponsiveSpacing.sm),
          Expanded(
            child: Text(
              widget.title!,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: VanHubSemanticColors.getTextColor(context),
              ),
            ),
          ),
          if (widget.onRefresh != null)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: widget.onRefresh,
              color: VanHubBrandColors.primary,
              tooltip: '刷新推荐',
            ),
        ],
      ),
    );
  }

  /// 构建网格布局
  Widget _buildGridLayout() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: VanHubResponsiveUtils.getSimpleValue<int>(
          context,
          mobile: 1,
          tablet: widget.gridColumns,
          desktop: widget.gridColumns + 1,
        ),
        crossAxisSpacing: VanHubResponsiveSpacing.md,
        mainAxisSpacing: VanHubResponsiveSpacing.md,
        childAspectRatio: 0.8,
      ),
      itemCount: _displayedItems.length,
      itemBuilder: (context, index) {
        return _buildRecommendationCard(_displayedItems[index], index);
      },
    );
  }

  /// 构建列表布局
  Widget _buildListLayout() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _displayedItems.length,
      separatorBuilder: (context, index) => SizedBox(height: VanHubResponsiveSpacing.md),
      itemBuilder: (context, index) {
        return _buildRecommendationCard(_displayedItems[index], index);
      },
    );
  }

  /// 构建推荐卡片
  Widget _buildRecommendationCard(RecommendationItem item, int index) {
    final hasFeedback = _feedbackItems.containsKey(item.id);
    final isLiked = _feedbackItems[item.id] ?? false;

    return VanHubCardV2.interactive(
      size: VanHubCardSize.md,
      enable3DEffect: VanHubResponsiveUtils.isDesktop(context),
      onTap: () => widget.onItemTap?.call(item),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部：类型和置信度
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: item.confidenceColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: item.confidenceColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      item.typeIcon,
                      size: 14,
                      color: item.confidenceColor,
                    ),
                    SizedBox(width: VanHubResponsiveSpacing.xs),
                    Text(
                      item.typeText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: item.confidenceColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              if (widget.showConfidence)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: item.confidenceColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    item.confidenceText,
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: item.confidenceColor,
                    ),
                  ),
                ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  size: 18,
                  color: VanHubSemanticColors.getTextColor(context, secondary: true),
                ),
                onSelected: (value) {
                  if (value == 'dismiss') {
                    _handleDismiss(item);
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'dismiss',
                    child: Row(
                      children: [
                        Icon(Icons.close, size: 16),
                        SizedBox(width: 8),
                        Text('忽略此推荐'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.sm),
          
          // 标题和描述
          Text(
            item.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: VanHubSemanticColors.getTextColor(context),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          SizedBox(height: VanHubResponsiveSpacing.xs),
          
          Text(
            item.description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          // 价格和评分
          if (item.price != null || item.rating != null) ...[
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Row(
              children: [
                if (item.price != null) ...[
                  Text(
                    '¥${item.price!.toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: VanHubSemanticColors.success,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                ],
                if (item.rating != null) ...[
                  Icon(
                    Icons.star,
                    size: 16,
                    color: VanHubSemanticColors.warning,
                  ),
                  SizedBox(width: VanHubResponsiveSpacing.xs),
                  Text(
                    item.rating!.toStringAsFixed(1),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (item.reviewCount != null) ...[
                    Text(
                      ' (${item.reviewCount})',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: VanHubSemanticColors.getTextColor(context, secondary: true),
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ],
          
          // 推荐原因
          if (widget.showReasons && item.reasons.isNotEmpty) ...[
            SizedBox(height: VanHubResponsiveSpacing.sm),
            Wrap(
              spacing: VanHubResponsiveSpacing.xs,
              runSpacing: VanHubResponsiveSpacing.xs,
              children: item.reasons.take(2).map((reason) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: VanHubBrandColors.primaryContainer,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    _getReasonText(reason),
                    style: TextStyle(
                      fontSize: 10,
                      color: VanHubBrandColors.onPrimaryContainer,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
          
          const Spacer(),
          
          // 反馈按钮
          if (widget.enableFeedback) ...[
            SizedBox(height: VanHubResponsiveSpacing.md),
            Row(
              children: [
                Expanded(
                  child: VanHubButtonV2(
                    text: hasFeedback && isLiked ? '已喜欢' : '喜欢',
                    variant: hasFeedback && isLiked 
                        ? VanHubButtonVariant.primary 
                        : VanHubButtonVariant.ghost,
                    size: VanHubButtonSize.sm,
                    leadingIcon: Icons.thumb_up_outlined,
                    onPressed: () => _handleFeedback(item, true),
                  ),
                ),
                SizedBox(width: VanHubResponsiveSpacing.sm),
                Expanded(
                  child: VanHubButtonV2(
                    text: hasFeedback && !isLiked ? '不喜欢' : '不感兴趣',
                    variant: hasFeedback && !isLiked 
                        ? VanHubButtonVariant.danger 
                        : VanHubButtonVariant.ghost,
                    size: VanHubButtonSize.sm,
                    leadingIcon: Icons.thumb_down_outlined,
                    onPressed: () => _handleFeedback(item, false),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    ).animate()
      .fadeIn(
        duration: widget.animationDuration,
        delay: widget.staggerDelay * index,
      )
      .slideY(
        begin: 0.2,
        end: 0,
        duration: widget.animationDuration,
        delay: widget.staggerDelay * index,
      );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_awesome_outlined,
            size: 64,
            color: VanHubSemanticColors.getTextColor(context, secondary: true),
          ),
          SizedBox(height: VanHubResponsiveSpacing.md),
          Text(
            '暂无推荐内容',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
          ),
          SizedBox(height: VanHubResponsiveSpacing.sm),
          Text(
            '继续使用应用，我们会为您提供个性化推荐',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: VanHubSemanticColors.getTextColor(context, secondary: true),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 获取推荐原因文本
  String _getReasonText(RecommendationReason reason) {
    switch (reason) {
      case RecommendationReason.userHistory:
        return '基于历史';
      case RecommendationReason.similarUsers:
        return '相似用户';
      case RecommendationReason.projectSimilarity:
        return '项目相似';
      case RecommendationReason.trending:
        return '热门趋势';
      case RecommendationReason.expertChoice:
        return '专家推荐';
      case RecommendationReason.costEffective:
        return '性价比高';
      case RecommendationReason.qualityFirst:
        return '质量优先';
      case RecommendationReason.innovative:
        return '创新产品';
    }
  }
}
