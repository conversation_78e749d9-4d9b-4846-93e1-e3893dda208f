import 'package:freezed_annotation/freezed_annotation.dart';

part 'project_export_config.freezed.dart';
part 'project_export_config.g.dart';

/// 项目导出配置
@freezed
class ProjectExportConfig with _$ProjectExportConfig {
  const factory ProjectExportConfig({
    // 基本设置
    @Default(true) bool includeOverview,
    @Default(true) bool includeBom,
    @Default(true) bool includeTimeline,
    @Default(true) bool includeProgress,
    @Default(true) bool includeCosts,
    @Default(false) bool includeImages,
    @Default(false) bool includeNotes,
    
    // 详细设置
    @Default(true) bool showMaterialDetails,
    @Default(true) bool showSupplierInfo,
    @Default(true) bool showPricing,
    @Default(false) bool showInternalNotes,
    
    // 格式设置
    @Default('standard') String template,
    @Default('A4') String pageSize,
    @Default(true) bool includeHeader,
    @Default(true) bool includeFooter,
    @Default(true) bool includePageNumbers,
    
    // 自定义设置
    String? customTitle,
    String? customSubtitle,
    String? companyName,
    String? companyLogo,
    Map<String, dynamic>? customFields,
    
    // 筛选设置
    List<String>? includedCategories,
    List<String>? excludedStatuses,
    DateTime? dateFrom,
    DateTime? dateTo,
  }) = _ProjectExportConfig;

  factory ProjectExportConfig.fromJson(Map<String, dynamic> json) => 
      _$ProjectExportConfigFromJson(json);
}

/// 导出模板类型
enum ProjectExportTemplate {
  standard,
  detailed,
  summary,
  presentation,
  technical,
  financial,
  custom,
}

/// 导出部分
enum ProjectExportSection {
  overview,
  bom,
  timeline,
  progress,
  costs,
  materials,
  suppliers,
  notes,
  images,
  analytics,
}

/// 项目导出配置扩展
extension ProjectExportConfigX on ProjectExportConfig {
  /// 获取包含的部分
  List<ProjectExportSection> get includedSections {
    final sections = <ProjectExportSection>[];
    
    if (includeOverview) sections.add(ProjectExportSection.overview);
    if (includeBom) sections.add(ProjectExportSection.bom);
    if (includeTimeline) sections.add(ProjectExportSection.timeline);
    if (includeProgress) sections.add(ProjectExportSection.progress);
    if (includeCosts) sections.add(ProjectExportSection.costs);
    if (showMaterialDetails) sections.add(ProjectExportSection.materials);
    if (showSupplierInfo) sections.add(ProjectExportSection.suppliers);
    if (includeNotes) sections.add(ProjectExportSection.notes);
    if (includeImages) sections.add(ProjectExportSection.images);
    
    return sections;
  }

  /// 是否为完整导出
  bool get isFullExport {
    return includeOverview && 
           includeBom && 
           includeTimeline && 
           includeProgress && 
           includeCosts;
  }

  /// 获取文件名后缀
  String get filenameSuffix {
    if (isFullExport) return 'complete';
    
    final sections = includedSections;
    if (sections.length == 1) {
      return sections.first.name;
    } else if (sections.length <= 3) {
      return sections.map((s) => s.name).join('_');
    } else {
      return 'custom';
    }
  }

  /// 预估文件大小（KB）
  int get estimatedFileSizeKB {
    int size = 50; // 基础大小
    
    if (includeOverview) size += 20;
    if (includeBom) size += 100;
    if (includeTimeline) size += 50;
    if (includeProgress) size += 30;
    if (includeCosts) size += 40;
    if (includeImages) size += 500; // 图片会显著增加大小
    if (showMaterialDetails) size += 80;
    if (includeNotes) size += 60;
    
    return size;
  }

  /// 预估导出时间（秒）
  int get estimatedExportTimeSeconds {
    int time = 2; // 基础时间
    
    if (includeBom) time += 3;
    if (includeTimeline) time += 2;
    if (includeImages) time += 10;
    if (showMaterialDetails) time += 5;
    
    return time;
  }
}
