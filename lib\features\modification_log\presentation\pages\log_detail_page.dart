import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_animate/flutter_animate.dart';
// import 'package:share_plus/share_plus.dart'; // 暂时禁用以解决网络问题
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/design_system/foundation/colors/brand_colors.dart';
import '../../../../core/design_system/foundation/spacing/spacing.dart';
import '../../../../core/design_system/foundation/typography/typography.dart';
import '../../domain/entities/log_entry.dart';
import '../../domain/usecases/get_log_entry_usecase.dart';
import '../../di/providers.dart';
import '../widgets/log_entry_detail_widget.dart';
import '../widgets/bom_items_section_widget.dart';
import '../widgets/enhanced_media_gallery_widget.dart';
import '../widgets/log_timeline_widget.dart';
import '../widgets/log_stats_widget.dart';
import 'log_edit_page.dart';

part 'log_detail_page.g.dart';

/// 获取日志详情的Provider
@riverpod
Future<LogEntry> logDetail(Ref ref, String logId) async {
  final useCase = ref.read(getLogEntryUseCaseProvider);
  final result = await useCase.call(GetLogEntryParams(logId: logId));

  return result.fold(
    (failure) => throw Exception(failure.message),
    (logEntry) => logEntry,
  );
}

/// 日志详情页面
/// 遵循Clean Architecture原则，只负责UI展示和用户交互
class LogDetailPage extends ConsumerWidget {
  final String logId;

  const LogDetailPage({
    super.key,
    required this.logId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logDetailAsync = ref.watch(logDetailProvider(logId));

    return Scaffold(
      body: logDetailAsync.when(
        data: (logEntry) => _buildLogDetailContent(context, ref, logEntry),
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => _buildErrorState(context, ref, error),
      ),
      floatingActionButton: logDetailAsync.maybeWhen(
        data: (logEntry) => _buildFloatingActionButton(context, ref, logEntry),
        orElse: () => null,
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日志详情'),
        backgroundColor: VanHubBrandColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: VanHubSpacing.md),
            Text(
              '加载日志详情失败',
              style: VanHubTypography.headlineSmall,
            ),
            const SizedBox(height: VanHubSpacing.sm),
            Text(
              error.toString(),
              style: VanHubTypography.bodyMedium.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: VanHubSpacing.lg),
            ElevatedButton.icon(
              onPressed: () => ref.refresh(logDetailProvider(logId)),
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
              style: ElevatedButton.styleFrom(
                backgroundColor: VanHubBrandColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogDetailContent(BuildContext context, WidgetRef ref, LogEntry logEntry) {
    return CustomScrollView(
      slivers: [
        // 自定义AppBar
        _buildSliverAppBar(context, ref, logEntry),

        // 内容区域
        SliverToBoxAdapter(
          child: Column(
            children: [
              // 日志统计卡片
              LogStatsWidget(logEntry: logEntry)
                  .animate()
                  .fadeIn(duration: 300.ms)
                  .slideY(begin: 0.2, end: 0),

              const SizedBox(height: VanHubSpacing.md),

              // 日志基本信息
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
                child: LogEntryDetailWidget(logEntry: logEntry)
                    .animate(delay: 100.ms)
                    .fadeIn(duration: 300.ms)
                    .slideY(begin: 0.2, end: 0),
              ),

              const SizedBox(height: VanHubSpacing.lg),

              // 媒体画廊
              if (logEntry.mediaIds.isNotEmpty) ...[
                _buildSectionHeader('相关媒体', Icons.photo_library),
                const SizedBox(height: VanHubSpacing.sm),
                EnhancedMediaGalleryWidget(
                  mediaIds: logEntry.mediaIds,
                  onMediaTap: (mediaId) => _showMediaViewer(context, mediaId),
                ).animate(delay: 200.ms)
                    .fadeIn(duration: 300.ms)
                    .slideY(begin: 0.2, end: 0),
                const SizedBox(height: VanHubSpacing.lg),
              ],

              // 关联的BOM项目
              if (logEntry.relatedBomItemIds.isNotEmpty) ...[
                _buildSectionHeader('使用的材料', Icons.inventory),
                const SizedBox(height: VanHubSpacing.sm),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
                  child: BomItemsSectionWidget(bomItemIds: logEntry.relatedBomItemIds)
                      .animate(delay: 300.ms)
                      .fadeIn(duration: 300.ms)
                      .slideY(begin: 0.2, end: 0),
                ),
                const SizedBox(height: VanHubSpacing.lg),
              ],

              // 时间轴关联
              _buildSectionHeader('项目时间轴', Icons.timeline),
              const SizedBox(height: VanHubSpacing.sm),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
                child: LogTimelineWidget(
                  projectId: logEntry.projectId,
                  currentLogId: logEntry.id,
                ).animate(delay: 400.ms)
                    .fadeIn(duration: 300.ms)
                    .slideY(begin: 0.2, end: 0),
              ),

              const SizedBox(height: VanHubSpacing.lg),

              // 成本和时间信息
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: VanHubSpacing.md),
                child: Row(
                  children: [
                    if (logEntry.totalCost > 0) ...[
                      Expanded(
                        child: _buildCostCard(logEntry)
                            .animate(delay: 500.ms)
                            .fadeIn(duration: 300.ms)
                            .slideX(begin: -0.2, end: 0),
                      ),
                      const SizedBox(width: VanHubSpacing.sm),
                    ],
                    Expanded(
                      child: _buildTimeCard(logEntry)
                          .animate(delay: 600.ms)
                          .fadeIn(duration: 300.ms)
                          .slideX(begin: 0.2, end: 0),
                    ),
                  ],
                ),
              ),

              // 底部间距
              const SizedBox(height: 100),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCostSection(LogEntry logEntry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '成本信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('总成本:'),
                Text(
                  '¥${logEntry.totalCost.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            if (logEntry.timeSpentMinutes > 0) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('工时:'),
                  Text('${logEntry.timeSpentMinutes} 分钟'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSection(LogEntry logEntry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '时间信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('记录日期:'),
                Text(logEntry.logDate.toString().split(' ')[0]),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('创建时间:'),
                Text(logEntry.createdAt.toString().split(' ')[0]),
              ],
            ),
            if (logEntry.updatedAt != logEntry.createdAt) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('最后更新:'),
                  Text(logEntry.updatedAt.toString().split(' ')[0]),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _navigateToEditPage(BuildContext context, WidgetRef ref) async {
    final logDetailAsync = ref.read(logDetailProvider(logId));

    logDetailAsync.when(
      data: (logEntry) async {
        final result = await Navigator.of(context).push<bool>(
          MaterialPageRoute(
            builder: (context) => LogEditPage(
              logEntry: logEntry,
              projectId: logEntry.projectId,
            ),
          ),
        );

        if (result == true) {
          // 刷新页面数据
          ref.invalidate(logDetailProvider(logId));
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('日志更新成功')),
          );
        }
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('正在加载日志数据...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $error')),
        );
      },
    );
  }

  void _shareLog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '分享日志',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.link),
              title: const Text('复制链接'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('链接已复制到剪贴板')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('分享到其他应用'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('分享功能开发中')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaSection(List<String> mediaIds) {
    if (mediaIds.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.photo_library,
                  color: Colors.deepOrange,
                ),
                const SizedBox(width: 8),
                Text(
                  '媒体文件 (${mediaIds.length}项)',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: mediaIds.map((mediaId) => _buildMediaPlaceholder(mediaId)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String mediaId) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image,
            color: Colors.grey[600],
            size: 32,
          ),
          const SizedBox(height: 4),
          Text(
            '媒体',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton(BuildContext context, WidgetRef ref, LogEntry logEntry) {
    return FloatingActionButton.extended(
      onPressed: () {
        // 分享功能开发中
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('分享功能开发中')),
        );
      },
      icon: const Icon(Icons.share),
      label: const Text('分享'),
    );
  }

  /// 构建SliverAppBar
  Widget _buildSliverAppBar(BuildContext context, WidgetRef ref, LogEntry logEntry) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(logEntry.title),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示媒体查看器
  void _showMediaViewer(BuildContext context, String mediaId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('媒体查看器'),
        content: const Text('媒体查看功能开发中'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 构建成本卡片
  Widget _buildCostCard(LogEntry logEntry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '成本信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text('总成本: ¥${logEntry.totalCost.toStringAsFixed(2) ?? '0.00'}'),
            Text('材料数量: ${logEntry.relatedBomItemIds.length}'),
          ],
        ),
      ),
    );
  }

  /// 构建时间卡片
  Widget _buildTimeCard(LogEntry logEntry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '时间信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text('创建时间: ${logEntry.createdAt.toString().substring(0, 19)}'),
            Text('更新时间: ${logEntry.updatedAt.toString().substring(0, 19)}'),
            Text('耗时: ${logEntry.timeSpentMinutes} 分钟'),
            if (logEntry.actualTimeMinutes != null)
              Text('实际耗时: ${logEntry.actualTimeMinutes!} 分钟'),
          ],
        ),
      ),
    );
  }
}