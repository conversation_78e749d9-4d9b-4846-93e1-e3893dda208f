import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../entities/share_permission.dart';
import '../entities/share_access_log.dart';
import 'share_service.dart';

/// 分享权限服务接口
abstract class SharePermissionService {
  /// 检查用户是否有分享权限
  Future<Either<Failure, bool>> canUserShare({
    required String contentId,
    required ShareContentType contentType,
    required String userId,
  });

  /// 检查用户是否有访问权限
  Future<Either<Failure, bool>> canUserAccess({
    required String shareId,
    String? userId,
    String? accessToken,
    String? password,
  });

  /// 设置内容分享权限
  Future<Either<Failure, SharePermission>> setContentPermission({
    required String contentId,
    required ShareContentType contentType,
    required String ownerId,
    required SharePermissionLevel permissionLevel,
    List<String>? allowedUsers,
    List<String>? allowedRoles,
  });

  /// 获取内容分享权限
  Future<Either<Failure, SharePermission>> getContentPermission({
    required String contentId,
    required ShareContentType contentType,
  });

  /// 添加用户到分享白名单
  Future<Either<Failure, void>> addUserToWhitelist({
    required String shareId,
    required String userId,
    required String operatorId,
  });

  /// 从分享白名单移除用户
  Future<Either<Failure, void>> removeUserFromWhitelist({
    required String shareId,
    required String userId,
    required String operatorId,
  });

  /// 添加用户到分享黑名单
  Future<Either<Failure, void>> addUserToBlacklist({
    required String shareId,
    required String userId,
    required String operatorId,
  });

  /// 从分享黑名单移除用户
  Future<Either<Failure, void>> removeUserFromBlacklist({
    required String shareId,
    required String userId,
    required String operatorId,
  });

  /// 记录分享访问日志
  Future<Either<Failure, void>> logShareAccess({
    required String shareId,
    String? userId,
    required String ipAddress,
    String? userAgent,
    String? referrer,
    bool success = true,
    String? failureReason,
  });

  /// 获取分享访问日志
  Future<Either<Failure, List<ShareAccessLog>>> getShareAccessLogs({
    required String shareId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
    int offset = 0,
  });

  /// 获取用户访问统计
  Future<Either<Failure, Map<String, dynamic>>> getUserAccessStats({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 获取内容分享统计
  Future<Either<Failure, Map<String, dynamic>>> getContentShareStats({
    required String contentId,
    required ShareContentType contentType,
  });

  /// 检查IP是否被限制
  Future<Either<Failure, bool>> isIpRestricted(String ipAddress);

  /// 添加IP到限制列表
  Future<Either<Failure, void>> addIpRestriction({
    required String ipAddress,
    required String reason,
    required String operatorId,
    DateTime? expiresAt,
  });

  /// 移除IP限制
  Future<Either<Failure, void>> removeIpRestriction({
    required String ipAddress,
    required String operatorId,
  });

  /// 检查访问频率限制
  Future<Either<Failure, bool>> checkRateLimit({
    required String shareId,
    String? userId,
    required String ipAddress,
  });

  /// 验证分享密码
  Future<Either<Failure, bool>> validateSharePassword({
    required String shareId,
    required String password,
  });

  /// 生成临时访问令牌
  Future<Either<Failure, String>> generateTemporaryAccessToken({
    required String shareId,
    required String userId,
    Duration? expiresIn,
  });

  /// 验证临时访问令牌
  Future<Either<Failure, bool>> validateTemporaryAccessToken({
    required String shareId,
    required String token,
  });

  /// 撤销临时访问令牌
  Future<Either<Failure, void>> revokeTemporaryAccessToken({
    required String shareId,
    required String token,
  });
}
