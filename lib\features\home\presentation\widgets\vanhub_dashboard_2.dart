/// VanHub Dashboard 2.0
/// 
/// 个性化仪表盘组件
/// 基于VanHub Design System 2.0设计
/// 
/// 特性：
/// 1. 个性化布局 - 根据用户习惯调整内容
/// 2. 智能推荐 - AI驱动的项目和材料推荐
/// 3. 实时数据 - 动态更新的统计信息
/// 4. 快速操作 - 一键访问常用功能
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/design_system/vanhub_design_system.dart';
import '../../../../core/design_system/components/vanhub_card.dart';

/// VanHub仪表盘2.0组件
class VanHubDashboard2 extends ConsumerStatefulWidget {
  const VanHubDashboard2({super.key});

  @override
  ConsumerState<VanHubDashboard2> createState() => _VanHubDashboard2State();
}

class _VanHubDashboard2State extends ConsumerState<VanHubDashboard2>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: VanHubDesignSystem.durationSlow,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: VanHubDesignSystem.curveDefault,
    ));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: CustomScrollView(
        slivers: [
          // 欢迎区域
          SliverToBoxAdapter(
            child: _buildWelcomeSection(),
          ),
          
          // 快速操作区域
          SliverToBoxAdapter(
            child: _buildQuickActionsSection(),
          ),
          
          // 项目概览区域
          SliverToBoxAdapter(
            child: _buildProjectOverviewSection(),
          ),
          
          // 智能推荐区域
          SliverToBoxAdapter(
            child: _buildSmartRecommendationsSection(),
          ),
          
          // 最近活动区域
          SliverToBoxAdapter(
            child: _buildRecentActivitySection(),
          ),
          
          // 统计数据区域
          SliverToBoxAdapter(
            child: _buildStatisticsSection(),
          ),
          
          // 底部间距
          SliverToBoxAdapter(
            child: VanHubSpacing.gapVerticalXl,
          ),
        ],
      ),
    );
  }

  /// 构建欢迎区域
  Widget _buildWelcomeSection() {
    return Container(
      margin: VanHubSpacing.paddingPageHorizontal,
      child: VanHubCard.filled(
        backgroundColor: VanHubDesignSystem.brandGradient.colors.first.withValues(alpha: 0.1),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: VanHubSpacing.paddingSm,
                  decoration: BoxDecoration(
                    color: VanHubColors.brandPrimary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
                  ),
                  child: Icon(
                    VanHubIcons.van,
                    color: VanHubColors.brandPrimary,
                    size: 32,
                  ),
                ),
                VanHubSpacing.gapHorizontalMd,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '欢迎回来！',
                        style: VanHubTypography.headlineMedium.copyWith(
                          color: VanHubColors.brandPrimary,
                        ),
                      ),
                      VanHubSpacing.gapVerticalXs,
                      Text(
                        '让我们继续打造您的梦想房车',
                        style: VanHubTypography.bodyMedium.copyWith(
                          color: VanHubColors.neutralGray600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            VanHubSpacing.gapVerticalMd,
            Container(
              padding: VanHubSpacing.paddingSm,
              decoration: BoxDecoration(
                color: VanHubColors.semanticInfo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                border: Border.all(
                  color: VanHubColors.semanticInfo.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    VanHubIcons.info,
                    color: VanHubColors.semanticInfo,
                    size: 16,
                  ),
                  VanHubSpacing.gapHorizontalSm,
                  Expanded(
                    child: Text(
                      '今日推荐：查看最新的太阳能系统优化方案',
                      style: VanHubTypography.labelMedium.copyWith(
                        color: VanHubColors.semanticInfo,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速操作区域
  Widget _buildQuickActionsSection() {
    return Container(
      margin: VanHubSpacing.paddingPageHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VanHubSpacing.gapVerticalLg,
          Text(
            '快速操作',
            style: VanHubTypography.headlineSmall,
          ),
          VanHubSpacing.gapVerticalMd,
          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  icon: VanHubIcons.addCircle,
                  title: '新建项目',
                  subtitle: '开始新的改装',
                  color: VanHubColors.brandPrimary,
                  onTap: () => _onCreateProject(),
                ),
              ),
              VanHubSpacing.gapHorizontalMd,
              Expanded(
                child: _buildQuickActionCard(
                  icon: VanHubIcons.materialsOutlined,
                  title: '添加材料',
                  subtitle: '扩充材料库',
                  color: VanHubColors.brandSecondary,
                  onTap: () => _onAddMaterial(),
                ),
              ),
            ],
          ),
          VanHubSpacing.gapVerticalMd,
          Row(
            children: [
              Expanded(
                child: _buildQuickActionCard(
                  icon: VanHubIcons.bom,
                  title: 'BOM管理',
                  subtitle: '管理物料清单',
                  color: VanHubColors.brandAccent,
                  onTap: () => _onManageBOM(),
                ),
              ),
              VanHubSpacing.gapHorizontalMd,
              Expanded(
                child: _buildQuickActionCard(
                  icon: VanHubIcons.analytics,
                  title: '数据分析',
                  subtitle: '查看统计报告',
                  color: VanHubColors.semanticInfo,
                  onTap: () => _onViewAnalytics(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建快速操作卡片
  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return VanHubCard.elevated(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: VanHubSpacing.paddingSm,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusLg),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          VanHubSpacing.gapVerticalSm,
          Text(
            title,
            style: VanHubTypography.labelLarge.copyWith(
              fontWeight: VanHubTypography.weightSemiBold,
            ),
            textAlign: TextAlign.center,
          ),
          VanHubSpacing.gapVerticalXs,
          Text(
            subtitle,
            style: VanHubTypography.labelSmall.copyWith(
              color: VanHubColors.neutralGray500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建项目概览区域
  Widget _buildProjectOverviewSection() {
    return Container(
      margin: VanHubSpacing.paddingPageHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VanHubSpacing.gapVerticalLg,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '我的项目',
                style: VanHubTypography.headlineSmall,
              ),
              VanHubButton(
                text: '查看全部',
                variant: VanHubButtonVariant.text,
                size: VanHubButtonSize.small,
                icon: VanHubIcons.right,
                onPressed: () => _onViewAllProjects(),
              ),
            ],
          ),
          VanHubSpacing.gapVerticalMd,
          SizedBox(
            height: 200,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: 3, // 示例数据
              separatorBuilder: (context, index) => VanHubSpacing.gapHorizontalMd,
              itemBuilder: (context, index) => _buildProjectCard(index),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建项目卡片
  Widget _buildProjectCard(int index) {
    final projects = [
      {
        'title': '我的第一台房车',
        'progress': 0.75,
        'status': '进行中',
        'image': 'assets/images/van_placeholder.png',
      },
      {
        'title': '越野房车改装',
        'progress': 0.45,
        'status': '规划中',
        'image': 'assets/images/van_placeholder.png',
      },
      {
        'title': '豪华房车定制',
        'progress': 0.90,
        'status': '即将完成',
        'image': 'assets/images/van_placeholder.png',
      },
    ];

    final project = projects[index];
    
    return SizedBox(
      width: 280,
      child: VanHubCard.elevated(
        onTap: () => _onProjectTap(index),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 项目图片
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: VanHubColors.neutralGray100,
                borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
                image: const DecorationImage(
                  image: AssetImage('assets/images/van_placeholder.png'),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: VanHubSpacing.sm,
                    right: VanHubSpacing.sm,
                    child: Container(
                      padding: VanHubSpacing.paddingXs,
                      decoration: BoxDecoration(
                        color: VanHubColors.semanticSuccess.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
                      ),
                      child: Text(
                        project['status'] as String,
                        style: VanHubTypography.labelSmall.copyWith(
                          color: VanHubColors.onSemanticSuccess,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            VanHubSpacing.gapVerticalSm,
            Text(
              project['title'] as String,
              style: VanHubTypography.labelLarge.copyWith(
                fontWeight: VanHubTypography.weightSemiBold,
              ),
            ),
            VanHubSpacing.gapVerticalXs,
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: project['progress'] as double,
                    backgroundColor: VanHubColors.neutralGray200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      VanHubColors.brandPrimary,
                    ),
                  ),
                ),
                VanHubSpacing.gapHorizontalSm,
                Text(
                  '${((project['progress'] as double) * 100).toInt()}%',
                  style: VanHubTypography.labelSmall.copyWith(
                    color: VanHubColors.neutralGray600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建智能推荐区域
  Widget _buildSmartRecommendationsSection() {
    return Container(
      margin: VanHubSpacing.paddingPageHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VanHubSpacing.gapVerticalLg,
          Row(
            children: [
              Icon(
                VanHubIcons.analytics,
                color: VanHubColors.brandAccent,
                size: 20,
              ),
              VanHubSpacing.gapHorizontalSm,
              Text(
                'AI智能推荐',
                style: VanHubTypography.headlineSmall.copyWith(
                  color: VanHubColors.brandAccent,
                ),
              ),
            ],
          ),
          VanHubSpacing.gapVerticalMd,
          VanHubCard.filled(
            backgroundColor: VanHubColors.brandAccent.withValues(alpha: 0.05),
            child: Column(
              children: [
                _buildRecommendationItem(
                  icon: VanHubIcons.solar,
                  title: '太阳能系统升级',
                  description: '基于您的用电需求，推荐400W太阳能板组合',
                  confidence: '95%匹配',
                ),
                Divider(color: VanHubColors.neutralGray200),
                _buildRecommendationItem(
                  icon: VanHubIcons.storage,
                  title: '储物空间优化',
                  description: '发现3个可优化的储物区域，预计增加30%空间',
                  confidence: '88%匹配',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建推荐项目
  Widget _buildRecommendationItem({
    required IconData icon,
    required String title,
    required String description,
    required String confidence,
  }) {
    return Padding(
      padding: VanHubSpacing.paddingVerticalSm,
      child: Row(
        children: [
          Container(
            padding: VanHubSpacing.paddingSm,
            decoration: BoxDecoration(
              color: VanHubColors.brandAccent.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
            child: Icon(
              icon,
              color: VanHubColors.brandAccent,
              size: 20,
            ),
          ),
          VanHubSpacing.gapHorizontalMd,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: VanHubTypography.labelLarge.copyWith(
                          fontWeight: VanHubTypography.weightSemiBold,
                        ),
                      ),
                    ),
                    Container(
                      padding: VanHubSpacing.paddingXs,
                      decoration: BoxDecoration(
                        color: VanHubColors.semanticSuccess.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusSm),
                      ),
                      child: Text(
                        confidence,
                        style: VanHubTypography.labelSmall.copyWith(
                          color: VanHubColors.semanticSuccess,
                        ),
                      ),
                    ),
                  ],
                ),
                VanHubSpacing.gapVerticalXs,
                Text(
                  description,
                  style: VanHubTypography.bodySmall.copyWith(
                    color: VanHubColors.neutralGray600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建最近活动区域
  Widget _buildRecentActivitySection() {
    return Container(
      margin: VanHubSpacing.paddingPageHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VanHubSpacing.gapVerticalLg,
          Text(
            '最近活动',
            style: VanHubTypography.headlineSmall,
          ),
          VanHubSpacing.gapVerticalMd,
          VanHubCard.outlined(
            child: Column(
              children: [
                _buildActivityItem(
                  icon: VanHubIcons.add,
                  title: '添加了新材料',
                  subtitle: '锂电池 - 200Ah',
                  time: '2小时前',
                  color: VanHubColors.brandPrimary,
                ),
                Divider(color: VanHubColors.neutralGray200),
                _buildActivityItem(
                  icon: VanHubIcons.edit,
                  title: '更新了项目进度',
                  subtitle: '我的第一台房车 - 75%',
                  time: '5小时前',
                  color: VanHubColors.brandSecondary,
                ),
                Divider(color: VanHubColors.neutralGray200),
                _buildActivityItem(
                  icon: VanHubIcons.success,
                  title: '完成了里程碑',
                  subtitle: '电路系统安装',
                  time: '1天前',
                  color: VanHubColors.semanticSuccess,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建活动项目
  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
    required Color color,
  }) {
    return Padding(
      padding: VanHubSpacing.paddingVerticalSm,
      child: Row(
        children: [
          Container(
            padding: VanHubSpacing.paddingSm,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(VanHubDesignSystem.radiusBase),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          VanHubSpacing.gapHorizontalMd,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: VanHubTypography.labelMedium.copyWith(
                    fontWeight: VanHubTypography.weightMedium,
                  ),
                ),
                VanHubSpacing.gapVerticalXs,
                Text(
                  subtitle,
                  style: VanHubTypography.labelSmall.copyWith(
                    color: VanHubColors.neutralGray600,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: VanHubTypography.labelSmall.copyWith(
              color: VanHubColors.neutralGray500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计数据区域
  Widget _buildStatisticsSection() {
    return Container(
      margin: VanHubSpacing.paddingPageHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VanHubSpacing.gapVerticalLg,
          Text(
            '数据概览',
            style: VanHubTypography.headlineSmall,
          ),
          VanHubSpacing.gapVerticalMd,
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: '总项目',
                  value: '12',
                  subtitle: '进行中: 3',
                  icon: VanHubIcons.projects,
                  color: VanHubColors.brandPrimary,
                ),
              ),
              VanHubSpacing.gapHorizontalMd,
              Expanded(
                child: _buildStatCard(
                  title: '材料库',
                  value: '156',
                  subtitle: '本月新增: 23',
                  icon: VanHubIcons.materials,
                  color: VanHubColors.brandSecondary,
                ),
              ),
            ],
          ),
          VanHubSpacing.gapVerticalMd,
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: '总投入',
                  value: '¥45.2K',
                  subtitle: '预算剩余: 60%',
                  icon: VanHubIcons.budget,
                  color: VanHubColors.brandAccent,
                ),
              ),
              VanHubSpacing.gapHorizontalMd,
              Expanded(
                child: _buildStatCard(
                  title: '完成度',
                  value: '68%',
                  subtitle: '超前进度: 5%',
                  icon: VanHubIcons.progress,
                  color: VanHubColors.semanticSuccess,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return VanHubCard.elevated(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: VanHubTypography.labelMedium.copyWith(
                  color: VanHubColors.neutralGray600,
                ),
              ),
              Icon(
                icon,
                color: color,
                size: 20,
              ),
            ],
          ),
          VanHubSpacing.gapVerticalSm,
          Text(
            value,
            style: VanHubTypography.displaySmall.copyWith(
              color: color,
              fontWeight: VanHubTypography.weightBold,
            ),
          ),
          VanHubSpacing.gapVerticalXs,
          Text(
            subtitle,
            style: VanHubTypography.labelSmall.copyWith(
              color: VanHubColors.neutralGray500,
            ),
          ),
        ],
      ),
    );
  }

  // 事件处理方法
  void _onCreateProject() {
    // TODO: 实现创建项目功能
  }

  void _onAddMaterial() {
    // TODO: 实现添加材料功能
  }

  void _onManageBOM() {
    // TODO: 实现BOM管理功能
  }

  void _onViewAnalytics() {
    // TODO: 实现数据分析功能
  }

  void _onViewAllProjects() {
    // TODO: 实现查看全部项目功能
  }

  void _onProjectTap(int index) {
    // TODO: 实现项目详情功能
  }
}
