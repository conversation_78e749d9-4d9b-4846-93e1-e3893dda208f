import 'package:fpdart/fpdart.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/services/notification_service.dart';
import '../../domain/entities/notification.dart';
import '../../domain/repositories/notification_repository.dart';

/// 通知服务实现
class NotificationServiceImpl implements NotificationService {
  final NotificationRepository _repository;

  const NotificationServiceImpl({
    required NotificationRepository repository,
  }) : _repository = repository;

  @override
  Future<Either<Failure, Notification>> createNotification(Notification notification) async {
    try {
      return await _repository.createNotification(notification);
    } catch (e) {
      return Left(UnknownFailure(message: '创建通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BatchOperationResult>> createNotifications(List<Notification> notifications) async {
    try {
      final result = await _repository.createNotifications(notifications);
      
      return result.fold(
        (failure) => Left(failure),
        (createdNotifications) {
          final batchResult = BatchOperationResult(
            successCount: createdNotifications.length,
            failureCount: notifications.length - createdNotifications.length,
            failedIds: [],
            errors: [],
          );
          return Right(batchResult);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '批量创建通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Notification>> getNotification(String notificationId) async {
    try {
      return await _repository.getNotification(notificationId);
    } catch (e) {
      return Left(UnknownFailure(message: '获取通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> getNotifications(NotificationQuery query) async {
    try {
      return await _repository.getNotifications(query);
    } catch (e) {
      return Left(UnknownFailure(message: '查询通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> getUserNotifications({
    required String userId,
    List<NotificationType>? types,
    List<NotificationStatus>? statuses,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final query = NotificationQuery(
        userId: userId,
        types: types,
        statuses: statuses,
        limit: limit,
        offset: offset,
      );
      return await _repository.getNotifications(query);
    } catch (e) {
      return Left(UnknownFailure(message: '获取用户通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> getUnreadNotifications(String userId) async {
    try {
      final query = NotificationQuery(
        userId: userId,
        statuses: [NotificationStatus.unread],
        limit: 100,
      );
      return await _repository.getNotifications(query);
    } catch (e) {
      return Left(UnknownFailure(message: '获取未读通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Notification>> markAsRead(String notificationId) async {
    try {
      final notificationResult = await _repository.getNotification(notificationId);
      
      return await notificationResult.fold(
        (failure) async => Left(failure),
        (notification) async {
          final updatedNotification = notification.markAsRead();
          return await _repository.updateNotification(updatedNotification);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '标记通知为已读失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Notification>> markAsUnread(String notificationId) async {
    try {
      final notificationResult = await _repository.getNotification(notificationId);
      
      return await notificationResult.fold(
        (failure) async => Left(failure),
        (notification) async {
          final updatedNotification = notification.markAsUnread();
          return await _repository.updateNotification(updatedNotification);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '标记通知为未读失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BatchOperationResult>> markAllAsRead(String userId) async {
    try {
      final unreadNotificationsResult = await getUnreadNotifications(userId);
      
      return await unreadNotificationsResult.fold(
        (failure) async => Left(failure),
        (notifications) async {
          final updatedNotifications = notifications.map((n) => n.markAsRead()).toList();
          
          final updateResult = await _repository.updateNotifications(updatedNotifications);
          
          return updateResult.fold(
            (failure) => Left(failure),
            (updated) {
              final batchResult = BatchOperationResult(
                successCount: updated.length,
                failureCount: notifications.length - updated.length,
                failedIds: [],
                errors: [],
              );
              return Right(batchResult);
            },
          );
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '批量标记为已读失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BatchOperationResult>> markNotificationsAsRead(List<String> notificationIds) async {
    try {
      final notifications = <Notification>[];
      final errors = <String>[];
      
      // 获取所有通知
      for (final id in notificationIds) {
        final result = await _repository.getNotification(id);
        result.fold(
          (failure) => errors.add('获取通知 $id 失败: ${failure.message}'),
          (notification) => notifications.add(notification),
        );
      }
      
      // 标记为已读
      final updatedNotifications = notifications.map((n) => n.markAsRead()).toList();
      
      final updateResult = await _repository.updateNotifications(updatedNotifications);
      
      return updateResult.fold(
        (failure) => Left(failure),
        (updated) {
          final batchResult = BatchOperationResult(
            successCount: updated.length,
            failureCount: notificationIds.length - updated.length,
            failedIds: [],
            errors: errors,
          );
          return Right(batchResult);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '批量标记通知为已读失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Notification>> archiveNotification(String notificationId) async {
    try {
      final notificationResult = await _repository.getNotification(notificationId);
      
      return await notificationResult.fold(
        (failure) async => Left(failure),
        (notification) async {
          final archivedNotification = notification.archive();
          return await _repository.updateNotification(archivedNotification);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '归档通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BatchOperationResult>> archiveNotifications(List<String> notificationIds) async {
    try {
      final notifications = <Notification>[];
      final errors = <String>[];
      
      // 获取所有通知
      for (final id in notificationIds) {
        final result = await _repository.getNotification(id);
        result.fold(
          (failure) => errors.add('获取通知 $id 失败: ${failure.message}'),
          (notification) => notifications.add(notification),
        );
      }
      
      // 归档通知
      final archivedNotifications = notifications.map((n) => n.archive()).toList();
      
      final updateResult = await _repository.updateNotifications(archivedNotifications);
      
      return updateResult.fold(
        (failure) => Left(failure),
        (updated) {
          final batchResult = BatchOperationResult(
            successCount: updated.length,
            failureCount: notificationIds.length - updated.length,
            failedIds: [],
            errors: errors,
          );
          return Right(batchResult);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '批量归档通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteNotification(String notificationId) async {
    try {
      return await _repository.deleteNotification(notificationId);
    } catch (e) {
      return Left(UnknownFailure(message: '删除通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BatchOperationResult>> deleteNotifications(List<String> notificationIds) async {
    try {
      await _repository.deleteNotifications(notificationIds);
      
      final batchResult = BatchOperationResult(
        successCount: notificationIds.length,
        failureCount: 0,
        failedIds: [],
        errors: [],
      );
      
      return Right(batchResult);
    } catch (e) {
      return Left(UnknownFailure(message: '批量删除通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearAllNotifications(String userId) async {
    try {
      final query = NotificationQuery(userId: userId, limit: 1000);
      final notificationsResult = await _repository.getNotifications(query);
      
      return await notificationsResult.fold(
        (failure) async => Left(failure),
        (notifications) async {
          final notificationIds = notifications.map((n) => n.id).toList();
          return await _repository.deleteNotifications(notificationIds);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '清空所有通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, NotificationStats>> getNotificationStats(String userId) async {
    try {
      return await _repository.getNotificationStats(userId);
    } catch (e) {
      return Left(UnknownFailure(message: '获取通知统计失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getUnreadCount(String userId) async {
    try {
      return await _repository.getUnreadCount(userId);
    } catch (e) {
      return Left(UnknownFailure(message: '获取未读通知数量失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> searchNotifications({
    required String userId,
    required String query,
    List<NotificationType>? types,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final searchQuery = NotificationQuery(
        userId: userId,
        types: types,
        searchQuery: query,
        limit: limit,
        offset: offset,
      );
      return await _repository.getNotifications(searchQuery);
    } catch (e) {
      return Left(UnknownFailure(message: '搜索通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> getNotificationsByType({
    required String userId,
    required NotificationType type,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final query = NotificationQuery(
        userId: userId,
        types: [type],
        limit: limit,
        offset: offset,
      );
      return await _repository.getNotifications(query);
    } catch (e) {
      return Left(UnknownFailure(message: '按类型获取通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> getNotificationsByPriority({
    required String userId,
    required NotificationPriority priority,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final query = NotificationQuery(
        userId: userId,
        priorities: [priority],
        limit: limit,
        offset: offset,
      );
      return await _repository.getNotifications(query);
    } catch (e) {
      return Left(UnknownFailure(message: '按优先级获取通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, List<Notification>>>> getGroupedNotifications({
    required String userId,
    String? groupId,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final query = NotificationQuery(
        userId: userId,
        groupId: groupId,
        limit: limit,
        offset: offset,
      );
      
      final notificationsResult = await _repository.getNotifications(query);
      
      return notificationsResult.fold(
        (failure) => Left(failure),
        (notifications) {
          final grouped = <String, List<Notification>>{};
          
          for (final notification in notifications) {
            final group = notification.groupId ?? 'default';
            grouped.putIfAbsent(group, () => []).add(notification);
          }
          
          return Right(grouped);
        },
      );
    } catch (e) {
      return Left(UnknownFailure(message: '获取分组通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> cleanupExpiredNotifications() async {
    try {
      return await _repository.cleanupExpiredNotifications();
    } catch (e) {
      return Left(UnknownFailure(message: '清理过期通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> cleanupDeletedNotifications() async {
    try {
      return await _repository.cleanupDeletedNotifications();
    } catch (e) {
      return Left(UnknownFailure(message: '清理已删除通知失败: $e'));
    }
  }

  @override
  Stream<List<Notification>> subscribeToNotifications(String userId) {
    return _repository.subscribeToNotifications(userId);
  }

  @override
  Stream<int> subscribeToUnreadCount(String userId) {
    return _repository.subscribeToUnreadCount(userId);
  }

  // TODO: 实现推送通知相关方法
  @override
  Future<Either<Failure, void>> sendPushNotification({
    required String userId,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      // TODO: 集成Firebase Cloud Messaging
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '发送推送通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, BatchOperationResult>> sendBatchPushNotifications({
    required List<String> userIds,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      // TODO: 实现批量推送通知
      final batchResult = BatchOperationResult(
        successCount: userIds.length,
        failureCount: 0,
        failedIds: [],
        errors: [],
      );
      return Right(batchResult);
    } catch (e) {
      return Left(UnknownFailure(message: '批量发送推送通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> scheduleNotification({
    required Notification notification,
    required DateTime scheduledAt,
  }) async {
    try {
      // TODO: 实现定时通知
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '定时通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelScheduledNotification(String notificationId) async {
    try {
      // TODO: 实现取消定时通知
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '取消定时通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Notification>>> getScheduledNotifications(String userId) async {
    try {
      // TODO: 实现获取定时通知列表
      return const Right([]);
    } catch (e) {
      return Left(UnknownFailure(message: '获取定时通知失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updateNotificationSettings({
    required String userId,
    required Map<String, dynamic> settings,
  }) async {
    try {
      // TODO: 实现更新通知设置
      return const Right(null);
    } catch (e) {
      return Left(UnknownFailure(message: '更新通知设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getNotificationSettings(String userId) async {
    try {
      // TODO: 实现获取通知设置
      return const Right({});
    } catch (e) {
      return Left(UnknownFailure(message: '获取通知设置失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkNotificationPermission() async {
    try {
      // TODO: 实现检查通知权限
      return const Right(true);
    } catch (e) {
      return Left(UnknownFailure(message: '检查通知权限失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> requestNotificationPermission() async {
    try {
      // TODO: 实现请求通知权限
      return const Right(true);
    } catch (e) {
      return Left(UnknownFailure(message: '请求通知权限失败: $e'));
    }
  }
}
