import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification.freezed.dart';
part 'notification.g.dart';

/// 通知类型
enum NotificationType {
  system,           // 系统通知
  project,          // 项目相关
  bom,              // BOM相关
  timeline,         // 时间轴相关
  material,         // 材料相关
  share,            // 分享相关
  comment,          // 评论相关
  reminder,         // 提醒通知
  warning,          // 警告通知
  error,            // 错误通知
}

/// 通知优先级
enum NotificationPriority {
  low,              // 低优先级
  normal,           // 普通优先级
  high,             // 高优先级
  urgent,           // 紧急优先级
}

/// 通知状态
enum NotificationStatus {
  unread,           // 未读
  read,             // 已读
  archived,         // 已归档
  deleted,          // 已删除
}

/// 通知实体
@freezed
class Notification with _$Notification {
  const factory Notification({
    required String id,
    required String userId,
    required NotificationType type,
    required NotificationPriority priority,
    required NotificationStatus status,
    required String title,
    required String message,
    String? subtitle,
    String? imageUrl,
    String? iconUrl,
    String? actionUrl,
    String? actionText,
    @Default({}) Map<String, dynamic> data,
    @Default({}) Map<String, dynamic> metadata,
    required DateTime createdAt,
    DateTime? readAt,
    DateTime? archivedAt,
    DateTime? expiresAt,
    String? sourceId,
    String? sourceType,
    String? groupId,
    @Default(false) bool isPersistent,
    @Default(false) bool isRemote,
    @Default(true) bool canDismiss,
    @Default(false) bool requiresAction,
  }) = _Notification;

  factory Notification.fromJson(Map<String, dynamic> json) => 
      _$NotificationFromJson(json);
}

/// 通知扩展方法
extension NotificationX on Notification {
  /// 是否未读
  bool get isUnread => status == NotificationStatus.unread;

  /// 是否已读
  bool get isRead => status == NotificationStatus.read;

  /// 是否已归档
  bool get isArchived => status == NotificationStatus.archived;

  /// 是否已删除
  bool get isDeleted => status == NotificationStatus.deleted;

  /// 是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 是否可见
  bool get isVisible => !isDeleted && !isExpired;

  /// 获取类型显示名称
  String get typeDisplayName {
    switch (type) {
      case NotificationType.system:
        return '系统通知';
      case NotificationType.project:
        return '项目通知';
      case NotificationType.bom:
        return 'BOM通知';
      case NotificationType.timeline:
        return '时间轴通知';
      case NotificationType.material:
        return '材料通知';
      case NotificationType.share:
        return '分享通知';
      case NotificationType.comment:
        return '评论通知';
      case NotificationType.reminder:
        return '提醒通知';
      case NotificationType.warning:
        return '警告通知';
      case NotificationType.error:
        return '错误通知';
    }
  }

  /// 获取优先级显示名称
  String get priorityDisplayName {
    switch (priority) {
      case NotificationPriority.low:
        return '低';
      case NotificationPriority.normal:
        return '普通';
      case NotificationPriority.high:
        return '高';
      case NotificationPriority.urgent:
        return '紧急';
    }
  }

  /// 获取优先级颜色
  int get priorityColor {
    switch (priority) {
      case NotificationPriority.low:
        return 0xFF9E9E9E; // 灰色
      case NotificationPriority.normal:
        return 0xFF2196F3; // 蓝色
      case NotificationPriority.high:
        return 0xFFFF9800; // 橙色
      case NotificationPriority.urgent:
        return 0xFFF44336; // 红色
    }
  }

  /// 获取类型图标
  String get typeIcon {
    switch (type) {
      case NotificationType.system:
        return 'system_update';
      case NotificationType.project:
        return 'folder';
      case NotificationType.bom:
        return 'list_alt';
      case NotificationType.timeline:
        return 'timeline';
      case NotificationType.material:
        return 'inventory';
      case NotificationType.share:
        return 'share';
      case NotificationType.comment:
        return 'comment';
      case NotificationType.reminder:
        return 'alarm';
      case NotificationType.warning:
        return 'warning';
      case NotificationType.error:
        return 'error';
    }
  }

  /// 获取时间显示文本
  String get timeDisplayText {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${createdAt.year}-${createdAt.month.toString().padLeft(2, '0')}-${createdAt.day.toString().padLeft(2, '0')}';
    }
  }

  /// 标记为已读
  Notification markAsRead() {
    return copyWith(
      status: NotificationStatus.read,
      readAt: DateTime.now(),
    );
  }

  /// 标记为未读
  Notification markAsUnread() {
    return copyWith(
      status: NotificationStatus.unread,
      readAt: null,
    );
  }

  /// 归档通知
  Notification archive() {
    return copyWith(
      status: NotificationStatus.archived,
      archivedAt: DateTime.now(),
    );
  }

  /// 删除通知
  Notification delete() {
    return copyWith(
      status: NotificationStatus.deleted,
    );
  }

  /// 创建系统通知
  factory Notification.system({
    required String userId,
    required String title,
    required String message,
    String? subtitle,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionText,
  }) {
    return Notification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: NotificationType.system,
      priority: priority,
      status: NotificationStatus.unread,
      title: title,
      message: message,
      subtitle: subtitle,
      data: data ?? {},
      actionUrl: actionUrl,
      actionText: actionText,
      createdAt: DateTime.now(),
      isPersistent: true,
    );
  }

  /// 创建项目通知
  factory Notification.project({
    required String userId,
    required String projectId,
    required String title,
    required String message,
    String? subtitle,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionText,
  }) {
    return Notification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: NotificationType.project,
      priority: priority,
      status: NotificationStatus.unread,
      title: title,
      message: message,
      subtitle: subtitle,
      sourceId: projectId,
      sourceType: 'project',
      data: data ?? {},
      actionUrl: actionUrl,
      actionText: actionText,
      createdAt: DateTime.now(),
    );
  }

  /// 创建提醒通知
  factory Notification.reminder({
    required String userId,
    required String title,
    required String message,
    String? subtitle,
    NotificationPriority priority = NotificationPriority.high,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionText,
    DateTime? expiresAt,
  }) {
    return Notification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: NotificationType.reminder,
      priority: priority,
      status: NotificationStatus.unread,
      title: title,
      message: message,
      subtitle: subtitle,
      data: data ?? {},
      actionUrl: actionUrl,
      actionText: actionText,
      expiresAt: expiresAt,
      createdAt: DateTime.now(),
      requiresAction: true,
    );
  }

  /// 创建警告通知
  factory Notification.warning({
    required String userId,
    required String title,
    required String message,
    String? subtitle,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionText,
  }) {
    return Notification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: NotificationType.warning,
      priority: NotificationPriority.high,
      status: NotificationStatus.unread,
      title: title,
      message: message,
      subtitle: subtitle,
      data: data ?? {},
      actionUrl: actionUrl,
      actionText: actionText,
      createdAt: DateTime.now(),
      isPersistent: true,
    );
  }

  /// 创建错误通知
  factory Notification.error({
    required String userId,
    required String title,
    required String message,
    String? subtitle,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? actionText,
  }) {
    return Notification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: NotificationType.error,
      priority: NotificationPriority.urgent,
      status: NotificationStatus.unread,
      title: title,
      message: message,
      subtitle: subtitle,
      data: data ?? {},
      actionUrl: actionUrl,
      actionText: actionText,
      createdAt: DateTime.now(),
      isPersistent: true,
      requiresAction: true,
    );
  }
}
